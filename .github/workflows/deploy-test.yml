# in JAVA_TOOL_OPTIONSJAVA_TOOL_OPTIONS add -Dfr.application.name=JAVA_SPRING_TEST_DARG

name: Deploy to Test Server

on:
  workflow_run:
    workflows:
      - build
    branches:
      - test
    types:
      - completed

permissions:
  contents: read

jobs:
  build:
    runs-on: [ self-hosted, Linux, X64, test-server ]

    if: ${{ github.event.workflow_run.conclusion == 'success' }}

    steps:
      - uses: actions/checkout@v3

      - name: Create a multiline file
        run: |
          cat << EOF > env.vars
          SPRING_DATASOURCE_WEB_URL=jdbc:mysql://${{ vars.TEST_MYSQL_HOSTNAME }}:3306/${{ vars.TEST_MYSQL_DBNAME_WEB }}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=America/Chicago 
          SPRING_DATASOURCE_WEB_USERNAME=${{ secrets.TEST_MYSQL_USERNAME }}
          SPRING_DATASOURCE_WEB_PASSWORD=${{ secrets.TEST_MYSQL_PASSWORD }}
          SPRING_DATASOURCE_FMCSA_URL=jdbc:mysql://${{ vars.TEST_MYSQL_HOSTNAME }}:3306/${{ vars.TEST_MYSQL_DBNAME_FMCSA }}?useSSL=false&allowPublicKeyRetrieval=true
          SPRING_DATASOURCE_FMCSA_USERNAME=${{ secrets.TEST_MYSQL_USERNAME }}
          SPRING_DATASOURCE_FMCSA_PASSWORD=${{ secrets.TEST_MYSQL_PASSWORD }} 
          SPRING_RABBITMQ_HOST=rabbitmq
          SPRING_PROFILES_ACTIVE=test,scheduler
          JWT_SECRET=${{ secrets.JWT_PRIVATE_KEY }}
          SPRING_MAIL_HOST=${{ vars.EMAIL_SERVER }}
          SPRING_MAIL_PORT=${{ vars.EMAIL_PORT }}
          SPRING_MAIL_USERNAME=${{ secrets.EMAIL_USERNAME }}
          SPRING_MAIL_PASSWORD=${{ secrets.EMAIL_PASSWORD }}          
          BULKLOADS_PUBNUB_PUB_KEY=${{ secrets.TEST_PUBNUB_PUB_KEY }}
          BULKLOADS_PUBNUB_SUB_KEY=${{ secrets.TEST_PUBNUB_SUB_KEY }}
          BULKLOADS_AGTRAX_PROVIDER=${{ secrets.TEST_AGTRAX_PROVIDER }}
          BULKLOADS_AGTRAX_PROVIDER_PASSWORD=${{ secrets.TEST_AGTRAX_PROVIDER_PASSWORD }}
          BULKLOADS_AGTRAX_USERNAME=${{ secrets.TEST_AGTRAX_USERNAME }}
          BULKLOADS_AGTRAX_PASSWORD=${{ secrets.TEST_AGTRAX_PASSWORD }}
          BULKLOADS_AWS_ACCESS_KEY=${{ secrets.TEST_AWS_ACCESS_KEY }}
          BULKLOADS_AWS_SECRET_KEY=${{ secrets.TEST_AWS_SECRET_KEY }}
          BULKLOADS_FILE_OCR_API_KEY=${{ secrets.TEST_FILE_OCR_API_KEY }}
          HOST_NODE_NAME=test
          JAVA_TOOL_OPTIONS=-Xms2g -Xmx4g -XX:MaxDirectMemorySize=64m -XX:MaxMetaspaceSize=512m -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
          EOF

      - name: Start RabbitMQ
        run: docker compose up -d rabbitmq

      - name: Create Alertmanager config from template
        run: |
          cat config/alertmanager/config.template.yml | \
          sed "s/\${HOST_NODE_NAME}/test/g" | \
          sed "s/\${MAIL_USERNAME}/${{ secrets.EMAIL_USERNAME }}/g" | \
          sed "s/\${MAIL_PASSWORD}/${{ secrets.EMAIL_PASSWORD }}/g" | \
          sed "s/\${MAIL_HOST}/${{ vars.EMAIL_SERVER }}/g" | \
          sed "s/\${MAIL_PORT}/${{ vars.EMAIL_PORT }}/g" > config/alertmanager/config.yml

      - name: Start Prometheus/Alertmanager
        run: docker compose up -d prometheus alertmanager

      - name: Dockerize (including FusionReactor)
        run: |
          ./gradlew clean bootBuildImage -PfusionReactorInstanceName=fr_test

      - name: Start Bulkloads Server
        env:
          EXTERNAL_PORT: 9000
          FUSION_REACTOR_CONTEXT: /home/<USER>/fusionreactor
          SERVER_CONTAINER_NAME: test-server
        run: docker compose up -d bulkloads-server

      - name: Wait for API to be ready
        uses: mydea/action-wait-for-api@v1
        with:
          url: http://localhost:9000/actuator/health
          expected-response-field: status
          expected-response-field-value: UP
          timeout: 1800

      - name: Match the SHA (current commit hash)
        uses: mydea/action-wait-for-api@v1
        with:
          url: http://localhost:9000/actuator/info
          expected-response-field: 'git.commit.id.full'
          expected-response-field-value: ${{ github.sha }}
          timeout: 10
          interval: 5
