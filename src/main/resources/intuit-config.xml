<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- QuickBooks Online API Configuration -->
    
    <!-- Base URLs for different environments -->
    <!-- Sandbox URL (default) -->
    <property name="BASE_URL_QBO" value="https://sandbox-quickbooks.api.intuit.com/v3/company"/>
    
    <!-- Production URL (uncomment for production) -->
    <!-- <property name="BASE_URL_QBO" value="https://quickbooks.api.intuit.com/v3/company"/> -->
    
    <!-- Logging Configuration -->
    <!-- Enable request/response logging -->
    <property name="REQUEST_RESPONSE_LOGGING_ENABLED" value="true"/>
    
    <!-- Enable detailed logging -->
    <property name="LOGGING_ENABLED" value="true"/>
    
    <!-- Enable HTTP request/response logging -->
    <property name="ENABLE_REQUEST_RESPONSE_LOGGING" value="true"/>
    
    <!-- Log level configuration -->
    <property name="LOG_LEVEL" value="DEBUG"/>
    
    <!-- HTTP Configuration -->
    <!-- Connection timeout in milliseconds -->
    <property name="TIMEOUT" value="30000"/>
    
    <!-- Read timeout in milliseconds -->
    <property name="READ_TIMEOUT" value="30000"/>
    
    <!-- Connection pool size -->
    <property name="MAX_CONNECTIONS" value="10"/>
    
    <!-- Retry Configuration -->
    <!-- Enable retry on failure -->
    <property name="RETRY_ENABLED" value="true"/>
    
    <!-- Maximum number of retries -->
    <property name="MAX_RETRY_ATTEMPTS" value="3"/>
    
    <!-- Retry delay in milliseconds -->
    <property name="RETRY_DELAY" value="1000"/>
    
    <!-- Compression Configuration -->
    <!-- Enable GZIP compression -->
    <property name="COMPRESSION_ENABLED" value="true"/>
    
    <!-- Response Format -->
    <!-- Default response format (JSON or XML) -->
    <property name="RESPONSE_FORMAT" value="JSON"/>
    
    <!-- Security Configuration -->
    <!-- Enable SSL verification -->
    <property name="SSL_VERIFICATION_ENABLED" value="true"/>
    
    <!-- Custom User Agent -->
    <property name="USER_AGENT" value="BulkLoads-QuickBooks-Integration/1.0"/>
    
    <!-- Development/Debug Settings -->
    <!-- Enable verbose logging for development -->
    <property name="VERBOSE_LOGGING" value="true"/>
    
    <!-- Log request headers -->
    <property name="LOG_REQUEST_HEADERS" value="true"/>
    
    <!-- Log response headers -->
    <property name="LOG_RESPONSE_HEADERS" value="true"/>
    
    <!-- Log request body -->
    <property name="LOG_REQUEST_BODY" value="true"/>
    
    <!-- Log response body -->
    <property name="LOG_RESPONSE_BODY" value="true"/>
    
    <!-- Performance Monitoring -->
    <!-- Enable performance metrics -->
    <property name="PERFORMANCE_MONITORING_ENABLED" value="true"/>
    
    <!-- Log execution time -->
    <property name="LOG_EXECUTION_TIME" value="true"/>
    
    <!-- Environment-specific settings -->
    <!-- These can be overridden programmatically based on active profile -->
    
    <!-- Sandbox-specific settings -->
    <property name="SANDBOX_MODE" value="true"/>
    
    <!-- Rate limiting -->
    <property name="RATE_LIMIT_ENABLED" value="true"/>
    <property name="REQUESTS_PER_MINUTE" value="500"/>
    
</configuration>
