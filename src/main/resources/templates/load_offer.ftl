<#-- @ftlvariable name="prodMode" type="boolean" -->
<#-- @ftlvariable name="log" type="org.slf4j.Logger" -->

<#-- @ftlvariable name="loadStatusTitle" type="String" -->
<#-- @ftlvariable name="model" type="com.bulkloads.web.offer.domain.template.OfferTemplateModel" -->

<#include "utils.ftl">

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
  <title>Load Offer</title>
</head>
<body>

From: ${model.fromFirstName} ${model.fromLastName} - ${model.fromCompanyName} <br/>
Phone: ${model.fromPhone} <br/>
Email: ${model.fromEmail} <br/>
<br/>
<#if model.message??>
  <div style="text-align: left; font-weight: bold; font-size: 1.2em;">
      ${model.message}
  </div>
  <br>
  <br>
</#if>
${model.pickupCity}, ${model.pickupState} to ${model.dropCity}, ${model.dropState}<br/>
${model.pickupCompanyName} - ${model.dropCompanyName}<br/>
<#if model.pickupDropMiles?has_content>
    ${model.pickupDropMiles} Miles<br/>
</#if>
${model.numberOfAvailableLoads} Loads Available<br/>

<#if model.shipFrom?has_content>
  Loading ${dateFormat(model.shipFrom, "MMM d")}
    <#if model.shipTo?has_content>
      to ${dateFormat(model.shipTo, "MMM d")}<br>
    </#if>
</#if>
<br>
<#if model.loCommodity??>
    ${model.loCommodity} <br>
</#if>
${model.rateMessage} <br>
<br>
Call ${model.fromFirstName} at
<#if model.fromCellPhone??>
    ${model.fromCellPhone}
<#else>
    ${model.fromPhone}
</#if> to book <br><br>
You can also view all of your offers and assignments in the BulkLoads.com mobile app.

<a href="${model.link.dynamicLink}">View details for this offer</a>
<a href="${model.link.originalLink}" class="link_url" style="display:none;"></a>

</body>
</html>