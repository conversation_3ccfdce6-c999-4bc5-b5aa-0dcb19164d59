<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- QuickBooks Online API Configuration - PRODUCTION -->
    
    <!-- Production URL -->
    <property name="BASE_URL_QBO" value="https://quickbooks.api.intuit.com/v3/company"/>
    
    <!-- Logging Configuration - Conservative for Production -->
    <!-- Enable basic logging but disable verbose details -->
    <property name="REQUEST_RESPONSE_LOGGING_ENABLED" value="true"/>
    <property name="LOGGING_ENABLED" value="true"/>
    <property name="ENABLE_REQUEST_RESPONSE_LOGGING" value="false"/>
    
    <!-- Disable verbose logging for production -->
    <property name="VERBOSE_LOGGING" value="false"/>
    <property name="LOG_REQUEST_BODY" value="false"/>
    <property name="LOG_RESPONSE_BODY" value="false"/>
    <property name="LOG_REQUEST_HEADERS" value="false"/>
    <property name="LOG_RESPONSE_HEADERS" value="false"/>
    
    <!-- Enable performance monitoring -->
    <property name="LOG_EXECUTION_TIME" value="true"/>
    <property name="PERFORMANCE_MONITORING_ENABLED" value="true"/>
    
    <!-- Production HTTP Configuration -->
    <!-- Longer timeouts for production -->
    <property name="TIMEOUT" value="60000"/>
    <property name="READ_TIMEOUT" value="60000"/>
    
    <!-- Higher connection pool for production -->
    <property name="MAX_CONNECTIONS" value="20"/>
    
    <!-- Retry Configuration -->
    <property name="RETRY_ENABLED" value="true"/>
    <property name="MAX_RETRY_ATTEMPTS" value="3"/>
    <property name="RETRY_DELAY" value="2000"/>
    
    <!-- Compression Configuration -->
    <property name="COMPRESSION_ENABLED" value="true"/>
    
    <!-- Response Format -->
    <property name="RESPONSE_FORMAT" value="JSON"/>
    
    <!-- Security Configuration -->
    <property name="SSL_VERIFICATION_ENABLED" value="true"/>
    
    <!-- Custom User Agent -->
    <property name="USER_AGENT" value="BulkLoads-QuickBooks-Integration/1.0"/>
    
    <!-- Production Settings -->
    <property name="SANDBOX_MODE" value="false"/>
    
    <!-- Conservative rate limiting for production -->
    <property name="RATE_LIMIT_ENABLED" value="true"/>
    <property name="REQUESTS_PER_MINUTE" value="450"/>
    
</configuration>
