-- liquibase formatted sql

-- changeset theo:20250529-1800

create table user_company_equipment_geo_history (
    id int auto_increment primary key,
    user_company_equipment_id int not null,
    latitude double not null,
    longitude double not null,
    bearing int,
    speed double,
    created_on timestamp not null,
    `timestamp` timestamp not null default current_timestamp,
    provider_id varchar(20) not null default '',
    constraint fk_geo_history_user_company
        foreign key (user_company_equipment_id)
        references user_company_equipments (user_company_equipment_id)
);

call CreateIndex('user_company_equipment_geo_history', 'idx_user_company_geo_history_user_company_equipments','user_company_equipment_id');
call CreateIndex('user_company_equipment_geo_history', 'idx_user_company_geo_history_recorded_at','created_on');

-- rollback drop table user_company_equipment_geo_history;

-- changeset theo:20250605-1500

CALL createEndpointAndPermissions('Company Equipments','GET','/companies/equipments/current_locations','Get Company Equipments Historical Locations','1,2');

-- changeset theo:20250606-0900

create table shedlock
(
    name       varchar(64)  not null,
    lock_until timestamp(3) not null,
    locked_at  timestamp(3) not null default current_timestamp(3),
    locked_by  varchar(255) not null,
    primary key (name)
);

