-- liquibase formatted sql

-- changeset andreas:20241113-1

SET SESSION sql_mode = 'ALLOW_INVALID_DATES';

ALTER TABLE commodities CHANGE COLUMN def_rate_type def_rate_type VARCHAR(10) NULL DEFAULT '2000';

ALTER TABLE contracts
    CHANGE COLUMN freight_rate_type freight_rate_type VARCHAR(10) NULL DEFAULT '2000',
    CHANGE COLUMN rate_type rate_type VARCHAR(10) NULL DEFAULT '2000';

ALTER TABLE load_assignments
    CHANGE COLUMN original_rate_type original_rate_type VARCHAR(10) NULL DEFAULT '2000',
    CHANGE COLUMN previous_rate_type previous_rate_type VARCHAR(10) NULL DEFAULT '2000',
    CHANGE COLUMN rate_type rate_type VARCHAR(10) NULL DEFAULT '2000';

ALTER TABLE load_invoice_items
    CHANGE COLUMN bill_rate_type bill_rate_type VARCHAR(10) NULL DEFAULT '2000',
    CHANGE COLUMN previous_bill_rate_type previous_bill_rate_type VARCHAR(10) NULL DEFAULT '2000';

ALTER TABLE load_offers CHANGE COLUMN offer_rate_type offer_rate_type VARCHAR(10) NULL DEFAULT '2000';

ALTER TABLE loads CHANGE COLUMN lo_rate_type lo_rate_type VARCHAR(10) NULL DEFAULT '2000';

ALTER TABLE offers CHANGE COLUMN offer_rate_type offer_rate_type VARCHAR(10) NULL DEFAULT '2000';

ALTER TABLE user_info CHANGE COLUMN default_driver_rate_type default_driver_rate_type VARCHAR(10) NULL DEFAULT '2000';


set sql_safe_updates = 0;

update commodities set def_rate_type = NULL WHERE def_rate_type = '';
update contracts set freight_rate_type = NULL WHERE freight_rate_type = '';
update contracts set rate_type = NULL WHERE rate_type = '';
update load_assignments set original_rate_type = NULL WHERE original_rate_type = '';
update load_assignments set previous_rate_type = NULL WHERE previous_rate_type = '';
update load_assignments set rate_type = NULL WHERE rate_type = '';
update load_invoice_items set bill_rate_type = NULL WHERE bill_rate_type = '';
update load_invoice_items set previous_bill_rate_type = NULL WHERE previous_bill_rate_type = '';
update load_offers set offer_rate_type = NULL WHERE offer_rate_type = '';
update loads set lo_rate_type = NULL WHERE lo_rate_type = '';
update offers set offer_rate_type = NULL WHERE offer_rate_type = '';
update user_info set default_driver_rate_type = NULL WHERE default_driver_rate_type = '';

set sql_safe_updates = 1;

