-- liquibase formatted sql

-- changeset andreas :20250423-0500

-- Landus API key
CALL createEndpointAndPermissions('', 'GET', '​/address_book​/user_types', '', '18');
CALL createEndpointAndPermissions('', 'GET', '​/cities', '', '18');
CALL createEndpointAndPermissions('', 'GET', '​/cities​/name', '', '18');
CALL createEndpointAndPermissions('', 'GET', '​/equipments', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/authorized_companies', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/authorized_users', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/contracts', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/contracts', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/contracts/close', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/contracts/reopen', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/loads', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/loads/complete', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}​/address_book​/companies', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}​/address_book​/companies', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}​/address_book​/companies​/{int}', '', '18');
CALL createEndpointAndPermissions('', 'PUT', '/integration/{int}​/address_book​/companies​/{int}', '', '18');
CALL createEndpointAndPermissions('', 'DELETE', '/integration/{int}​/address_book​/companies​/{int}', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}​/address_book​/users', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}​/address_book​/users', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}​/address_book​/users​/{int}', '', '18');
CALL createEndpointAndPermissions('', 'PUT', '/integration/{int}​/address_book​/users​/{int}', '', '18');
CALL createEndpointAndPermissions('', 'DELETE', '/integration/{int}​/address_book​/users​/{int}', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}​/files', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}​/files​/my_files', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/loads/assignments', '', '18');
CALL createEndpointAndPermissions('', 'GET', '​/products​/categories', '', '18');


-- changeset andreas :20250424-1700
CALL createEndpointAndPermissions('', 'GET', '/address_book/user_types', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/cities', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/cities/name', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/equipments', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/authorized_companies', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/authorized_users', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/contracts', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/contracts', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/contracts/close', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/contracts/reopen', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/loads', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/loads/complete', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/address_book/companies', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/address_book/companies', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/address_book/companies/{int}', '', '18');
CALL createEndpointAndPermissions('', 'PUT', '/integration/{int}/address_book/companies/{int}', '', '18');
CALL createEndpointAndPermissions('', 'DELETE', '/integration/{int}/address_book/companies/{int}', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/address_book/users', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/address_book/users', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/address_book/users/{int}', '', '18');
CALL createEndpointAndPermissions('', 'PUT', '/integration/{int}/address_book/users/{int}', '', '18');
CALL createEndpointAndPermissions('', 'DELETE', '/integration/{int}/address_book/users/{int}', '', '18');
CALL createEndpointAndPermissions('', 'POST', '/integration/{int}/files', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/files/my_files', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/integration/{int}/loads/assignments', '', '18');
CALL createEndpointAndPermissions('', 'GET', '/products/categories', '', '18');

-- CHS, N7, GrainWorx, Aurora, Aljex, MFA, Landus API keys
CALL createEndpointAndPermissions(
        'Address Book',
        'GET',
        '/integration/address_book/user_types',
        'Get User Types',
        '7,11,12,14,15,17,18'
     );

CALL createEndpointAndPermissions(
        'Cities',
        'GET',
        '/integration/cities',
        'Get Cities',
        '7,11,12,14,15,17,18'
     );

CALL createEndpointAndPermissions(
        'Cities',
        'GET',
        '/integration/cities/name',
        'Get City Names',
        '7,11,12,14,15,17,18'
     );

CALL createEndpointAndPermissions(
        'Equipments',
        'GET',
        '/integration/equipments',
        'Get Equipments',
        '7,11,12,14,15,17,18'
     );

CALL createEndpointAndPermissions(
        'Products',
        'GET',
        '/integration/products/categories',
        'Get Product Categories',
        '7,11,12,14,15,17,18'
     );

-- changeset andreas :20250424-1800

-- Hansen Mueller managed by GrainWorx
INSERT INTO api_key_integration_permissions (api_key_id, user_company_id)
SELECT '7', uc.user_company_id
FROM user_company uc
WHERE uc.company_name = 'HANSEN-MUELLER'
  AND NOT EXISTS (SELECT 1
                  FROM api_key_integration_permissions
                  WHERE api_key_id = '7'
                    AND user_company_id = uc.user_company_id);

