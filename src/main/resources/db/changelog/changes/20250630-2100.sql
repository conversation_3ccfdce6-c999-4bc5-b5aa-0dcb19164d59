-- liquibase formatted sql

-- changeset theo:20250630-2100

create table user_info_hos_history
(
    id                               int auto_increment primary key,
    user_id                          int         not null,
    break_time_remaining             bigint      not null,
    cycle_time_remaining             bigint      not null,
    cycle_started_time               timestamp   null,
    cycle_tomorrow_time_remaining    bigint      not null,
    drive_time_remaining             bigint      not null,
    shift_time_remaining             bigint      not null,
    current_duty_status              varchar(30) not null,
    created_on                       timestamp   not null,
    provider_id                      varchar(20) not null default '',
    constraint fk_user_info_hos_user_info
        foreign key (user_id)
            references user_info (user_id)
);

call CreateIndex('user_info_hos_history', 'idx_user_info_hos_history_user_id','user_id');
call CreateIndex('user_info_hos_history', 'idx_user_info_hos_history_recorded_at','created_on');

-- rollback drop table user_info_hos_history;

-- changeset theo:20250631-1000

CALL createEndpointAndPermissions('Users','GET','/users/current_hos','Get Users Historical Hours of Service','1,2');

