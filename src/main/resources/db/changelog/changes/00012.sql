-- liquibase formatted sql

-- changeset petros:20250124


INSERT INTO `api_endpoints`
(`category`, `method`, `path`, `description`)
VALUES
    ('Forum', 'POST', '/files/{int}/ocr', 'Ocr file');

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2) -- mobile, TMS
  and (
    method = 'POST' and path = '/files/{int}/ocr'
    )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2) -- mobile, TMS
);
