-- liquibase formatted sql

-- changeset system:1729142640065-1
SET GLOBAL log_bin_trust_function_creators = 1;

CREATE TABLE ab_companies
(
    ab_company_id            INT AUTO_INCREMENT        NOT NULL,
    external_ab_company_id   VARCHAR(50)    DEFAULT '' NOT NULL,
    external_ab_company_type VARCHAR(100)   DEFAULT '' NOT NULL,
    user_id                  INT                       NOT NULL,
    user_company_id          INT                       NOT NULL,
    source                   VARCHAR(45)    DEFAULT '' NOT NULL,
    source_id                VARCHAR(45)    DEFAULT '' NOT NULL,
    facility_id              INT                       NULL,
    bl_user_company_id       INT                       NULL,
    company_name             VARCHAR(150)   DEFAULT '' NOT NULL,
    company_code             VARCHAR(10)    DEFAULT '' NOT NULL,
    census_num               DECIMAL(9)                NULL,
    mc_num                   VARCHAR(20)    DEFAULT '' NOT NULL,
    user_type_ids            VARCHAR(45)    DEFAULT '' NOT NULL,
    user_types               VARCHAR(100)   DEFAULT '' NOT NULL,
    company_phone            VARCHAR(25)    DEFAULT '' NOT NULL,
    company_phone_type       VARCHAR(10)    DEFAULT '' NOT NULL,
    company_email            VARCHAR(100)   DEFAULT '' NOT NULL,
    address                  VARCHAR(150)   DEFAULT '' NOT NULL,
    state                    VARCHAR(50)    DEFAULT '' NOT NULL,
    location                 VARCHAR(80)    DEFAULT '' NOT NULL,
    city                     VARCHAR(80)    DEFAULT '' NOT NULL,
    zip                      VARCHAR(25)    DEFAULT '' NOT NULL,
    country                  VARCHAR(50)    DEFAULT '' NOT NULL,
    longitude                DOUBLE                    NULL,
    latitude                 DOUBLE                    NULL,
    mailing_same_as_physical TINYINT        DEFAULT 0  NOT NULL,
    mailing_address          VARCHAR(150)   DEFAULT '' NOT NULL,
    mailing_location         VARCHAR(80)    DEFAULT '' NOT NULL,
    mailing_city             VARCHAR(80)    DEFAULT '' NOT NULL,
    mailing_state            VARCHAR(50)    DEFAULT '' NOT NULL,
    mailing_zip              VARCHAR(25)    DEFAULT '' NOT NULL,
    mailing_country          VARCHAR(50)    DEFAULT '' NOT NULL,
    private_notes            TEXT                      NULL,
    appt_required            TINYINT        DEFAULT 0  NOT NULL,
    receiving_hours          VARCHAR(50)    DEFAULT '' NOT NULL,
    directions               TEXT                      NULL,
    company_notes            MEDIUMTEXT                NULL,
    insurance_info           VARCHAR(200)   DEFAULT '' NOT NULL,
    ins_liab_company         VARCHAR(100)   DEFAULT '' NOT NULL,
    ins_liab_policy          VARCHAR(50)    DEFAULT '' NOT NULL,
    ins_liab_exp_date        date                      NULL,
    ins_liab_phone           VARCHAR(25)    DEFAULT '' NOT NULL,
    ins_liab_contact         VARCHAR(100)   DEFAULT '' NOT NULL,
    ins_liab_amount          INT                       NULL,
    ins_liab_notes           TEXT                      NULL,
    ins_work_same_as_liab    TINYINT        DEFAULT 1  NOT NULL,
    ins_work_company         VARCHAR(100)   DEFAULT '' NOT NULL,
    ins_work_policy          VARCHAR(50)    DEFAULT '' NOT NULL,
    ins_work_exp_date        date                      NULL,
    ins_work_phone           VARCHAR(25)    DEFAULT '' NOT NULL,
    ins_work_contact         VARCHAR(100)   DEFAULT '' NOT NULL,
    ins_work_amount          INT                       NULL,
    ins_work_notes           TEXT                      NULL,
    ins_cargo_same_as_liab   TINYINT        DEFAULT 1  NOT NULL,
    ins_cargo_company        VARCHAR(100)   DEFAULT '' NOT NULL,
    ins_cargo_policy         VARCHAR(50)    DEFAULT '' NOT NULL,
    ins_cargo_exp_date       date                      NULL,
    ins_cargo_phone          VARCHAR(25)    DEFAULT '' NOT NULL,
    ins_cargo_contact        VARCHAR(100)   DEFAULT '' NOT NULL,
    ins_cargo_amount         INT                       NULL,
    ins_cargo_notes          TEXT                      NULL,
    balance                  DECIMAL(12, 2) DEFAULT 0  NOT NULL,
    freq                     INT            DEFAULT 0  NOT NULL,
    number_of_files          INT            DEFAULT 0  NOT NULL,
    risk_assessment_overall  VARCHAR(20)    DEFAULT '' NOT NULL,
    mcp_monitored            TINYINT        DEFAULT 0  NOT NULL,
    mcp_details_url          VARCHAR(100)   DEFAULT '' NOT NULL,
    mcp_sync_date            datetime                  NULL,
    auth_code                VARCHAR(50)    DEFAULT '' NOT NULL COMMENT 'The auth_code is used by facilities to access a list of loads being picked up or dropped at that facility',
    date_added               datetime                  NOT NULL,
    modified_date            datetime                  NULL,
    date_deleted             datetime                  NULL,
    active                   TINYINT        DEFAULT 1  NOT NULL,
    deleted                  TINYINT        DEFAULT 0  NOT NULL,
    CONSTRAINT PK_AB_COMPANIES PRIMARY KEY (ab_company_id)
);


CREATE TABLE ab_company_files
(
    ab_company_id INT                                NOT NULL,
    file_id       INT                                NOT NULL,
    date_added    datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
    file_order    INT      DEFAULT 1                 NOT NULL,
    CONSTRAINT PK_AB_COMPANY_FILES PRIMARY KEY (ab_company_id, file_id)
);


CREATE TABLE ab_user_group_ab_users
(
    ab_user_group_id INT NOT NULL,
    ab_user_id       INT NOT NULL,
    CONSTRAINT PK_AB_USER_GROUP_AB_USERS PRIMARY KEY (ab_user_group_id, ab_user_id)
);


CREATE TABLE ab_user_groups
(
    ab_user_group_id INT AUTO_INCREMENT NOT NULL,
    user_id          INT                NOT NULL,
    user_company_id  INT                NULL,
    group_name       VARCHAR(100)       NOT NULL,
    date_added       datetime           NULL,
    deleted          TINYINT DEFAULT 0  NOT NULL,
    CONSTRAINT PK_AB_USER_GROUPS PRIMARY KEY (ab_user_group_id)
);


CREATE TABLE ab_user_roles
(
    ab_user_role_id INT AUTO_INCREMENT NOT NULL,
    ab_user_role    VARCHAR(45)        NOT NULL,
    `description`   MEDIUMTEXT         NULL,
    CONSTRAINT PK_AB_USER_ROLES PRIMARY KEY (ab_user_role_id)
);


CREATE TABLE ab_user_roles_ref
(
    ab_user_id      INT NOT NULL,
    ab_user_role_id INT NOT NULL,
    CONSTRAINT PK_AB_USER_ROLES_REF PRIMARY KEY (ab_user_id, ab_user_role_id)
);


CREATE TABLE ab_users
(
    ab_user_id                 INT AUTO_INCREMENT                                NOT NULL,
    external_ab_user_id        VARCHAR(50)                       DEFAULT ''      NOT NULL,
    ab_company_id              INT                                               NOT NULL,
    user_id                    INT                                               NOT NULL,
    user_company_id            INT                                               NOT NULL,
    source                     VARCHAR(45)                       DEFAULT ''      NOT NULL,
    source_id                  VARCHAR(45)                       DEFAULT ''      NOT NULL,
    bl_user_id                 INT                                               NULL,
    ab_user_role_ids           VARCHAR(100)                      DEFAULT ''      NOT NULL,
    ab_user_roles              VARCHAR(200)                      DEFAULT ''      NOT NULL,
    invitation_id              INT                                               NULL,
    first_name                 VARCHAR(30)                                       NOT NULL,
    last_name                  VARCHAR(45)                                       NOT NULL,
    email                      VARCHAR(100)                      DEFAULT ''      NOT NULL,
    bad_email_date             datetime                                          NULL,
    bad_email_reason           VARCHAR(45)                       DEFAULT ''      NOT NULL,
    phone_1                    VARCHAR(25)                       DEFAULT ''      NOT NULL,
    phone_1_type               VARCHAR(10)                       DEFAULT ''      NOT NULL,
    phone_2                    VARCHAR(25)                       DEFAULT ''      NOT NULL,
    phone_2_type               VARCHAR(10)                       DEFAULT ''      NOT NULL,
    preferred_contact_method   ENUM ('', 'email', 'sms', 'both') DEFAULT 'email' NOT NULL,
    ab_user_notes              MEDIUMTEXT                                        NULL,
    freq                       INT                               DEFAULT 0       NOT NULL COMMENT 'The number of times this user has been used so we can sort by most frequently used',
    default_geo_request_method ENUM ('sms', 'email')             DEFAULT 'email' NOT NULL,
    date_added                 datetime                                          NOT NULL,
    modified_date              datetime                                          NULL,
    date_deleted               datetime                                          NULL,
    deleted                    TINYINT                           DEFAULT 0       NOT NULL,
    CONSTRAINT PK_AB_USERS PRIMARY KEY (ab_user_id)
);


CREATE TABLE activities
(
    activity_id      INT AUTO_INCREMENT NOT NULL,
    user_id          INT                NOT NULL,
    user_company_id  INT                NOT NULL,
    added_date       datetime           NOT NULL,
    activity         VARCHAR(500)       NOT NULL,
    action           VARCHAR(100)       NOT NULL,
    activity_type_id INT                NOT NULL,
    data             VARCHAR(2000)      NOT NULL,
    load_id          INT                NULL,
    CONSTRAINT PK_ACTIVITIES PRIMARY KEY (activity_id)
);


CREATE TABLE activity_types
(
    activity_type_id  INT AUTO_INCREMENT NOT NULL,
    activity_type     VARCHAR(45)        NOT NULL,
    activity_metadata VARCHAR(500)       NOT NULL,
    CONSTRAINT PK_ACTIVITY_TYPES PRIMARY KEY (activity_type_id)
);


CREATE TABLE agtrax_scale_ticket_factors
(
    agtrax_scale_ticket_factor_id INT AUTO_INCREMENT      NOT NULL,
    agtrax_scale_ticket_id        INT                     NOT NULL,
    factor_code                   VARCHAR(45)  DEFAULT '' NOT NULL,
    factor_description            VARCHAR(255) DEFAULT '' NOT NULL,
    factor_value                  VARCHAR(45)  DEFAULT '' NOT NULL,
    CONSTRAINT PK_AGTRAX_SCALE_TICKET_FACTORS PRIMARY KEY (agtrax_scale_ticket_factor_id)
);


CREATE TABLE agtrax_scale_tickets
(
    agtrax_scale_ticket_id INT AUTO_INCREMENT      NOT NULL,
    user_company_id        INT                     NOT NULL,
    serial_id              INT                     NOT NULL,
    date                   datetime                NULL,
    ticket_number          INT                     NULL,
    branch_id              INT                     NULL,
    commodity_id           INT                     NULL,
    external_commodity_id  VARCHAR(50)             NOT NULL,
    gross_pounds           VARCHAR(50)             NOT NULL,
    tare_pounds            VARCHAR(50)             NOT NULL,
    reference1             VARCHAR(255) DEFAULT '' NOT NULL,
    reference2             VARCHAR(255) DEFAULT '' NOT NULL,
    discount_schedule_id   INT                     NULL,
    destination_id         INT                     NULL,
    hauler_id              INT                     NULL,
    origin_payload         JSON                    NULL,
    destination_payload    TEXT                    NULL,
    post_response          TEXT                    NULL,
    destination_file_id    INT                     NULL,
    load_assignment_id     INT                     NULL,
    created_date           date                    NULL,
    modified_date          date                    NULL,
    deleted_date           date                    NULL,
    deleted                TINYINT      DEFAULT 0  NULL,
    CONSTRAINT PK_AGTRAX_SCALE_TICKETS PRIMARY KEY (agtrax_scale_ticket_id)
);


CREATE TABLE agtrax_scale_tickets_log
(
    agtrax_scale_tickets_log_id INT AUTO_INCREMENT NOT NULL,
    agtrax_scale_ticket_id      INT                NOT NULL,
    request_date                datetime           NOT NULL,
    origin_payload              JSON               NULL,
    destination_payload         TEXT               NULL,
    post_response               TEXT               NULL,
    CONSTRAINT PK_AGTRAX_SCALE_TICKETS_LOG PRIMARY KEY (agtrax_scale_tickets_log_id)
);


CREATE TABLE agtrax_user_company_settings
(
    user_company_id INT                      NOT NULL,
    postback_url    VARCHAR(2048) DEFAULT '' NOT NULL,
    CONSTRAINT PK_AGTRAX_USER_COMPANY_SETTINGS PRIMARY KEY (user_company_id)
);


CREATE TABLE analytics_daily
(
    date                                date                                    NOT NULL,
    users_total                         INT           DEFAULT 0                 NOT NULL,
    users_tms                           INT           DEFAULT 0                 NOT NULL,
    users_tms_percent                   DECIMAL(5, 2) DEFAULT 0                 NOT NULL,
    users_mobile                        INT           DEFAULT 0                 NOT NULL,
    users_mobile_percent                DECIMAL(5, 2) DEFAULT 0                 NOT NULL,
    users_with_assignments              INT           DEFAULT 0                 NOT NULL,
    users_with_assignments_percent      DECIMAL(5, 2) DEFAULT 0                 NOT NULL,
    users_with_assignments_new          INT           DEFAULT 0                 NOT NULL,
    users_with_assignments_new_user_ids TEXT                                    NULL,
    assignments_total                   INT           DEFAULT 0                 NOT NULL,
    assignments_accepted                INT           DEFAULT 0                 NOT NULL,
    assignments_accepted_percent        DECIMAL(5, 2) DEFAULT 0                 NOT NULL,
    date_added                          datetime      DEFAULT CURRENT_TIMESTAMP NOT NULL,
    execution_ms                        INT           DEFAULT 0                 NOT NULL,
    CONSTRAINT PK_ANALYTICS_DAILY PRIMARY KEY (date)
);


CREATE TABLE api_endpoints
(
    api_endpoint_id INT AUTO_INCREMENT      NOT NULL,
    category        VARCHAR(50)             NOT NULL,
    method          VARCHAR(10)             NOT NULL,
    `path`          VARCHAR(150)            NOT NULL,
    `description`   VARCHAR(500) DEFAULT '' NOT NULL,
    CONSTRAINT PK_API_ENDPOINTS PRIMARY KEY (api_endpoint_id)
);


CREATE TABLE api_key_endpoints
(
    api_key_endpoint_id INT AUTO_INCREMENT NOT NULL,
    api_key_id          INT                NOT NULL,
    api_endpoint_id     INT                NOT NULL,
    CONSTRAINT PK_API_KEY_ENDPOINTS PRIMARY KEY (api_key_endpoint_id)
);


CREATE TABLE api_key_integration_permissions
(
    api_key_id      INT NOT NULL,
    user_company_id INT NOT NULL,
    CONSTRAINT PK_API_KEY_INTEGRATION_PERMISSIONS PRIMARY KEY (api_key_id, user_company_id)
);


CREATE TABLE api_keys
(
    api_key_id      INT                 NOT NULL,
    api_key         VARCHAR(90)         NOT NULL,
    app_name        VARCHAR(45)         NOT NULL,
    enabled         BIT DEFAULT 1       NOT NULL,
    quota           INT DEFAULT 1000000 NOT NULL,
    num_of_requests INT DEFAULT 0       NOT NULL,
    valid_until     datetime            NULL,
    CONSTRAINT PK_API_KEYS PRIMARY KEY (api_key_id)
);


CREATE TABLE app_carriers
(
    car_id          INT AUTO_INCREMENT      NOT NULL,
    name            VARCHAR(100) DEFAULT '' NOT NULL,
    company_name    VARCHAR(100) DEFAULT '' NOT NULL,
    address         VARCHAR(100) DEFAULT '' NOT NULL,
    phone           VARCHAR(100) DEFAULT '' NOT NULL,
    phone_ext       VARCHAR(100) DEFAULT '' NOT NULL,
    tax_id          VARCHAR(100) DEFAULT '' NOT NULL,
    app_carrierscol VARCHAR(45)  DEFAULT '' NOT NULL,
    inv_id          INT                     NOT NULL,
    status          INT          DEFAULT 0  NULL,
    email           VARCHAR(100) DEFAULT '' NOT NULL,
    CONSTRAINT PK_APP_CARRIERS PRIMARY KEY (car_id)
);


CREATE TABLE app_documents
(
    doc_id      INT AUTO_INCREMENT                                    NOT NULL,
    user_id     INT                                                   NOT NULL,
    send_status INT          DEFAULT 0                                NULL,
    load_number VARCHAR(255) DEFAULT ''                               NOT NULL,
    bol_number  VARCHAR(255) DEFAULT ''                               NOT NULL,
    created_at  datetime     DEFAULT CURRENT_TIMESTAMP                NULL,
    archived    INT          DEFAULT 0                                NULL,
    notes       MEDIUMTEXT                                            NULL,
    other       MEDIUMTEXT                                            NULL,
    doc_name    VARCHAR(100) DEFAULT ''                               NOT NULL,
    is_dirty    TINYINT      DEFAULT 1                                NOT NULL,
    modified_at datetime     DEFAULT null on update CURRENT_TIMESTAMP NULL,
    sent_at     datetime                                              NULL,
    CONSTRAINT PK_APP_DOCUMENTS PRIMARY KEY (doc_id)
);


CREATE TABLE app_invoices
(
    inv_id         INT AUTO_INCREMENT                                      NOT NULL,
    user_id        INT                                                     NOT NULL,
    doc_id         INT                                                     NULL,
    load_number    VARCHAR(255)   DEFAULT ''                               NOT NULL,
    archived       INT            DEFAULT 0                                NULL,
    paid           INT            DEFAULT 0                                NULL,
    notes          VARCHAR(1000)  DEFAULT ''                               NOT NULL,
    check_number   INT            DEFAULT 0                                NULL,
    total_amount   DECIMAL(10, 2) DEFAULT 0                                NULL,
    recipient      VARCHAR(100)   DEFAULT ''                               NOT NULL,
    invoice_number VARCHAR(45)    DEFAULT ''                               NOT NULL,
    created_at     datetime       DEFAULT CURRENT_TIMESTAMP                NULL,
    modified_at    datetime       DEFAULT null on update CURRENT_TIMESTAMP NULL,
    due_date       datetime                                                NULL,
    paid_date      datetime                                                NULL,
    CONSTRAINT PK_APP_INVOICES PRIMARY KEY (inv_id)
);


CREATE TABLE app_load_surcharge_types
(
    app_load_surcharge_type_id INT         NOT NULL,
    app_load_surcharge_type    VARCHAR(45) NOT NULL,
    CONSTRAINT PK_APP_LOAD_SURCHARGE_TYPES PRIMARY KEY (app_load_surcharge_type_id)
);


CREATE TABLE app_load_surcharges
(
    app_load_surcharge_id      INT AUTO_INCREMENT     NOT NULL,
    load_id                    INT                    NOT NULL,
    app_load_surcharge_type_id INT                    NOT NULL,
    surcharge_description      VARCHAR(45) DEFAULT '' NOT NULL,
    surcharge_amount           DECIMAL(10, 2)         NOT NULL,
    added_date                 datetime               NOT NULL,
    added_by_user_id           INT                    NOT NULL,
    CONSTRAINT PK_APP_LOAD_SURCHARGES PRIMARY KEY (app_load_surcharge_id)
);


CREATE TABLE app_loads
(
    load_id              INT AUTO_INCREMENT        NOT NULL,
    user_id              INT                       NULL,
    inv_id               INT                       NULL,
    load_number          VARCHAR(45)    DEFAULT '' NOT NULL,
    freight_amount       DECIMAL(10, 2)            NULL,
    surcharges           DECIMAL(10, 2) DEFAULT 0  NOT NULL,
    total                DECIMAL(10, 2)            NULL,
    load_date            datetime                  NULL,
    commodify            VARCHAR(45)    DEFAULT '' NOT NULL,
    pick_up_location     VARCHAR(200)   DEFAULT '' NOT NULL,
    pick_up_city         VARCHAR(60)    DEFAULT '' NOT NULL,
    pick_up_state        VARCHAR(25)    DEFAULT '' NOT NULL,
    pick_up_zip          VARCHAR(7)     DEFAULT '' NOT NULL,
    pick_up_country      VARCHAR(25)               NOT NULL,
    pick_up_lat          DOUBLE                    NULL,
    pick_up_long         DOUBLE                    NULL,
    pick_up_notes        VARCHAR(255)   DEFAULT '' NOT NULL,
    drop_off_location    VARCHAR(200)   DEFAULT '' NOT NULL,
    drop_off_city        VARCHAR(60)    DEFAULT '' NOT NULL,
    drop_off_state       VARCHAR(25)    DEFAULT '' NOT NULL,
    drop_off_zip         VARCHAR(7)     DEFAULT '' NOT NULL,
    drop_off_country     VARCHAR(25)    DEFAULT '' NOT NULL,
    drop_off_lat         DOUBLE                    NULL,
    drop_off_long        DOUBLE                    NULL,
    drop_off_notes       VARCHAR(255)   DEFAULT '' NOT NULL,
    miles                DECIMAL(10, 2)            NULL,
    hours                DECIMAL(10, 2)            NULL,
    weight               DECIMAL(10, 2)            NULL,
    tonnage              DECIMAL(10, 2)            NULL,
    rate                 VARCHAR(200)   DEFAULT '' NOT NULL,
    rate_num             DECIMAL(10, 2)            NULL,
    rate_type            VARCHAR(25)    DEFAULT '' NOT NULL,
    notes                VARCHAR(1000)  DEFAULT '' NOT NULL,
    status               INT            DEFAULT 0  NULL,
    processed_by_user_id INT                       NULL,
    CONSTRAINT PK_APP_LOADS PRIMARY KEY (load_id)
);


CREATE TABLE app_recipient_history
(
    history_id     INT AUTO_INCREMENT NOT NULL,
    rec_id         INT                NOT NULL,
    sent_at        datetime           NOT NULL,
    email_queue_id INT                NULL,
    CONSTRAINT PK_APP_RECIPIENT_HISTORY PRIMARY KEY (history_id)
);


CREATE TABLE app_recipients
(
    rec_id        INT AUTO_INCREMENT      NOT NULL,
    email         VARCHAR(255)            NOT NULL,
    status        INT          DEFAULT 0  NULL,
    doc_id        INT                     NULL,
    user_id       INT                     NULL,
    name          VARCHAR(100) DEFAULT '' NOT NULL,
    delivered_old INT          DEFAULT 0  NULL,
    to_user_id    INT                     NULL,
    CONSTRAINT PK_APP_RECIPIENTS PRIMARY KEY (rec_id)
);


CREATE TABLE app_scans
(
    scan_id     INT AUTO_INCREMENT                                    NOT NULL,
    doc_id      INT                                                   NOT NULL,
    filename    VARCHAR(255) DEFAULT ''                               NOT NULL,
    created_at  datetime     DEFAULT CURRENT_TIMESTAMP                NULL,
    archived    INT          DEFAULT 0                                NULL,
    modified_dt datetime     DEFAULT null on update CURRENT_TIMESTAMP NULL,
    user_id     INT                                                   NOT NULL,
    sort_order  INT          DEFAULT 0                                NULL,
    scan_name   VARCHAR(50)  DEFAULT ''                               NOT NULL,
    scan_thumb  VARCHAR(50)  DEFAULT ''                               NOT NULL,
    CONSTRAINT PK_APP_SCANS PRIMARY KEY (scan_id)
);


CREATE TABLE app_shippers
(
    ship_id      INT AUTO_INCREMENT      NOT NULL,
    inv_id       INT                     NULL,
    user_id      INT                     NULL,
    company_name VARCHAR(100) DEFAULT '' NOT NULL,
    attn_to      VARCHAR(100) DEFAULT '' NOT NULL,
    email        VARCHAR(100) DEFAULT '' NOT NULL,
    status       INT          DEFAULT 0  NULL,
    address      VARCHAR(100) DEFAULT '' NOT NULL,
    name         VARCHAR(100) DEFAULT '' NOT NULL,
    phone        VARCHAR(100) DEFAULT '' NOT NULL,
    phone_ext    VARCHAR(100) DEFAULT '' NOT NULL,
    CONSTRAINT PK_APP_SHIPPERS PRIMARY KEY (ship_id)
);


CREATE TABLE app_versions
(
    version_id      INT AUTO_INCREMENT                    NOT NULL,
    version         VARCHAR(20)                           NOT NULL,
    android_version VARCHAR(20) DEFAULT ''                NOT NULL,
    ios_version     VARCHAR(20) DEFAULT ''                NOT NULL,
    date_added      datetime    DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_APP_VERSIONS PRIMARY KEY (version_id)
);


CREATE TABLE appdelete_document_scan_type
(
    doc_id       INT NOT NULL,
    scan_type_id INT NOT NULL,
    CONSTRAINT PK_APPDELETE_DOCUMENT_SCAN_TYPE PRIMARY KEY (doc_id)
);


CREATE TABLE appdelete_scan_types
(
    scan_type_id INT AUTO_INCREMENT NOT NULL,
    name         VARCHAR(255)       NOT NULL,
    title        VARCHAR(255)       NOT NULL,
    CONSTRAINT PK_APPDELETE_SCAN_TYPES PRIMARY KEY (scan_type_id)
);


CREATE TABLE arb_removal
(
    arb_removal_id    INT AUTO_INCREMENT      NOT NULL,
    arb_removal_date  date                    NOT NULL,
    x_subscription_id INT                     NOT NULL,
    status            VARCHAR(255) DEFAULT '' NOT NULL,
    CONSTRAINT PK_ARB_REMOVAL PRIMARY KEY (arb_removal_id)
);


CREATE TABLE authnet_arbs
(
    arb_id                    BIGINT                 NOT NULL,
    user_id                   INT                    NULL,
    user_company_id           INT                    NULL,
    name                      VARCHAR(45) DEFAULT '' NOT NULL,
    status                    VARCHAR(45) DEFAULT '' NOT NULL,
    createTimeStampUTC        datetime               NULL,
    firstName                 VARCHAR(45) DEFAULT '' NOT NULL,
    lastName                  VARCHAR(45) DEFAULT '' NOT NULL,
    totalOccurrences          INT                    NULL,
    pastOccurrences           INT                    NULL,
    paymentMethod             VARCHAR(45) DEFAULT '' NOT NULL,
    accountNumber             VARCHAR(45) DEFAULT '' NOT NULL,
    invoice                   VARCHAR(45) DEFAULT '' NOT NULL,
    amount                    DECIMAL(10, 2)         NULL,
    currencyCode              VARCHAR(15) DEFAULT '' NOT NULL,
    customerProfileId         VARCHAR(45) DEFAULT '' NOT NULL,
    customerPaymentProfileId  VARCHAR(45) DEFAULT '' NOT NULL,
    order_id                  INT                    NULL,
    order_type                VARCHAR(45) DEFAULT '' NOT NULL,
    last_four_account_numbers VARCHAR(4)  DEFAULT '' NOT NULL,
    bank_name                 VARCHAR(45) DEFAULT '' NOT NULL,
    exp_month                 INT                    NULL,
    exp_year                  INT                    NULL,
    canceled_date             date                   NULL,
    deactivationDate          date                   NULL,
    deleteDate                date                   NULL,
    failedDate                date                   NULL,
    orig_user_id              INT                    NULL,
    msg                       VARCHAR(45) DEFAULT '' NOT NULL,
    CONSTRAINT PK_AUTHNET_ARBS PRIMARY KEY (arb_id)
);


CREATE TABLE authnet_arbs_unmatched
(
    user_id INT    NOT NULL,
    arb_id  BIGINT NOT NULL,
    CONSTRAINT PK_AUTHNET_ARBS_UNMATCHED PRIMARY KEY (user_id, arb_id)
);


CREATE TABLE authnet_trans
(
    transid           BIGINT                 NOT NULL,
    batch_id          BIGINT                 NULL,
    arb_id            BIGINT                 NULL,
    user_id           INT                    NULL,
    user_company_id   INT                    NULL,
    payNum            INT                    NULL,
    submitTimeUTC     datetime               NULL,
    submitTimeLocal   datetime               NULL,
    transactionStatus VARCHAR(45) DEFAULT '' NOT NULL,
    invoiceNumber     VARCHAR(45) DEFAULT '' NOT NULL,
    firstName         VARCHAR(45) DEFAULT '' NOT NULL,
    lastName          VARCHAR(45) DEFAULT '' NOT NULL,
    accountType       VARCHAR(45) DEFAULT '' NOT NULL,
    accountNumber     VARCHAR(45) DEFAULT '' NOT NULL,
    settleAmount      DECIMAL(10, 2)         NULL,
    marketType        VARCHAR(45) DEFAULT '' NOT NULL,
    product           VARCHAR(45) DEFAULT '' NOT NULL,
    orig_arb_id       BIGINT                 NULL,
    refund_transid    BIGINT                 NULL,
    orig_user_id      INT                    NULL,
    msg               VARCHAR(45) DEFAULT '' NOT NULL,
    CONSTRAINT PK_AUTHNET_TRANS PRIMARY KEY (transid)
);


CREATE TABLE banner_views
(
    banner_view_id INT AUTO_INCREMENT NOT NULL,
    banner_id      INT                NOT NULL,
    user_id        INT                NOT NULL,
    click_date     datetime           NOT NULL,
    CONSTRAINT PK_BANNER_VIEWS PRIMARY KEY (banner_view_id)
);


CREATE TABLE banners
(
    banner_id         INT AUTO_INCREMENT            NOT NULL,
    banner_alt        VARCHAR(255)                  NOT NULL,
    banner_pic        VARCHAR(255)                  NOT NULL,
    banner_url        VARCHAR(255)                  NOT NULL,
    banner_weight     INT                           NOT NULL,
    banner_clicks     INT                           NULL,
    banner_views      INT                           NULL,
    clickable         INT                           NOT NULL,
    banner_width      INT                           NOT NULL,
    banner_height     INT                           NOT NULL,
    homepage_ad       INT                           NULL,
    background_color  VARCHAR(6)   DEFAULT 'FFFFFF' NOT NULL,
    homepage_position INT                           NULL,
    display_position  INT                           NULL,
    display_cfml      VARCHAR(500) DEFAULT ''       NOT NULL,
    email_displays    INT                           NULL,
    site_id           INT          DEFAULT 1        NOT NULL,
    CONSTRAINT PK_BANNERS PRIMARY KEY (banner_id)
);


CREATE TABLE bearings
(
    id                INT AUTO_INCREMENT NOT NULL,
    bearing_min       DOUBLE             NOT NULL,
    bearing_max       DOUBLE             NOT NULL,
    bearing_direction CHAR(3)            NOT NULL,
    CONSTRAINT PK_BEARINGS PRIMARY KEY (id)
);


CREATE TABLE bentrei_data
(
    id                INT AUTO_INCREMENT      NOT NULL,
    source_company_id VARCHAR(45)  DEFAULT '' NOT NULL,
    load_id           INT                     NOT NULL,
    action            CHAR(1)      DEFAULT '' NOT NULL,
    origin_city       VARCHAR(40)  DEFAULT '' NOT NULL,
    origin_state      VARCHAR(25)  DEFAULT '' NOT NULL,
    ship_from         date                    NULL,
    destination_city  VARCHAR(40)  DEFAULT '' NOT NULL,
    destination_state VARCHAR(25)  DEFAULT '' NOT NULL,
    ship_to           date                    NULL,
    equipment_type    VARCHAR(5)   DEFAULT '' NOT NULL,
    contact_name      VARCHAR(100) DEFAULT '' NOT NULL,
    contact_number    VARCHAR(50)  DEFAULT '' NOT NULL,
    number_of_loads   INT          DEFAULT 1  NOT NULL,
    processed         TINYINT      DEFAULT 0  NOT NULL,
    processed_date    datetime                NULL,
    success           TINYINT      DEFAULT 0  NOT NULL,
    message           LONGTEXT                NULL,
    log_id            INT                     NOT NULL,
    CONSTRAINT PK_BENTREI_DATA PRIMARY KEY (id)
);


CREATE TABLE bl_user_company_settings
(
    user_company_id         INT                          NOT NULL,
    canceled_date           date                         NULL,
    membership_end_date     date                         NULL,
    trial_start_date        date                         NULL,
    trial_end_date          date                         NULL,
    mobile_trial_end_date   date                         NULL,
    deletion_date           date                         NULL,
    monthly_rate            DECIMAL(7, 2) DEFAULT 49.95  NULL,
    threemonth_rate         DECIMAL(8, 2) DEFAULT 149.95 NULL,
    sixmonth_rate           DECIMAL(8, 2) DEFAULT 249.95 NULL,
    yearly_rate             DECIMAL(8, 2) DEFAULT 499.95 NULL,
    biyearly_rate           DECIMAL(8, 2) DEFAULT 899.95 NULL,
    is_custom_rate          TINYINT       DEFAULT 0      NOT NULL,
    comment                 VARCHAR(45)   DEFAULT ''     NOT NULL,
    private_load_posts      INT           DEFAULT 0      NOT NULL,
    private_load_count      INT           DEFAULT 0      NOT NULL,
    carrier_load_posts      INT           DEFAULT 0      NOT NULL,
    carrier_load_count      INT           DEFAULT 0      NOT NULL,
    public_load_posts       INT           DEFAULT 0      NOT NULL,
    public_load_count       INT           DEFAULT 0      NOT NULL,
    receive_standard_export TINYINT       DEFAULT 0      NOT NULL,
    standard_export_email   VARCHAR(100)  DEFAULT ''     NOT NULL,
    CONSTRAINT PK_BL_USER_COMPANY_SETTINGS PRIMARY KEY (user_company_id)
);


CREATE TABLE bl_user_company_verified
(
    user_company_id INT      NOT NULL,
    date_verified   datetime NULL,
    CONSTRAINT PK_BL_USER_COMPANY_VERIFIED PRIMARY KEY (user_company_id)
);


CREATE TABLE bl_user_settings
(
    user_id                       INT AUTO_INCREMENT                                              NOT NULL,
    user_company_id               INT                                 DEFAULT 0                   NOT NULL,
    deletion_date                 date                                                            NULL,
    view_company_posts            INT                                 DEFAULT 1                   NULL,
    classifieds_posted            INT                                 DEFAULT 0                   NOT NULL,
    last_activity                 INT                                                             NULL,
    use_chat                      INT                                 DEFAULT 1                   NULL,
    currentSessionID              VARCHAR(255)                        DEFAULT ''                  NOT NULL,
    truck_matching                INT                                 DEFAULT 1                   NULL,
    truck_available               INT                                 DEFAULT 1                   NULL,
    last_chat_activity            INT                                                             NULL,
    truck_matching_miles          INT                                 DEFAULT 100                 NULL,
    last_company_finder_search    date                                                            NULL,
    show_in_load_history          INT                                 DEFAULT 1                   NULL,
    loads_posted                  INT                                 DEFAULT 0                   NOT NULL,
    load_message_type             VARCHAR(10)                         DEFAULT 'ask'               NOT NULL,
    show_in_company_finder        INT                                 DEFAULT 1                   NULL,
    allow_group_send              INT                                 DEFAULT 1                   NULL,
    trucks_posted                 INT                                 DEFAULT 0                   NOT NULL,
    forum_view_types              VARCHAR(45)                         DEFAULT '1,2,3,4,5,6,7,8,9' NOT NULL,
    last_login_date               timestamp                                                       NULL,
    last_login_date_new_site      timestamp                                                       NULL,
    last_login_date_old_site      timestamp                                                       NULL,
    last_mobile_login_date        timestamp                                                       NULL,
    last_mobile_device            VARCHAR(100)                        DEFAULT ''                  NOT NULL,
    last_login_device             VARCHAR(100)                        DEFAULT ''                  NOT NULL,
    old_load_summary              INT                                 DEFAULT 1                   NULL,
    old_load_matching             INT                                 DEFAULT 1                   NULL,
    load_matching_miles           INT                                 DEFAULT 100                 NULL,
    old_load_lead_emails          TINYINT                             DEFAULT 1                   NOT NULL,
    current_auto_change           ENUM ('ask', 'always', 'never')     DEFAULT 'always'            NOT NULL,
    use_new_site                  TINYINT                             DEFAULT 1                   NOT NULL,
    use_new_site_asked_date       datetime                                                        NULL,
    hide_my_loads_from_non_admins TINYINT                             DEFAULT 0                   NOT NULL,
    app_mode                      ENUM ('bulkloads', 'driver', 'sff') DEFAULT 'bulkloads'         NOT NULL,
    allowed_app_modes             VARCHAR(30)                         DEFAULT 'bulkloads'         NOT NULL,
    last_scheduled_email_date     datetime                                                        NULL,
    settings_updated_date         datetime                                                        NULL,
    CONSTRAINT PK_BL_USER_SETTINGS PRIMARY KEY (user_id)
);


CREATE TABLE blocked_companies
(
    user_id                 INT           NOT NULL,
    user_company_id         INT DEFAULT 0 NOT NULL,
    blocked_user_company_id INT           NOT NULL,
    date_added              datetime      NOT NULL,
    CONSTRAINT PK_BLOCKED_COMPANIES PRIMARY KEY (user_company_id, blocked_user_company_id)
);


CREATE TABLE blocked_errors
(
    blocked_errors_id  INT AUTO_INCREMENT       NOT NULL,
    blocked_history_id INT                      NULL,
    ip                 VARCHAR(45)              NOT NULL,
    error_date         datetime                 NOT NULL,
    user_id            INT                      NULL,
    request_url        VARCHAR(2048) DEFAULT '' NOT NULL,
    http_host          VARCHAR(45)   DEFAULT '' NOT NULL,
    script_name        VARCHAR(1024) DEFAULT '' NOT NULL,
    query_string       VARCHAR(2048) DEFAULT '' NOT NULL,
    http_cookie        VARCHAR(2048) DEFAULT '' NOT NULL,
    local_addr         VARCHAR(45)   DEFAULT '' NOT NULL,
    http_user_agent    VARCHAR(1024) DEFAULT '' NOT NULL,
    http_referer       VARCHAR(2048) DEFAULT '' NOT NULL,
    error_message      VARCHAR(2048) DEFAULT '' NOT NULL,
    debug_info         MEDIUMTEXT               NULL,
    CONSTRAINT PK_BLOCKED_ERRORS PRIMARY KEY (blocked_errors_id)
);


CREATE TABLE blocked_history
(
    blocked_history_id INT AUTO_INCREMENT     NOT NULL,
    ip                 VARCHAR(45)            NOT NULL,
    user_id            INT                    NULL,
    added_date         datetime               NOT NULL,
    blocked_end_date   datetime               NOT NULL,
    edit_by_user_id    INT                    NULL,
    edit_date          datetime               NULL,
    note               VARCHAR(45) DEFAULT '' NOT NULL,
    CONSTRAINT PK_BLOCKED_HISTORY PRIMARY KEY (blocked_history_id)
);


CREATE TABLE blocked_ips
(
    ip                 VARCHAR(45)            NOT NULL,
    user_id            INT                    NULL,
    blocked_history_id INT                    NOT NULL,
    added_date         datetime               NOT NULL,
    blocked_end_date   datetime               NOT NULL,
    edit_by_user_id    INT                    NULL,
    edit_date          datetime               NULL,
    note               VARCHAR(45) DEFAULT '' NOT NULL,
    CONSTRAINT PK_BLOCKED_IPS PRIMARY KEY (ip)
);


CREATE TABLE blocked_requests
(
    blocked_requests_id INT AUTO_INCREMENT       NOT NULL,
    blocked_history_id  INT                      NOT NULL,
    ip                  VARCHAR(45)              NOT NULL,
    request_date        datetime                 NOT NULL,
    user_id             INT                      NULL,
    request_url         VARCHAR(2048) DEFAULT '' NOT NULL,
    http_host           VARCHAR(45)   DEFAULT '' NOT NULL,
    script_name         VARCHAR(1024) DEFAULT '' NOT NULL,
    query_string        VARCHAR(2048) DEFAULT '' NOT NULL,
    http_cookie         VARCHAR(2048) DEFAULT '' NOT NULL,
    local_addr          VARCHAR(45)   DEFAULT '' NOT NULL,
    http_user_agent     VARCHAR(1024) DEFAULT '' NOT NULL,
    http_referer        VARCHAR(2048) DEFAULT '' NOT NULL,
    CONSTRAINT PK_BLOCKED_REQUESTS PRIMARY KEY (blocked_requests_id)
);


CREATE TABLE carrier_profile_equipment_run
(
    old_carrier_profile_equipment_run_id INT           NULL,
    old_carrier_profile_id               INT           NULL,
    equipment                            CHAR(5)       NOT NULL,
    quantity                             INT DEFAULT 0 NOT NULL,
    user_id                              INT           NOT NULL,
    site_id                              INT DEFAULT 0 NOT NULL,
    CONSTRAINT PK_CARRIER_PROFILE_EQUIPMENT_RUN PRIMARY KEY (equipment, user_id, site_id)
);


CREATE TABLE carrier_profile_files
(
    file_id           INT AUTO_INCREMENT      NOT NULL,
    user_id           INT                     NULL,
    user_company_id   INT                     NULL,
    filename          VARCHAR(200) DEFAULT '' NOT NULL,
    thumb             VARCHAR(200) DEFAULT '' NOT NULL,
    title             MEDIUMTEXT              NULL,
    isSecured         TINYINT      DEFAULT 0  NULL,
    security_password VARCHAR(45)  DEFAULT '' NOT NULL,
    dateCreated       timestamp               NULL,
    CONSTRAINT PK_CARRIER_PROFILE_FILES PRIMARY KEY (file_id)
);


CREATE TABLE carrier_profile_states_run
(
    old_carrier_profile_states_run_id INT         NULL,
    old_carrier_profile_id            INT         NULL,
    state                             VARCHAR(12) NOT NULL,
    user_id                           INT         NOT NULL,
    CONSTRAINT PK_CARRIER_PROFILE_STATES_RUN PRIMARY KEY (state, user_id)
);


CREATE TABLE cities
(
    ID        INT AUTO_INCREMENT     NOT NULL,
    name      VARCHAR(80) DEFAULT '' NOT NULL,
    city      VARCHAR(60) DEFAULT '' NOT NULL,
    state     VARCHAR(2)  DEFAULT '' NOT NULL,
    zip       VARCHAR(60) DEFAULT '' NOT NULL,
    region    VARCHAR(10) DEFAULT '' NOT NULL,
    country   VARCHAR(2)  DEFAULT '' NOT NULL,
    Latitude  DOUBLE                 NULL,
    Longitude DOUBLE                 NULL,
    ismatch   TINYINT     DEFAULT 1  NOT NULL,
    freq      INT         DEFAULT 0  NOT NULL,
    CONSTRAINT PK_CITIES PRIMARY KEY (ID)
);


CREATE TABLE commodities
(
    commodity_id             INT AUTO_INCREMENT                                                  NOT NULL,
    user_id                  INT                                                                 NOT NULL,
    user_company_id          INT                                                                 NOT NULL,
    commodity                VARCHAR(100)                                DEFAULT ''              NOT NULL,
    external_commodity_id    VARCHAR(50)                                 DEFAULT ''              NOT NULL,
    commodity_abbr           VARCHAR(10)                                 DEFAULT ''              NOT NULL,
    color_code               VARCHAR(20)                                 DEFAULT ''              NOT NULL,
    def_rate_type            VARCHAR(10)                                 DEFAULT ''              NOT NULL,
    rate_product_category_id INT                                                                 NULL,
    equipment_ids            VARCHAR(50)                                 DEFAULT ''              NOT NULL,
    equipment_names          VARCHAR(300)                                DEFAULT ''              NOT NULL,
    added_date               datetime                                                            NOT NULL,
    added_by_user_id         INT                                                                 NULL,
    edit_date                datetime                                                            NULL,
    edit_by_user_id          INT                                                                 NULL,
    modified_date            datetime                                                            NOT NULL,
    deleted_date             datetime                                                            NULL,
    deleted_by_user_id       INT                                                                 NULL,
    deleted                  TINYINT                                     DEFAULT 0               NOT NULL,
    default_bill_weight_use  ENUM ('', 'loaded_weight', 'unload_weight') DEFAULT 'unload_weight' NOT NULL,
    CONSTRAINT PK_COMMODITIES PRIMARY KEY (commodity_id)
);


CREATE TABLE commodities_temp
(
    commodity_id             INT          DEFAULT 0  NOT NULL,
    user_id                  INT                     NOT NULL,
    user_company_id          INT                     NOT NULL,
    commodity                VARCHAR(45)  DEFAULT '' NOT NULL,
    commodity_abbr           VARCHAR(10)  DEFAULT '' NOT NULL,
    color_code               VARCHAR(20)  DEFAULT '' NOT NULL,
    def_rate_type            VARCHAR(10)  DEFAULT '' NOT NULL,
    rate_product_category_id INT                     NULL,
    equipment_ids            VARCHAR(50)  DEFAULT '' NOT NULL,
    equipment_names          VARCHAR(300) DEFAULT '' NOT NULL,
    added_date               datetime                NOT NULL,
    added_by_user_id         INT                     NULL,
    edit_date                datetime                NULL,
    edit_by_user_id          INT                     NULL,
    modified_date            datetime                NOT NULL,
    deleted_date             datetime                NULL,
    deleted_by_user_id       INT                     NULL,
    deleted                  TINYINT      DEFAULT 0  NOT NULL
);


CREATE TABLE commodity_equipments
(
    commodity_id INT        NOT NULL,
    equipment_id VARCHAR(4) NOT NULL,
    CONSTRAINT PK_COMMODITY_EQUIPMENTS PRIMARY KEY (commodity_id, equipment_id)
);


CREATE TABLE commodity_listing
(
    commodity_listing_id INT AUTO_INCREMENT                      NOT NULL,
    user_id              INT                                     NOT NULL,
    user_company_id      INT                                     NOT NULL,
    commodity_type       VARCHAR(200)                            NOT NULL,
    price_type           VARCHAR(3)    DEFAULT ''                NOT NULL,
    price                VARCHAR(25)   DEFAULT ''                NOT NULL,
    origin               VARCHAR(255)  DEFAULT ''                NOT NULL,
    contact_company      VARCHAR(50)                             NOT NULL,
    contact_name         VARCHAR(50)                             NOT NULL,
    contact_phone        VARCHAR(25)                             NOT NULL,
    contact_email        VARCHAR(50)   DEFAULT ''                NOT NULL,
    notes                VARCHAR(1000) DEFAULT ''                NOT NULL,
    date_posted          datetime                                NOT NULL,
    date_added           datetime      DEFAULT CURRENT_TIMESTAMP NOT NULL,
    current_city         VARCHAR(60)   DEFAULT ''                NOT NULL,
    current_state        VARCHAR(2)    DEFAULT ''                NOT NULL,
    current_zip          VARCHAR(10)   DEFAULT ''                NOT NULL,
    current_country      VARCHAR(5)    DEFAULT ''                NOT NULL,
    current_address      VARCHAR(150)  DEFAULT ''                NOT NULL,
    current_latitude     DOUBLE                                  NULL,
    current_longitude    DOUBLE                                  NULL,
    CONSTRAINT PK_COMMODITY_LISTING PRIMARY KEY (commodity_listing_id)
);


CREATE TABLE commodity_type
(
    commodity_type_id INT AUTO_INCREMENT NOT NULL,
    commodity_type    VARCHAR(75)        NOT NULL,
    CONSTRAINT PK_COMMODITY_TYPE PRIMARY KEY (commodity_type_id)
);


CREATE TABLE company_finder_criteria_search
(
    company_finder_criteria_search_id INT AUTO_INCREMENT     NOT NULL,
    user_id                           INT                    NULL,
    date                              date                   NULL,
    name                              VARCHAR(50) DEFAULT '' NOT NULL,
    phone                             VARCHAR(30) DEFAULT '' NOT NULL,
    dot                               VARCHAR(15) DEFAULT '' NOT NULL,
    state                             VARCHAR(2)  DEFAULT '' NOT NULL,
    carriers                          INT                    NULL,
    shippers                          INT                    NULL,
    bulkloads_members_only            INT                    NULL,
    recordcount                       INT                    NULL,
    CONSTRAINT PK_COMPANY_FINDER_CRITERIA_SEARCH PRIMARY KEY (company_finder_criteria_search_id)
);


CREATE TABLE company_finder_radius_search
(
    company_finder_radius_search_id INT AUTO_INCREMENT      NOT NULL,
    user_id                         INT                     NOT NULL,
    date                            date                    NOT NULL,
    state                           VARCHAR(5)   DEFAULT '' NOT NULL,
    city                            VARCHAR(60)  DEFAULT '' NOT NULL,
    distance                        INT                     NULL,
    carriers                        INT                     NULL,
    shippers                        INT                     NULL,
    hauling                         VARCHAR(100) DEFAULT '' NOT NULL,
    recordcount                     INT                     NULL,
    CONSTRAINT PK_COMPANY_FINDER_RADIUS_SEARCH PRIMARY KEY (company_finder_radius_search_id)
);


CREATE TABLE company_finder_views
(
    company_finder_view_id INT AUTO_INCREMENT NOT NULL,
    user_id                INT                NULL,
    census_num             INT                NULL,
    owner_user_id          INT                NOT NULL,
    date                   date               NOT NULL,
    CONSTRAINT PK_COMPANY_FINDER_VIEWS PRIMARY KEY (company_finder_view_id)
);


CREATE TABLE company_searches
(
    company_search_id INT AUTO_INCREMENT                     NOT NULL,
    user_id           INT                                    NOT NULL,
    site_id           INT                                    NOT NULL,
    search_type       INT                                    NULL,
    date_added        datetime     DEFAULT CURRENT_TIMESTAMP NULL,
    criteria          VARCHAR(100) DEFAULT ''                NOT NULL,
    location          VARCHAR(100) DEFAULT ''                NOT NULL,
    distance          INT                                    NULL,
    tot_trucks_x      VARCHAR(5)   DEFAULT ''                NOT NULL,
    tot_trucks        INT                                    NULL,
    permitted_to_haul VARCHAR(100) DEFAULT ''                NOT NULL,
    equip_types       VARCHAR(100) DEFAULT ''                NOT NULL,
    create_days       INT                                    NULL,
    only_members      TINYINT                                NOT NULL,
    search_carriers   TINYINT                                NOT NULL,
    search_brokers    TINYINT                                NOT NULL,
    search_shippers   TINYINT                                NOT NULL,
    CONSTRAINT PK_COMPANY_SEARCHES PRIMARY KEY (company_search_id)
);


CREATE TABLE contracts
(
    contract_id               INT AUTO_INCREMENT                     NOT NULL,
    external_contract_id      VARCHAR(50)             DEFAULT ''     NOT NULL,
    user_id                   INT                                    NOT NULL,
    user_company_id           INT                                    NOT NULL,
    contract_status           ENUM ('Open', 'Closed') DEFAULT 'Open' NOT NULL,
    buy_sell                  ENUM ('', 'Buy', 'Sell')               NOT NULL,
    contract_number           VARCHAR(45)             DEFAULT ''     NOT NULL,
    rate                      DECIMAL(10, 2)                         NULL,
    rate_type                 VARCHAR(10)                            NOT NULL,
    freight_rate              DECIMAL(10, 2)                         NULL,
    freight_rate_type         VARCHAR(10)             DEFAULT ''     NOT NULL,
    freight_rate_per_mile     DECIMAL(10, 2)                         NULL,
    number_of_loads           INT                                    NULL,
    remaining_number_of_loads INT                                    NULL,
    quantity                  DECIMAL(10, 2)                         NULL,
    contract_total            DECIMAL(14, 2)                         NULL,
    remaining_quantity        DECIMAL(10, 2)                         NULL,
    weight                    DECIMAL(12, 2)                         NULL,
    remaining_weight          DECIMAL(12, 2)                         NULL,
    ship_from                 datetime                               NULL,
    ship_to                   datetime                               NULL,
    commodity_id              INT                                    NULL,
    commodity                 VARCHAR(100)            DEFAULT ''     NOT NULL,
    contact_info              VARCHAR(150)            DEFAULT ''     NOT NULL,
    notes                     VARCHAR(500)            DEFAULT ''     NOT NULL,
    pickup_ab_company_id      INT                                    NULL,
    pickup_company_name       VARCHAR(150)            DEFAULT ''     NOT NULL,
    pickup_address            VARCHAR(120)            DEFAULT ''     NOT NULL,
    pickup_location           VARCHAR(45)             DEFAULT ''     NOT NULL,
    pickup_city               VARCHAR(45)             DEFAULT ''     NOT NULL,
    pickup_state              VARCHAR(2)              DEFAULT ''     NOT NULL,
    pickup_zip                VARCHAR(7)              DEFAULT ''     NOT NULL,
    pickup_country            VARCHAR(45)             DEFAULT ''     NOT NULL,
    pickup_lat                DOUBLE                                 NULL,
    pickup_long               DOUBLE                                 NULL,
    drop_ab_company_id        INT                                    NULL,
    drop_company_name         VARCHAR(150)            DEFAULT ''     NOT NULL,
    drop_address              VARCHAR(120)            DEFAULT ''     NOT NULL,
    drop_location             VARCHAR(45)             DEFAULT ''     NOT NULL,
    drop_city                 VARCHAR(45)             DEFAULT ''     NOT NULL,
    drop_state                VARCHAR(2)              DEFAULT ''     NOT NULL,
    drop_zip                  VARCHAR(7)              DEFAULT ''     NOT NULL,
    drop_country              VARCHAR(45)             DEFAULT ''     NOT NULL,
    drop_lat                  DOUBLE                                 NULL,
    drop_long                 DOUBLE                                 NULL,
    miles                     DECIMAL(10, 2)                         NULL,
    added_date                datetime                               NOT NULL,
    added_by_user_id          INT                                    NOT NULL,
    edit_date                 datetime                               NULL,
    edit_by_user_id           INT                                    NULL,
    closed_date               datetime                               NULL,
    closed_by_user_id         INT                                    NULL,
    modified_date             datetime                               NULL,
    deleted_date              datetime                               NULL,
    deleted_by_user_id        INT                                    NULL,
    deleted                   TINYINT                 DEFAULT 0      NOT NULL,
    CONSTRAINT PK_CONTRACTS PRIMARY KEY (contract_id)
);


CREATE TABLE delete_load_rate_types
(
    rate_type            VARCHAR(10) NOT NULL,
    rate_type_text       VARCHAR(25) NOT NULL,
    rate_type_short_text VARCHAR(25) NOT NULL,
    CONSTRAINT PK_DELETE_LOAD_RATE_TYPES PRIMARY KEY (rate_type)
);


CREATE TABLE deletebad_email
(
    bad_email_id           INT AUTO_INCREMENT     NOT NULL,
    bad_email_address      VARCHAR(100)           NOT NULL,
    unsubscribed           VARCHAR(1) DEFAULT 'N' NOT NULL,
    date_added             date                   NOT NULL,
    marked_as_spam         VARCHAR(3) DEFAULT ''  NOT NULL,
    unsubscribed_marketing VARCHAR(3) DEFAULT ''  NOT NULL,
    dead_account           VARCHAR(3) DEFAULT ''  NOT NULL,
    CONSTRAINT PK_DELETEBAD_EMAIL PRIMARY KEY (bad_email_id, bad_email_address)
);


CREATE TABLE deleteload_analytics_views
(
    load_analytics_views  INT AUTO_INCREMENT      NOT NULL,
    user_id               INT                     NOT NULL,
    owner_user_company_id INT                     NOT NULL,
    origin_state          VARCHAR(5)              NOT NULL,
    date                  date                    NOT NULL,
    equipment             VARCHAR(255) DEFAULT '' NOT NULL,
    site_id               INT          DEFAULT 1  NOT NULL,
    CONSTRAINT PK_DELETELOAD_ANALYTICS_VIEWS PRIMARY KEY (load_analytics_views)
);


CREATE TABLE dot_info
(
    dot_info_id    INT AUTO_INCREMENT NOT NULL,
    dot_info_state VARCHAR(2)         NOT NULL,
    dot_info_long  DOUBLE             NOT NULL,
    dot_info_lat   DOUBLE             NOT NULL,
    CONSTRAINT PK_DOT_INFO PRIMARY KEY (dot_info_id)
);


CREATE TABLE email_categories
(
    email_category_id          INT AUTO_INCREMENT                                        NOT NULL,
    email_category             VARCHAR(50)                                               NOT NULL,
    email_category_description VARCHAR(255)                           DEFAULT ''         NOT NULL,
    user_type_ids              VARCHAR(45)                            DEFAULT ''         NOT NULL,
    can_unsub                  INT                                    DEFAULT 1          NOT NULL,
    display_order              FLOAT(12)                                                 NULL,
    default_email_frequency    ENUM ('never', 'realtime', 'schedule') DEFAULT 'schedule' NOT NULL,
    default_send_push          TINYINT                                DEFAULT 0          NOT NULL,
    default_send_sms           TINYINT                                DEFAULT 0          NOT NULL,
    metadata_schema            MEDIUMTEXT                                                NULL,
    CONSTRAINT PK_EMAIL_CATEGORIES PRIMARY KEY (email_category_id)
);


CREATE TABLE email_metrics
(
    email_metrics_id    INT AUTO_INCREMENT                                    NOT NULL,
    sg_event            VARCHAR(255) DEFAULT ''                               NOT NULL,
    email               VARCHAR(255) DEFAULT ''                               NOT NULL,
    sg_response         MEDIUMTEXT                                            NULL,
    sg_deferred_attempt INT                                                   NULL,
    sg_click_url        MEDIUMTEXT                                            NULL,
    sg_bounce_status    VARCHAR(255) DEFAULT ''                               NOT NULL,
    sg_bounce_reason    MEDIUMTEXT                                            NULL,
    sg_bounce_type      VARCHAR(255) DEFAULT ''                               NOT NULL,
    sg_drop_reason      MEDIUMTEXT                                            NULL,
    comm_metrics_date   VARCHAR(255) DEFAULT ''                               NOT NULL,
    sg_useragent        MEDIUMTEXT                                            NULL,
    sg_newsletter_id    INT                                                   NULL,
    sg_timestamp        VARCHAR(255) DEFAULT ''                               NOT NULL,
    email_queue_id      VARCHAR(255) DEFAULT ''                               NOT NULL,
    cmet_dt_created     timestamp    DEFAULT CURRENT_TIMESTAMP                NULL,
    cmet_dt_mod         timestamp    DEFAULT null on update CURRENT_TIMESTAMP NULL,
    bet_id              INT                                                   NULL,
    CONSTRAINT PK_EMAIL_METRICS PRIMARY KEY (email_metrics_id)
);


CREATE TABLE email_queue
(
    email_queue_id    INT AUTO_INCREMENT      NOT NULL,
    sender_user_id    INT                     NOT NULL,
    send_date         datetime                NOT NULL,
    from_email        VARCHAR(100)            NOT NULL,
    replyto_email     VARCHAR(100) DEFAULT '' NOT NULL,
    to_email          VARCHAR(150)            NOT NULL,
    subject           VARCHAR(500)            NOT NULL,
    message           LONGTEXT                NOT NULL,
    sent              INT          DEFAULT 0  NOT NULL,
    to_user_id        INT                     NULL,
    email_category_id INT                     NULL,
    site_id           INT                     NULL,
    `description`     VARCHAR(500) DEFAULT '' NOT NULL,
    sg_status         VARCHAR(100) DEFAULT '' NOT NULL,
    opened_date       datetime                NULL,
    cc                VARCHAR(150) DEFAULT '' NOT NULL,
    bcc               VARCHAR(150) DEFAULT '' NOT NULL,
    CONSTRAINT PK_EMAIL_QUEUE PRIMARY KEY (email_queue_id)
);


CREATE TABLE email_unsubs
(
    user_id           INT           NOT NULL,
    email_category_id INT DEFAULT 0 NOT NULL,
    site_id           INT DEFAULT 1 NOT NULL,
    unsub_date        datetime      NULL,
    CONSTRAINT PK_EMAIL_UNSUBS PRIMARY KEY (user_id, email_category_id, site_id)
);


CREATE TABLE equipment
(
    equipment_id   VARCHAR(4)             NOT NULL,
    equipment_name VARCHAR(50)            NOT NULL,
    PE_mapping     VARCHAR(25) DEFAULT '' NOT NULL,
    mcleod_mapping VARCHAR(25) DEFAULT '' NOT NULL,
    site_id        INT         DEFAULT 1  NOT NULL,
    freq           INT         DEFAULT 0  NOT NULL,
    CONSTRAINT PK_EQUIPMENT PRIMARY KEY (equipment_id)
);


CREATE TABLE external_grades
(
    external_grade_id          INT AUTO_INCREMENT      NOT NULL,
    user_company_id            INT                     NOT NULL,
    external_grade_code        VARCHAR(10)             NOT NULL,
    external_grade_description VARCHAR(500) DEFAULT '' NOT NULL,
    grade_id                   INT                     NULL,
    CONSTRAINT PK_EXTERNAL_GRADES PRIMARY KEY (external_grade_id)
);


CREATE TABLE external_rate_product_categories
(
    external_rate_product_category_id   VARCHAR(10)                NOT NULL,
    user_company_id                     INT         DEFAULT 113858 NOT NULL,
    rate_product_category_id            INT                        NULL,
    external_rate_product_category      VARCHAR(45)                NOT NULL,
    external_rate_product_category_type VARCHAR(45) DEFAULT ''     NOT NULL,
    CONSTRAINT PK_EXTERNAL_RATE_PRODUCT_CATEGORIES PRIMARY KEY (external_rate_product_category_id, user_company_id)
);


CREATE TABLE facilities
(
    facility_id                      INT AUTO_INCREMENT       NOT NULL,
    facility_outside_record_id       INT                      NULL COMMENT 'ID from charles or barchar',
    facility_outside_source          VARCHAR(50)   DEFAULT '' NOT NULL COMMENT 'company where we got the data from',
    facility_name                    VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_location_name           VARCHAR(255)  DEFAULT '' NOT NULL COMMENT 'Common name of the facility, might not match city,st',
    facility_type                    VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_city                    VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_state                   VARCHAR(25)   DEFAULT '' NOT NULL,
    facility_address                 VARCHAR(1000) DEFAULT '' NOT NULL,
    facility_zip                     VARCHAR(25)   DEFAULT '' NOT NULL,
    facility_county                  VARCHAR(100)  DEFAULT '' NOT NULL,
    facility_longitude               DOUBLE                   NULL,
    facility_latitude                DOUBLE                   NULL,
    facility_website                 VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_phone_1                 VARCHAR(50)   DEFAULT '' NOT NULL,
    facility_fax                     VARCHAR(50)   DEFAULT '' NOT NULL,
    facility_email                   VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_contact_name            VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_contact_title           VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_mailing_address         VARCHAR(1000) DEFAULT '' NOT NULL,
    facility_mailing_city            VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_mailing_state           VARCHAR(25)   DEFAULT '' NOT NULL,
    facility_mailing_zip             VARCHAR(25)   DEFAULT '' NOT NULL,
    facility_storage_amount          INT                      NULL,
    facility_railroad                VARCHAR(50)   DEFAULT '' NOT NULL,
    facility_user_company_id         INT                      NULL COMMENT 'user_company_id of the company that entered this facility data',
    facility_is_private              TINYINT       DEFAULT 0  NOT NULL COMMENT 'if 1 then this facility should not be shown to any other companies besides the company that added it',
    facility_address_for_coordinates VARCHAR(500)  DEFAULT '' NOT NULL COMMENT 'address used for coordinates by charles data',
    facility_deleted                 TINYINT       DEFAULT 0  NOT NULL,
    facility_location                VARCHAR(255)  DEFAULT '' NOT NULL,
    facility_country                 VARCHAR(100)  DEFAULT '' NOT NULL,
    facility_mailing_country         VARCHAR(100)  DEFAULT '' NOT NULL,
    facility_mailing_location        VARCHAR(255)  DEFAULT '' NOT NULL,
    CONSTRAINT PK_FACILITIES PRIMARY KEY (facility_id)
);


CREATE TABLE favorite_companies
(
    user_id         INT                                 NOT NULL COMMENT 'the user id of the user adding the favorite',
    user_company_id INT                                 NOT NULL COMMENT 'the company id that is being favorited',
    site_id         INT                                 NOT NULL,
    date_added      timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_FAVORITE_COMPANIES PRIMARY KEY (user_id, user_company_id, site_id)
);


CREATE TABLE favorite_fmcsa
(
    census_num DECIMAL(9)                                                      NOT NULL,
    user_id    INT                                                             NOT NULL,
    site_id    INT                                                             NOT NULL,
    date_added timestamp DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_FAVORITE_FMCSA PRIMARY KEY (census_num, user_id, site_id)
);


CREATE TABLE favorite_lanes
(
    user_id    INT                                    NOT NULL,
    lane       VARCHAR(200) DEFAULT ''                NOT NULL,
    site_id    INT                                    NOT NULL,
    date_added timestamp    DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_FAVORITE_LANES PRIMARY KEY (user_id, lane, site_id)
);


CREATE TABLE favorite_loads
(
    user_id    INT                                 NOT NULL,
    load_id    INT                                 NOT NULL,
    site_id    INT                                 NOT NULL,
    date_added timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_FAVORITE_LOADS PRIMARY KEY (user_id, load_id, site_id)
);


CREATE TABLE file_field_definitions
(
    field_source_name VARCHAR(50)  DEFAULT ''       NOT NULL,
    field_name        VARCHAR(50)                   NOT NULL,
    field_type        VARCHAR(10)  DEFAULT 'string' NOT NULL,
    field_label       VARCHAR(50)  DEFAULT ''       NOT NULL,
    field_description VARCHAR(200) DEFAULT ''       NOT NULL,
    field_order       INT          DEFAULT 0        NOT NULL,
    date              datetime                      NULL,
    grade_id          INT                           NULL,
    CONSTRAINT PK_FILE_FIELD_DEFINITIONS PRIMARY KEY (field_source_name)
);


CREATE TABLE file_fields
(
    file_id         INT                           NOT NULL,
    field_name      VARCHAR(50)                   NOT NULL,
    field_label     VARCHAR(50)  DEFAULT ''       NOT NULL,
    field_type      VARCHAR(10)  DEFAULT 'string' NOT NULL,
    field_value     VARCHAR(100) DEFAULT ''       NOT NULL,
    confidence      DOUBLE                        NULL,
    page_number     INT                           NULL,
    polygon         JSON                          NULL,
    span_offset     INT                           NULL,
    span_length     INT                           NULL,
    edit_by_user_id INT                           NULL,
    edit_date       datetime                      NULL,
    grade_id        INT                           NULL,
    CONSTRAINT PK_FILE_FIELDS PRIMARY KEY (file_id, field_name)
);


CREATE TABLE file_pages
(
    file_page_id INT AUTO_INCREMENT      NOT NULL,
    file_id      INT                     NOT NULL,
    file_url     VARCHAR(500)            NOT NULL,
    thumb_url    VARCHAR(500)            NOT NULL,
    mime_type    VARCHAR(255) DEFAULT '' NOT NULL,
    is_image     TINYINT      DEFAULT 1  NOT NULL,
    is_audio     TINYINT      DEFAULT 0  NOT NULL,
    size         INT          DEFAULT 0  NOT NULL,
    extension    VARCHAR(200) DEFAULT '' NOT NULL,
    page_number  INT                     NULL,
    filename     VARCHAR(100) DEFAULT '' NOT NULL,
    CONSTRAINT PK_FILE_PAGES PRIMARY KEY (file_page_id)
);


CREATE TABLE file_types
(
    file_type_id              INT AUTO_INCREMENT      NOT NULL,
    file_type                 VARCHAR(75)  DEFAULT '' NOT NULL,
    file_type_user_id         INT                     NULL COMMENT 'User who added new file type.  0 if added for everyone',
    file_type_user_company_id INT                     NULL COMMENT 'Company this file type should be displayed to.  0 if displayed to everyone',
    ocr_model_id              VARCHAR(100) DEFAULT '' NOT NULL,
    CONSTRAINT PK_FILE_TYPES PRIMARY KEY (file_type_id)
);


CREATE TABLE file_views
(
    file_id              INT           NOT NULL,
    datetime_last_viewed datetime      NOT NULL,
    view_count           INT DEFAULT 0 NOT NULL
);


CREATE TABLE files
(
    file_id                 INT AUTO_INCREMENT      NOT NULL,
    added_by_user_id        INT                     NULL,
    added_by_ab_user_id     INT                     NULL,
    file_url                VARCHAR(500)            NOT NULL,
    thumb_url               VARCHAR(500)            NOT NULL,
    full_file_url           VARCHAR(500) DEFAULT '' NOT NULL,
    mime_type               VARCHAR(255) DEFAULT '' NOT NULL,
    is_image                TINYINT      DEFAULT 0  NOT NULL,
    is_audio                TINYINT      DEFAULT 0  NOT NULL,
    size                    INT          DEFAULT 0  NOT NULL,
    full_size               INT          DEFAULT 0  NOT NULL,
    extension               VARCHAR(200) DEFAULT '' NOT NULL,
    filename                VARCHAR(500) DEFAULT '' NOT NULL,
    number_of_pages         INT                     NULL,
    caption                 VARCHAR(500) DEFAULT '' NOT NULL,
    date_added              datetime                NOT NULL,
    file_type_id            INT                     NULL,
    fields_raw              MEDIUMTEXT              NULL,
    fields_by_user_id       INT                     NULL,
    fields_date             datetime                NULL,
    fields_submodel_id      VARCHAR(50)  DEFAULT '' NOT NULL,
    ocr_processed           TINYINT      DEFAULT 0  NULL,
    ocr_model_id            VARCHAR(100) DEFAULT '' NOT NULL,
    ocr_approved            TINYINT      DEFAULT 0  NULL,
    ocr_approved_date       datetime                NULL,
    ocr_approved_by_user_id INT                     NULL,
    CONSTRAINT PK_FILES PRIMARY KEY (file_id)
);


CREATE TABLE fl_user_company_settings
(
    user_company_id       INT                          NOT NULL,
    membership_end_date   date                         NULL,
    trial_start_date      date                         NULL,
    trial_end_date        date                         NULL,
    mobile_trial_end_date date                         NULL,
    canceled_date         date                         NULL,
    deletion_date         date                         NULL,
    monthly_rate          DECIMAL(7, 2) DEFAULT 49.95  NULL,
    threemonth_rate       DECIMAL(8, 2) DEFAULT 149.95 NULL,
    sixmonth_rate         DECIMAL(8, 2) DEFAULT 249.95 NULL,
    yearly_rate           DECIMAL(8, 2) DEFAULT 499.95 NULL,
    biyearly_rate         DECIMAL(8, 2) DEFAULT 899.95 NULL,
    is_custom_rate        TINYINT       DEFAULT 0      NOT NULL,
    comment               VARCHAR(45)   DEFAULT ''     NOT NULL,
    private_load_posts    INT           DEFAULT 0      NOT NULL,
    private_load_count    INT           DEFAULT 0      NOT NULL,
    carrier_load_posts    INT           DEFAULT 0      NOT NULL,
    carrier_load_count    INT           DEFAULT 0      NOT NULL,
    public_load_posts     INT           DEFAULT 0      NOT NULL,
    public_load_count     INT           DEFAULT 0      NOT NULL,
    CONSTRAINT PK_FL_USER_COMPANY_SETTINGS PRIMARY KEY (user_company_id)
);


CREATE TABLE fl_user_settings
(
    user_id                       INT AUTO_INCREMENT                                              NOT NULL,
    user_company_id               INT                                 DEFAULT 0                   NOT NULL,
    deletion_date                 date                                                            NULL,
    view_company_posts            INT                                 DEFAULT 1                   NULL,
    classifieds_posted            INT                                 DEFAULT 0                   NULL,
    load_matching                 INT                                 DEFAULT 1                   NULL,
    last_activity                 INT                                                             NULL,
    use_chat                      INT                                 DEFAULT 1                   NULL,
    load_summary                  INT                                 DEFAULT 1                   NULL,
    currentSessionID              VARCHAR(255)                        DEFAULT ''                  NOT NULL,
    truck_matching                INT                                 DEFAULT 1                   NULL,
    truck_available               INT                                 DEFAULT 1                   NOT NULL,
    load_matching_miles           INT                                 DEFAULT 100                 NULL,
    last_chat_activity            INT                                                             NULL,
    truck_matching_miles          INT                                 DEFAULT 100                 NULL,
    last_company_finder_search    date                                                            NULL,
    show_in_load_history          INT                                 DEFAULT 1                   NULL,
    loads_posted                  INT                                 DEFAULT 0                   NULL,
    load_message_type             VARCHAR(10)                         DEFAULT 'ask'               NOT NULL,
    show_in_company_finder        INT                                 DEFAULT 1                   NULL,
    allow_group_send              INT                                 DEFAULT 1                   NULL,
    trucks_posted                 INT                                 DEFAULT 0                   NULL,
    forum_view_types              VARCHAR(45)                         DEFAULT '1,2,3,4,5,6,7,8,9' NOT NULL,
    last_login_date               timestamp                                                       NULL,
    settings_updated_date         datetime                                                        NULL,
    last_mobile_login_date        timestamp                                                       NULL,
    last_mobile_device            VARCHAR(50)                         DEFAULT ''                  NOT NULL,
    last_login_device             VARCHAR(25)                         DEFAULT ''                  NOT NULL,
    last_scheduled_email_date     datetime                                                        NULL,
    current_auto_change           ENUM ('ask', 'always', 'never')     DEFAULT 'always'            NOT NULL,
    use_new_site                  TINYINT                             DEFAULT 1                   NOT NULL,
    use_new_site_asked_date       datetime                                                        NULL,
    hide_my_loads_from_non_admins TINYINT                             DEFAULT 0                   NOT NULL,
    app_mode                      ENUM ('bulkloads', 'driver', 'sff') DEFAULT 'bulkloads'         NOT NULL,
    CONSTRAINT PK_FL_USER_SETTINGS PRIMARY KEY (user_id, user_company_id)
);


CREATE TABLE fmcsa_emails
(
    fmcsa_email_id INT AUTO_INCREMENT      NOT NULL,
    user_id        INT                     NOT NULL,
    site_id        INT          DEFAULT 1  NOT NULL,
    census_num     DECIMAL(8)              NOT NULL,
    from_name      VARCHAR(100) DEFAULT '' NOT NULL,
    from_email     VARCHAR(100) DEFAULT '' NOT NULL,
    to_name        VARCHAR(100) DEFAULT '' NOT NULL,
    to_email       VARCHAR(100) DEFAULT '' NOT NULL,
    subject        VARCHAR(255) DEFAULT '' NOT NULL,
    body           MEDIUMTEXT              NOT NULL,
    date_added     timestamp               NULL,
    sent           TINYINT      DEFAULT 0  NULL,
    date_sent      timestamp               NULL,
    CONSTRAINT PK_FMCSA_EMAILS PRIMARY KEY (fmcsa_email_id)
);


CREATE TABLE forum_delete_canned_responses
(
    id              INT      NOT NULL,
    canned_response LONGTEXT NOT NULL,
    CONSTRAINT PK_FORUM_DELETE_CANNED_RESPONSES PRIMARY KEY (id)
);


CREATE TABLE forum_files
(
    forum_file_id           INT AUTO_INCREMENT      NOT NULL,
    forum_id                INT                     NULL,
    reply_id                INT                     NULL,
    filename                VARCHAR(200) DEFAULT '' NOT NULL,
    thumb                   VARCHAR(200) DEFAULT '' NOT NULL,
    caption                 MEDIUMTEXT              NULL,
    shared_on_social        BIT          DEFAULT 0  NOT NULL,
    shared_on_social_ignore BIT          DEFAULT 0  NOT NULL,
    shared_on_social_date   datetime                NULL,
    CONSTRAINT PK_FORUM_FILES PRIMARY KEY (forum_file_id)
);


CREATE TABLE forum_opinions
(
    forum_opinion_id INT AUTO_INCREMENT                                              NOT NULL,
    forum_id         INT                                                             NOT NULL,
    reply_id         INT                                                             NULL,
    user_id          INT                                                             NOT NULL,
    opinion          INT                                                             NOT NULL COMMENT '1 for like, 0 for dislike',
    date_added       timestamp DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_FORUM_OPINIONS PRIMARY KEY (forum_opinion_id)
);


CREATE TABLE forum_post
(
    forum_id            INT AUTO_INCREMENT                     NOT NULL,
    site_id             INT          DEFAULT 1                 NOT NULL COMMENT '1=bln,2=lsn... id from site table',
    user_id             INT                                    NOT NULL,
    post_type_id        INT          DEFAULT 1                 NOT NULL COMMENT '1=forum,2=news,3=industry updates,4=website updates',
    date_added          timestamp    DEFAULT CURRENT_TIMESTAMP NOT NULL,
    forum_title         VARCHAR(200)                           NOT NULL,
    forum_content       LONGTEXT                               NOT NULL,
    visibility_site_ids VARCHAR(20)  DEFAULT '0'               NOT NULL COMMENT '0=all,1=bln,2=lsn... id from site table',
    views               INT          DEFAULT 0                 NOT NULL,
    last_reply          datetime                               NULL,
    alias               VARCHAR(255) DEFAULT ''                NOT NULL,
    forum_link          VARCHAR(255) DEFAULT ''                NOT NULL,
    isFeatured          TINYINT      DEFAULT 0                 NOT NULL,
    thumb_path          VARCHAR(255) DEFAULT ''                NOT NULL,
    show_date           date                                   NULL,
    active              TINYINT      DEFAULT 1                 NOT NULL,
    approved            TINYINT      DEFAULT 1                 NULL,
    approved_by_user_id INT                                    NULL,
    old_id              INT                                    NULL,
    old_char_id         VARCHAR(100) DEFAULT ''                NOT NULL,
    premium_only        INT          DEFAULT 0                 NOT NULL,
    current_city        VARCHAR(60)  DEFAULT ''                NOT NULL,
    current_state       VARCHAR(2)   DEFAULT ''                NOT NULL,
    current_zip         VARCHAR(10)  DEFAULT ''                NOT NULL,
    current_country     VARCHAR(5)   DEFAULT ''                NOT NULL,
    current_address     VARCHAR(150) DEFAULT ''                NOT NULL,
    current_latitude    DOUBLE                                 NULL,
    contact_name        VARCHAR(100) DEFAULT ''                NOT NULL,
    contact_phone       VARCHAR(20)  DEFAULT ''                NOT NULL,
    contact_email       VARCHAR(100) DEFAULT ''                NOT NULL,
    current_longitude   DOUBLE                                 NULL,
    orderby_show_date   datetime                               NULL,
    orderby_last_reply  datetime                               NULL,
    CONSTRAINT PK_FORUM_POST PRIMARY KEY (forum_id)
);


CREATE TABLE forum_post_types
(
    post_type_id      INT                          NOT NULL,
    post_type         VARCHAR(45)                  NOT NULL,
    accessLevel       VARCHAR(45)                  NOT NULL,
    maindir           VARCHAR(50) DEFAULT '/forum' NOT NULL,
    email_category_id INT                          NULL,
    CONSTRAINT PK_FORUM_POST_TYPES PRIMARY KEY (post_type_id)
);


CREATE TABLE forum_reply
(
    reply_id        INT AUTO_INCREMENT                                                 NOT NULL,
    forum_id        INT          DEFAULT 0                                             NOT NULL,
    parent_reply_id INT                                                                NULL,
    user_id         INT          DEFAULT 0                                             NOT NULL,
    content         LONGTEXT                                                           NULL,
    date_added      timestamp    DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    origin_site_id  TINYINT      DEFAULT 1                                             NOT NULL COMMENT '1=bln,2=lsn... id from site table',
    approved        TINYINT      DEFAULT 1                                             NULL,
    thumb_path      VARCHAR(255) DEFAULT ''                                            NOT NULL,
    old_id          INT                                                                NULL,
    old_parent_id   INT                                                                NULL,
    CONSTRAINT PK_FORUM_REPLY PRIMARY KEY (reply_id)
);


CREATE TABLE forum_subscription
(
    forum_id   INT       NOT NULL,
    user_id    INT       NOT NULL,
    date_added timestamp NULL,
    CONSTRAINT PK_FORUM_SUBSCRIPTION PRIMARY KEY (forum_id, user_id)
);


CREATE TABLE fuel_rates
(
    fuel_rate_id INT AUTO_INCREMENT NOT NULL,
    date         date               NOT NULL,
    state        VARCHAR(2)         NOT NULL,
    fuel_rate    DECIMAL(5, 3)      NOT NULL,
    CONSTRAINT PK_FUEL_RATES PRIMARY KEY (fuel_rate_id)
);


CREATE TABLE gavilon_data
(
    id                 INT AUTO_INCREMENT      NOT NULL,
    loadid             VARCHAR(30)             NOT NULL,
    assignmentnumber   INT                     NULL,
    commoditycode      VARCHAR(20)  DEFAULT '' NOT NULL,
    commodity          VARCHAR(100) DEFAULT '' NOT NULL,
    origincity         VARCHAR(100) DEFAULT '' NOT NULL,
    originstate        VARCHAR(100) DEFAULT '' NOT NULL,
    originzip          VARCHAR(100) DEFAULT '' NOT NULL,
    origincountry      VARCHAR(100) DEFAULT '' NOT NULL,
    destinationcity    VARCHAR(100) DEFAULT '' NOT NULL,
    destinationstate   VARCHAR(100) DEFAULT '' NOT NULL,
    destinationzip     VARCHAR(100) DEFAULT '' NOT NULL,
    destinationcountry VARCHAR(100) DEFAULT '' NOT NULL,
    pickupdate         date                    NULL,
    rate               DECIMAL(10, 2)          NULL,
    ratetype           VARCHAR(100) DEFAULT '' NOT NULL,
    bookeddate         date                    NULL,
    traderid           INT                     NULL,
    tradername         VARCHAR(100) DEFAULT '' NOT NULL,
    tradercontact      VARCHAR(100) DEFAULT '' NOT NULL,
    quantity           INT                     NULL,
    equipmenttype      VARCHAR(100) DEFAULT '' NOT NULL,
    carrierid          INT                     NULL,
    carriername        VARCHAR(100) DEFAULT '' NOT NULL,
    carrieraddress1    VARCHAR(100) DEFAULT '' NOT NULL,
    carrieraddress2    VARCHAR(100) DEFAULT '' NOT NULL,
    carriercity        VARCHAR(100) DEFAULT '' NOT NULL,
    carrierzip         VARCHAR(100) DEFAULT '' NOT NULL,
    processed          TINYINT      DEFAULT 0  NULL,
    processed_date     datetime                NULL,
    success            TINYINT      DEFAULT 0  NULL,
    message            LONGTEXT                NULL,
    log_id             INT                     NULL,
    CONSTRAINT PK_GAVILON_DATA PRIMARY KEY (id)
);


CREATE TABLE grade_schedules
(
    grade_schedule_id          INT AUTO_INCREMENT NOT NULL,
    user_company_id            INT                NULL,
    external_grade_schedule_id INT                NULL,
    external_commodity_id      VARCHAR(50)        NOT NULL,
    external_destination_id    INT                NOT NULL,
    CONSTRAINT PK_GRADE_SCHEDULES PRIMARY KEY (grade_schedule_id)
);


CREATE TABLE grade_schedules_external_grades
(
    grade_schedule_id INT NOT NULL,
    external_grade_id INT NOT NULL,
    CONSTRAINT PK_GRADE_SCHEDULES_EXTERNAL_GRADES PRIMARY KEY (grade_schedule_id, external_grade_id)
);


CREATE TABLE grades
(
    grade_id          INT AUTO_INCREMENT      NOT NULL,
    grade_name        VARCHAR(50)             NOT NULL,
    grade_code        VARCHAR(4)              NOT NULL,
    grade_description VARCHAR(500) DEFAULT '' NOT NULL,
    deleted           TINYINT      DEFAULT 0  NOT NULL,
    deleted_date      date                    NULL,
    CONSTRAINT PK_GRADES PRIMARY KEY (grade_id)
);


CREATE TABLE insurance_leads
(
    insurance_lead_id INT AUTO_INCREMENT                                    NOT NULL,
    date              datetime     DEFAULT null on update CURRENT_TIMESTAMP NULL,
    name              VARCHAR(100)                                          NOT NULL,
    dot               VARCHAR(25)                                           NOT NULL,
    phone             VARCHAR(50)  DEFAULT ''                               NOT NULL,
    email             VARCHAR(255) DEFAULT ''                               NOT NULL,
    current_provider  VARCHAR(450) DEFAULT ''                               NOT NULL,
    user_id           INT                                                   NULL,
    CONSTRAINT PK_INSURANCE_LEADS PRIMARY KEY (insurance_lead_id)
);


CREATE TABLE integration_log
(
    log_id       INT AUTO_INCREMENT NOT NULL,
    date_added   datetime           NOT NULL,
    source       VARCHAR(20)        NOT NULL,
    user_id      INT                NULL,
    method       VARCHAR(20)        NOT NULL,
    total        INT DEFAULT 0      NOT NULL,
    failed       INT DEFAULT 0      NOT NULL,
    message      MEDIUMTEXT         NOT NULL,
    content      MEDIUMTEXT         NULL,
    execution_ms INT DEFAULT 0      NOT NULL,
    CONSTRAINT PK_INTEGRATION_LOG PRIMARY KEY (log_id)
);


CREATE TABLE ints
(
    i TINYINT NULL
);


CREATE TABLE invoice_batches
(
    batch_id         BIGINT                  NOT NULL,
    date_added       datetime                NULL,
    date_settled_utc datetime                NULL,
    processed        TINYINT      DEFAULT 0  NULL,
    date_processed   datetime                NULL,
    message          VARCHAR(100) DEFAULT '' NOT NULL,
    total            INT                     NULL,
    arbs_created     INT                     NULL,
    arbs_failed      INT                     NULL,
    CONSTRAINT PK_INVOICE_BATCHES PRIMARY KEY (batch_id)
);


CREATE TABLE invoice_files
(
    file_id    INT AUTO_INCREMENT                     NOT NULL,
    invoice_id INT                                    NOT NULL,
    filename   VARCHAR(200) DEFAULT ''                NOT NULL,
    thumb      VARCHAR(200) DEFAULT ''                NOT NULL,
    title      MEDIUMTEXT                             NULL,
    added_date timestamp    DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_INVOICE_FILES PRIMARY KEY (file_id)
);


CREATE TABLE invoice_item_types
(
    invoice_item_type_id INT         NOT NULL,
    invoice_item_type    VARCHAR(45) NOT NULL,
    CONSTRAINT PK_INVOICE_ITEM_TYPES PRIMARY KEY (invoice_item_type_id)
);


CREATE TABLE invoice_items
(
    invoice_item_id      INT AUTO_INCREMENT NOT NULL,
    user_id              INT                NOT NULL,
    user_company_id      INT                NOT NULL,
    invoice_id           INT                NULL,
    invoice_item_type_id INT     DEFAULT 1  NOT NULL,
    item_description     VARCHAR(75)        NOT NULL,
    item_amount          DECIMAL(10, 2)     NOT NULL,
    added_date           datetime           NOT NULL,
    added_by_user_id     INT                NULL,
    deleted              TINYINT DEFAULT 0  NULL,
    deleted_date         datetime           NULL,
    deleted_by_user_id   INT                NULL,
    is_surcharge         INT     DEFAULT 0  NOT NULL,
    CONSTRAINT PK_INVOICE_ITEMS PRIMARY KEY (invoice_item_id)
);


CREATE TABLE invoice_notes
(
    invoice_note_id   INT AUTO_INCREMENT                  NOT NULL,
    invoice_id        INT                                 NOT NULL,
    dateCreated       timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
    user_id           INT                                 NOT NULL,
    note              VARCHAR(1000)                       NOT NULL,
    old_order_note_id INT                                 NULL,
    CONSTRAINT PK_INVOICE_NOTES PRIMARY KEY (invoice_note_id)
);


CREATE TABLE invoice_payments
(
    invoice_payment_id        INT AUTO_INCREMENT     NOT NULL,
    user_id                   INT                    NOT NULL,
    user_company_id           INT                    NOT NULL,
    invoice_id                INT                    NOT NULL,
    payment_date              datetime               NOT NULL,
    payment_type              VARCHAR(15)            NOT NULL,
    payment_amount            DECIMAL(10, 2)         NOT NULL,
    check_number              VARCHAR(40) DEFAULT '' NOT NULL,
    authorize_auth_code       VARCHAR(40) DEFAULT '' NOT NULL,
    authorize_transaction_id  VARCHAR(40) DEFAULT '' NOT NULL,
    last_four_account_numbers VARCHAR(4)  DEFAULT '' NOT NULL,
    bank_name                 VARCHAR(50) DEFAULT '' NOT NULL,
    added_date                datetime               NOT NULL,
    added_by_user_id          INT                    NULL,
    deleted                   TINYINT     DEFAULT 0  NULL,
    deleted_date              datetime               NULL,
    deleted_by_user_id        INT                    NULL,
    CONSTRAINT PK_INVOICE_PAYMENTS PRIMARY KEY (invoice_payment_id)
);


CREATE TABLE invoices
(
    invoice_id                   INT AUTO_INCREMENT        NOT NULL,
    user_id                      INT                       NULL,
    user_company_id              INT                       NULL,
    site_id                      INT            DEFAULT 1  NULL,
    subscription_id              INT                       NULL,
    period_start_date            date                      NULL,
    period_end_date              date                      NULL,
    invoice_date                 datetime                  NOT NULL,
    added_date                   datetime                  NULL,
    payment_due_date             date                      NOT NULL,
    payment_type                 VARCHAR(15)    DEFAULT '' NOT NULL,
    last_four_account_numbers    VARCHAR(4)     DEFAULT '' NOT NULL,
    bank_name                    VARCHAR(50)    DEFAULT '' NOT NULL,
    exp_month                    INT                       NULL,
    exp_year                     INT                       NULL,
    bill_to                      VARCHAR(75)    DEFAULT '' NOT NULL,
    bill_attn_to                 VARCHAR(50)    DEFAULT '' NOT NULL,
    bill_phone                   VARCHAR(25)    DEFAULT '' NOT NULL,
    email                        VARCHAR(100)   DEFAULT '' NOT NULL,
    bill_address                 VARCHAR(300)   DEFAULT '' NOT NULL,
    bill_delivery                VARCHAR(25)    DEFAULT '' NOT NULL,
    bill_amount                  DECIMAL(7, 2)             NULL,
    bill_description             VARCHAR(75)    DEFAULT '' NOT NULL,
    state                        VARCHAR(2)     DEFAULT '' NOT NULL,
    tax_amount                   DECIMAL(7, 2)             NULL,
    total_order_amount           DECIMAL(7, 2)             NULL,
    paid_date                    datetime                  NULL,
    emailed_date                 datetime                  NULL,
    mailed_date                  datetime                  NULL,
    resent_date                  datetime                  NULL,
    check_number                 VARCHAR(40)    DEFAULT '' NOT NULL,
    total_paid                   DECIMAL(10, 2) DEFAULT 0  NULL,
    deleteDate                   datetime                  NULL,
    delete_message               VARCHAR(1000)  DEFAULT '' NOT NULL,
    parent_invoice_id            INT                       NULL,
    old_order_id                 INT                       NULL,
    old_authorize_auth_code      VARCHAR(40)    DEFAULT '' NOT NULL,
    old_authorize_transaction_id VARCHAR(40)    DEFAULT '' NOT NULL,
    CONSTRAINT PK_INVOICES PRIMARY KEY (invoice_id)
);


CREATE TABLE jqm_ranges
(
    rangeID     INT                   NOT NULL,
    range_min   INT                   NULL,
    range_max   INT                   NULL,
    range_color VARCHAR(8) DEFAULT '' NOT NULL,
    site_id     INT        DEFAULT 1  NULL,
    CONSTRAINT PK_JQM_RANGES PRIMARY KEY (rangeID)
);


CREATE TABLE jqm_states
(
    countryID VARCHAR(2)  DEFAULT '' NOT NULL,
    stateID   VARCHAR(2)  DEFAULT '' NOT NULL,
    state     VARCHAR(50) DEFAULT '' NOT NULL,
    CONSTRAINT PK_JQM_STATES PRIMARY KEY (countryID, stateID)
);


CREATE TABLE lo_carrier_comments
(
    lo_carrier_comment_id INT AUTO_INCREMENT NOT NULL,
    lo_carrier_id         INT                NOT NULL,
    user_id               INT                NOT NULL,
    date                  date               NULL,
    comment               MEDIUMTEXT         NOT NULL,
    deleted               TINYINT DEFAULT 0  NOT NULL,
    CONSTRAINT PK_LO_CARRIER_COMMENTS PRIMARY KEY (lo_carrier_comment_id)
);


CREATE TABLE lo_carrier_equipment
(
    lo_carrier_id INT        NOT NULL,
    equipment_id  VARCHAR(4) NOT NULL,
    CONSTRAINT PK_LO_CARRIER_EQUIPMENT PRIMARY KEY (lo_carrier_id, equipment_id)
);


CREATE TABLE lo_carrier_loads
(
    lo_carrier_load_id         INT AUTO_INCREMENT         NOT NULL,
    lo_carrier_id              INT                        NOT NULL,
    load_id                    INT                        NOT NULL,
    assignment_number          VARCHAR(25)   DEFAULT ''   NOT NULL,
    number_of_loads            INT                        NULL,
    origin_city                VARCHAR(75)   DEFAULT ''   NOT NULL,
    origin_state               VARCHAR(2)    DEFAULT ''   NOT NULL,
    origin_zip                 VARCHAR(20)   DEFAULT ''   NOT NULL,
    origin_country             VARCHAR(20)   DEFAULT 'US' NOT NULL,
    destination_city           VARCHAR(75)   DEFAULT ''   NOT NULL,
    destination_state          VARCHAR(2)    DEFAULT ''   NOT NULL,
    destination_zip            VARCHAR(20)   DEFAULT ''   NOT NULL,
    destination_country        VARCHAR(20)   DEFAULT 'US' NOT NULL,
    pickup_date                date                       NULL,
    commodity                  VARCHAR(100)  DEFAULT ''   NOT NULL,
    rate                       DECIMAL(10, 2)             NULL,
    rate_type                  VARCHAR(25)   DEFAULT ''   NOT NULL,
    booked_date                date                       NULL,
    booked_user_id             INT                        NOT NULL,
    booked_company_id          INT                        NOT NULL,
    complete_date              date                       NULL,
    complete_by_user_id        INT                        NULL,
    edit_date                  date                       NULL,
    edit_by_user_id            INT                        NULL,
    confirmation_email_date    date                       NULL,
    confirmation_email_user_id INT                        NULL,
    confirmation_phone_date    date                       NULL,
    confirmation_phone_user_id INT                        NULL,
    confirmation_text_date     date                       NULL,
    confirmation_text_user_id  INT                        NULL,
    comments                   VARCHAR(1000) DEFAULT ''   NOT NULL,
    loaded_weight              DOUBLE                     NULL,
    est_rate_per_mile          DECIMAL(10, 2)             NULL,
    est_pay                    DECIMAL(10, 2)             NULL,
    mileage                    DECIMAL(10, 2)             NULL,
    paid                       TINYINT       DEFAULT 0    NOT NULL,
    paid_date                  datetime                   NULL,
    paid_user_id               INT                        NULL,
    paid_notes                 VARCHAR(1000) DEFAULT ''   NOT NULL,
    CONSTRAINT PK_LO_CARRIER_LOADS PRIMARY KEY (lo_carrier_load_id)
);


CREATE TABLE lo_carrier_states_run
(
    lo_carrier_id INT NOT NULL,
    state_id      INT NOT NULL,
    CONSTRAINT PK_LO_CARRIER_STATES_RUN PRIMARY KEY (lo_carrier_id, state_id)
);


CREATE TABLE lo_carriers
(
    lo_carrier_id                INT AUTO_INCREMENT         NOT NULL,
    user_company_id              INT                        NOT NULL COMMENT 'company that added this carrier --> #session.user_company_id#',
    added_user_id                INT                        NOT NULL COMMENT 'user that added the carrier ---> #session.user_id#',
    added_date                   date                       NULL,
    edit_user_id                 INT                        NULL,
    edit_date                    date                       NULL,
    census_num                   INT                        NULL,
    company_name                 VARCHAR(200)               NOT NULL,
    contact_name                 VARCHAR(75)   DEFAULT ''   NOT NULL,
    city                         VARCHAR(100)  DEFAULT ''   NOT NULL,
    state                        VARCHAR(2)    DEFAULT ''   NOT NULL,
    zip                          VARCHAR(20)   DEFAULT ''   NOT NULL,
    country                      VARCHAR(10)   DEFAULT 'US' NOT NULL,
    phone_1                      VARCHAR(50)   DEFAULT ''   NOT NULL,
    phone_1_is_cell              INT           DEFAULT 0    NULL,
    phone_1_comments             VARCHAR(30)   DEFAULT ''   NOT NULL,
    phone_2                      VARCHAR(50)   DEFAULT ''   NOT NULL,
    phone_2_is_cell              INT           DEFAULT 0    NULL,
    phone_2_comments             VARCHAR(30)   DEFAULT ''   NOT NULL,
    phone_3                      VARCHAR(50)   DEFAULT ''   NOT NULL,
    phone_3_is_cell              INT           DEFAULT 0    NULL,
    phone_3_comments             VARCHAR(30)   DEFAULT ''   NOT NULL,
    fax                          VARCHAR(50)   DEFAULT ''   NOT NULL,
    email                        VARCHAR(100)  DEFAULT ''   NOT NULL,
    mailing_address              VARCHAR(1000) DEFAULT ''   NOT NULL,
    w9_on_file_date              date                       NULL,
    insurance_company            VARCHAR(100)  DEFAULT ''   NOT NULL,
    insurance_expiration_date    date                       NULL,
    insurance_on_file_date       date                       NULL,
    insurance_cargo_coverage     DECIMAL(10)                NULL,
    insurance_liability_coverage DECIMAL(10)                NULL,
    active                       INT           DEFAULT 1    NULL,
    source                       VARCHAR(45)   DEFAULT ''   NOT NULL,
    source_id                    VARCHAR(45)   DEFAULT ''   NOT NULL,
    CONSTRAINT PK_LO_CARRIERS PRIMARY KEY (lo_carrier_id)
);


CREATE TABLE lo_todo
(
    lo_todo_id               INT AUTO_INCREMENT      NOT NULL,
    task                     BLOB                    NOT NULL,
    assigned_to_user_id_list VARCHAR(255)            NOT NULL,
    assigned_by_user_id      INT                     NULL,
    due_date                 VARCHAR(255) DEFAULT '' NOT NULL,
    date_assigned            date                    NOT NULL,
    completed_date           date                    NULL,
    completed_user_id        INT                     NULL,
    CONSTRAINT PK_LO_TODO PRIMARY KEY (lo_todo_id)
);


CREATE TABLE lo_todo_comments
(
    lo_todo_comment_id INT AUTO_INCREMENT NOT NULL,
    lo_todo_id         INT                NOT NULL,
    user_id            INT                NOT NULL,
    date               date               NOT NULL,
    comment            BLOB               NOT NULL,
    CONSTRAINT PK_LO_TODO_COMMENTS PRIMARY KEY (lo_todo_comment_id)
);


CREATE TABLE lo_user_company_settings
(
    user_company_id     INT                     NOT NULL,
    membership_end_date date                    NULL,
    trial_start_date    date                    NULL,
    trial_end_date      date                    NULL,
    canceled_date       date                    NULL,
    deletion_date       date                    NULL,
    monthly_rate        DECIMAL(6, 2) DEFAULT 0 NULL,
    threemonth_rate     DECIMAL(6, 2) DEFAULT 0 NULL,
    sixmonth_rate       DECIMAL(6, 2) DEFAULT 0 NULL,
    yearly_rate         DECIMAL(6, 2) DEFAULT 0 NULL,
    biyearly_rate       DECIMAL(6, 2) DEFAULT 0 NULL,
    is_custom_rate      TINYINT       DEFAULT 0 NOT NULL,
    CONSTRAINT PK_LO_USER_COMPANY_SETTINGS PRIMARY KEY (user_company_id)
);


CREATE TABLE lo_user_settings
(
    user_id                     INT                     NOT NULL,
    user_company_id             INT                     NOT NULL,
    deletion_date               date                    NULL,
    loads_posted                INT          DEFAULT 0  NULL,
    carriers_assigned           INT          DEFAULT 0  NULL,
    currentSessionID            VARCHAR(255) DEFAULT '' NOT NULL,
    last_activity               INT                     NULL,
    use_chat                    INT          DEFAULT 1  NULL,
    view_open_contracts         INT          DEFAULT 3  NULL,
    view_open_contracts_group   INT          DEFAULT 1  NULL,
    view_closed_contracts       INT          DEFAULT 3  NULL,
    view_closed_contracts_group INT          DEFAULT 1  NULL,
    view_closed_contracts_days  INT          DEFAULT 90 NULL,
    view_todo                   INT          DEFAULT 3  NULL,
    last_chat_activity          INT                     NULL,
    show_closed_contracts_hours INT          DEFAULT 24 NULL COMMENT 'The number of hours a closed contract should show up on the Open contracts page',
    last_login_date             timestamp               NULL,
    settings_updated_date       datetime                NULL,
    CONSTRAINT PK_LO_USER_SETTINGS PRIMARY KEY (user_id)
);


CREATE TABLE load_alert_notifications
(
    load_alert_notification_id INT AUTO_INCREMENT       NOT NULL,
    notification_id            INT                      NOT NULL,
    load_alert_id              INT                      NOT NULL,
    load_ids                   VARCHAR(5000) DEFAULT '' NOT NULL,
    CONSTRAINT PK_LOAD_ALERT_NOTIFICATIONS PRIMARY KEY (load_alert_notification_id)
);


CREATE TABLE load_alerts
(
    load_alert_id             INT AUTO_INCREMENT                      NOT NULL,
    user_id                   INT                                     NOT NULL,
    site_id                   INT                                     NOT NULL,
    origin_country            VARCHAR(25)   DEFAULT ''                NOT NULL,
    origin_state              VARCHAR(255)  DEFAULT ''                NOT NULL,
    origin_city               VARCHAR(60)   DEFAULT ''                NOT NULL,
    origin_zip                VARCHAR(7)    DEFAULT ''                NOT NULL,
    origin_lat                DOUBLE                                  NULL,
    origin_long               DOUBLE                                  NULL,
    origin_radius             INT                                     NULL,
    destination_country       VARCHAR(25)   DEFAULT ''                NOT NULL,
    destination_state         VARCHAR(255)  DEFAULT ''                NOT NULL,
    destination_city          VARCHAR(60)   DEFAULT ''                NOT NULL,
    destination_zip           VARCHAR(7)    DEFAULT ''                NOT NULL,
    destination_lat           DOUBLE                                  NULL,
    destination_long          DOUBLE                                  NULL,
    destination_radius        INT                                     NULL,
    user_company_ids          VARCHAR(5000) DEFAULT ''                NOT NULL,
    company_names             MEDIUMTEXT                              NULL,
    equipment_ids             VARCHAR(100)  DEFAULT ''                NOT NULL,
    equipment_names           VARCHAR(1000) DEFAULT ''                NOT NULL,
    rate_product_category_ids VARCHAR(100)  DEFAULT ''                NOT NULL,
    product                   MEDIUMTEXT                              NULL,
    date_added                datetime                                NOT NULL,
    date_edited               datetime                                NULL,
    deleted_date              datetime                                NULL,
    deleted                   TINYINT       DEFAULT 0                 NOT NULL,
    active                    TINYINT       DEFAULT 1                 NOT NULL,
    old_alert_method          VARCHAR(10)   DEFAULT ''                NOT NULL,
    frequency                 INT           DEFAULT 0                 NOT NULL,
    last_run                  datetime      DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_LOAD_ALERTS PRIMARY KEY (load_alert_id)
);


CREATE TABLE load_alerts_log
(
    log_id        INT AUTO_INCREMENT NOT NULL,
    min_post_date datetime           NOT NULL,
    max_post_date datetime           NOT NULL,
    alerts_sent   INT                NULL,
    loads_sent    INT                NULL,
    execution_ms  INT                NULL,
    CONSTRAINT PK_LOAD_ALERTS_LOG PRIMARY KEY (log_id)
);


CREATE TABLE load_alerts_user_load_log
(
    user_id    INT      NOT NULL,
    load_id    INT      NOT NULL,
    added_date datetime NOT NULL,
    CONSTRAINT PK_LOAD_ALERTS_USER_LOAD_LOG PRIMARY KEY (user_id, load_id)
);


CREATE TABLE load_analytics_emails
(
    load_analytic_email_id INT AUTO_INCREMENT      NOT NULL,
    user_id                INT                     NOT NULL,
    dateAdded              date                    NOT NULL,
    equipment              VARCHAR(255) DEFAULT '' NOT NULL,
    state                  VARCHAR(255) DEFAULT '' NOT NULL,
    subject                VARCHAR(255) DEFAULT '' NOT NULL,
    message                MEDIUMTEXT              NULL,
    site_id                INT          DEFAULT 1  NOT NULL,
    CONSTRAINT PK_LOAD_ANALYTICS_EMAILS PRIMARY KEY (load_analytic_email_id)
);


CREATE TABLE load_analytics_search
(
    load_analytics_search_id INT AUTO_INCREMENT      NOT NULL,
    user_id                  INT                     NOT NULL,
    origin_state             VARCHAR(5)              NOT NULL,
    date                     date                    NOT NULL,
    equipment                VARCHAR(255) DEFAULT '' NOT NULL,
    site_id                  INT          DEFAULT 1  NOT NULL,
    CONSTRAINT PK_LOAD_ANALYTICS_SEARCH PRIMARY KEY (load_analytics_search_id)
);


CREATE TABLE load_assignment_confirmation_log
(
    load_assignment_confirmation_log_id INT AUTO_INCREMENT      NOT NULL,
    load_assignment_id                  INT                     NOT NULL,
    confirmation_file_id                INT                     NOT NULL,
    to_ab_user_id                       INT                     NULL,
    log_action                          VARCHAR(100) DEFAULT '' NOT NULL,
    log_date                            datetime                NOT NULL,
    log_by_user_id                      INT                     NULL,
    log_by_ab_user_id                   INT                     NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENT_CONFIRMATION_LOG PRIMARY KEY (load_assignment_confirmation_log_id)
);


CREATE TABLE load_assignment_export_schedules
(
    user_company_id         INT                                         NOT NULL,
    export_active           TINYINT                  DEFAULT 0          NOT NULL,
    export_emails           VARCHAR(500)             DEFAULT ''         NOT NULL,
    last_run_date           datetime                                    NULL,
    next_run_date_client_tz datetime                                    NULL,
    next_run_date           datetime                                    NULL,
    start_date              date                                        NOT NULL,
    start_time              time                     DEFAULT '08:00:00' NOT NULL,
    export_from_date        datetime                                    NULL,
    export_to_date          datetime                                    NULL,
    first_run               TINYINT                  DEFAULT 1          NOT NULL,
    date_only               TINYINT                  DEFAULT 0          NOT NULL,
    frequency               ENUM ('daily', 'weekly') DEFAULT 'weekly'   NULL,
    export_master           TINYINT                  DEFAULT 1          NOT NULL,
    export_receivables      TINYINT                  DEFAULT 1          NOT NULL,
    export_payables         TINYINT                  DEFAULT 1          NOT NULL,
    added_date              datetime                                    NOT NULL,
    added_by_user_id        INT                                         NOT NULL,
    edit_date               datetime                                    NULL,
    edit_by_user_id         INT                                         NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENT_EXPORT_SCHEDULES PRIMARY KEY (user_company_id)
);


CREATE TABLE load_assignment_exports
(
    load_assignment_export_id INT AUTO_INCREMENT      NOT NULL,
    user_id                   INT                     NULL,
    user_company_id           INT                     NULL,
    export_date               datetime                NULL,
    export_start_date         datetime                NULL,
    export_end_date           datetime                NULL,
    number_of_items           INT                     NULL,
    export_file_url           VARCHAR(150) DEFAULT '' NOT NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENT_EXPORTS PRIMARY KEY (load_assignment_export_id)
);


CREATE TABLE load_assignment_file_fields
(
    load_assignment_id INT                           NOT NULL,
    file_id            INT                           NOT NULL,
    field_name         VARCHAR(50)                   NOT NULL,
    field_type         VARCHAR(10)  DEFAULT 'string' NOT NULL,
    field_value        VARCHAR(100) DEFAULT ''       NOT NULL,
    confidence         DOUBLE                        NULL,
    page_number        INT                           NULL,
    bounding_box       JSON                          NULL,
    span_offset        INT                           NULL,
    span_length        INT                           NULL,
    edit_by_user_id    INT                           NULL,
    edit_date          datetime                      NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENT_FILE_FIELDS PRIMARY KEY (load_assignment_id, file_id, field_name)
);


CREATE TABLE load_assignment_files
(
    load_assignment_id          INT                                     NOT NULL,
    file_id                     INT                                     NOT NULL,
    date_added                  datetime      DEFAULT CURRENT_TIMESTAMP NOT NULL,
    file_order                  INT           DEFAULT 1                 NOT NULL,
    fields_extracted            TINYINT       DEFAULT 0                 NULL,
    fields_extracted_by_user_id INT                                     NULL,
    fields_extracted_date       datetime                                NULL,
    needs_attention             TINYINT       DEFAULT 0                 NOT NULL,
    needs_attention_date        datetime                                NULL,
    needs_attention_by_user_id  INT                                     NULL,
    attention_note              VARCHAR(1000) DEFAULT ''                NOT NULL,
    approved                    TINYINT       DEFAULT 0                 NULL,
    approved_by_user_id         INT                                     NULL,
    approved_date               datetime                                NULL,
    deleted                     TINYINT       DEFAULT 0                 NOT NULL,
    deleted_date                datetime                                NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENT_FILES PRIMARY KEY (load_assignment_id, file_id)
);


CREATE TABLE load_assignment_geo_history
(
    load_assignment_geo_history_id INT AUTO_INCREMENT                     NOT NULL,
    load_assignment_id             INT                                    NOT NULL,
    user_id                        INT                                    NULL,
    ab_user_id                     INT                                    NULL,
    latitude                       DOUBLE                                 NOT NULL,
    longitude                      DOUBLE                                 NOT NULL,
    accuracy                       FLOAT(12)                              NULL,
    speed                          FLOAT(12)                              NULL,
    heading                        FLOAT(12)                              NULL,
    altitude                       FLOAT(12)                              NULL,
    timestamp                      datetime     DEFAULT CURRENT_TIMESTAMP NOT NULL,
    event                          VARCHAR(250) DEFAULT ''                NOT NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENT_GEO_HISTORY PRIMARY KEY (load_assignment_geo_history_id)
);


CREATE TABLE load_assignment_surcharge_types
(
    load_assignment_surcharge_type_id INT AUTO_INCREMENT NOT NULL,
    load_assignment_surcharge_type    VARCHAR(45)        NOT NULL,
    is_percentage                     BIT     DEFAULT 0  NOT NULL,
    is_per_mile                       TINYINT DEFAULT 0  NOT NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENT_SURCHARGE_TYPES PRIMARY KEY (load_assignment_surcharge_type_id)
);


CREATE TABLE load_assignment_surcharges
(
    load_assignment_surcharge_id      INT AUTO_INCREMENT NOT NULL,
    load_assignment_id                INT                NOT NULL,
    load_assignment_surcharge_type_id INT                NOT NULL,
    load_assignment_surcharge         DECIMAL(12, 4)     NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENT_SURCHARGES PRIMARY KEY (load_assignment_surcharge_id, load_assignment_id, load_assignment_surcharge_type_id)
);


CREATE TABLE load_assignments
(
    load_assignment_id                    INT AUTO_INCREMENT                                                                                                                  NOT NULL,
    external_load_assignment_id           VARCHAR(50)                                                                                                 DEFAULT ''              NOT NULL,
    user_id                               INT                                                                                                                                 NULL,
    user_company_id                       INT                                                                                                                                 NULL,
    load_id                               INT                                                                                                                                 NULL,
    to_ab_company_id                      INT                                                                                                                                 NULL,
    to_ab_user_id                         INT                                                                                                                                 NULL COMMENT 'The driver or the assignee sales rep',
    to_user_id                            INT                                                                                                                                 NULL,
    to_user_company_id                    INT                                                                                                                                 NULL,
    to_load_id                            INT                                                                                                                                 NULL,
    hiring_ab_company_id                  INT                                                                                                                                 NULL,
    hiring_ab_user_id                     INT                                                                                                                                 NULL COMMENT 'The driver or the assignee sales rep',
    bill_to_ab_company_id                 INT                                                                                                                                 NULL,
    bill_to_ab_user_id                    INT                                                                                                                                 NULL,
    load_invoice_id                       INT                                                                                                                                 NULL,
    auto_invoice                          TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    ready_to_invoice                      TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    default_dispatcher_user_id            INT                                                                                                                                 NULL,
    assignment_status                     ENUM ('Unassigned', 'Assigned', 'Dispatched', 'Loading', 'En Route', 'Unloading', 'Delivered', 'Completed') DEFAULT 'Unassigned'    NOT NULL,
    assignment_phone                      VARCHAR(25)                                                                                                 DEFAULT ''              NOT NULL,
    assignment_email                      VARCHAR(100)                                                                                                DEFAULT ''              NOT NULL,
    contract_number                       VARCHAR(45)                                                                                                 DEFAULT ''              NOT NULL,
    pickup_number                         VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    pickup_notes                          MEDIUMTEXT                                                                                                                          NULL,
    drop_number                           VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    drop_notes                            MEDIUMTEXT                                                                                                                          NULL,
    work_order_number                     VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    inside_notes                          MEDIUMTEXT                                                                                                                          NULL,
    personal_message                      MEDIUMTEXT                                                                                                                          NULL,
    created_date                          datetime                                                                                                                            NOT NULL,
    created_by_user_id                    INT                                                                                                                                 NULL,
    created_by_user_company_id            INT                                                                                                                                 NULL,
    assigned_date                         datetime                                                                                                                            NULL,
    assigned_by_user_id                   INT                                                                                                                                 NULL,
    dispatched_date                       datetime                                                                                                                            NULL,
    dispatched_by_user_id                 INT                                                                                                                                 NULL,
    dispatched_by_ab_user_id              INT                                                                                                                                 NULL,
    loading_date                          datetime                                                                                                                            NULL,
    loading_by_user_id                    INT                                                                                                                                 NULL,
    loading_by_ab_user_id                 INT                                                                                                                                 NULL,
    enroute_date                          datetime                                                                                                                            NULL,
    enroute_by_user_id                    INT                                                                                                                                 NULL,
    enroute_by_ab_user_id                 INT                                                                                                                                 NULL,
    unloading_date                        datetime                                                                                                                            NULL,
    unloading_by_user_id                  INT                                                                                                                                 NULL,
    unloading_by_ab_user_id               INT                                                                                                                                 NULL,
    delivered_date                        datetime                                                                                                                            NULL,
    delivered_by_user_id                  INT                                                                                                                                 NULL,
    delivered_by_ab_user_id               INT                                                                                                                                 NULL,
    completed_date                        datetime                                                                                                                            NULL,
    completed_by_user_id                  INT                                                                                                                                 NULL,
    completed_by_ab_user_id               INT                                                                                                                                 NULL,
    shared_with_hired_company             TINYINT                                                                                                     DEFAULT 0               NULL,
    shared_with_hired_company_response    ENUM ('Pending', 'Accepted', 'Dismissed')                                                                                           NULL,
    shared_with_hiring_company            TINYINT                                                                                                     DEFAULT 0               NULL,
    shared_with_hiring_company_response   ENUM ('Pending', 'Accepted', 'Dismissed')                                                                                           NULL,
    edit_date                             datetime                                                                                                                            NULL,
    edit_by_user_id                       INT                                                                                                                                 NULL,
    deleted_date                          datetime                                                                                                                            NULL,
    deleted_by_user_id                    INT                                                                                                                                 NULL,
    deleted                               TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    deleted_message                       VARCHAR(1000)                                                                                               DEFAULT ''              NOT NULL,
    to_deleted_date                       datetime                                                                                                                            NULL,
    to_deleted_by_user_id                 INT                                                                                                                                 NULL,
    to_deleted                            TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    to_deleted_message                    VARCHAR(1000)                                                                                               DEFAULT ''              NOT NULL,
    booked_from_offer_recipient_id        INT                                                                                                                                 NULL,
    number_of_files                       INT                                                                                                         DEFAULT 0               NOT NULL,
    total_files_size                      INT                                                                                                         DEFAULT 0               NOT NULL,
    needs_attention                       TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    needs_attention_date                  datetime                                                                                                                            NULL,
    assignment_confirmation_ready_to_send TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    booking_confirmation_ready_to_send    TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    send_confirmation                     TINYINT                                                                                                     DEFAULT 1               NOT NULL,
    confirmation_file_id                  INT                                                                                                                                 NULL,
    confirmation_file_code                VARCHAR(50)                                                                                                 DEFAULT ''              NOT NULL,
    confirmation_sent_date                datetime                                                                                                                            NULL,
    confirmation_sent_method              VARCHAR(50)                                                                                                 DEFAULT ''              NOT NULL,
    confirmation_sent_by_user_id          INT                                                                                                                                 NULL,
    confirmation_revised_date             datetime                                                                                                                            NULL,
    confirmation_sms_message              VARCHAR(200)                                                                                                DEFAULT ''              NOT NULL,
    confirmation_opened_date              datetime                                                                                                                            NULL,
    confirmation_confirmed_date           datetime                                                                                                                            NULL,
    confirmation_confirmed_method         VARCHAR(50)                                                                                                 DEFAULT ''              NOT NULL,
    confirmation_confirmed_by_user_id     INT                                                                                                                                 NULL,
    confirmation_confirmed_by_ab_user_id  INT                                                                                                                                 NULL,
    confirmation_email_status             VARCHAR(25)                                                                                                 DEFAULT ''              NOT NULL,
    confirmation_email_queue_id           INT                                                                                                                                 NULL,
    confirmation_to_ab_user_ids           VARCHAR(100)                                                                                                DEFAULT ''              NOT NULL,
    confirmation_cc_others                VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    mileage                               DECIMAL(10, 2)                                                                                                                      NULL,
    loaded_weight                         DOUBLE                                                                                                                              NULL,
    loaded_volume                         DOUBLE                                                                                                                              NULL,
    unload_weight                         DOUBLE                                                                                                                              NULL,
    unload_volume                         DOUBLE                                                                                                                              NULL,
    loading_ticket_number                 VARCHAR(50)                                                                                                 DEFAULT ''              NOT NULL,
    unloading_ticket_number               VARCHAR(50)                                                                                                 DEFAULT ''              NOT NULL,
    bol_number                            VARCHAR(50)                                                                                                 DEFAULT ''              NOT NULL,
    load_assignment_number                VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    hauled_notes                          MEDIUMTEXT                                                                                                                          NULL,
    scheduled_hauled_date                 date                                                                                                                                NULL,
    hauled_date                           date                                                                                                                                NULL,
    original_rate                         DECIMAL(13, 5)                                                                                                                      NULL,
    original_rate_type                    VARCHAR(10)                                                                                                 DEFAULT '2000'          NOT NULL,
    original_rate_visible                 TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    original_rate_percentage              DECIMAL(5, 2)                                                                                                                       NULL,
    rate                                  DECIMAL(13, 5)                                                                                                                      NULL,
    rate_type                             VARCHAR(10)                                                                                                 DEFAULT '2000'          NOT NULL,
    est_weight                            DOUBLE                                                                                                                              NULL,
    est_volume                            DOUBLE                                                                                                                              NULL,
    est_miles                             DECIMAL(10, 2)                                                                                                                      NULL,
    est_hours                             DECIMAL(10, 2)                                                                                                                      NULL,
    est_quantity                          DECIMAL(14, 6)                                                                                                                      NULL,
    est_subtotal                          DECIMAL(10, 2)                                                                                                                      NULL,
    est_surcharges                        DECIMAL(10, 2)                                                                                              DEFAULT 0               NOT NULL,
    est_total                             DECIMAL(10, 2)                                                                                                                      NULL,
    est_rate_per_mile                     DECIMAL(10, 2)                                                                                                                      NULL,
    bill_weight_use                       ENUM ('loaded_weight', 'unload_weight')                                                                     DEFAULT 'loaded_weight' NOT NULL,
    bill_weight                           DOUBLE                                                                                                                              NULL,
    bill_volume                           DOUBLE                                                                                                                              NULL,
    bill_miles                            DECIMAL(10, 2)                                                                                                                      NULL,
    bill_hours                            DECIMAL(10, 2)                                                                                                                      NULL,
    bill_quantity                         DECIMAL(14, 6)                                                                                                                      NULL,
    bill_subtotal                         DECIMAL(10, 2)                                                                                                                      NULL,
    bill_surcharges                       DECIMAL(10, 2)                                                                                              DEFAULT 0               NOT NULL,
    bill_total                            DECIMAL(10, 2)                                                                                                                      NULL,
    bill_rate_per_mile                    DECIMAL(10, 2)                                                                                                                      NULL,
    payment                               DECIMAL(10, 2)                                                                                              DEFAULT 0               NULL COMMENT 'shippe or /broker posted payment, the hiring company',
    payment_date                          datetime                                                                                                                            NULL,
    payment_by_user_id                    INT                                                                                                                                 NULL,
    payment_notes                         VARCHAR(1000)                                                                                               DEFAULT ''              NOT NULL,
    payment_approved                      TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    payment_approved_date                 datetime                                                                                                                            NULL,
    payment_approved_by_user_id           INT                                                                                                                                 NULL,
    paid                                  TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    paid_date                             datetime                                                                                                                            NULL,
    paid_by_user_id                       INT                                                                                                                                 NULL,
    bill_to_payment                       DECIMAL(10, 2)                                                                                                                      NULL,
    bill_to_payment_date                  datetime                                                                                                                            NULL,
    bill_to_payment_by_user_id            INT                                                                                                                                 NULL,
    bill_to_payment_notes                 VARCHAR(1000)                                                                                               DEFAULT ''              NOT NULL,
    to_payment                            DECIMAL(10, 2)                                                                                              DEFAULT 0               NULL COMMENT 'carrier or broker posted payment, the hired company from to_ab_user_id',
    to_payment_date                       datetime                                                                                                                            NULL,
    to_payment_by_user_id                 INT                                                                                                                                 NULL,
    to_payment_notes                      VARCHAR(1000)                                                                                               DEFAULT ''              NOT NULL,
    to_paid                               TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    to_paid_date                          datetime                                                                                                                            NULL,
    to_paid_by_user_id                    INT                                                                                                                                 NULL,
    geo_share_location                    TINYINT                                                                                                     DEFAULT 1               NOT NULL,
    geo_request_status                    ENUM ('', 'pending', 'accepted')                                                                                                    NULL,
    geo_request_method                    ENUM ('', 'sms', 'email', 'both')                                                                                                   NOT NULL,
    geo_request_date                      datetime                                                                                                                            NULL,
    geo_response_date                     datetime                                                                                                                            NULL,
    geo_tracking_enabled                  BIT                                                                                                         DEFAULT 0               NOT NULL,
    geo_tracking_start_date               datetime                                                                                                                            NULL,
    geo_tracking_stop_date                datetime                                                                                                                            NULL,
    geo_updated_date                      datetime                                                                                                                            NULL,
    geo_updated_by_user_id                INT                                                                                                                                 NULL,
    geo_updated_by_ab_user_id             INT                                                                                                                                 NULL,
    geo_tracking_until                    datetime                                                                                                                            NULL,
    geo_latitude                          DOUBLE                                                                                                                              NULL,
    geo_longitude                         DOUBLE                                                                                                                              NULL,
    geo_accuracy                          FLOAT(12)                                                                                                                           NULL,
    geo_speed                             FLOAT(12)                                                                                                                           NULL,
    geo_heading                           FLOAT(12)                                                                                                                           NULL,
    geo_altitude                          FLOAT(12)                                                                                                                           NULL,
    reroute_request                       TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    reroute_request_date                  datetime                                                                                                                            NULL,
    reroute_request_reason                VARCHAR(100)                                                                                                DEFAULT ''              NOT NULL,
    reroute_request_notes                 VARCHAR(250)                                                                                                DEFAULT ''              NOT NULL,
    reroute_date                          datetime                                                                                                                            NULL,
    reroute_by_user_id                    INT                                                                                                                                 NULL,
    reroute_reason                        VARCHAR(1000)                                                                                               DEFAULT ''              NOT NULL,
    reroute_contract_id                   INT                                                                                                                                 NULL,
    reroute_contract_number               VARCHAR(45)                                                                                                 DEFAULT ''              NOT NULL,
    previous_load_assignment_number       VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    previous_pickup_number                VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    previous_drop_number                  VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    previous_work_order_number            VARCHAR(150)                                                                                                DEFAULT ''              NOT NULL,
    previous_rate                         DECIMAL(13, 5)                                                                                                                      NULL,
    previous_rate_type                    VARCHAR(10)                                                                                                 DEFAULT '2000'          NOT NULL,
    previous_bill_subtotal                DECIMAL(10, 2)                                                                                                                      NULL,
    previous_bill_surcharges              DECIMAL(10, 2)                                                                                              DEFAULT 0               NOT NULL,
    previous_bill_total                   DECIMAL(10, 2)                                                                                                                      NULL,
    previous_bill_rate_per_mile           DECIMAL(10, 2)                                                                                                                      NULL,
    reroute_pickup_drop                   ENUM ('pickup', 'drop')                                                                                     DEFAULT 'drop'          NOT NULL,
    reroute_ab_company_id                 INT                                                                                                                                 NULL,
    reroute_to_ab_company_id              INT                                                                                                                                 NULL,
    parent_load_assignment_id             INT                                                                                                                                 NULL,
    child_load_assignment_id              INT                                                                                                                                 NULL,
    chain_load_assignment_id              INT                                                                                                                                 NULL,
    is_rerouted                           TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    is_reassigned                         TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    is_intra_company                      TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    is_driver                             TINYINT                                                                                                     DEFAULT 0               NOT NULL,
    modified_date                         datetime                                                                                                                            NULL,
    load_assignment_export_id             INT                                                                                                                                 NULL,
    load_booking_export_id                INT                                                                                                                                 NULL,
    load_paid_export_id                   INT                                                                                                                                 NULL,
    truck_user_company_equipment_id       INT                                                                                                                                 NULL,
    trailer_user_company_equipment_id     INT                                                                                                                                 NULL,
    CONSTRAINT PK_LOAD_ASSIGNMENTS PRIMARY KEY (load_assignment_id)
);


CREATE TABLE load_emails
(
    load_email_id INT AUTO_INCREMENT      NOT NULL,
    load_id       INT                     NOT NULL,
    user_id       INT                     NOT NULL,
    site_id       INT                     NOT NULL,
    from_name     VARCHAR(100) DEFAULT '' NOT NULL,
    from_email    VARCHAR(100) DEFAULT '' NOT NULL,
    to_name       VARCHAR(100) DEFAULT '' NOT NULL,
    to_email      VARCHAR(100) DEFAULT '' NOT NULL,
    subject       VARCHAR(255) DEFAULT '' NOT NULL,
    body          MEDIUMTEXT              NOT NULL,
    date_added    timestamp               NULL,
    sent          TINYINT      DEFAULT 0  NULL,
    date_sent     timestamp               NULL,
    CONSTRAINT PK_LOAD_EMAILS PRIMARY KEY (load_email_id)
);


CREATE TABLE load_equipment
(
    load_equipment_id INT AUTO_INCREMENT NOT NULL,
    load_id           INT                NOT NULL,
    equipment_id      VARCHAR(20)        NOT NULL,
    CONSTRAINT PK_LOAD_EQUIPMENT PRIMARY KEY (load_equipment_id)
);


CREATE TABLE load_flags
(
    flag_id              INT AUTO_INCREMENT     NOT NULL,
    user_id              INT                    NOT NULL,
    site_id              INT         DEFAULT 1  NOT NULL,
    flagged_load_id      INT                    NOT NULL,
    reason               MEDIUMTEXT             NULL,
    date_added           datetime               NOT NULL,
    processed_by_user_id INT                    NULL,
    processed_action     VARCHAR(45) DEFAULT '' NOT NULL,
    processed_date       datetime               NULL,
    processed            TINYINT     DEFAULT 0  NOT NULL,
    CONSTRAINT PK_LOAD_FLAGS PRIMARY KEY (flag_id)
);


CREATE TABLE load_invoice_items
(
    load_invoice_item_id            INT AUTO_INCREMENT                     NOT NULL,
    load_invoice_id                 INT                                    NOT NULL,
    load_assignment_id              INT                                    NOT NULL,
    pickup_company_name             VARCHAR(150)            DEFAULT ''     NOT NULL,
    pickup_city                     VARCHAR(60)             DEFAULT ''     NOT NULL,
    pickup_state                    VARCHAR(2)              DEFAULT ''     NOT NULL,
    drop_company_name               VARCHAR(150)            DEFAULT ''     NOT NULL,
    drop_city                       VARCHAR(60)             DEFAULT ''     NOT NULL,
    drop_state                      VARCHAR(2)              DEFAULT ''     NOT NULL,
    load_assignment_number          VARCHAR(150)            DEFAULT ''     NOT NULL,
    commodity                       VARCHAR(255)            DEFAULT ''     NOT NULL,
    loaded_weight                   DOUBLE                                 NULL,
    loaded_volume                   DOUBLE                                 NULL,
    unload_weight                   DOUBLE                                 NULL,
    unload_volume                   DOUBLE                                 NULL,
    drop_number                     VARCHAR(150)            DEFAULT ''     NOT NULL,
    bol_number                      VARCHAR(50)             DEFAULT ''     NOT NULL,
    work_order_number               VARCHAR(150)            DEFAULT ''     NOT NULL,
    is_rerouted                     TINYINT                 DEFAULT 0      NOT NULL,
    bill_weight                     DOUBLE                                 NULL,
    bill_volume                     DOUBLE                                 NULL,
    bill_hours                      DECIMAL(10, 2)                         NULL,
    bill_miles                      DECIMAL(10, 2)                         NULL,
    bill_rate                       DECIMAL(13, 5)                         NULL,
    bill_rate_type                  VARCHAR(10)             DEFAULT ''     NOT NULL,
    bill_rate_message               VARCHAR(100)            DEFAULT ''     NOT NULL,
    hauled_date                     date                                   NULL,
    hauled_notes                    MEDIUMTEXT                             NULL,
    item_description                VARCHAR(1000)           DEFAULT ''     NOT NULL,
    item_amount                     DECIMAL(10, 2)                         NOT NULL,
    is_surcharge                    INT                     DEFAULT 0      NOT NULL,
    item_quantity                   DECIMAL(14, 6)                         NULL,
    pickup_number                   VARCHAR(150)            DEFAULT ''     NOT NULL,
    loading_ticket_number           VARCHAR(50)             DEFAULT ''     NOT NULL,
    unloading_ticket_number         VARCHAR(50)             DEFAULT ''     NOT NULL,
    reroute_company_name            VARCHAR(150)            DEFAULT ''     NOT NULL,
    reroute_city                    VARCHAR(60)             DEFAULT ''     NOT NULL,
    reroute_state                   VARCHAR(2)              DEFAULT ''     NOT NULL,
    reroute_pickup_drop             ENUM ('pickup', 'drop') DEFAULT 'drop' NOT NULL,
    previous_load_assignment_number VARCHAR(150)            DEFAULT ''     NOT NULL,
    previous_bill_rate              DECIMAL(13, 5)                         NULL,
    previous_bill_rate_type         VARCHAR(10)             DEFAULT ''     NOT NULL,
    previous_bill_rate_message      VARCHAR(100)            DEFAULT ''     NOT NULL,
    previous_item_amount            DECIMAL(10, 2)                         NULL,
    deleted_date                    datetime                               NULL,
    deleted_by_user_id              INT                                    NULL,
    deleted                         TINYINT                 DEFAULT 0      NULL,
    CONSTRAINT PK_LOAD_INVOICE_ITEMS PRIMARY KEY (load_invoice_item_id)
);


CREATE TABLE load_invoice_notes
(
    load_invoice_note_id INT AUTO_INCREMENT      NOT NULL,
    load_invoice_id      INT                     NOT NULL,
    user_id              INT                     NOT NULL,
    added_date           datetime                NULL,
    load_invoice_note    VARCHAR(200) DEFAULT '' NOT NULL,
    CONSTRAINT PK_LOAD_INVOICE_NOTES PRIMARY KEY (load_invoice_note_id)
);


CREATE TABLE load_invoice_payments
(
    load_invoice_payment_id INT AUTO_INCREMENT       NOT NULL,
    user_id                 INT                      NOT NULL,
    user_company_id         INT                      NOT NULL,
    settlement_file_id      INT                      NULL,
    payment_amount          DECIMAL(10, 2)           NOT NULL,
    payment_notes           VARCHAR(2048) DEFAULT '' NOT NULL,
    created_date            datetime                 NOT NULL,
    CONSTRAINT PK_LOAD_INVOICE_PAYMENTS PRIMARY KEY (load_invoice_payment_id)
);


CREATE TABLE load_invoice_payments_old
(
    load_invoice_payment_id      INT AUTO_INCREMENT      NOT NULL,
    load_invoice_id              INT                     NOT NULL,
    user_id                      INT                     NOT NULL,
    user_company_id              INT                     NOT NULL,
    payment_amount               DECIMAL(10, 2)          NULL,
    payment_notes                VARCHAR(200) DEFAULT '' NOT NULL,
    payment_confirmed            TINYINT      DEFAULT 0  NULL,
    payment_confirmed_date       datetime                NULL,
    payment_confirmed_by_user_id INT                     NULL,
    added_date                   datetime                NOT NULL,
    deleted_date                 datetime                NULL,
    deleted_by_user_id           INT                     NULL,
    deleted                      TINYINT      DEFAULT 0  NULL,
    edited_date                  datetime                NULL,
    edited_by_user_id            INT                     NULL,
    CONSTRAINT PK_LOAD_INVOICE_PAYMENTS_OLD PRIMARY KEY (load_invoice_payment_id)
);


CREATE TABLE load_invoices
(
    load_invoice_id              INT AUTO_INCREMENT        NOT NULL,
    load_invoice_payment_id      INT                       NULL,
    user_id                      INT                       NOT NULL,
    user_company_id              INT                       NOT NULL,
    ab_company_id                INT                       NULL,
    first_name                   VARCHAR(30)    DEFAULT '' NOT NULL,
    last_name                    VARCHAR(45)    DEFAULT '' NOT NULL,
    company_name                 VARCHAR(150)   DEFAULT '' NOT NULL,
    hiring_ab_user_id            INT                       NULL,
    hiring_ab_company_id         INT                       NULL,
    archived                     TINYINT        DEFAULT 0  NOT NULL,
    archived_date                datetime                  NULL,
    archived_by_user_id          INT                       NULL,
    bill_to_ab_user_id           INT                       NULL,
    bill_to_ab_company_id        INT                       NULL,
    bill_to_user_company_id      INT                       NULL,
    bill_to_user_id              INT                       NULL,
    bill_to_first_name           VARCHAR(30)    DEFAULT '' NOT NULL,
    bill_to_last_name            VARCHAR(45)    DEFAULT '' NOT NULL,
    bill_to_company_name         VARCHAR(150)   DEFAULT '' NOT NULL,
    bill_to_email                VARCHAR(100)   DEFAULT '' NOT NULL,
    bill_to_phone                VARCHAR(25)    DEFAULT '' NOT NULL,
    bill_to_address              VARCHAR(150)   DEFAULT '' NOT NULL,
    bill_to_city                 VARCHAR(60)    DEFAULT '' NOT NULL,
    bill_to_state                VARCHAR(2)     DEFAULT '' NOT NULL,
    bill_to_zip                  VARCHAR(10)    DEFAULT '' NOT NULL,
    bill_to_country              VARCHAR(25)    DEFAULT '' NOT NULL,
    bill_to_location             VARCHAR(80)    DEFAULT '' NOT NULL,
    bill_description             VARCHAR(1000)  DEFAULT '' NOT NULL,
    bill_to_archived             TINYINT        DEFAULT 0  NOT NULL,
    bill_to_archived_date        datetime                  NULL,
    bill_to_archived_by_user_id  INT                       NULL,
    revised_from_load_invoice_id INT                       NULL COMMENT 'When an invoice replaces a previous, the original load_invoice_id is stored here',
    modified_date                datetime                  NULL,
    email_queue_id               INT            DEFAULT 0  NULL,
    email_status                 VARCHAR(100)   DEFAULT '' NOT NULL,
    email_status_date            datetime                  NULL,
    invoice_total                DECIMAL(10, 2) DEFAULT 0  NULL,
    payment_due_date             datetime                  NULL,
    invoice_file_id              INT                       NULL,
    invoice_date                 datetime                  NULL,
    added_date                   datetime                  NULL,
    deleted_date                 datetime                  NULL,
    deleted_by_user_id           INT                       NULL,
    deleted                      TINYINT        DEFAULT 0  NOT NULL,
    CONSTRAINT PK_LOAD_INVOICES PRIMARY KEY (load_invoice_id)
);


CREATE TABLE load_invoices_backup
(
    load_invoice_id              INT            DEFAULT 0  NOT NULL,
    load_invoice_payment_id      INT                       NULL,
    user_id                      INT                       NOT NULL,
    user_company_id              INT                       NOT NULL,
    hiring_ab_user_id            INT                       NULL,
    hiring_ab_company_id         INT                       NULL,
    archived                     TINYINT        DEFAULT 0  NOT NULL,
    archived_date                datetime                  NULL,
    archived_by_user_id          INT                       NULL,
    bill_to_ab_user_id           INT                       NULL,
    bill_to_ab_company_id        INT                       NULL,
    bill_to_user_company_id      INT                       NULL,
    bill_to_user_id              INT                       NULL,
    bill_to_first_name           VARCHAR(30)    DEFAULT '' NOT NULL,
    bill_to_last_name            VARCHAR(45)    DEFAULT '' NOT NULL,
    bill_to_company_name         VARCHAR(150)   DEFAULT '' NOT NULL,
    bill_to_email                VARCHAR(100)   DEFAULT '' NOT NULL,
    bill_to_phone                VARCHAR(25)    DEFAULT '' NOT NULL,
    bill_to_address              VARCHAR(150)   DEFAULT '' NOT NULL,
    bill_to_city                 VARCHAR(60)    DEFAULT '' NOT NULL,
    bill_to_state                VARCHAR(2)     DEFAULT '' NOT NULL,
    bill_to_zip                  VARCHAR(10)    DEFAULT '' NOT NULL,
    bill_to_country              VARCHAR(25)    DEFAULT '' NOT NULL,
    bill_to_location             VARCHAR(80)    DEFAULT '' NOT NULL,
    bill_description             VARCHAR(1000)  DEFAULT '' NOT NULL,
    bill_to_archived             TINYINT        DEFAULT 0  NOT NULL,
    bill_to_archived_date        datetime                  NULL,
    bill_to_archived_by_user_id  INT                       NULL,
    revised_from_load_invoice_id INT                       NULL COMMENT 'When an invoice replaces a previous, the original load_invoice_id is stored here',
    modified_date                datetime                  NULL,
    email_queue_id               INT            DEFAULT 0  NULL,
    email_status                 VARCHAR(100)   DEFAULT '' NOT NULL,
    email_status_date            datetime                  NULL,
    invoice_total                DECIMAL(10, 2) DEFAULT 0  NULL,
    payment_due_date             datetime                  NULL,
    invoice_file_id              INT                       NULL,
    invoice_date                 datetime                  NULL,
    added_date                   datetime                  NULL,
    deleted_date                 datetime                  NULL,
    deleted_by_user_id           INT                       NULL,
    deleted                      TINYINT        DEFAULT 0  NOT NULL
);


CREATE TABLE load_leads
(
    load_id     INT                                                             NOT NULL,
    user_id     INT                                                             NOT NULL,
    truck_id    INT                                                             NULL,
    lead_source CHAR(1)   DEFAULT ''                                            NULL,
    date_added  timestamp DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_LOAD_LEADS PRIMARY KEY (load_id, user_id)
);


CREATE TABLE load_offers
(
    load_offer_id       INT AUTO_INCREMENT                                                                     NOT NULL,
    load_id             INT                                                                                    NOT NULL,
    message             MEDIUMTEXT                                                                             NULL,
    user_id             INT                                                                                    NULL,
    ab_user_id          INT                                                                                    NULL,
    offer_rate          DECIMAL(11, 3)                                                                         NULL,
    offer_rate_type     VARCHAR(25)                                                             DEFAULT ''     NOT NULL,
    offer_rate_per_mile DECIMAL(10, 2)                                                                         NULL,
    offer_date          datetime                                                                               NULL,
    offer_id            INT                                                                                    NULL,
    offer_status        ENUM ('Sent', 'Accepted', 'Dismissed', 'Booked', 'Rejected', 'Deleted') DEFAULT 'Sent' NOT NULL,
    offer_recipient_id  INT                                                                                    NULL,
    allow_auto_booking  TINYINT                                                                 DEFAULT 0      NOT NULL,
    CONSTRAINT PK_LOAD_OFFERS PRIMARY KEY (load_offer_id)
);


CREATE TABLE load_search
(
    load_search_id       INT AUTO_INCREMENT         NOT NULL,
    user_id              INT                        NOT NULL,
    site_id              INT           DEFAULT 1    NOT NULL,
    search_type          INT                        NULL,
    date                 date                       NULL,
    origin_country       CHAR(2)       DEFAULT 'US' NOT NULL,
    origin_state         VARCHAR(200)  DEFAULT ''   NOT NULL,
    origin_city          VARCHAR(60)   DEFAULT ''   NOT NULL,
    origin_zip           VARCHAR(45)   DEFAULT ''   NOT NULL,
    origin_lat           DOUBLE                     NULL,
    origin_long          DOUBLE                     NULL,
    distance             INT                        NULL,
    destination_country  CHAR(2)       DEFAULT 'US' NOT NULL,
    destination_state    VARCHAR(200)  DEFAULT ''   NOT NULL,
    destination_city     VARCHAR(60)   DEFAULT ''   NOT NULL,
    destination_zip      VARCHAR(45)   DEFAULT ''   NOT NULL,
    destination_lat      DOUBLE                     NULL,
    destination_long     DOUBLE                     NULL,
    destination_distance INT                        NULL,
    equipment            VARCHAR(500)  DEFAULT ''   NOT NULL,
    days                 INT                        NULL,
    favorite_loads       TINYINT       DEFAULT 0    NOT NULL,
    offered_loads        TINYINT       DEFAULT 0    NOT NULL,
    favorite_lanes       TINYINT       DEFAULT 0    NOT NULL,
    favorite_companies   TINYINT       DEFAULT 0    NOT NULL,
    fl_truck_size        VARCHAR(10)   DEFAULT ''   NOT NULL,
    fl_num_of_stops      INT                        NULL,
    user_company_ids     VARCHAR(5000) DEFAULT ''   NOT NULL,
    CONSTRAINT PK_LOAD_SEARCH PRIMARY KEY (load_search_id)
);


CREATE TABLE load_views
(
    load_view_id          INT AUTO_INCREMENT      NOT NULL,
    user_id               INT                     NOT NULL,
    load_id               INT                     NOT NULL,
    date_added            timestamp               NULL,
    origin_city           VARCHAR(60)  DEFAULT '' NOT NULL,
    origin_state          VARCHAR(5)   DEFAULT '' NOT NULL,
    destination_city      VARCHAR(60)  DEFAULT '' NOT NULL,
    destination_state     VARCHAR(5)   DEFAULT '' NOT NULL,
    equipment             VARCHAR(250) DEFAULT '' NOT NULL,
    owner_user_company_id INT                     NULL,
    owner_user_id         INT                     NULL,
    site_id               INT          DEFAULT 1  NOT NULL,
    CONSTRAINT PK_LOAD_VIEWS PRIMARY KEY (load_view_id)
);


CREATE TABLE loads
(
    load_id                           INT AUTO_INCREMENT                                              NOT NULL,
    external_load_id                  VARCHAR(50)                             DEFAULT ''              NOT NULL,
    user_id                           INT                                     DEFAULT 0               NOT NULL,
    user_company_id                   INT                                                             NOT NULL,
    contact_name                      VARCHAR(100)                            DEFAULT ''              NOT NULL,
    contact_number                    VARCHAR(50)                             DEFAULT ''              NOT NULL,
    contact_number_type               VARCHAR(10)                             DEFAULT ''              NOT NULL,
    nickname                          VARCHAR(50)                             DEFAULT ''              NOT NULL,
    ship_from                         date                                                            NULL,
    ship_to                           date                                                            NULL,
    load_security                     INT                                     DEFAULT 1               NOT NULL,
    number_of_loads                   INT                                     DEFAULT 1               NOT NULL,
    estimated_weight                  VARCHAR(20)                             DEFAULT ''              NOT NULL,
    default_bill_weight_use           ENUM ('loaded_weight', 'unload_weight') DEFAULT 'unload_weight' NOT NULL,
    origin_country                    VARCHAR(25)                             DEFAULT ''              NOT NULL,
    origin_state                      VARCHAR(15)                             DEFAULT '0'             NOT NULL,
    origin_city                       VARCHAR(60)                             DEFAULT ''              NOT NULL,
    destination_country               VARCHAR(25)                             DEFAULT ''              NOT NULL,
    destination_state                 VARCHAR(25)                             DEFAULT '0'             NOT NULL,
    destination_city                  VARCHAR(60)                             DEFAULT ''              NOT NULL,
    estimated_miles                   DECIMAL(10, 2)                                                  NULL,
    rate                              VARCHAR(50)                             DEFAULT ''              NOT NULL,
    load_comments                     VARCHAR(2000)                           DEFAULT ''              NOT NULL,
    post_date                         timestamp                                                       NOT NULL,
    update_date                       date                                                            NULL,
    text                              CHAR(3)                                                         NULL,
    origin_zipcode                    VARCHAR(7)                              DEFAULT ''              NOT NULL,
    destination_zipcode               VARCHAR(7)                              DEFAULT ''              NOT NULL,
    origin_lat                        DOUBLE                                                          NULL,
    origin_long                       DOUBLE                                                          NULL,
    dest_lat                          DOUBLE                                                          NULL,
    dest_long                         DOUBLE                                                          NULL,
    active                            INT                                     DEFAULT 1               NOT NULL,
    is_managed                        TINYINT                                 DEFAULT 0               NOT NULL,
    radius                            VARCHAR(4)                              DEFAULT ''              NOT NULL,
    send_to_amount                    VARCHAR(2)                              DEFAULT ''              NOT NULL,
    text_type                         VARCHAR(15)                             DEFAULT ''              NOT NULL,
    outside_tracking_id               VARCHAR(100)                            DEFAULT ''              NOT NULL,
    miles_checked                     INT                                                             NULL,
    pcmiler                           INT                                                             NULL,
    scraped                           INT                                     DEFAULT 0               NULL COMMENT 'if load was scraped from user''s website =1',
    lo_origin_notes                   VARCHAR(1000)                           DEFAULT ''              NOT NULL,
    lo_destination_notes              VARCHAR(1000)                           DEFAULT ''              NOT NULL,
    commodity_id                      INT                                                             NULL,
    lo_commodity                      VARCHAR(100)                            DEFAULT ''              NOT NULL,
    lo_rate                           DECIMAL(11, 3)                                                  NULL,
    lo_rate_type                      VARCHAR(25)                             DEFAULT ''              NOT NULL,
    lo_comments                       VARCHAR(500)                            DEFAULT ''              NOT NULL,
    site_id                           INT                                     DEFAULT 1               NOT NULL,
    lo_contract_number                VARCHAR(75)                             DEFAULT ''              NOT NULL,
    suffix_load_assignment_numbers    TINYINT                                 DEFAULT 1               NOT NULL,
    starting_load_assignment_number   INT                                     DEFAULT 1               NOT NULL,
    edit_date                         timestamp                                                       NULL,
    edit_by_user_id                   INT                                                             NULL,
    inactive_date                     timestamp                                                       NULL,
    inactive_by_user_id               INT                                                             NULL,
    delete_date                       timestamp                                                       NULL,
    delete_by_user_id                 INT                                                             NULL,
    deleted                           INT                                     DEFAULT 0               NOT NULL,
    lo_public_post_date               timestamp                                                       NULL,
    lo_est_rate_per_mile              DECIMAL(10, 2)                                                  NULL,
    lo_est_pay                        DECIMAL(10, 2)                                                  NULL,
    lo_estimated_weight               DOUBLE                                                          NULL,
    lo_estimated_volume               DOUBLE                                                          NULL,
    lo_est_quantity                   DECIMAL(10, 2)                                                  NULL,
    lo_est_miles                      DECIMAL(10, 2)                                                  NULL,
    lo_est_hours                      DECIMAL(10, 2)                                                  NULL,
    routing_result                    MEDIUMTEXT                                                      NULL,
    load_bearing                      DOUBLE                                                          NULL,
    load_bearing_direction            CHAR(3)                                                         NULL,
    routeid                           INT                                                             NULL,
    fl_num_of_stops                   INT                                                             NULL,
    fl_truck_size                     VARCHAR(10)                             DEFAULT 'Full'          NOT NULL,
    source                            VARCHAR(50)                             DEFAULT ''              NOT NULL,
    source_id                         VARCHAR(45)                             DEFAULT ''              NOT NULL,
    lead_count                        INT                                     DEFAULT 0               NOT NULL,
    manual_rate_per_mile              DECIMAL(10, 2)                                                  NULL COMMENT 'Manually Calculated Rate Per Mile',
    manual_rate_per_mile_date         date                                                            NULL COMMENT 'The date the rate per mile was entered',
    load_access                       ENUM ('private', 'carrier', 'public')   DEFAULT 'public'        NOT NULL,
    pickup_drop_miles                 DECIMAL(10, 2)                                                  NULL,
    originals_required                TINYINT                                 DEFAULT 0               NOT NULL,
    washout_required                  TINYINT                                 DEFAULT 0               NOT NULL,
    pickup_appt                       TINYINT                                 DEFAULT 0               NOT NULL,
    drop_appt                         TINYINT                                 DEFAULT 0               NOT NULL,
    pickup_po                         VARCHAR(100)                            DEFAULT ''              NOT NULL,
    drop_po                           VARCHAR(100)                            DEFAULT ''              NOT NULL,
    number_of_assigned_loads          INT                                     DEFAULT 0               NOT NULL,
    number_of_assigned_drivers        INT                                     DEFAULT 0               NOT NULL,
    number_of_available_loads         INT                                     DEFAULT 0               NOT NULL,
    number_of_delivered_loads         INT                                     DEFAULT 0               NOT NULL,
    contract_id                       INT                                                             NULL,
    buy_contract_id                   INT                                                             NULL,
    sell_contract_id                  INT                                                             NULL,
    create_date                       datetime                                                        NULL,
    required_file_type_ids            VARCHAR(45)                             DEFAULT ''              NOT NULL,
    required_file_types               VARCHAR(300)                            DEFAULT ''              NOT NULL,
    required_files_note               VARCHAR(1024)                           DEFAULT ''              NOT NULL,
    modified_date                     datetime                                                        NULL,
    added_by_user_id                  INT                                     DEFAULT 0               NOT NULL,
    rate_product_category_id          INT                                                             NULL,
    rate_product_category             VARCHAR(50)                             DEFAULT ''              NOT NULL,
    external_rate_product_category_id VARCHAR(10)                             DEFAULT ''              NOT NULL,
    is_hazmat                         TINYINT                                 DEFAULT 0               NOT NULL,
    avg_rate_per_mile                 DECIMAL(10, 2)                                                  NULL,
    equipment_ids                     VARCHAR(100)                            DEFAULT ''              NOT NULL,
    equipment_names                   VARCHAR(300)                            DEFAULT ''              NOT NULL,
    load_details_url                  VARCHAR(500)                            DEFAULT ''              NOT NULL,
    is_broker                         TINYINT                                 DEFAULT 0               NOT NULL,
    hiring_ab_company_id              INT                                                             NULL,
    hiring_ab_user_id                 INT                                                             NULL,
    bill_to_ab_company_id             INT                                                             NULL,
    bill_to_ab_user_id                INT                                                             NULL,
    pickup_ab_company_id              INT                                                             NULL,
    drop_ab_company_id                INT                                                             NULL,
    old_dispatcher_user_id            INT                                                             NULL,
    sales_rep_user_id                 INT                                                             NULL,
    shared_with_hiring_company        TINYINT                                 DEFAULT 0               NOT NULL,
    is_root_load                      TINYINT                                 DEFAULT 1               NOT NULL,
    CONSTRAINT PK_LOADS PRIMARY KEY (load_id)
);


CREATE TABLE logins
(
    login_id        INT AUTO_INCREMENT      NOT NULL,
    user_id         INT                     NOT NULL,
    date            timestamp               NOT NULL,
    ip_address      VARCHAR(100) DEFAULT '' NOT NULL,
    latitude        DECIMAL(15, 10)         NULL,
    longitude       DECIMAL(15, 10)         NULL,
    city            VARCHAR(60)  DEFAULT '' NOT NULL,
    mobile          INT          DEFAULT 0  NOT NULL,
    state           VARCHAR(25)  DEFAULT '' NOT NULL,
    country         VARCHAR(45)  DEFAULT '' NOT NULL,
    website         VARCHAR(75)             NOT NULL,
    site_id         INT          DEFAULT 1  NOT NULL,
    device          VARCHAR(50)  DEFAULT '' NOT NULL,
    device_model    VARCHAR(50)  DEFAULT '' NOT NULL,
    os_version      VARCHAR(25)  DEFAULT '' NOT NULL,
    app_version     VARCHAR(25)  DEFAULT '' NOT NULL,
    dst             TINYINT                 NULL COMMENT 'daylight saving time',
    timezone_offset INT                     NULL,
    timezone_id     VARCHAR(50)  DEFAULT '' NOT NULL,
    CONSTRAINT PK_LOGINS PRIMARY KEY (login_id)
);


CREATE TABLE managed_loads
(
    load_id INT DEFAULT 0 NOT NULL
);


CREATE TABLE mcleod_data
(
    id                 INT AUTO_INCREMENT     NOT NULL,
    source_company_id  VARCHAR(45) DEFAULT '' NOT NULL,
    source_user_id     VARCHAR(32) DEFAULT '' NOT NULL,
    message_type       CHAR(2)                NULL,
    message_version    CHAR(2)                NULL,
    posting_id         VARCHAR(8)  DEFAULT '' NOT NULL,
    dispatcher_id      VARCHAR(10) DEFAULT '' NOT NULL,
    equipment_type     VARCHAR(2)  DEFAULT '' NOT NULL,
    from_city          VARCHAR(14) DEFAULT '' NOT NULL,
    from_state         CHAR(2)                NULL,
    from_postal_code   VARCHAR(9)  DEFAULT '' NOT NULL,
    to_city            VARCHAR(14) DEFAULT '' NOT NULL,
    to_state           CHAR(2)                NULL,
    to_postal_code     VARCHAR(9)  DEFAULT '' NOT NULL,
    callback_phone     VARCHAR(10) DEFAULT '' NOT NULL,
    available_date     date                   NULL,
    enhancements       VARCHAR(4)  DEFAULT '' NOT NULL,
    number_of_loads    INT                    NULL,
    deck_length        INT                    NULL,
    load_weight        INT                    NULL,
    cubic_feet         INT                    NULL,
    intermediate_stops INT                    NULL,
    rate               VARCHAR(5)  DEFAULT '' NOT NULL,
    rate_per           CHAR(1)                NULL,
    from_latitude      DOUBLE                 NULL,
    from_longitude     DOUBLE                 NULL,
    to_latitude        DOUBLE                 NULL,
    to_longitude       DOUBLE                 NULL,
    locations_list     VARCHAR(70) DEFAULT '' NOT NULL,
    comment_1          VARCHAR(70) DEFAULT '' NOT NULL,
    comment_2          VARCHAR(70) DEFAULT '' NOT NULL,
    processed          TINYINT     DEFAULT 0  NULL,
    processed_date     datetime               NULL,
    success            TINYINT     DEFAULT 0  NULL,
    message            LONGTEXT               NULL,
    log_id             INT                    NULL,
    CONSTRAINT PK_MCLEOD_DATA PRIMARY KEY (id)
);


CREATE TABLE message_email_metrics
(
    email_metrics_id INT NOT NULL,
    message_id       INT NOT NULL
);


CREATE TABLE message_files
(
    message_file_id INT AUTO_INCREMENT      NOT NULL,
    message_id      INT                     NOT NULL,
    file_id         INT                     NULL,
    file_url        VARCHAR(500) DEFAULT '' NOT NULL,
    thumb_url       VARCHAR(500) DEFAULT '' NOT NULL,
    mime_type       VARCHAR(255) DEFAULT '' NOT NULL,
    is_image        BIT          DEFAULT 0  NULL,
    is_audio        BIT          DEFAULT 0  NULL,
    CONSTRAINT PK_MESSAGE_FILES PRIMARY KEY (message_file_id)
);


CREATE TABLE message_group_users
(
    group_id INT NOT NULL,
    user_id  INT NOT NULL
);


CREATE TABLE message_groups
(
    group_id    INT AUTO_INCREMENT                     NOT NULL,
    group_name  VARCHAR(100) DEFAULT ''                NOT NULL,
    create_date timestamp    DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_MESSAGE_GROUPS PRIMARY KEY (group_id)
);


CREATE TABLE messages
(
    message_id      INT AUTO_INCREMENT                      NOT NULL,
    from_user_id    INT                                     NULL,
    from_email      VARCHAR(200)  DEFAULT ''                NOT NULL,
    from_phone      VARCHAR(50)   DEFAULT ''                NOT NULL,
    to_user_id      INT                                     NULL,
    to_email        VARCHAR(200)  DEFAULT ''                NOT NULL,
    to_phone        VARCHAR(50)   DEFAULT ''                NOT NULL,
    to_group_id     INT                                     NULL,
    message         VARCHAR(4000) DEFAULT ''                NOT NULL,
    create_date     datetime      DEFAULT CURRENT_TIMESTAMP NOT NULL,
    source          VARCHAR(10)   DEFAULT 'online'          NOT NULL,
    has_file        BIT           DEFAULT 0                 NULL,
    is_read         BIT           DEFAULT 0                 NOT NULL,
    notified_date   datetime                                NULL,
    notification_id INT                                     NULL,
    emailed_date    datetime                                NULL COMMENT 'this field includes a timestamp if an unread message has been emailed to the message receiver',
    extra_data      VARCHAR(1000) DEFAULT ''                NOT NULL,
    CONSTRAINT PK_MESSAGES PRIMARY KEY (message_id)
);


CREATE TABLE messages_archived
(
    archived_by_user_id INT                     NOT NULL,
    archived_user_id    INT                     NULL,
    archived_email      VARCHAR(200) DEFAULT '' NOT NULL,
    archived_phone      VARCHAR(50)  DEFAULT '' NOT NULL
);


CREATE TABLE messages_users_online
(
    user_id      INT                                 NOT NULL,
    connection   VARCHAR(100)                        NOT NULL,
    json_data    LONGTEXT                            NULL,
    online_since timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);


CREATE TABLE mileages
(
    mileage_id            INT AUTO_INCREMENT                                               NOT NULL,
    origin_state          VARCHAR(3)                                                       NOT NULL,
    origin_city           VARCHAR(50)                                                      NOT NULL,
    destination_state     VARCHAR(3)                                                       NOT NULL,
    destination_city      VARCHAR(50)                                                      NOT NULL,
    destination_latitude  DECIMAL(20, 10)                                                  NOT NULL,
    destination_longitude DECIMAL(20, 10)                                                  NOT NULL,
    lmiles                DECIMAL(4)                                                       NOT NULL,
    tmiles                DECIMAL(4)                                                       NULL,
    lcostmile             VARCHAR(5) DEFAULT ''                                            NOT NULL,
    tcostmile             VARCHAR(5) DEFAULT ''                                            NOT NULL,
    lhours                VARCHAR(6) DEFAULT ''                                            NOT NULL,
    thours                VARCHAR(6) DEFAULT ''                                            NOT NULL,
    lestghg               VARCHAR(6) DEFAULT ''                                            NOT NULL,
    testghg               VARCHAR(6) DEFAULT ''                                            NOT NULL,
    dateCreated           timestamp  DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_MILEAGES PRIMARY KEY (mileage_id)
);


CREATE TABLE notification_devices
(
    notification_device_id INT AUTO_INCREMENT       NOT NULL,
    notification_id        INT                      NOT NULL,
    device_id              VARCHAR(100)             NOT NULL,
    notification_token     VARCHAR(1024) DEFAULT '' NOT NULL,
    device_data            VARCHAR(1024) DEFAULT '' NOT NULL,
    date_added             datetime                 NOT NULL,
    is_sent                TINYINT       DEFAULT 0  NOT NULL,
    date_sent              datetime                 NULL,
    fcm_message_id         VARCHAR(100)  DEFAULT '' NOT NULL,
    errormsg               MEDIUMTEXT               NULL,
    notification_json      JSON                     NULL,
    CONSTRAINT PK_NOTIFICATION_DEVICES PRIMARY KEY (notification_device_id, date_added)
);


CREATE TABLE notification_types
(
    notification_type_id INT                     NOT NULL,
    notification_type    VARCHAR(45)             NOT NULL,
    `description`        VARCHAR(255) DEFAULT '' NOT NULL,
    user_type_ids        VARCHAR(45)  DEFAULT '' NOT NULL,
    can_unsub            TINYINT      DEFAULT 1  NOT NULL,
    metadata_schema      MEDIUMTEXT              NULL,
    CONSTRAINT PK_NOTIFICATION_TYPES PRIMARY KEY (notification_type_id)
);


CREATE TABLE notifications
(
    notification_id     INT AUTO_INCREMENT       NOT NULL,
    user_id             INT                      NOT NULL,
    site_id             INT           DEFAULT 1  NOT NULL,
    title               VARCHAR(1024) DEFAULT '' NOT NULL,
    body                VARCHAR(2048) DEFAULT '' NOT NULL,
    data                VARCHAR(2048) DEFAULT '' NOT NULL,
    data_metadata       VARCHAR(256)  DEFAULT '' NOT NULL,
    email_category_id   INT                      NOT NULL,
    email_category      VARCHAR(45)              NOT NULL,
    device_count        INT           DEFAULT 0  NOT NULL,
    device_ids          VARCHAR(1024) DEFAULT '' NOT NULL,
    notification_tokens VARCHAR(2500) DEFAULT '' NOT NULL,
    date_added          datetime                 NOT NULL,
    is_read             TINYINT       DEFAULT 0  NOT NULL,
    date_read           datetime                 NULL,
    is_emailed          TINYINT       DEFAULT 0  NOT NULL,
    date_emailed        datetime                 NULL,
    email_queue_id      INT                      NULL,
    notification_json   JSON                     NULL,
    CONSTRAINT PK_NOTIFICATIONS PRIMARY KEY (notification_id, date_added)
);


CREATE TABLE offer_recipients
(
    offer_recipient_id               INT AUTO_INCREMENT                                                                     NOT NULL,
    offer_id                         INT                                                                                    NOT NULL,
    ab_user_id                       INT                                                                                    NULL,
    bl_user_id                       INT                                                                                    NULL,
    first_name                       VARCHAR(30)                                                                            NOT NULL,
    last_name                        VARCHAR(45)                                                                            NOT NULL,
    email                            VARCHAR(100)                                                                           NOT NULL,
    phone_1                          VARCHAR(25)                                                                            NOT NULL,
    company_name                     VARCHAR(150)                                                                           NOT NULL,
    ab_user_group_id                 INT                                                                                    NULL,
    message_sent_date                datetime                                                                               NULL,
    message_response                 VARCHAR(100)                                                            DEFAULT ''     NOT NULL,
    allow_auto_booking               TINYINT                                                                 DEFAULT 0      NOT NULL,
    offer_status                     ENUM ('Sent', 'Accepted', 'Dismissed', 'Booked', 'Rejected', 'Deleted') DEFAULT 'Sent' NULL,
    number_of_loads_accepted         INT                                                                                    NULL,
    already_accepted_loads           INT                                                                                    NULL,
    accepted_date                    datetime                                                                               NULL,
    dismissed_date                   datetime                                                                               NULL,
    accepted_dismissed_by_user_id    INT                                                                                    NULL,
    accepted_dismissed_by_ab_user_id INT                                                                                    NULL,
    booked_date                      datetime                                                                               NULL,
    rejected_date                    datetime                                                                               NULL,
    booked_rejected_by_user_id       INT                                                                                    NULL,
    deleted_date                     datetime                                                                               NULL,
    deleted_by_user_id               INT                                                                                    NULL,
    CONSTRAINT PK_OFFER_RECIPIENTS PRIMARY KEY (offer_recipient_id)
);


CREATE TABLE offers
(
    offer_id            INT AUTO_INCREMENT      NOT NULL,
    user_id             INT                     NOT NULL,
    load_id             INT                     NULL,
    message             MEDIUMTEXT              NULL,
    offer_rate          DECIMAL(11, 3)          NULL,
    offer_rate_type     VARCHAR(25)  DEFAULT '' NOT NULL,
    offer_rate_per_mile DECIMAL(10, 2)          NULL,
    offer_date          datetime                NOT NULL,
    allow_auto_booking  TINYINT      DEFAULT 0  NOT NULL,
    replyto_email       VARCHAR(75)  DEFAULT '' NOT NULL,
    subject             VARCHAR(500) DEFAULT '' NOT NULL,
    CONSTRAINT PK_OFFERS PRIMARY KEY (offer_id)
);


CREATE TABLE order_notes
(
    order_note_id INT AUTO_INCREMENT                  NOT NULL,
    order_id      INT                                 NOT NULL,
    dateCreated   timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
    user_id       INT                                 NOT NULL,
    note          VARCHAR(1000)                       NOT NULL,
    CONSTRAINT PK_ORDER_NOTES PRIMARY KEY (order_note_id)
);


CREATE TABLE orders
(
    order_id                  INT AUTO_INCREMENT      NOT NULL,
    user_id                   INT                     NULL,
    user_company_id           INT                     NULL,
    order_type                VARCHAR(50)             NOT NULL,
    order_date                date                    NOT NULL,
    membership_end_date       date                    NOT NULL,
    authorize_auth_code       VARCHAR(40)  DEFAULT '' NOT NULL,
    authorize_transaction_id  VARCHAR(40)  DEFAULT '' NOT NULL,
    canceled_date             date                    NULL,
    payment_type              VARCHAR(15)  DEFAULT '' NOT NULL,
    last_four_account_numbers VARCHAR(4)   DEFAULT '' NOT NULL,
    bank_name                 VARCHAR(50)  DEFAULT '' NOT NULL,
    arb_subscription_id       VARCHAR(50)  DEFAULT '' NOT NULL,
    exp_month                 INT                     NULL,
    exp_year                  INT                     NULL,
    bill_to                   VARCHAR(75)  DEFAULT '' NOT NULL,
    bill_attn_to              VARCHAR(50)  DEFAULT '' NOT NULL,
    bill_phone                VARCHAR(25)  DEFAULT '' NOT NULL,
    email                     VARCHAR(100) DEFAULT '' NOT NULL,
    bill_address              VARCHAR(300) DEFAULT '' NOT NULL,
    bill_delivery             VARCHAR(25)  DEFAULT '' NOT NULL,
    bill_amount               VARCHAR(15)  DEFAULT '' NOT NULL,
    bill_description          VARCHAR(75)  DEFAULT '' NOT NULL,
    state                     VARCHAR(2)   DEFAULT '' NOT NULL,
    tax_amount                DECIMAL(6, 2)           NULL,
    total_order_amount        DECIMAL(6, 2)           NULL,
    paid_date                 date                    NULL,
    emailed_date              date                    NULL,
    mailed_date               date                    NULL,
    resent_date               date                    NULL,
    check_number              VARCHAR(40)  DEFAULT '' NOT NULL,
    total_paid                DECIMAL(10, 2)          NULL,
    deactivationDate          date                    NULL,
    deleteDate                date                    NULL,
    failedDate                date                    NULL,
    site_id                   INT          DEFAULT 1  NOT NULL,
    CONSTRAINT PK_ORDERS PRIMARY KEY (order_id)
);


CREATE TABLE page_training
(
    id         INT AUTO_INCREMENT NOT NULL,
    content    VARCHAR(2000)      NOT NULL,
    page_title VARCHAR(50)        NOT NULL,
    categories VARCHAR(50)        NOT NULL,
    site_id    INT DEFAULT 1      NOT NULL,
    CONSTRAINT PK_PAGE_TRAINING PRIMARY KEY (id)
);


CREATE TABLE pc_route_stops
(
    stopID    INT AUTO_INCREMENT      NOT NULL,
    routeID   INT                     NOT NULL,
    address   VARCHAR(100) DEFAULT '' NOT NULL,
    cityID    INT                     NOT NULL,
    stopOrder INT                     NOT NULL,
    CONSTRAINT PK_PC_ROUTE_STOPS PRIMARY KEY (stopID),
    UNIQUE (stopID)
);


CREATE TABLE pc_routes
(
    routeID           INT AUTO_INCREMENT       NOT NULL,
    startAddress      VARCHAR(100) DEFAULT ''  NOT NULL,
    startCityID       INT                      NOT NULL,
    endAddress        VARCHAR(100) DEFAULT ''  NOT NULL,
    endCityID         INT                      NOT NULL,
    bearing           DOUBLE                   NULL,
    bearing_direction CHAR(3)                  NULL,
    errorMessage      MEDIUMTEXT               NOT NULL,
    mileageXml        LONGTEXT                 NULL,
    driverXml         LONGTEXT                 NULL,
    routingType       CHAR(1)      DEFAULT 'S' NULL,
    trailer53         BIT          DEFAULT 0   NOT NULL,
    miles             DECIMAL(10, 2)           NULL,
    dateCreated       datetime                 NULL,
    startLat          DOUBLE                   NULL,
    startLng          DOUBLE                   NULL,
    endLat            DOUBLE                   NULL,
    endLng            DOUBLE                   NULL,
    stops             INT                      NULL,
    CONSTRAINT PK_PC_ROUTES PRIMARY KEY (routeID)
);


CREATE TABLE pc_searches
(
    pc_search_id   INT AUTO_INCREMENT          NOT NULL,
    routeid        INT                         NOT NULL,
    contact_id     INT                         NOT NULL,
    site           VARCHAR(50) DEFAULT 'Other' NOT NULL,
    showMap        TINYINT     DEFAULT 1       NOT NULL,
    showDirections TINYINT     DEFAULT 0       NOT NULL,
    dateCreated    datetime                    NULL,
    CONSTRAINT PK_PC_SEARCHES PRIMARY KEY (pc_search_id)
);


CREATE TABLE phone_numbers
(
    phone_number  DECIMAL(20)             NOT NULL,
    std_format    VARCHAR(20)  DEFAULT '' NOT NULL,
    carrier_type  VARCHAR(45)  DEFAULT '' NOT NULL,
    carrier_name  VARCHAR(60)  DEFAULT '' NOT NULL,
    error_code    VARCHAR(20)  DEFAULT '' NOT NULL,
    error_message VARCHAR(200) DEFAULT '' NOT NULL,
    date_added    datetime                NOT NULL,
    CONSTRAINT PK_PHONE_NUMBERS PRIMARY KEY (phone_number)
);


CREATE TABLE podcasts
(
    podcast_id     INT AUTO_INCREMENT                     NOT NULL,
    seo_podcast_id VARCHAR(255) DEFAULT ''                NOT NULL,
    date           datetime     DEFAULT CURRENT_TIMESTAMP NOT NULL,
    `description`  VARCHAR(255)                           NOT NULL,
    link           VARCHAR(255)                           NOT NULL,
    views          INT          DEFAULT 0                 NOT NULL,
    CONSTRAINT PK_PODCASTS PRIMARY KEY (podcast_id)
);


CREATE TABLE priorities
(
    priority_id     INT AUTO_INCREMENT                                              NOT NULL,
    item            VARCHAR(1000)                                                   NOT NULL,
    priority        INT                                                             NOT NULL,
    user_id         INT                                                             NOT NULL,
    dateAdded       timestamp DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    addedBy_user_id INT                                                             NOT NULL,
    CONSTRAINT PK_PRIORITIES PRIMARY KEY (priority_id)
);


CREATE TABLE purchase_views
(
    purchase_view_id INT AUTO_INCREMENT NOT NULL,
    user_id          INT                NOT NULL,
    view_date        date               NOT NULL,
    site_id          INT                NOT NULL,
    CONSTRAINT PK_PURCHASE_VIEWS PRIMARY KEY (purchase_view_id)
);


CREATE TABLE rate_product_categories
(
    rate_product_category_id        INT AUTO_INCREMENT      NOT NULL,
    rate_product_category           VARCHAR(50)             NOT NULL,
    rate_product_category_equipment VARCHAR(100) DEFAULT '' NOT NULL,
    CONSTRAINT PK_RATE_PRODUCT_CATEGORIES PRIMARY KEY (rate_product_category_id)
);


CREATE TABLE rate_product_category_equipments
(
    rate_product_category_id INT        NOT NULL,
    equipment_id             VARCHAR(4) NOT NULL,
    CONSTRAINT PK_RATE_PRODUCT_CATEGORY_EQUIPMENTS PRIMARY KEY (rate_product_category_id, equipment_id)
);


CREATE TABLE rate_products
(
    rate_product_id            INT AUTO_INCREMENT     NOT NULL,
    rate_product_category_id   INT                    NULL,
    rate_product               VARCHAR(50)            NOT NULL,
    rate_product_category_name VARCHAR(50) DEFAULT '' NOT NULL,
    CONSTRAINT PK_RATE_PRODUCTS PRIMARY KEY (rate_product_id)
);


CREATE TABLE rate_search
(
    rate_search_id            INT AUTO_INCREMENT                     NOT NULL,
    user_id                   INT                                    NOT NULL,
    site_id                   INT          DEFAULT 1                 NOT NULL,
    origin_country            CHAR(2)      DEFAULT 'US'              NOT NULL,
    origin_state              VARCHAR(5)   DEFAULT ''                NOT NULL,
    origin_city               VARCHAR(60)  DEFAULT ''                NOT NULL,
    origin_zip                VARCHAR(45)  DEFAULT ''                NOT NULL,
    origin_lat                DOUBLE                                 NULL,
    origin_long               DOUBLE                                 NULL,
    distance                  INT                                    NULL,
    destination_country       CHAR(2)      DEFAULT 'US'              NOT NULL,
    destination_state         VARCHAR(5)   DEFAULT ''                NOT NULL,
    destination_city          VARCHAR(60)  DEFAULT ''                NOT NULL,
    destination_zip           VARCHAR(45)  DEFAULT ''                NOT NULL,
    destination_lat           DOUBLE                                 NULL,
    destination_long          DOUBLE                                 NULL,
    destination_distance      INT                                    NULL,
    equipment_ids             VARCHAR(100) DEFAULT ''                NOT NULL,
    rate_product_category_ids VARCHAR(100) DEFAULT ''                NOT NULL,
    start_date                date                                   NULL,
    end_date                  date                                   NULL,
    added_date                datetime     DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_RATE_SEARCH PRIMARY KEY (rate_search_id)
);


CREATE TABLE rate_types
(
    rate_type                    VARCHAR(10)            NOT NULL,
    rate_type_text               VARCHAR(20)            NOT NULL,
    rate_type_text_medium        VARCHAR(20)            NOT NULL,
    rate_type_text_medium_plural VARCHAR(20)            NOT NULL,
    rate_type_text_abbr          VARCHAR(20)            NOT NULL,
    sort_order                   INT                    NOT NULL,
    is_weight                    TINYINT     DEFAULT 1  NOT NULL,
    rate_type_chs                VARCHAR(20) DEFAULT '' NOT NULL,
    CONSTRAINT PK_RATE_TYPES PRIMARY KEY (rate_type)
);


CREATE TABLE rate_types_back
(
    rate_type             VARCHAR(10)            NOT NULL,
    rate_type_text        VARCHAR(20)            NOT NULL,
    rate_type_text_medium VARCHAR(20)            NOT NULL,
    rate_type_text_abbr   VARCHAR(20)            NOT NULL,
    sort_order            INT                    NOT NULL,
    is_weight             TINYINT     DEFAULT 1  NOT NULL,
    rate_type_chs         VARCHAR(20) DEFAULT '' NOT NULL
);


CREATE TABLE rates
(
    rate_id                      INT AUTO_INCREMENT                      NOT NULL,
    rate_site_id                 INT           DEFAULT 1                 NOT NULL,
    gw_record_id                 INT                                     NULL,
    gw_remarks                   VARCHAR(1000) DEFAULT ''                NOT NULL,
    gw_rate_type_unit            DECIMAL(12, 2)                          NULL,
    gw_rate_type                 VARCHAR(25)   DEFAULT ''                NOT NULL,
    gw_lbs                       DECIMAL(10)                             NULL,
    gw_rate                      DECIMAL(10, 2)                          NULL,
    gw_total_amount              DECIMAL(10, 2)                          NULL,
    gw_product                   VARCHAR(75)   DEFAULT ''                NOT NULL,
    gw_carrier_code              VARCHAR(45)   DEFAULT ''                NOT NULL,
    gw_origin_city               VARCHAR(75)   DEFAULT ''                NOT NULL,
    gw_origin_state              VARCHAR(4)    DEFAULT ''                NOT NULL,
    gw_destination_city          VARCHAR(75)   DEFAULT ''                NOT NULL,
    gw_destination_state         VARCHAR(4)    DEFAULT ''                NOT NULL,
    rate_product_category_id     INT                                     NULL,
    rate_hauled_date             date                                    NULL,
    rate_rate                    DECIMAL(10, 2)                          NULL,
    rate_type                    VARBINARY(25)                           NULL,
    rate_weight                  DECIMAL(10)                             NULL,
    rate_total_amount            DECIMAL(10, 2)                          NULL,
    rate_origin_city             VARCHAR(75)   DEFAULT ''                NOT NULL,
    rate_origin_state            VARCHAR(2)    DEFAULT ''                NOT NULL,
    rate_origin_lat              DOUBLE                                  NULL,
    rate_origin_long             DOUBLE                                  NULL,
    rate_destination_city        VARCHAR(75)   DEFAULT ''                NOT NULL,
    rate_destination_state       VARCHAR(2)    DEFAULT ''                NOT NULL,
    rate_destination_lat         DOUBLE                                  NULL,
    rate_destination_long        DOUBLE                                  NULL,
    rate_mileage                 DECIMAL(5)                              NULL,
    rate_rate_per_mile           DECIMAL(7, 2)                           NULL,
    formatting_completed_date    datetime                                NULL,
    formatting_completed_user_id INT                                     NULL,
    rate_miles_checked           TINYINT       DEFAULT 0                 NULL,
    processed                    BIT           DEFAULT 0                 NULL,
    processed_date               datetime                                NULL,
    added_date                   datetime      DEFAULT CURRENT_TIMESTAMP NULL,
    CONSTRAINT PK_RATES PRIMARY KEY (rate_id)
);


CREATE TABLE rating_comment
(
    rating_comment_id              INT AUTO_INCREMENT                                              NOT NULL,
    rating_id                      INT                                                             NOT NULL,
    rating_comment_date            timestamp DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    rating_comment_user_id         INT                                                             NOT NULL,
    rating_comment_user_company_id INT                                                             NOT NULL,
    rating_comment                 MEDIUMTEXT                                                      NOT NULL,
    CONSTRAINT PK_RATING_COMMENT PRIMARY KEY (rating_comment_id)
);


CREATE TABLE ratings
(
    rating_id             INT AUTO_INCREMENT                                                 NOT NULL,
    user_company_id       INT                                                                NULL,
    census_num            DECIMAL(8)                                                         NULL,
    rater_user_id         INT                                                                NULL,
    rater_user_company_id INT                                                                NOT NULL,
    overall_experience    INT                                                                NOT NULL,
    feedback              VARCHAR(250) DEFAULT ''                                            NOT NULL,
    carrier_shipper       VARCHAR(25)  DEFAULT ''                                            NOT NULL,
    days_to_pay           INT                                                                NULL,
    non_payment           INT                                                                NULL,
    rating_date           timestamp    DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT PK_RATINGS PRIMARY KEY (rating_id)
);


CREATE TABLE regions
(
    id   INT         NULL,
    type VARCHAR(15) NOT NULL,
    CONSTRAINT PK_REGIONS PRIMARY KEY (type)
);


CREATE TABLE reminder_queue
(
    reminder_queue_id INT AUTO_INCREMENT NOT NULL,
    user_id           INT                NOT NULL,
    user_company_id   INT                NOT NULL,
    date_added        date               NOT NULL,
    site_id           INT DEFAULT 1      NOT NULL,
    CONSTRAINT PK_REMINDER_QUEUE PRIMARY KEY (reminder_queue_id)
);


CREATE TABLE request_log
(
    request_log_id  INT AUTO_INCREMENT       NOT NULL,
    user_id         INT                      NULL,
    user_company_id INT                      NULL,
    method          VARCHAR(10)   DEFAULT '' NOT NULL,
    url             VARCHAR(300)             NOT NULL,
    ip_address      VARCHAR(100)  DEFAULT '' NOT NULL,
    user_agent      VARCHAR(200)  DEFAULT '' NOT NULL,
    date            datetime                 NOT NULL,
    api_key_id      INT                      NULL,
    body            VARCHAR(2048) DEFAULT '' NOT NULL,
    response_status VARCHAR(10)   DEFAULT '' NOT NULL,
    response        VARCHAR(300)  DEFAULT '' NOT NULL,
    CONSTRAINT PK_REQUEST_LOG PRIMARY KEY (request_log_id, date)
);


CREATE TABLE routes
(
    routeID           INT AUTO_INCREMENT                         NOT NULL,
    startAddress      VARCHAR(80)               DEFAULT ''       NOT NULL,
    startCity         VARCHAR(60)               DEFAULT ''       NOT NULL,
    startState        VARCHAR(2)                DEFAULT ''       NOT NULL,
    startZip          VARCHAR(60)               DEFAULT ''       NOT NULL,
    startCountry      VARCHAR(2)                DEFAULT ''       NOT NULL,
    endAddress        VARCHAR(80)               DEFAULT ''       NOT NULL,
    endCity           VARCHAR(60)               DEFAULT ''       NOT NULL,
    endState          VARCHAR(2)                DEFAULT ''       NOT NULL,
    endZip            VARCHAR(60)               DEFAULT ''       NOT NULL,
    endCountry        VARCHAR(2)                DEFAULT ''       NOT NULL,
    startLat          DOUBLE                                     NULL,
    startLng          DOUBLE                                     NULL,
    endLat            DOUBLE                                     NULL,
    endLng            DOUBLE                                     NULL,
    response          LONGTEXT                                   NOT NULL,
    bearing           DOUBLE                                     NULL,
    bearing_direction CHAR(3)                                    NULL,
    errorMessage      MEDIUMTEXT                                 NOT NULL,
    miles             DECIMAL(10, 2)                             NULL,
    duration          INT                                        NULL,
    durationText      VARCHAR(60)               DEFAULT ''       NOT NULL,
    dateCreated       datetime                                   NULL,
    stops             INT                       DEFAULT 0        NULL,
    provider          ENUM ('google', 'mapbox') DEFAULT 'google' NULL,
    CONSTRAINT PK_ROUTES PRIMARY KEY (routeID)
);


CREATE TABLE routes_unique_coords
(
    startLat DOUBLE         NOT NULL,
    startLng DOUBLE         NOT NULL,
    endLat   DOUBLE         NOT NULL,
    endLng   DOUBLE         NOT NULL,
    routeid  INT            NOT NULL,
    miles    DECIMAL(10, 2) NOT NULL,
    CONSTRAINT PK_ROUTES_UNIQUE_COORDS PRIMARY KEY (startLat, startLng, endLat, endLng)
);


CREATE TABLE scale_comments
(
    scale_comment_id      INT AUTO_INCREMENT NOT NULL,
    scale_id              INT                NOT NULL,
    scale_comment_user_id INT                NULL,
    scale_comment_date    datetime           NOT NULL,
    scale_comment         VARCHAR(500)       NOT NULL,
    CONSTRAINT PK_SCALE_COMMENTS PRIMARY KEY (scale_comment_id)
);


CREATE TABLE scales
(
    scale_id       INT AUTO_INCREMENT      NOT NULL,
    state          CHAR(2)                 NOT NULL,
    road_type      CHAR(1)                 NULL,
    road_number    INT                     NULL,
    road_direction VARCHAR(20)  DEFAULT '' NOT NULL,
    milemarker     DECIMAL(10)             NULL,
    `description`  VARCHAR(255) DEFAULT '' NOT NULL,
    status         INT                     NULL,
    last_update    datetime                NULL,
    latitude       DECIMAL(10, 6)          NULL,
    longitude      DECIMAL(10, 6)          NULL,
    dateAdded      datetime                NULL,
    approved       INT          DEFAULT 0  NOT NULL,
    scale_id_old   INT                     NOT NULL,
    CONSTRAINT PK_SCALES PRIMARY KEY (scale_id)
);


CREATE TABLE scales_static
(
    scale_id             INT AUTO_INCREMENT       NOT NULL,
    type                 VARCHAR(255)  DEFAULT '' NOT NULL,
    scale_name           VARCHAR(255)  DEFAULT '' NOT NULL,
    country              VARCHAR(255)  DEFAULT '' NOT NULL,
    city                 VARCHAR(255)             NOT NULL,
    state                VARCHAR(255)             NOT NULL,
    highway              VARCHAR(255)  DEFAULT '' NOT NULL,
    direction            VARCHAR(255)  DEFAULT '' NOT NULL,
    location_description VARCHAR(255)  DEFAULT '' NOT NULL,
    mile_marker          VARCHAR(255)  DEFAULT '' NOT NULL,
    phone_1              VARCHAR(255)  DEFAULT '' NOT NULL,
    address              VARCHAR(255)  DEFAULT '' NOT NULL,
    required_to_stop     VARCHAR(255)  DEFAULT '' NOT NULL,
    parking              VARCHAR(255)  DEFAULT '' NOT NULL,
    exit_before          VARCHAR(255)  DEFAULT '' NOT NULL,
    exit_after           VARCHAR(255)  DEFAULT '' NOT NULL,
    prepass              VARCHAR(255)  DEFAULT '' NOT NULL,
    comments             VARCHAR(255)  DEFAULT '' NOT NULL,
    longitude            DOUBLE                   NULL,
    latitude             DOUBLE                   NULL,
    exact_lat            DOUBLE                   NULL,
    exact_long           DOUBLE                   NULL,
    google_embed         VARCHAR(1000) DEFAULT '' NOT NULL,
    google_street_embed  VARCHAR(1000) DEFAULT '' NOT NULL,
    CONSTRAINT PK_SCALES_STATIC PRIMARY KEY (scale_id)
);


CREATE TABLE sched_tasks
(
    scheduledtask_id INT AUTO_INCREMENT              NOT NULL COMMENT 'Scheduled task primary key',
    shot_id          INT                             NOT NULL COMMENT 'Here foreign key. Primary key of table sched_tasks_shots',
    `group`          VARCHAR(150) DEFAULT 'default'  NOT NULL COMMENT 'The group to which the task belongs',
    task             VARCHAR(150)                    NOT NULL COMMENT 'The name of the current task',
    mode             VARCHAR(100) DEFAULT ''         NOT NULL COMMENT 'It''s a fixed string for all tasks in neo-cron.xml and = server',
    status           VARCHAR(40)                     NOT NULL COMMENT 'The task status. It can be
- Expired
- Running
- ........',
    chainedtask      BIT          DEFAULT 0          NOT NULL,
    `path`           VARCHAR(550)                    NOT NULL COMMENT 'The path of the URL (??)',
    url              VARCHAR(550)                    NOT NULL COMMENT 'The url to be executed whenever the task runs. Includes path??',
    port             INT          DEFAULT 80         NOT NULL,
    resolveurl       BIT          DEFAULT 0          NOT NULL,
    username         VARCHAR(250)                    NOT NULL,
    password         VARCHAR(250)                    NOT NULL,
    proxyserver      VARCHAR(600)                    NOT NULL,
    proxyport        INT          DEFAULT 80         NOT NULL,
    proxyuser        VARCHAR(250)                    NOT NULL,
    proxypassword    VARCHAR(100)                    NOT NULL,
    cluster          BIT          DEFAULT 0          NOT NULL,
    clustered        BIT          DEFAULT 0          NOT NULL,
    priority         INT          DEFAULT 5          NOT NULL,
    requestTimeOut   INT                             NULL COMMENT 'The request timeout (in seconds)',
    publish          BIT          DEFAULT 0          NOT NULL,
    file             VARCHAR(550) DEFAULT ''         NOT NULL COMMENT 'The file where the output wil be written.',
    startDate        date                            NOT NULL COMMENT 'The date the task will start runnng. Absolutely obligatory whatever the other settings are.',
    startTime        time                            NULL COMMENT 'The time of the day the task will start runnng. Obligatory in all cases except Crontime is entered.',
    endDate          date                            NULL COMMENT 'The date the task will stop runnng',
    endTime          time                            NULL COMMENT 'The time of the day the task will stop runnng',
    crontime         VARCHAR(30)  DEFAULT ''         NOT NULL,
    `interval`       VARCHAR(20)  DEFAULT ''         NOT NULL,
    `repeat`         VARCHAR(20)  DEFAULT ''         NOT NULL,
    overwrite        BIT                             NOT NULL,
    `exclude`        VARCHAR(550) DEFAULT ''         NOT NULL,
    lastfire         datetime                        NULL COMMENT 'The last date-time the task run',
    remainingCount   VARCHAR(20)  DEFAULT 'Infinite' NOT NULL,
    retryCount       BIT                             NOT NULL COMMENT 'The retry count can not be more than 3 or less than 0.',
    onMisfire        VARCHAR(30)                     NOT NULL COMMENT 'What to do in case something goes wrong. The options are:  Ignore / Fire now / Invoke handler.',
    eventhandler     VARCHAR(550) DEFAULT ''         NOT NULL COMMENT 'A dot-delimited CFC path under webroot, for example a.b.server (without the CFC extension). The CFC should implement CFIDE.scheduler.ITaskEventHandler.\\\\n',
    onException      VARCHAR(30)                     NOT NULL COMMENT 'The options are:  Ignore / Pause /  Re-Fire / Invoke handler.\\n',
    onComplete       VARCHAR(550) DEFAULT ''         NOT NULL,
    CONSTRAINT PK_SCHED_TASKS PRIMARY KEY (scheduledtask_id)
);


CREATE TABLE sched_tasks_shots
(
    shot_id     INT AUTO_INCREMENT NOT NULL COMMENT 'Primary Key',
    theDateTime datetime           NOT NULL,
    status      BIT DEFAULT 0      NOT NULL COMMENT 'Status of the shot. Success = 1',
    CONSTRAINT PK_SCHED_TASKS_SHOTS PRIMARY KEY (shot_id)
);


CREATE TABLE schedules_log
(
    schedules_log_id INT AUTO_INCREMENT NOT NULL,
    task_name        VARCHAR(150)       NOT NULL,
    report_url       VARCHAR(150)       NOT NULL,
    date_started     datetime           NOT NULL,
    date_ended       datetime           NULL,
    execution_ms     INT     DEFAULT 0  NOT NULL,
    message          MEDIUMTEXT         NOT NULL,
    succeeded        TINYINT DEFAULT 0  NULL,
    CONSTRAINT PK_SCHEDULES_LOG PRIMARY KEY (schedules_log_id)
);


CREATE TABLE schedules_log_sync_mcp_status
(
    sync_mcp_id             INT AUTO_INCREMENT     NOT NULL,
    user_company_id         INT                    NULL,
    schedules_log_id        INT                    NOT NULL,
    sync_date               datetime               NOT NULL,
    sync_date_utc           VARCHAR(30)            NOT NULL,
    census_num              DECIMAL(9)             NULL,
    mcp_monitored           BIT                    NOT NULL,
    risk_assessment_overall VARCHAR(20) DEFAULT '' NOT NULL,
    error_message           MEDIUMTEXT             NULL,
    message                 MEDIUMTEXT             NULL,
    CONSTRAINT PK_SCHEDULES_LOG_SYNC_MCP_STATUS PRIMARY KEY (sync_mcp_id)
);


CREATE TABLE settings
(
    setting_id INT AUTO_INCREMENT NOT NULL,
    `group`    VARCHAR(50)        NOT NULL,
    `key`      VARCHAR(50)        NOT NULL,
    value      MEDIUMTEXT         NULL,
    extra      MEDIUMTEXT         NULL,
    CONSTRAINT PK_SETTINGS PRIMARY KEY (setting_id),
    UNIQUE (`key`)
);


CREATE TABLE sfc_user_company_settings
(
    user_company_id     INT                     NOT NULL,
    membership_end_date date                    NULL,
    trial_start_date    date                    NULL,
    trial_end_date      date                    NULL,
    canceled_date       date                    NULL,
    deletion_date       date                    NULL,
    monthly_rate        DECIMAL(6, 2) DEFAULT 0 NULL,
    threemonth_rate     DECIMAL(6, 2) DEFAULT 0 NULL,
    sixmonth_rate       DECIMAL(6, 2) DEFAULT 0 NULL,
    yearly_rate         DECIMAL(6, 2) DEFAULT 0 NULL,
    biyearly_rate       DECIMAL(6, 2) DEFAULT 0 NULL,
    is_custom_rate      TINYINT       DEFAULT 0 NOT NULL,
    CONSTRAINT PK_SFC_USER_COMPANY_SETTINGS PRIMARY KEY (user_company_id)
);


CREATE TABLE sfc_user_settings
(
    user_id               INT                     NOT NULL,
    user_company_id       INT                     NOT NULL,
    deletion_date         date                    NULL,
    currentSessionID      VARCHAR(255) DEFAULT '' NOT NULL,
    last_activity         INT                     NULL,
    last_login_date       timestamp               NULL,
    settings_updated_date datetime                NULL,
    CONSTRAINT PK_SFC_USER_SETTINGS PRIMARY KEY (user_id, user_company_id)
);


CREATE TABLE sff_adviews
(
    sff_adview_id INT AUTO_INCREMENT      NOT NULL,
    user_id       INT                     NULL,
    name          VARCHAR(255) DEFAULT '' NOT NULL,
    phone         VARCHAR(100) DEFAULT '' NOT NULL,
    email         VARCHAR(150) DEFAULT '' NOT NULL,
    view_date     datetime                NULL,
    source        VARCHAR(50)  DEFAULT '' NOT NULL,
    load_id       INT                     NULL,
    CONSTRAINT PK_SFF_ADVIEWS PRIMARY KEY (sff_adview_id)
);


CREATE TABLE sff_customers
(
    sff_customers_id     INT AUTO_INCREMENT      NOT NULL,
    customer_code        VARCHAR(15)  DEFAULT '' NOT NULL,
    company_name         VARCHAR(255) DEFAULT '' NOT NULL,
    first_name           VARCHAR(255) DEFAULT '' NOT NULL,
    last_name            VARCHAR(255) DEFAULT '' NOT NULL,
    email                VARCHAR(255) DEFAULT '' NOT NULL,
    bl_user_company_id   INT                     NULL,
    number_of_bl_matches INT                     NULL,
    CONSTRAINT PK_SFF_CUSTOMERS PRIMARY KEY (sff_customers_id)
);


CREATE TABLE sff_prospects
(
    sff_prospect_id      INT AUTO_INCREMENT                     NOT NULL,
    user_id              INT                                    NOT NULL,
    date_added           datetime     DEFAULT CURRENT_TIMESTAMP NOT NULL,
    date_contacted       datetime                               NULL,
    contacted_by_user_id INT                                    NULL,
    converted            TINYINT      DEFAULT 0                 NOT NULL,
    comment              VARCHAR(500) DEFAULT ''                NOT NULL,
    CONSTRAINT PK_SFF_PROSPECTS PRIMARY KEY (sff_prospect_id)
);


CREATE TABLE sff_shippers
(
    sff_shipper_id         INT AUTO_INCREMENT      NOT NULL,
    company_name           VARCHAR(150) DEFAULT '' NOT NULL,
    city                   VARCHAR(80)  DEFAULT '' NOT NULL,
    state                  VARCHAR(2)   DEFAULT '' NOT NULL,
    external_ab_company_id VARCHAR(50)  DEFAULT '' NOT NULL,
    email                  VARCHAR(150) DEFAULT '' NOT NULL,
    processed              TINYINT      DEFAULT 0  NOT NULL,
    CONSTRAINT PK_SFF_SHIPPERS PRIMARY KEY (sff_shipper_id)
);


CREATE TABLE signup_abandonment
(
    id          INT AUTO_INCREMENT     NOT NULL,
    date        date                   NOT NULL,
    page        VARCHAR(25)            NOT NULL,
    form_fields INT                    NULL,
    ip_address  VARCHAR(40) DEFAULT '' NOT NULL,
    site_id     INT         DEFAULT 1  NOT NULL,
    CONSTRAINT PK_SIGNUP_ABANDONMENT PRIMARY KEY (id)
);


CREATE TABLE site_alerts
(
    site_id        INT           NOT NULL,
    user_id        INT           NOT NULL,
    name           VARCHAR(100)  NOT NULL,
    dismissed      BIT DEFAULT 0 NULL,
    dismissed_date datetime      NULL,
    CONSTRAINT PK_SITE_ALERTS PRIMARY KEY (site_id, user_id)
);


CREATE TABLE site_pages
(
    site_page_id        INT AUTO_INCREMENT      NOT NULL,
    site_id             INT          DEFAULT 1  NOT NULL,
    slug                VARCHAR(255)            NOT NULL COMMENT 'use this to match the cgi.path_info against.',
    page_name           VARCHAR(50)  DEFAULT '' NOT NULL,
    page_title          VARCHAR(255) DEFAULT '' NOT NULL,
    page_keywords       VARCHAR(500) DEFAULT '' NOT NULL,
    page_description    VARCHAR(500) DEFAULT '' NOT NULL,
    page_include_menu   BIT          DEFAULT 1  NOT NULL,
    header_content      VARCHAR(500) DEFAULT '' NOT NULL,
    page_secure         BIT          DEFAULT 0  NOT NULL,
    section_title       VARCHAR(255) DEFAULT '' NOT NULL,
    section_title_right VARCHAR(255) DEFAULT '' NOT NULL,
    page_must_login     BIT          DEFAULT 1  NOT NULL,
    page_include_login  BIT          DEFAULT 1  NOT NULL,
    page_include_chat   BIT          DEFAULT 1  NOT NULL,
    page_check_session  BIT          DEFAULT 1  NOT NULL,
    page_content        MEDIUMTEXT              NULL,
    page_ancestorid     INT          DEFAULT 0  NOT NULL COMMENT 'sub page id. 0 = a root page, otherwise the pageid for the parent page',
    page_updated        datetime                NULL,
    CONSTRAINT PK_SITE_PAGES PRIMARY KEY (site_page_id)
);


CREATE TABLE site_updates
(
    site_update_id          INT AUTO_INCREMENT      NOT NULL,
    site_update_description VARCHAR(200)            NOT NULL,
    site_update_posted      datetime                NOT NULL,
    site_update_post_id     VARCHAR(500) DEFAULT '' NOT NULL,
    site_updates_type_id    INT                     NOT NULL,
    site_id                 INT          DEFAULT 1  NOT NULL,
    CONSTRAINT PK_SITE_UPDATES PRIMARY KEY (site_update_id)
);


CREATE TABLE site_updates_type
(
    site_updates_type_id INT AUTO_INCREMENT NOT NULL,
    site_updates_type    VARCHAR(255)       NOT NULL,
    site_updates_url     VARCHAR(75)        NOT NULL,
    CONSTRAINT PK_SITE_UPDATES_TYPE PRIMARY KEY (site_updates_type_id)
);


CREATE TABLE sites
(
    id                  INT AUTO_INCREMENT           NOT NULL,
    site_base_href      VARCHAR(150)                 NOT NULL,
    site_name           VARCHAR(150)                 NOT NULL,
    site_alias          VARCHAR(3)                   NOT NULL,
    def_monthly_rate    DECIMAL(6, 2) DEFAULT 0      NOT NULL,
    def_threemonth_rate DECIMAL(6, 2) DEFAULT 0      NULL,
    def_sixmonth_rate   DECIMAL(6, 2) DEFAULT 0      NULL,
    def_yearly_rate     DECIMAL(6, 2) DEFAULT 0      NOT NULL,
    def_biyearly_rate   DECIMAL(6, 2) DEFAULT 0      NOT NULL,
    site_protocol       VARCHAR(10)   DEFAULT 'http' NOT NULL,
    CONSTRAINT PK_SITES PRIMARY KEY (id)
);


CREATE TABLE sms_accounts
(
    sms_account_id INT AUTO_INCREMENT     NOT NULL,
    site           VARCHAR(15) DEFAULT '' NOT NULL,
    vaspid         VARCHAR(20) DEFAULT '' NOT NULL,
    vasid          VARCHAR(20) DEFAULT '' NOT NULL,
    sender_num     VARCHAR(20) DEFAULT '' NOT NULL,
    last_used      datetime               NULL,
    times_used     INT         DEFAULT 0  NULL,
    CONSTRAINT PK_SMS_ACCOUNTS PRIMARY KEY (sms_account_id)
);


CREATE TABLE sms_log
(
    id         INT AUTO_INCREMENT      NOT NULL,
    dt         datetime                NULL,
    transid    VARCHAR(255) DEFAULT '' NOT NULL,
    sender     VARCHAR(255) DEFAULT '' NOT NULL,
    recipients VARCHAR(255) DEFAULT '' NOT NULL,
    message    VARCHAR(255) DEFAULT '' NOT NULL,
    CONSTRAINT PK_SMS_LOG PRIMARY KEY (id)
);


CREATE TABLE sms_queue
(
    id             BIGINT AUTO_INCREMENT   NOT NULL,
    site_id        INT          DEFAULT 1  NOT NULL COMMENT 'site id from sites table',
    user_id        INT          DEFAULT 0  NOT NULL,
    created        datetime                NULL,
    sent_from      VARCHAR(25)  DEFAULT '' NOT NULL,
    sent_to        VARCHAR(25)  DEFAULT '' NOT NULL,
    message        VARCHAR(255) DEFAULT '' NOT NULL,
    sent           INT          DEFAULT 0  NOT NULL,
    transaction_id VARCHAR(255) DEFAULT '' NOT NULL,
    CONSTRAINT PK_SMS_QUEUE PRIMARY KEY (id)
);


CREATE TABLE some_table
(
    idsome_tabled INT AUTO_INCREMENT NOT NULL,
    CONSTRAINT PK_SOME_TABLE PRIMARY KEY (idsome_tabled)
);


CREATE TABLE states
(
    state_id     INT AUTO_INCREMENT           NOT NULL,
    state        VARCHAR(45) DEFAULT ''       NOT NULL,
    abbreviation VARCHAR(11) DEFAULT ''       NOT NULL,
    country      VARCHAR(10) DEFAULT 'USA'    NOT NULL,
    region       INT                          NULL,
    longitude    DECIMAL(30, 15)              NULL,
    latitude     DECIMAL(30, 15)              NULL,
    color_code   VARCHAR(6)  DEFAULT 'FFFFFF' NOT NULL,
    CONSTRAINT PK_STATES PRIMARY KEY (state_id)
);


CREATE TABLE stats_by_month
(
    year                INT            NOT NULL,
    month               INT            NOT NULL,
    start_date          date           NULL,
    end_date            date           NULL,
    free_carriers       INT            NULL,
    free_shippers       INT            NULL,
    free_all            INT            NULL,
    paid_carriers       INT            NULL,
    paid_shippers       INT            NULL,
    paid_all            INT            NULL,
    total_carriers      INT            NULL,
    total_shippers      INT            NULL,
    total_all           INT            NULL,
    new_paid_carriers   INT            NULL,
    new_paid_shippers   INT            NULL,
    new_paid_all        INT            NULL,
    cancels_carriers    INT            NULL,
    cancels_shippers    INT            NULL,
    cancels_all         INT            NULL,
    churn_rate_carriers DECIMAL(10, 2) NULL,
    churn_rate_shippers DECIMAL(10, 2) NULL,
    churn_rate_all      DECIMAL(10, 2) NULL,
    mrr_carriers        DECIMAL(12, 2) NULL,
    mrr_shippers        DECIMAL(12, 2) NULL,
    mrr_all             DECIMAL(12, 2) NULL,
    arpu_carriers       DECIMAL(10, 2) NULL,
    arpu_shippers       DECIMAL(10, 2) NULL,
    arpu_all            DECIMAL(10, 2) NULL,
    cac_carriers        DECIMAL(10, 2) NULL,
    cac_shippers        DECIMAL(10, 2) NULL,
    cac_all             DECIMAL(10, 2) NULL,
    ltv_carriers        DECIMAL(10, 2) NULL,
    ltv_shippers        DECIMAL(10, 2) NULL,
    ltv_all             DECIMAL(10, 2) NULL,
    dtc_avg_carriers    DECIMAL(10, 2) NULL,
    dtc_avg_shippers    DECIMAL(10, 2) NULL,
    dtc_avg_all         DECIMAL(10, 2) NULL,
    dtc_median_carriers DECIMAL(10, 2) NULL,
    dtc_median_shippers DECIMAL(10, 2) NULL,
    dtc_median_all      DECIMAL(10, 2) NULL,
    date_updated        datetime       NULL,
    CONSTRAINT PK_STATS_BY_MONTH PRIMARY KEY (year, month)
);


CREATE TABLE stats_days_converted
(
    days      INT NOT NULL,
    converted INT NOT NULL,
    CONSTRAINT PK_STATS_DAYS_CONVERTED PRIMARY KEY (days)
);


CREATE TABLE subscriptions
(
    subscription_id           INT AUTO_INCREMENT       NOT NULL,
    user_id                   INT                      NOT NULL,
    user_company_id           INT                      NOT NULL,
    site_id                   INT           DEFAULT 1  NULL,
    subscription_type         VARCHAR(50)              NOT NULL,
    subscription_start_date   date                     NOT NULL,
    subscription_end_date     date                     NOT NULL,
    canceled_date             datetime                 NULL,
    canceled_message          VARCHAR(1000) DEFAULT '' NOT NULL,
    failedDate                datetime                 NULL,
    failed_message            VARCHAR(200)  DEFAULT '' NOT NULL,
    deactivationDate          datetime                 NULL,
    payment_type              VARCHAR(15)   DEFAULT '' NOT NULL,
    last_four_account_numbers VARCHAR(4)    DEFAULT '' NOT NULL,
    bank_name                 VARCHAR(50)   DEFAULT '' NOT NULL,
    arb_subscription_id       VARCHAR(50)   DEFAULT '' NOT NULL,
    arb_start_date            date                     NULL,
    exp_month                 INT                      NULL,
    exp_year                  INT                      NULL,
    bill_to                   VARCHAR(75)   DEFAULT '' NOT NULL,
    bill_attn_to              VARCHAR(50)   DEFAULT '' NOT NULL,
    bill_phone                VARCHAR(25)   DEFAULT '' NOT NULL,
    email                     VARCHAR(100)  DEFAULT '' NOT NULL,
    bill_address              VARCHAR(300)  DEFAULT '' NOT NULL,
    bill_delivery             VARCHAR(25)   DEFAULT '' NOT NULL,
    bill_amount               DECIMAL(7, 2)            NULL,
    bill_description          VARCHAR(75)   DEFAULT '' NOT NULL,
    state                     VARCHAR(2)    DEFAULT '' NOT NULL,
    tax_amount                DECIMAL(7, 2)            NULL,
    total_order_amount        DECIMAL(7, 2)            NULL,
    date_added                datetime                 NOT NULL,
    old_order_id              INT                      NULL,
    CONSTRAINT PK_SUBSCRIPTIONS PRIMARY KEY (subscription_id)
);


CREATE TABLE temp_ab_users_unmatched
(
    ab_user_id INT DEFAULT 0 NOT NULL
);


CREATE TABLE test_time
(
    id   INT AUTO_INCREMENT NOT NULL,
    time datetime           NOT NULL,
    CONSTRAINT PK_TEST_TIME PRIMARY KEY (id)
);


CREATE TABLE testimonials
(
    testimonial_id      INT AUTO_INCREMENT NOT NULL,
    testimonial_content VARCHAR(500)       NOT NULL,
    testimonial_name    VARCHAR(100)       NOT NULL,
    posted_date         date               NOT NULL,
    upgrade_page        INT DEFAULT 0      NOT NULL,
    site_id             INT DEFAULT 1      NOT NULL,
    CONSTRAINT PK_TESTIMONIALS PRIMARY KEY (testimonial_id)
);


CREATE TABLE timezones
(
    timezone_id                 VARCHAR(50)            NOT NULL COMMENT 'Olsen Timezone',
    timezone_offset             INT                    NOT NULL COMMENT 'In minutes',
    dst                         TINYINT     DEFAULT 0  NOT NULL,
    dst_observes                TINYINT     DEFAULT 0  NOT NULL,
    tz_string                   VARCHAR(50)            NOT NULL COMMENT 'POSIX string EST+5EDT,M3.2.0/2,M11.1.0/2',
    dst_next_change_local_date  datetime               NULL,
    dst_next_change_utc_date    datetime               NULL,
    dst_next_change_server_date datetime               NULL,
    city                        VARCHAR(80) DEFAULT '' NOT NULL,
    state                       VARCHAR(20) DEFAULT '' NOT NULL,
    country                     VARCHAR(20) DEFAULT '' NOT NULL,
    latitude                    DOUBLE                 NULL,
    longitude                   DOUBLE                 NULL,
    modified_date               datetime               NOT NULL,
    CONSTRAINT PK_TIMEZONES PRIMARY KEY (timezone_id)
);


CREATE TABLE tms_views
(
    tms_view_id INT AUTO_INCREMENT NOT NULL,
    user_id     INT                NOT NULL,
    date        date               NOT NULL,
    banner_view INT DEFAULT 0      NULL,
    CONSTRAINT PK_TMS_VIEWS PRIMARY KEY (tms_view_id)
);


CREATE TABLE truck_emails
(
    truck_email_id INT AUTO_INCREMENT NOT NULL,
    user_id        INT                NOT NULL,
    dateAdded      date               NOT NULL,
    equipment      VARCHAR(255)       NOT NULL,
    state          VARCHAR(255)       NOT NULL,
    subject        VARCHAR(255)       NOT NULL,
    message        VARCHAR(5000)      NOT NULL,
    site_id        INT DEFAULT 1      NOT NULL,
    CONSTRAINT PK_TRUCK_EMAILS PRIMARY KEY (truck_email_id)
);


CREATE TABLE truck_search
(
    truck_search_id INT AUTO_INCREMENT      NOT NULL,
    user_id         INT                     NOT NULL,
    origin_state    VARCHAR(5)   DEFAULT '' NOT NULL,
    origin_city     VARCHAR(60)  DEFAULT '' NOT NULL,
    distance        VARCHAR(11)  DEFAULT '' NOT NULL,
    days            VARCHAR(11)  DEFAULT '' NOT NULL,
    equipment       VARCHAR(500) DEFAULT '' NOT NULL,
    date            date                    NULL,
    site_id         INT          DEFAULT 1  NOT NULL,
    CONSTRAINT PK_TRUCK_SEARCH PRIMARY KEY (truck_search_id)
);


CREATE TABLE truck_views
(
    truck_view_id         INT AUTO_INCREMENT      NOT NULL,
    user_id               INT                     NOT NULL,
    truck_id              INT                     NULL,
    date                  date                    NULL,
    origin_city           VARCHAR(60)  DEFAULT '' NOT NULL,
    origin_state          VARCHAR(5)   DEFAULT '' NOT NULL,
    destination           VARCHAR(500) DEFAULT '' NOT NULL,
    equipment             VARCHAR(250) DEFAULT '' NOT NULL,
    owner_user_company_id INT                     NULL,
    owner_user_id         INT                     NULL,
    site_id               INT          DEFAULT 1  NOT NULL,
    CONSTRAINT PK_TRUCK_VIEWS PRIMARY KEY (truck_view_id)
);


CREATE TABLE trucks
(
    truck_id             INT AUTO_INCREMENT           NOT NULL,
    user_id              INT           DEFAULT 0      NOT NULL,
    user_company_id      INT                          NOT NULL,
    contact_name         VARCHAR(50)   DEFAULT ''     NOT NULL,
    contact_number       VARCHAR(50)   DEFAULT ''     NOT NULL,
    contact_number_type  VARCHAR(10)   DEFAULT ''     NOT NULL,
    nickname             VARCHAR(255)  DEFAULT ''     NOT NULL,
    date_available       date                         NOT NULL,
    time_available       VARCHAR(15)   DEFAULT ''     NOT NULL,
    origin_city          VARCHAR(60)   DEFAULT ''     NOT NULL,
    origin_state         VARCHAR(2)    DEFAULT '0'    NOT NULL,
    origin_zipcode       VARCHAR(15)   DEFAULT ''     NOT NULL,
    origin_country       VARCHAR(15)   DEFAULT ''     NOT NULL,
    origin_lat           DOUBLE                       NULL,
    origin_long          DOUBLE                       NULL,
    destination_city     VARCHAR(40)   DEFAULT ''     NOT NULL,
    destination_state    VARCHAR(15)   DEFAULT ''     NOT NULL,
    destination_zipcode  VARCHAR(15)   DEFAULT ''     NOT NULL,
    destination_country  VARCHAR(45)   DEFAULT ''     NOT NULL,
    dest_lat             DOUBLE                       NULL,
    dest_long            DOUBLE                       NULL,
    prefered_destination VARCHAR(500)  DEFAULT ''     NOT NULL,
    trailer_type         VARCHAR(50)   DEFAULT ''     NOT NULL,
    comments             VARCHAR(1000) DEFAULT ''     NOT NULL,
    post_date            datetime                     NOT NULL,
    active               TINYINT                      NOT NULL,
    outside_tracking_id  VARCHAR(100)  DEFAULT ''     NOT NULL,
    repost_days          INT           DEFAULT 0      NULL,
    repost_count         INT           DEFAULT 0      NULL,
    repost_date          date                         NULL,
    site_id              INT           DEFAULT 1      NOT NULL,
    load_matching        TINYINT       DEFAULT 1      NOT NULL,
    truck_size           VARCHAR(10)   DEFAULT 'Full' NOT NULL,
    truck_length         VARCHAR(30)   DEFAULT ''     NOT NULL,
    truck_area           TINYINT       DEFAULT 0      NOT NULL,
    source               VARCHAR(50)   DEFAULT ''     NOT NULL,
    source_id            VARCHAR(45)   DEFAULT ''     NOT NULL,
    CONSTRAINT PK_TRUCKS PRIMARY KEY (truck_id)
);


CREATE TABLE twilio_sms_log
(
    twilio_sms_log_id INT AUTO_INCREMENT      NOT NULL,
    from_user_id      INT                     NULL,
    from_phone_number VARCHAR(20)  DEFAULT '' NOT NULL,
    to_user_id        INT                     NULL,
    to_phone_number   VARCHAR(20)  DEFAULT '' NOT NULL,
    message           TEXT                    NULL,
    sid               VARCHAR(45)  DEFAULT '' NOT NULL,
    status            VARCHAR(20)  DEFAULT '' NOT NULL,
    date_created      datetime                NULL,
    date_updated      datetime                NULL,
    date_sent         datetime                NULL,
    price             DECIMAL(10, 2)          NULL,
    price_unit        VARCHAR(8)   DEFAULT '' NOT NULL,
    error_code        VARCHAR(45)  DEFAULT '' NOT NULL,
    error_message     VARCHAR(256) DEFAULT '' NOT NULL,
    CONSTRAINT PK_TWILIO_SMS_LOG PRIMARY KEY (twilio_sms_log_id)
);


CREATE TABLE user_comment
(
    usercomment_id   INT AUTO_INCREMENT NOT NULL,
    user_id          INT                NOT NULL,
    site_id          INT DEFAULT 1      NOT NULL,
    comment          VARCHAR(2000)      NOT NULL,
    date             datetime           NOT NULL,
    added_by_user_id INT                NULL,
    CONSTRAINT PK_USER_COMMENT PRIMARY KEY (usercomment_id)
);


CREATE TABLE user_company
(
    user_company_id                       INT AUTO_INCREMENT                 NOT NULL,
    company_owner_id                      INT                                NULL,
    census_num                            DECIMAL(9)                         NULL,
    mc_num                                VARCHAR(20)    DEFAULT ''          NOT NULL,
    company_name                          VARCHAR(255)                       NOT NULL,
    company_name_dba                      VARCHAR(255)   DEFAULT ''          NOT NULL,
    deletion_date                         date                               NULL,
    merged_to                             INT                                NULL,
    signup_site                           VARCHAR(45)    DEFAULT 'BulkLoads' NOT NULL,
    order_count                           INT            DEFAULT 0           NULL,
    company_logo_url                      VARCHAR(500)   DEFAULT ''          NOT NULL,
    company_logo_thumb_url                VARCHAR(500)   DEFAULT ''          NOT NULL,
    user_type_ids                         VARCHAR(45)    DEFAULT ''          NOT NULL,
    user_types                            VARCHAR(200)   DEFAULT ''          NOT NULL,
    canadian_authority                    INT            DEFAULT 0           NOT NULL,
    avg_rating                            DOUBLE                             NULL,
    rating_count                          INT            DEFAULT 0           NOT NULL,
    avg_days_to_pay                       DOUBLE                             NULL,
    non_payment_count                     INT            DEFAULT 0           NOT NULL,
    comment_count                         INT            DEFAULT 0           NOT NULL,
    allowed_ips                           VARCHAR(200)   DEFAULT ''          NOT NULL,
    credit_limit                          DECIMAL(10, 2) DEFAULT 10000       NOT NULL,
    balance                               DECIMAL(10, 2) DEFAULT 0           NOT NULL,
    source_company                        VARCHAR(15)    DEFAULT ''          NOT NULL,
    source_company_id                     VARCHAR(45)    DEFAULT ''          NOT NULL,
    verification_enabled                  TINYINT        DEFAULT 0           NOT NULL,
    verified                              TINYINT        DEFAULT 0           NOT NULL,
    verified_date                         datetime                           NULL,
    verified_by_user_id                   INT                                NULL,
    no_census_reason                      VARCHAR(500)   DEFAULT ''          NOT NULL,
    number_of_trucks                      INT                                NULL,
    company_code                          VARCHAR(20)    DEFAULT ''          NOT NULL,
    allow_drivers                         TINYINT        DEFAULT 1           NOT NULL,
    require_documents_for_completing_load TINYINT        DEFAULT 0           NOT NULL,
    approve_loads_for_payment             TINYINT        DEFAULT 0           NOT NULL,
    use_tms_for_invoices                  TINYINT        DEFAULT 1           NOT NULL,
    use_dispatch                          TINYINT        DEFAULT 1           NOT NULL,
    sff_enabled                           TINYINT        DEFAULT 1           NULL,
    integration_mcp_enabled               TINYINT        DEFAULT 0           NOT NULL,
    mcp_checked                           BIT            DEFAULT 0           NOT NULL,
    mcp_monitored                         BIT            DEFAULT 0           NOT NULL,
    mcp_error                             VARCHAR(200)   DEFAULT ''          NOT NULL,
    risk_assessment_overall               VARCHAR(20)    DEFAULT ''          NOT NULL,
    business_start_year                   INT                                NULL,
    intra_interstate                      VARCHAR(10)    DEFAULT ''          NOT NULL,
    hazmat_certified                      BIT            DEFAULT 0           NULL,
    interested_in                         VARCHAR(100)   DEFAULT ''          NOT NULL,
    auto_generate_work_order_numbers      TINYINT        DEFAULT 0           NULL,
    auto_confirm_assignments              TINYINT        DEFAULT 0           NULL,
    allow_driver_load_submissions         TINYINT        DEFAULT 0           NULL,
    CONSTRAINT PK_USER_COMPANY PRIMARY KEY (user_company_id)
);


CREATE TABLE user_company_comment
(
    usercompanycomment_id INT AUTO_INCREMENT NOT NULL,
    user_company_id       INT                NOT NULL,
    site_id               INT DEFAULT 1      NOT NULL,
    comment               VARCHAR(2000)      NOT NULL,
    date                  datetime           NOT NULL,
    added_by_user_id      INT                NULL,
    CONSTRAINT PK_USER_COMPANY_COMMENT PRIMARY KEY (usercompanycomment_id)
);


CREATE TABLE user_company_equipment_files
(
    file_id                   INT               NOT NULL,
    user_company_equipment_id INT               NOT NULL,
    file_order                INT     DEFAULT 1 NULL,
    created_date              datetime          NOT NULL,
    created_by_user_id        INT               NOT NULL,
    modified_date             datetime          NULL,
    modified_by_user_id       INT               NULL,
    deleted                   TINYINT DEFAULT 0 NOT NULL,
    deleted_date              datetime          NULL,
    deleted_by_user_id        INT               NULL,
    CONSTRAINT PK_USER_COMPANY_EQUIPMENT_FILES PRIMARY KEY (file_id, user_company_equipment_id)
);


CREATE TABLE user_company_equipment_log_files
(
    file_id                       INT               NOT NULL,
    user_company_equipment_log_id INT               NOT NULL,
    file_order                    INT     DEFAULT 1 NULL,
    created_date                  datetime          NOT NULL,
    created_by_user_id            INT               NOT NULL,
    modified_date                 datetime          NULL,
    modified_by_user_id           INT               NULL,
    deleted                       TINYINT DEFAULT 0 NOT NULL,
    deleted_date                  datetime          NULL,
    deleted_by_user_id            INT               NULL,
    CONSTRAINT PK_USER_COMPANY_EQUIPMENT_LOG_FILES PRIMARY KEY (file_id, user_company_equipment_log_id)
);


CREATE TABLE user_company_equipment_logs
(
    user_company_equipment_log_id INT AUTO_INCREMENT NOT NULL,
    user_company_equipment_id     INT                NOT NULL,
    log_type                      VARCHAR(255)       NOT NULL,
    log_date                      date               NOT NULL,
    mileage                       INT                NULL,
    notes                         TEXT               NULL,
    expense                       DECIMAL(14, 2)     NULL,
    created_date                  datetime           NOT NULL,
    created_by_user_id            INT                NOT NULL,
    modified_date                 datetime           NULL,
    modified_by_user_id           INT                NULL,
    deleted                       TINYINT DEFAULT 0  NOT NULL,
    deleted_date                  datetime           NULL,
    deleted_by_user_id            INT                NULL,
    CONSTRAINT PK_USER_COMPANY_EQUIPMENT_LOGS PRIMARY KEY (user_company_equipment_log_id)
);


CREATE TABLE user_company_equipments
(
    user_company_equipment_id       INT AUTO_INCREMENT                           NOT NULL,
    user_company_id                 INT                                          NOT NULL,
    equipment_type                  ENUM ('truck', 'trailer')                    NOT NULL,
    external_equipment_id           VARCHAR(255)                DEFAULT ''       NOT NULL,
    make                            VARCHAR(255)                DEFAULT ''       NOT NULL,
    model                           VARCHAR(255)                DEFAULT ''       NOT NULL,
    model_year                      VARCHAR(4)                                   NOT NULL,
    trailer_type                    VARCHAR(255)                DEFAULT ''       NOT NULL,
    vin                             VARCHAR(17)                                  NOT NULL,
    equipment_value                 DECIMAL(14, 2)                               NULL,
    estimated_mileage               INT                                          NULL,
    license_plate_number            VARCHAR(32)                 DEFAULT ''       NOT NULL,
    license_plate_expiration_date   date                                         NULL,
    dot_expiration_date             date                                         NULL,
    insurance_expiration_date       date                                         NULL,
    insurance_company               VARCHAR(255)                DEFAULT ''       NOT NULL,
    inspection_expiration_date      date                                         NULL,
    registration_expiration_date    date                                         NULL,
    notes                           TEXT                                         NULL,
    default_assigned_driver_user_id INT                                          NULL,
    status                          ENUM ('active', 'inactive') DEFAULT 'active' NOT NULL,
    created_date                    datetime                                     NOT NULL,
    created_by_user_id              INT                                          NOT NULL,
    modified_date                   datetime                                     NULL,
    modified_by_user_id             INT                                          NULL,
    deleted                         TINYINT                     DEFAULT 0        NOT NULL,
    deleted_date                    datetime                                     NULL,
    deleted_by_user_id              INT                                          NULL,
    CONSTRAINT PK_USER_COMPANY_EQUIPMENTS PRIMARY KEY (user_company_equipment_id)
);


CREATE TABLE user_company_mcp_integrations
(
    user_company_id INT                      NOT NULL,
    mcp_username    VARCHAR(50)   DEFAULT '' NOT NULL,
    mcp_password    VARCHAR(50)   DEFAULT '' NOT NULL,
    access_token    VARCHAR(1024) DEFAULT '' NOT NULL,
    issued          datetime                 NULL,
    expires         datetime                 NULL,
    sync_date       datetime                 NULL,
    last_from_date  VARCHAR(45)   DEFAULT '' NOT NULL,
    last_to_date    VARCHAR(45)   DEFAULT '' NOT NULL,
    sync_message    TEXT                     NULL,
    CONSTRAINT PK_USER_COMPANY_MCP_INTEGRATIONS PRIMARY KEY (user_company_id)
);


CREATE TABLE user_company_user_types
(
    user_company_id INT NOT NULL,
    user_type_id    INT NOT NULL,
    CONSTRAINT PK_USER_COMPANY_USER_TYPES PRIMARY KEY (user_company_id, user_type_id)
);


CREATE TABLE user_devices
(
    user_id            INT                                    NOT NULL,
    device_id          VARCHAR(255)                           NOT NULL,
    notification_token VARCHAR(500) DEFAULT ''                NOT NULL,
    push_enabled       TINYINT      DEFAULT 0                 NOT NULL,
    sms_enabled        TINYINT      DEFAULT 0                 NOT NULL,
    date_added         datetime     DEFAULT CURRENT_TIMESTAMP NOT NULL,
    date_updated       datetime                               NULL,
    CONSTRAINT PK_USER_DEVICES PRIMARY KEY (user_id, device_id)
);


CREATE TABLE user_email_categories
(
    user_id           INT                                                       NOT NULL,
    site_id           INT                                    DEFAULT 1          NOT NULL,
    email_category_id INT                                                       NOT NULL,
    email_frequency   ENUM ('never', 'realtime', 'schedule') DEFAULT 'schedule' NOT NULL,
    changed_date      datetime                                                  NULL,
    send_push         TINYINT                                DEFAULT 0          NOT NULL,
    send_sms          TINYINT                                DEFAULT 0          NOT NULL,
    CONSTRAINT PK_USER_EMAIL_CATEGORIES PRIMARY KEY (user_id, site_id, email_category_id)
);


CREATE TABLE user_email_schedules
(
    email_schedule_id INT AUTO_INCREMENT NOT NULL,
    user_id           INT                NOT NULL,
    site_id           INT     DEFAULT 1  NOT NULL,
    email_time        time               NOT NULL,
    is_first          TINYINT DEFAULT 0  NOT NULL,
    CONSTRAINT PK_USER_EMAIL_SCHEDULES PRIMARY KEY (email_schedule_id)
);


CREATE TABLE user_emails
(
    email_id          INT AUTO_INCREMENT      NOT NULL,
    load_id           INT                     NULL,
    site_id           INT                     NOT NULL,
    user_id           INT                     NOT NULL,
    user_company_id   INT                     NOT NULL,
    email_category_id INT          DEFAULT 8  NOT NULL,
    from_name         VARCHAR(100) DEFAULT '' NOT NULL,
    from_email        VARCHAR(100) DEFAULT '' NOT NULL,
    replyto_email     VARCHAR(100)            NOT NULL,
    to_user_id        INT                     NULL,
    census_num        DECIMAL(8)              NULL,
    to_name           VARCHAR(100) DEFAULT '' NOT NULL,
    to_email          VARCHAR(100) DEFAULT '' NOT NULL,
    subject           VARCHAR(255) DEFAULT '' NOT NULL,
    body              MEDIUMTEXT              NOT NULL,
    date_added        timestamp               NULL,
    sent              TINYINT      DEFAULT 0  NULL,
    date_sent         timestamp               NULL,
    email_queue_id    INT                     NULL,
    CONSTRAINT PK_USER_EMAILS PRIMARY KEY (email_id)
);


CREATE TABLE user_files
(
    user_file_id       INT AUTO_INCREMENT      NOT NULL,
    file_id            INT                     NOT NULL,
    user_id            INT                     NULL,
    user_email         VARCHAR(100) DEFAULT '' NOT NULL,
    user_phone         VARCHAR(50)  DEFAULT '' NOT NULL,
    last_used_date     datetime                NULL,
    times_used         INT          DEFAULT 0  NOT NULL,
    last_sent_date     datetime                NULL,
    last_received_date datetime                NULL,
    is_favorite        TINYINT      DEFAULT 0  NOT NULL,
    shared_in          VARCHAR(100) DEFAULT '' NOT NULL,
    date_added         datetime                NOT NULL,
    date_deleted       datetime                NULL,
    deleted            TINYINT      DEFAULT 0  NOT NULL,
    CONSTRAINT PK_USER_FILES PRIMARY KEY (user_file_id)
);


CREATE TABLE user_followup
(
    userfollowup_id  INT AUTO_INCREMENT NOT NULL,
    user_id          INT                NOT NULL,
    site_id          INT DEFAULT 1      NOT NULL,
    notes            VARCHAR(2000)      NOT NULL,
    followup_on      datetime           NOT NULL,
    date             datetime           NOT NULL,
    added_by_user_id INT                NULL,
    completed        BIT DEFAULT 0      NULL,
    CONSTRAINT PK_USER_FOLLOWUP PRIMARY KEY (userfollowup_id)
);


CREATE TABLE user_geo_history
(
    user_geo_history_id INT AUTO_INCREMENT                     NOT NULL,
    user_id             INT                                    NOT NULL,
    latitude            DOUBLE                                 NOT NULL,
    longitude           DOUBLE                                 NOT NULL,
    accuracy            FLOAT(12)                              NULL,
    speed               FLOAT(12)                              NULL,
    heading             FLOAT(12)                              NULL,
    altitude            FLOAT(12)                              NULL,
    timestamp           datetime     DEFAULT CURRENT_TIMESTAMP NOT NULL,
    event               VARCHAR(250) DEFAULT ''                NOT NULL,
    CONSTRAINT PK_USER_GEO_HISTORY PRIMARY KEY (user_geo_history_id)
);


CREATE TABLE user_geo_tracking
(
    geo_tracking_id INT AUTO_INCREMENT                     NOT NULL,
    user_id         INT                                    NOT NULL,
    latitude        FLOAT(12)                              NOT NULL,
    longitude       FLOAT(12)                              NOT NULL,
    accuracy        FLOAT(12)                              NULL,
    speed           FLOAT(12)                              NULL,
    heading         FLOAT(12)                              NULL,
    altitude        FLOAT(12)                              NULL,
    timestamp       datetime     DEFAULT CURRENT_TIMESTAMP NOT NULL,
    event           VARCHAR(250) DEFAULT ''                NOT NULL,
    data            TEXT                                   NULL,
    CONSTRAINT PK_USER_GEO_TRACKING PRIMARY KEY (geo_tracking_id)
);


CREATE TABLE user_groups
(
    user_group_id   INT AUTO_INCREMENT NOT NULL,
    user_id         INT                NOT NULL,
    user_company_id INT                NOT NULL,
    user_group      VARCHAR(100)       NOT NULL,
    CONSTRAINT PK_USER_GROUPS PRIMARY KEY (user_group_id)
);


CREATE TABLE user_groups_ref
(
    user_id       INT NOT NULL,
    user_group_id INT NOT NULL,
    CONSTRAINT PK_USER_GROUPS_REF PRIMARY KEY (user_id, user_group_id)
);


CREATE TABLE user_info
(
    user_id                                INT AUTO_INCREMENT                                                                                       NOT NULL,
    user_company_id                        INT                                                                                                      NULL,
    external_user_id                       VARCHAR(50)   DEFAULT ''                                                                                 NOT NULL,
    google_id                              VARCHAR(128)  DEFAULT ''                                                                                 NOT NULL,
    google_email                           VARCHAR(100)  DEFAULT ''                                                                                 NOT NULL,
    facebook_id                            VARCHAR(128)  DEFAULT ''                                                                                 NOT NULL,
    facebook_email                         VARCHAR(100)  DEFAULT ''                                                                                 NOT NULL,
    first_name                             VARCHAR(50)                                                                                              NOT NULL,
    last_name                              VARCHAR(50)                                                                                              NOT NULL,
    cell_phone                             VARCHAR(25)   DEFAULT ''                                                                                 NOT NULL,
    cell_phone_verified                    TINYINT       DEFAULT 0                                                                                  NOT NULL,
    cell_phone_hidden                      BIT           DEFAULT 0                                                                                  NULL,
    user_phone_1                           VARCHAR(25)   DEFAULT ''                                                                                 NOT NULL,
    user_phone_1_type                      VARCHAR(10)   DEFAULT ''                                                                                 NOT NULL,
    user_phone_1_hidden                    BIT           DEFAULT 0                                                                                  NULL,
    user_phone_2                           VARCHAR(25)   DEFAULT ''                                                                                 NOT NULL,
    user_phone_2_type                      VARCHAR(10)   DEFAULT ''                                                                                 NOT NULL,
    user_phone_2_hidden                    BIT           DEFAULT 0                                                                                  NULL,
    phone_1                                VARCHAR(25)   DEFAULT ''                                                                                 NOT NULL,
    phone_1_type                           VARCHAR(10)   DEFAULT ''                                                                                 NOT NULL,
    phone_1_hidden                         BIT           DEFAULT 0                                                                                  NULL,
    phone_2                                VARCHAR(25)   DEFAULT ''                                                                                 NOT NULL,
    phone_2_type                           VARCHAR(10)   DEFAULT ''                                                                                 NOT NULL,
    phone_2_hidden                         BIT           DEFAULT 0                                                                                  NULL,
    fax                                    VARCHAR(25)   DEFAULT ''                                                                                 NOT NULL,
    fax_hidden                             BIT           DEFAULT 0                                                                                  NULL,
    email                                  VARCHAR(100)  DEFAULT ''                                                                                 NOT NULL,
    email_verified                         TINYINT       DEFAULT 0                                                                                  NOT NULL,
    username                               VARCHAR(100)                                                                                             NOT NULL,
    old_username                           VARCHAR(100)  DEFAULT ''                                                                                 NOT NULL,
    password_hash                          VARCHAR(80)   DEFAULT ''                                                                                 NOT NULL,
    password_lowercase                     TINYINT       DEFAULT 0                                                                                  NULL,
    sign_up_date                           date                                                                                                     NOT NULL,
    ratings_private                        VARCHAR(3)    DEFAULT '0'                                                                                NOT NULL,
    chat_name                              VARCHAR(255)  DEFAULT ''                                                                                 NOT NULL,
    avatar_small                           VARCHAR(200)  DEFAULT 'default.png'                                                                      NOT NULL,
    avatar_large                           VARCHAR(200)  DEFAULT ''                                                                                 NOT NULL,
    avatar_size                            INT                                                                                                      NULL,
    avatar_extension                       VARCHAR(20)   DEFAULT ''                                                                                 NOT NULL,
    avatar_mime_type                       VARCHAR(45)   DEFAULT ''                                                                                 NOT NULL,
    avatar_width                           INT           DEFAULT 0                                                                                  NOT NULL,
    avatar_height                          INT           DEFAULT 0                                                                                  NOT NULL,
    avatar_updated                         timestamp                                                                                                NULL,
    heard_about_us                         VARCHAR(500)  DEFAULT ''                                                                                 NOT NULL,
    deletion_date                          date                                                                                                     NULL,
    country                                VARCHAR(5)    DEFAULT ''                                                                                 NOT NULL,
    state                                  VARCHAR(2)    DEFAULT ''                                                                                 NOT NULL,
    city                                   VARCHAR(60)   DEFAULT ''                                                                                 NOT NULL,
    address                                VARCHAR(150)  DEFAULT ''                                                                                 NOT NULL,
    zip                                    VARCHAR(10)   DEFAULT ''                                                                                 NOT NULL,
    longitude                              DECIMAL(15, 10)                                                                                          NULL,
    latitude                               DECIMAL(15, 10)                                                                                          NULL,
    dst                                    TINYINT                                                                                                  NULL,
    timezone_offset                        INT                                                                                                      NULL,
    timezone_id                            VARCHAR(50)   DEFAULT ''                                                                                 NOT NULL,
    current_city                           VARCHAR(60)   DEFAULT ''                                                                                 NOT NULL,
    current_state                          VARCHAR(2)    DEFAULT ''                                                                                 NOT NULL,
    current_zip                            VARCHAR(10)   DEFAULT ''                                                                                 NOT NULL,
    current_country                        VARCHAR(5)    DEFAULT ''                                                                                 NOT NULL,
    current_address                        VARCHAR(150)  DEFAULT ''                                                                                 NOT NULL,
    current_latitude                       DECIMAL(15, 10)                                                                                          NULL,
    current_longitude                      DECIMAL(15, 10)                                                                                          NULL,
    geo_latitude                           DOUBLE                                                                                                   NULL,
    geo_longitude                          DOUBLE                                                                                                   NULL,
    geo_accuracy                           FLOAT(12)                                                                                                NULL,
    geo_speed                              FLOAT(12)                                                                                                NULL,
    geo_heading                            FLOAT(12)                                                                                                NULL,
    geo_altitude                           FLOAT(12)                                                                                                NULL,
    geo_updated_date                       datetime                                                                                                 NULL,
    current_dst                            TINYINT                                                                                                  NULL,
    current_timezone_offset                INT                                                                                                      NULL,
    current_timezone_id                    VARCHAR(50)   DEFAULT ''                                                                                 NOT NULL,
    mailing_address                        VARCHAR(255)  DEFAULT ''                                                                                 NOT NULL,
    company_notes                          VARCHAR(1000) DEFAULT ''                                                                                 NOT NULL,
    website                                VARCHAR(100)  DEFAULT ''                                                                                 NOT NULL,
    sign_up_site_id                        INT           DEFAULT 1                                                                                  NOT NULL,
    comment_count                          INT           DEFAULT 0                                                                                  NULL,
    sb_old_member_id                       INT                                                                                                      NULL,
    sb_email_sent                          INT           DEFAULT 0                                                                                  NULL,
    source                                 VARCHAR(10)   DEFAULT ''                                                                                 NOT NULL,
    source_id                              VARCHAR(45)   DEFAULT ''                                                                                 NOT NULL,
    bad_email_date                         date                                                                                                     NULL,
    bad_email_reason                       VARCHAR(255)  DEFAULT ''                                                                                 NOT NULL,
    bad_email_drop_count                   INT                                                                                                      NULL,
    bad_email_latest_drop_date             date                                                                                                     NULL,
    bad_email_marked_spam_date             date                                                                                                     NULL,
    trip_length                            VARCHAR(45)   DEFAULT ''                                                                                 NOT NULL,
    haul_out_of_state                      TINYINT                                                                                                  NULL,
    commercially_licensed                  TINYINT                                                                                                  NULL,
    hazmat_license                         TINYINT                                                                                                  NULL,
    haul_in_canada                         TINYINT                                                                                                  NULL,
    cc_avatar                              VARCHAR(255)  DEFAULT 'https://s3.amazonaws.com/cdn.bulkloads.com/user_files/profile/thumbs/default.png' NOT NULL,
    password_changed_date                  datetime                                                                                                 NULL,
    user_role_ids                          VARCHAR(100)  DEFAULT ''                                                                                 NOT NULL,
    user_roles                             VARCHAR(200)  DEFAULT ''                                                                                 NOT NULL,
    default_driver_rate                    DECIMAL(10, 3)                                                                                           NULL,
    default_driver_rate_type               VARCHAR(10)   DEFAULT ''                                                                                 NOT NULL,
    invoice_footer_notes                   TEXT                                                                                                     NULL,
    user_group_ids                         VARCHAR(100)  DEFAULT ''                                                                                 NOT NULL,
    user_groups                            VARCHAR(200)  DEFAULT ''                                                                                 NOT NULL,
    geo_tracking_until                     datetime                                                                                                 NULL,
    geo_tracking_load_assignment_id        INT                                                                                                      NULL,
    sff_id                                 VARCHAR(50)   DEFAULT ''                                                                                 NOT NULL,
    default_bill_to_ab_company_id          INT                                                                                                      NULL,
    default_bill_to_ab_user_id             INT                                                                                                      NULL,
    accounting_email                       VARCHAR(100)  DEFAULT ''                                                                                 NOT NULL,
    accounting_email_verified              TINYINT       DEFAULT 0                                                                                  NOT NULL,
    accounting_email_cc_me                 TINYINT       DEFAULT 1                                                                                  NOT NULL,
    load_confirmation_footer               TEXT                                                                                                     NULL,
    last_truck_user_company_equipment_id   INT                                                                                                      NULL,
    last_trailer_user_company_equipment_id INT                                                                                                      NULL,
    CONSTRAINT PK_USER_INFO PRIMARY KEY (user_id)
);


CREATE TABLE user_info_app
(
    user_id INT                      NOT NULL,
    pin_ids VARCHAR(1000) DEFAULT '' NOT NULL,
    CONSTRAINT PK_USER_INFO_APP PRIMARY KEY (user_id)
);


CREATE TABLE user_interface_state
(
    user_id       INT          NOT NULL,
    app_name      VARCHAR(45)  NOT NULL,
    `key`         VARCHAR(150) NOT NULL,
    value         TEXT         NOT NULL,
    modified_date datetime     NOT NULL,
    CONSTRAINT PK_USER_INTERFACE_STATE PRIMARY KEY (user_id, app_name, `key`)
);


CREATE TABLE user_permissions
(
    user_permission_id       INT AUTO_INCREMENT     NOT NULL,
    user_permission          VARCHAR(50)            NOT NULL,
    user_permission_category VARCHAR(50) DEFAULT '' NOT NULL,
    CONSTRAINT PK_USER_PERMISSIONS PRIMARY KEY (user_permission_id)
);


CREATE TABLE user_photos
(
    photo_id     INT AUTO_INCREMENT                                                 NOT NULL,
    user_id      INT                                                                NOT NULL,
    filename     VARCHAR(200) DEFAULT ''                                            NOT NULL,
    thumb        VARCHAR(200) DEFAULT ''                                            NOT NULL,
    caption      VARCHAR(400) DEFAULT ''                                            NOT NULL,
    size         INT                                                                NULL,
    extension    VARCHAR(20)  DEFAULT ''                                            NOT NULL,
    mime_type    VARCHAR(45)  DEFAULT 'image/jpg'                                   NOT NULL,
    width        INT          DEFAULT 0                                             NOT NULL,
    height       INT          DEFAULT 0                                             NOT NULL,
    dateCreated  timestamp    DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    dateModified timestamp                                                          NULL,
    dateDeleted  timestamp                                                          NULL,
    deleted      TINYINT      DEFAULT 0                                             NOT NULL,
    CONSTRAINT PK_USER_PHOTOS PRIMARY KEY (photo_id)
);


CREATE TABLE user_role_permissions
(
    user_role_id       INT NOT NULL,
    user_permission_id INT NOT NULL,
    CONSTRAINT PK_USER_ROLE_PERMISSIONS PRIMARY KEY (user_role_id, user_permission_id)
);


CREATE TABLE user_role_temp
(
    user_id INT NULL
);


CREATE TABLE user_roles
(
    user_role_id  INT AUTO_INCREMENT     NOT NULL,
    user_role     VARCHAR(45)            NOT NULL,
    user_type_ids VARCHAR(45) DEFAULT '' NOT NULL,
    `description` MEDIUMTEXT             NULL,
    CONSTRAINT PK_USER_ROLES PRIMARY KEY (user_role_id)
);


CREATE TABLE user_roles_ref
(
    user_id      INT NOT NULL,
    user_role_id INT NOT NULL,
    CONSTRAINT PK_USER_ROLES_REF PRIMARY KEY (user_id, user_role_id)
);


CREATE TABLE user_type
(
    user_type_id INT AUTO_INCREMENT NOT NULL,
    user_type    VARCHAR(255)       NOT NULL,
    CONSTRAINT PK_USER_TYPE PRIMARY KEY (user_type_id)
);


CREATE TABLE user_vars
(
    user_id    INT                      NOT NULL,
    site_id    INT                      NOT NULL,
    var_name   VARCHAR(45)              NOT NULL,
    var_value  VARCHAR(1000) DEFAULT '' NOT NULL,
    date_added datetime                 NOT NULL,
    CONSTRAINT PK_USER_VARS PRIMARY KEY (user_id, site_id, var_name)
);


CREATE TABLE verifications
(
    verification_id     INT AUTO_INCREMENT      NOT NULL,
    user_company_id     INT                     NOT NULL,
    verification_method VARCHAR(10)  DEFAULT '' NOT NULL,
    phone_number        VARCHAR(20)  DEFAULT '' NOT NULL,
    email               VARCHAR(120) DEFAULT '' NOT NULL,
    verification_code   VARCHAR(15)  DEFAULT '' NOT NULL,
    date_added          datetime                NOT NULL,
    date_verified       datetime                NULL,
    verified_by_user_id INT                     NULL,
    comment             MEDIUMTEXT              NULL,
    CONSTRAINT PK_VERIFICATIONS PRIMARY KEY (verification_id)
);


CREATE TABLE verifications_email
(
    verifications_email_id INT AUTO_INCREMENT                                                NOT NULL,
    user_id                INT                                                               NULL,
    email                  VARCHAR(120)                                                      NOT NULL,
    phone                  VARCHAR(25) DEFAULT ''                                            NOT NULL,
    verification_code      VARCHAR(45)                                                       NOT NULL,
    date_added             timestamp   DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL,
    date_verified          timestamp                                                         NULL,
    active                 TINYINT     DEFAULT 1                                             NOT NULL,
    verification_token     VARCHAR(80) DEFAULT ''                                            NOT NULL,
    CONSTRAINT PK_VERIFICATIONS_EMAIL PRIMARY KEY (verifications_email_id)
);


CREATE TABLE washout_comments
(
    washout_comment_id                  INT AUTO_INCREMENT                               NOT NULL,
    washout_id                          INT                                              NOT NULL,
    washout_comment_user_id             INT                                              NULL,
    washout_comment_date                date                                             NOT NULL,
    washout_comment                     VARCHAR(1500)                                    NOT NULL,
    washout_comment_type                ENUM ('comment', 'suggestion') DEFAULT 'comment' NULL,
    washout_comment_approved            TINYINT                        DEFAULT 0         NOT NULL,
    washout_comment_approved_date       datetime                                         NULL,
    washout_comment_approved_by_user_id INT                                              NULL,
    deleted                             TINYINT                        DEFAULT 0         NOT NULL,
    deleted_date                        datetime                                         NULL,
    deleted_by_user_id                  INT                                              NULL,
    CONSTRAINT PK_WASHOUT_COMMENTS PRIMARY KEY (washout_comment_id)
);


CREATE TABLE washouts
(
    washout_id             INT AUTO_INCREMENT       NOT NULL,
    washout_name           VARCHAR(50)  DEFAULT ''  NOT NULL,
    origin_city            VARCHAR(60)  DEFAULT ''  NOT NULL,
    origin_state           VARCHAR(2)   DEFAULT ''  NOT NULL,
    phone_1                VARCHAR(15)  DEFAULT ''  NOT NULL,
    phone_2                VARCHAR(15)  DEFAULT ''  NOT NULL,
    directions             VARCHAR(500) DEFAULT ''  NOT NULL,
    cost                   VARCHAR(20)  DEFAULT ''  NOT NULL,
    address                VARCHAR(150) DEFAULT ''  NOT NULL,
    hot_water              VARCHAR(1)   DEFAULT ''  NOT NULL,
    allow_livestock        VARCHAR(1)   DEFAULT '0' NOT NULL,
    allow_tanker           VARCHAR(1)   DEFAULT ''  NULL,
    edit_date              datetime                 NULL,
    edit_by_user_id        INT                      NULL,
    take_credit_cards      VARCHAR(1)   DEFAULT ''  NOT NULL,
    location_verified_date date                     NULL,
    date_verified          date                     NULL,
    day_contact            VARCHAR(255) DEFAULT ''  NOT NULL,
    after_hours_contact    VARCHAR(255) DEFAULT ''  NOT NULL,
    hours_of_operation     VARCHAR(255) DEFAULT ''  NOT NULL,
    longitude              DOUBLE                   NULL,
    latitude               DOUBLE                   NULL,
    posted_date            date                     NULL,
    notes                  VARCHAR(255) DEFAULT ''  NOT NULL,
    missing_info           VARCHAR(3)   DEFAULT ''  NOT NULL,
    public_notes           VARCHAR(500) DEFAULT ''  NOT NULL,
    premium_end_date       date                     NULL,
    website                VARCHAR(500)             NULL,
    CONSTRAINT PK_WASHOUTS PRIMARY KEY (washout_id)
);


CREATE INDEX FK_load_assignment_geo_history_idx ON load_assignment_geo_history (load_assignment_id);


CREATE INDEX FK_pc_routes_EndCity ON pc_routes (endCityID);


CREATE INDEX FK_user_geo_history_idx ON user_geo_history (user_id);


CREATE INDEX FK_user_id_idx ON user_geo_tracking (user_id);


CREATE INDEX IX_Online_Since ON messages_users_online (online_since);


CREATE INDEX SCHED_SHOTS_shot_id ON sched_tasks (shot_id);


CREATE INDEX SHOT_DATETIME_DESC ON sched_tasks_shots (theDateTime DESC);


CREATE INDEX abbreviation ON states (abbreviation, state, country);


CREATE INDEX active ON loads (active);


CREATE INDEX active ON trucks (active);


CREATE INDEX bl_set_del_date ON bl_user_settings (deletion_date);


CREATE INDEX census ON user_company (census_num);


CREATE INDEX company ON user_info (user_company_id);


CREATE INDEX `company name` ON user_company (company_name, merged_to, company_owner_id);


CREATE INDEX `completed id` ON user_followup (completed);


CREATE INDEX country ON states (country);


CREATE INDEX create_date_idx ON messages (create_date);


CREATE INDEX date_added ON forum_post (date_added);


CREATE INDEX days_to_pay ON ratings (days_to_pay);


CREATE INDEX `dead email` ON deletebad_email (dead_account);


CREATE INDEX `dest state` ON loads (destination_state);


CREATE INDEX destinationcity ON mileages (destination_city);


CREATE INDEX destinationstate ON mileages (destination_state);


CREATE INDEX `email address` ON deletebad_email (bad_email_address);


CREATE INDEX email_index ON email_metrics (email);


CREATE INDEX `first name` ON user_info (first_name);


CREATE INDEX fk_ab_comp_user_id_idx ON ab_companies (user_id);


CREATE INDEX fk_ab_company_id_idx ON ab_users (ab_company_id);


CREATE INDEX fk_ab_user_groups_u_id_idx ON ab_user_group_ab_users (ab_user_id);


CREATE INDEX fk_ab_user_roles_ref_role_idx ON ab_user_roles_ref (ab_user_role_id);


CREATE INDEX fk_ab_user_user_id_idx ON ab_users (user_id);


CREATE INDEX fk_activities_type_id_idx ON activities (activity_type_id);


CREATE INDEX fk_activities_user_id_idx ON activities (user_id);


CREATE INDEX fk_anal_search_user_id_idx ON load_analytics_search (user_id);


CREATE INDEX fk_anal_user_id_idx ON load_analytics_emails (user_id);


CREATE INDEX fk_anal_views_user_id_idx ON deleteload_analytics_views (user_id);


CREATE INDEX fk_api_key_endpoint_endpoint_id_idx ON api_key_endpoints (api_endpoint_id);


CREATE INDEX fk_api_key_endpoint_key_id_idx ON api_key_endpoints (api_key_id);


CREATE INDEX fk_api_key_int_perm_c_id_idx ON api_key_integration_permissions (user_company_id);


CREATE INDEX fk_app_rec_history_idx ON app_recipient_history (rec_id);


CREATE INDEX fk_b_history_id_idx ON blocked_requests (blocked_history_id);


CREATE INDEX fk_bl_user_settings_cid ON bl_user_settings (user_company_id);


CREATE INDEX fk_blockeduserid_idx ON blocked_companies (user_id);


CREATE INDEX fk_carr_states_carrier_id_idx ON lo_carrier_states_run (lo_carrier_id);


CREATE INDEX fk_carriers_comp_id_idx ON lo_carriers (user_company_id);


CREATE INDEX fk_cl_lo_carrier_id ON lo_carrier_loads (lo_carrier_id);


CREATE INDEX fk_cl_load_id ON lo_carrier_loads (load_id);


CREATE INDEX fk_commodities_user_company_id_idx ON commodities (user_company_id);


CREATE INDEX fk_comp_search_user_id_idx ON company_searches (user_id);


CREATE INDEX fk_compfind_radius_user_id_idx ON company_finder_radius_search (user_id);


CREATE INDEX fk_compfind_user_id_idx ON company_finder_criteria_search (user_id);


CREATE INDEX fk_compfind_views_user_id_idx ON company_finder_views (user_id);


CREATE INDEX fk_contracts_c_id_idx ON contracts (user_company_id);


CREATE INDEX fk_contracts_commodity_id_idx ON contracts (commodity_id);


CREATE INDEX fk_email_metrics_id ON message_email_metrics (email_metrics_id);


CREATE INDEX fk_equipment_id_idx ON load_equipment (equipment_id);


CREATE INDEX fk_erpc_user_company_id_idx ON external_rate_product_categories (user_company_id);


CREATE INDEX fk_error_b_history_id_idx ON blocked_errors (blocked_history_id);


CREATE INDEX fk_fc_company_id_idx ON favorite_companies (user_company_id);


CREATE INDEX fk_fc_site_id_idx ON favorite_companies (site_id);


CREATE INDEX fk_file_id_idx ON user_files (file_id);


CREATE INDEX fk_file_views_file_id_idx ON file_views (file_id);


CREATE INDEX fk_fl_routeid_idx ON favorite_lanes (lane);


CREATE INDEX fk_fl_site_id_idx ON favorite_lanes (site_id);


CREATE INDEX fk_fl_user_settings_cid ON fl_user_settings (user_company_id);


CREATE INDEX fk_flag_user_id_idx ON load_flags (user_id);


CREATE INDEX fk_floads_load_id_idx ON favorite_loads (load_id);


CREATE INDEX fk_floads_site_id_idx ON favorite_loads (site_id);


CREATE INDEX fk_fmcsa_userid_idx ON favorite_fmcsa (user_id);


CREATE INDEX fk_fmcsaemails_site_id_idx ON fmcsa_emails (site_id);


CREATE INDEX fk_fmcsaemails_user_id_idx ON fmcsa_emails (user_id);


CREATE INDEX fk_forum_file_idx ON forum_files (forum_id);


CREATE INDEX fk_inv_id_idx ON invoice_payments (invoice_id);


CREATE INDEX fk_inv_idx ON invoice_items (invoice_id);


CREATE INDEX fk_inv_types_idx ON invoice_items (invoice_item_type_id);


CREATE INDEX fk_invoice_id ON invoice_files (invoice_id);


CREATE INDEX fk_la_conf_log_ab_user_id_idx ON load_assignment_confirmation_log (to_ab_user_id);


CREATE INDEX fk_la_conf_log_assignment_id_idx ON load_assignment_confirmation_log (load_assignment_id);


CREATE INDEX fk_la_conf_log_file_id_idx ON load_assignment_confirmation_log (confirmation_file_id);


CREATE INDEX fk_la_conf_log_user_id_idx ON load_assignment_confirmation_log (log_by_user_id);


CREATE INDEX fk_la_files_file_id_idx ON load_assignment_files (file_id);


CREATE INDEX fk_la_hiring_ab_user_id_idx ON load_assignments (hiring_ab_user_id);


CREATE INDEX fk_la_load_id_idx ON load_assignments (load_id);


CREATE INDEX fk_la_to_ab_user_id_idx ON load_assignments (to_ab_user_id);


CREATE INDEX fk_la_to_load_id_idx ON load_assignments (to_load_id);


CREATE INDEX fk_la_to_user_company_id_idx ON load_assignments (to_user_company_id);


CREATE INDEX fk_la_to_user_id_idx ON load_assignments (to_user_id);


CREATE INDEX fk_la_user_company_id_idx ON load_assignments (user_company_id);


CREATE INDEX fk_la_user_id_idx ON load_assignments (user_id);


CREATE INDEX fk_le_load_id_idx ON load_emails (load_id);


CREATE INDEX fk_le_site_id_idx ON load_emails (site_id);


CREATE INDEX fk_le_user_id_idx ON load_emails (user_id);


CREATE INDEX fk_lee_load_id_idx ON user_emails (load_id);


CREATE INDEX fk_lee_site_id_idx ON user_emails (site_id);


CREATE INDEX fk_lee_user_id_idx ON user_emails (user_id);


CREATE INDEX fk_ll_user_id_idx ON load_leads (user_id);


CREATE INDEX fk_lo_carrier_userid_idx ON lo_carrier_comments (user_id);


CREATE INDEX fk_lo_comments_carr_id_idx ON lo_carrier_comments (lo_carrier_id);


CREATE INDEX fk_lo_user_settings_cid ON lo_user_settings (user_company_id);


CREATE INDEX fk_load_alert_id_idx ON load_alert_notifications (load_alert_id);


CREATE INDEX fk_load_alert_user_id_idx ON load_alerts (user_id);


CREATE INDEX fk_load_assignment_id_idx ON load_assignment_surcharges (load_assignment_id);


CREATE INDEX fk_load_commodity_id_idx ON loads (commodity_id);


CREATE INDEX fk_load_invoice_items_inv_id_idx ON load_invoice_items (load_invoice_id);


CREATE INDEX fk_load_invoice_items_la_id_idx ON load_invoice_items (load_assignment_id);


CREATE INDEX fk_load_invoice_notes_inv_id_idx ON load_invoice_notes (load_invoice_id);


CREATE INDEX fk_load_invoice_payments_inv_id_idx ON load_invoice_payments_old (load_invoice_id);


CREATE INDEX fk_load_invoices_bil_to_ab_company_id_idx ON load_invoices (bill_to_ab_company_id);


CREATE INDEX fk_load_invoices_bil_to_ab_user_id_idx ON load_invoices (hiring_ab_user_id);


CREATE INDEX fk_load_invoices_user_company_id_idx ON load_invoices (user_company_id);


CREATE INDEX fk_load_invoices_user_id_idx ON load_invoices (user_id);


CREATE INDEX fk_load_offer_load_id_idx ON load_offers (load_id);


CREATE INDEX fk_login_userid_idx ON logins (user_id);


CREATE INDEX fk_lv_load_id_idx ON load_views (load_id);


CREATE INDEX fk_lv_site_id_idx ON load_views (site_id);


CREATE INDEX fk_lv_user_id_idx ON load_views (user_id);


CREATE INDEX fk_message_id_idx ON message_email_metrics (message_id);


CREATE INDEX fk_notes_inv_idx ON invoice_notes (invoice_id);


CREATE INDEX fk_offer_rec_ab_user_group_id_idx ON offer_recipients (ab_user_group_id);


CREATE INDEX fk_offer_rec_ab_user_id_idx ON offer_recipients (ab_user_id);


CREATE INDEX fk_offer_rec_offer_id_idx ON offer_recipients (offer_id);


CREATE INDEX fk_op_reply_id_idx ON forum_opinions (reply_id);


CREATE INDEX fk_op_user_id_idx ON forum_opinions (user_id);


CREATE INDEX fk_pc_contact_id ON pc_searches (contact_id);


CREATE INDEX fk_pc_route_id ON pc_searches (routeid);


CREATE INDEX fk_pc_routes_endcity_idx ON pc_routes (startCityID);


CREATE INDEX fk_perm_user_id_idx ON load_offers (user_id);


CREATE INDEX fk_prof_equip_idx ON carrier_profile_equipment_run (user_id);


CREATE INDEX fk_prof_files_comp_id_idx ON carrier_profile_files (user_company_id);


CREATE INDEX fk_prof_files_idx ON carrier_profile_states_run (user_id);


CREATE INDEX fk_rate_product_cat_id_idx ON loads (rate_product_category_id);


CREATE INDEX fk_reminder_userid_idx ON reminder_queue (user_id);


CREATE INDEX fk_reply_file_idx ON forum_files (reply_id);


CREATE INDEX fk_reply_forum_userid_idx ON forum_reply (user_id);


CREATE INDEX fk_route_stops_cities ON pc_route_stops (cityID);


CREATE INDEX fk_sched_user_id_idx ON user_email_schedules (user_id);


CREATE INDEX fk_schedules_log_idx ON schedules_log_sync_mcp_status (schedules_log_id);


CREATE INDEX fk_sfc_user_settings_cid ON sfc_user_settings (user_company_id);


CREATE INDEX fk_sff_prospects_uid_idx ON sff_prospects (user_id);


CREATE INDEX fk_stops_routes ON pc_route_stops (routeID);


CREATE INDEX fk_sub_user_id_idx ON forum_subscription (user_id);


CREATE INDEX fk_sur_app_load_id ON app_load_surcharges (load_id);


CREATE INDEX fk_truckemails_userid_idx ON truck_emails (user_id);


CREATE INDEX fk_trucks_user_id ON trucks (user_id);


CREATE INDEX fk_truckviews_truckid_idx ON truck_views (truck_id);


CREATE INDEX fk_truckviews_userid_idx ON truck_views (user_id);


CREATE INDEX fk_ucat_catid_idx ON user_email_categories (email_category_id);


CREATE INDEX fk_user_file_idx ON carrier_profile_files (user_id);


CREATE INDEX fk_user_files_user_id_idx ON user_files (user_id);


CREATE INDEX fk_user_group_c_id_idx ON user_groups (user_company_id);


CREATE INDEX fk_user_group_u_id_idx ON user_groups (user_id);


CREATE INDEX fk_user_groups_ref_group_idx ON user_groups_ref (user_group_id);


CREATE INDEX fk_user_id ON loads (user_id);


CREATE INDEX fk_user_permission_idx ON user_role_permissions (user_permission_id);


CREATE INDEX fk_user_roles_ref_role_idx ON user_roles_ref (user_role_id);


CREATE INDEX fk_ver_comp_id_idx ON verifications (user_company_id);


CREATE INDEX fk_verifications_email_user_id_idx ON verifications_email (user_id);


CREATE INDEX fk_washout_id_idx ON washout_comments (washout_id);


CREATE INDEX fmcsa_emails_last ON fmcsa_emails (user_id, census_num, date_sent);


CREATE INDEX forum_id ON forum_reply (forum_id);


CREATE INDEX from_email_idx ON messages (from_email);


CREATE INDEX from_phone_idx ON messages (from_phone);


CREATE INDEX from_user_id_idx ON messages (from_user_id);


CREATE INDEX gw_record ON rates (gw_record_id);


CREATE INDEX `home page ad` ON banners (homepage_ad);


CREATE INDEX `home page position` ON banners (homepage_position);


CREATE INDEX idx_archived_by_user_id ON messages_archived (archived_by_user_id);


CREATE INDEX in_old_id ON scales (scale_id_old);


CREATE INDEX is_read ON messages (is_read);


CREATE INDEX ix_ab_comp_external_ab_id ON ab_companies (external_ab_company_id);


CREATE INDEX ix_ab_comp_user_company_id ON ab_companies (user_company_id);


CREATE INDEX ix_act_comp ON user_company (deletion_date, merged_to);


CREATE INDEX ix_activities_user_company_id ON activities (user_company_id);


CREATE INDEX ix_arbid ON authnet_trans (arb_id);


CREATE INDEX ix_arbid ON subscriptions (arb_subscription_id);


CREATE INDEX ix_assignment_number ON lo_carrier_loads (assignment_number);


CREATE INDEX ix_auth_cprofid ON authnet_arbs (customerProfileId);


CREATE INDEX ix_auth_uid ON authnet_arbs (user_id, user_company_id);


CREATE INDEX ix_avg_rate ON rates (rate_hauled_date, rate_product_category_id, rate_origin_lat, rate_origin_long, rate_destination_lat, rate_destination_long,
                                   rate_rate_per_mile);


CREATE INDEX ix_bentreid_processed ON bentrei_data (processed);


CREATE INDEX ix_bl_us_comp_finder ON bl_user_settings (show_in_company_finder);


CREATE INDEX ix_bl_us_comp_finder ON fl_user_settings (show_in_company_finder);


CREATE INDEX ix_bl_user_set_last_activity ON bl_user_settings (last_chat_activity);


CREATE INDEX ix_blocked_error_date ON blocked_errors (error_date);


CREATE INDEX ix_blocked_ip_end_date ON blocked_ips (blocked_end_date);


CREATE INDEX ix_census_num ON lo_carriers (census_num);


CREATE INDEX ix_commodities ON commodities (user_company_id, commodity);


CREATE INDEX ix_comp_search ON company_searches (user_id, site_id, search_type);


CREATE INDEX ix_company_code ON user_company (company_code);


CREATE INDEX ix_company_name ON lo_carriers (company_name);


CREATE INDEX ix_contract_id ON loads (contract_id);


CREATE INDEX ix_dateCreated ON pc_routes (dateCreated);


CREATE INDEX ix_deletion_date ON user_info (deletion_date);


CREATE INDEX ix_drop_ab_company_id ON loads (drop_ab_company_id);


CREATE INDEX ix_email_category ON email_queue (email_category_id);


CREATE INDEX ix_end_loc ON routes (endCity);


CREATE INDEX ix_equip_name ON equipment (equipment_name, site_id);


CREATE INDEX ix_external_commodity_id ON commodities (user_company_id, external_commodity_id);


CREATE INDEX ix_external_contract_id ON contracts (user_company_id, external_contract_id);


CREATE INDEX ix_facilitycompany ON facilities (facility_user_company_id);


CREATE INDEX ix_facilitycoords ON facilities (facility_latitude, facility_longitude);


CREATE INDEX ix_facilitydeleted ON facilities (facility_deleted);


CREATE INDEX ix_facilitylocation ON facilities (facility_location);


CREATE INDEX ix_facilityname ON facilities (facility_name);


CREATE INDEX ix_facilityprivate ON facilities (facility_is_private);


CREATE INDEX ix_fl_u_settings_delete ON fl_user_settings (deletion_date);


CREATE INDEX ix_forum_active ON forum_post (post_type_id, active, approved, show_date, orderby_last_reply);


CREATE INDEX ix_forum_alias ON forum_post (alias);


CREATE INDEX ix_forum_orderby_last_reply ON forum_post (orderby_last_reply);


CREATE INDEX ix_forum_orderby_show_date ON forum_post (orderby_show_date);


CREATE INDEX ix_forum_post_type ON forum_post (post_type_id);


CREATE INDEX ix_forum_show_date ON forum_post (show_date);


CREATE INDEX ix_fuel_date ON fuel_rates (date, state, fuel_rate);


CREATE INDEX ix_inactive_date ON loads (inactive_date);


CREATE INDEX ix_inv_date ON invoices (deleteDate, invoice_date, total_order_amount);


CREATE INDEX ix_inv_deleted ON invoices (deleteDate);


CREATE INDEX ix_ip ON logins (user_id, ip_address, login_id);


CREATE INDEX ix_jqmstates ON jqm_states (stateID, countryID, state);


CREATE INDEX ix_lat ON cities (Latitude);


CREATE INDEX ix_latlongmiles ON pc_routes (routingType, trailer53, stops, startLat, startLng, endLat, endLng, miles);


CREATE INDEX ix_lead_truck ON load_leads (truck_id);


CREATE INDEX ix_leads ON load_search (origin_state, date);


CREATE INDEX ix_load_alerts_user_load_log_date ON load_alerts_user_load_log (added_date);


CREATE INDEX ix_load_assignment_chain_id ON load_assignments (chain_load_assignment_id);


CREATE INDEX ix_load_assignment_child_id ON load_assignments (child_load_assignment_id);


CREATE INDEX ix_load_assignment_parent_id ON load_assignments (parent_load_assignment_id DESC);


CREATE INDEX ix_load_equip ON load_equipment (load_id, equipment_id);


CREATE INDEX ix_load_invoice_items_amount ON load_invoice_items (load_invoice_id, deleted, item_amount);


CREATE INDEX ix_load_invoice_payments_amount ON load_invoice_payments_old (load_invoice_id, deleted, payment_confirmed, payment_amount);


CREATE INDEX ix_load_invoices_deleted ON load_invoices (deleted);


CREATE INDEX ix_load_invoices_email_queue_id ON load_invoices (email_queue_id);


CREATE INDEX ix_load_invoices_invoice_total ON load_invoices (invoice_total);


CREATE INDEX ix_load_search ON load_search (user_id, site_id, search_type, load_search_id);


CREATE INDEX ix_load_search ON loads (active, site_id, load_access, post_date, equipment_ids, origin_state, origin_city, origin_lat, origin_long,
                                      destination_state, destination_city, dest_lat, dest_long, user_company_id, user_id, number_of_loads);


CREATE INDEX ix_load_search_date ON load_search (date);


CREATE INDEX ix_load_search_destination ON load_search (destination_country, destination_state, destination_city, destination_zip);


CREATE INDEX ix_load_search_origin ON load_search (origin_city, origin_state, origin_zip, origin_country);


CREATE INDEX ix_load_views_date_added ON load_views (load_id, user_id, site_id, date_added);


CREATE INDEX ix_load_views_owner_c_id ON load_views (owner_user_company_id);


CREATE INDEX ix_login_date ON logins (date);


CREATE INDEX ix_long ON cities (Longitude);


CREATE INDEX ix_not_devices ON notification_devices (notification_id, device_id, fcm_message_id);


CREATE INDEX ix_not_is_read ON notifications (user_id, is_read);


CREATE INDEX ix_not_mark_read ON notifications (user_id DESC, email_category_id);


CREATE INDEX ix_notif_is_read ON notifications (is_read);


CREATE INDEX ix_offer_recipient_status ON offer_recipients (offer_id, offer_status);


CREATE INDEX ix_opinion ON forum_opinions (forum_id, reply_id, user_id, opinion);


CREATE INDEX ix_origin_state_deleted ON loads (origin_state, deleted);


CREATE INDEX ix_payment_date ON invoice_payments (payment_date);


CREATE INDEX ix_pe_mapping ON equipment (PE_mapping);


CREATE INDEX ix_pickup_ab_company_id ON loads (pickup_ab_company_id);


CREATE INDEX ix_podcast_date ON podcasts (date);


CREATE INDEX ix_post_date ON loads (post_date);


CREATE INDEX ix_rate_dest_city ON rates (rate_destination_state, rate_destination_city, rate_destination_lat, rate_destination_long);


CREATE INDEX ix_rate_origin_city ON rates (rate_origin_state, rate_origin_city, rate_origin_lat);


CREATE INDEX ix_rate_origin_state ON rates (rate_origin_state);


CREATE INDEX ix_rate_search ON rate_search (user_id, site_id, rate_search_id);


CREATE INDEX `ix_rate_search_user id` ON rate_search (user_id, site_id);


CREATE INDEX ix_rec_email ON app_recipients (email);


CREATE INDEX ix_recent ON user_emails (user_company_id, to_user_id, date_added);


CREATE INDEX ix_req_log_date ON request_log (date);


CREATE INDEX ix_reroute_contract_id ON load_assignments (reroute_contract_id);


CREATE INDEX ix_scale_id ON scale_comments (scale_id);


CREATE INDEX ix_send_date ON email_queue (send_date, sent);


CREATE INDEX ix_seo_podcast_id ON podcasts (seo_podcast_id);


CREATE INDEX ix_set_truck_avail ON bl_user_settings (truck_available);


CREATE INDEX ix_set_truck_avail ON fl_user_settings (truck_available);


CREATE INDEX ix_sff_shippers_full ON sff_shippers (company_name, city, state, external_ab_company_id, email);


CREATE INDEX ix_sff_shippers_processed ON sff_shippers (processed);


CREATE INDEX ix_ship_to ON loads (ship_to);


CREATE INDEX ix_site_update_posted ON site_updates (site_update_posted);


CREATE INDEX ix_sitepages_slug_site_id ON site_pages (slug, site_id);


CREATE INDEX ix_soiurce ON user_company (source_company, source_company_id);


CREATE INDEX ix_source ON loads (source, source_id);


CREATE INDEX ix_start_loc ON routes (startCity);


CREATE INDEX ix_state ON lo_carriers (state);


CREATE INDEX ix_statesite ON loads (site_id, load_security, origin_state);


CREATE INDEX ix_testim_site_id ON testimonials (site_id);


CREATE INDEX ix_transid ON invoice_payments (authorize_transaction_id);


CREATE INDEX ix_truck_source ON trucks (source, source_id);


CREATE INDEX ix_tstatus ON authnet_trans (transactionStatus);


CREATE INDEX ix_ucs_car_load_count ON bl_user_company_settings (carrier_load_count);


CREATE INDEX ix_ucs_car_load_count ON fl_user_company_settings (carrier_load_count);


CREATE INDEX ix_ucs_car_load_posts ON bl_user_company_settings (carrier_load_posts);


CREATE INDEX ix_ucs_car_load_posts ON fl_user_company_settings (carrier_load_posts);


CREATE INDEX ix_ucs_membership_end_date ON fl_user_company_settings (membership_end_date);


CREATE INDEX ix_ucs_priv_load_count ON bl_user_company_settings (private_load_count);


CREATE INDEX ix_ucs_priv_load_count ON fl_user_company_settings (private_load_count);


CREATE INDEX ix_ucs_priv_load_posts ON bl_user_company_settings (private_load_posts);


CREATE INDEX ix_ucs_priv_load_posts ON fl_user_company_settings (private_load_posts);


CREATE INDEX ix_ucs_pub_load_count ON bl_user_company_settings (public_load_count);


CREATE INDEX ix_ucs_pub_load_count ON fl_user_company_settings (public_load_count);


CREATE INDEX ix_ucs_pub_load_posts ON bl_user_company_settings (public_load_posts);


CREATE INDEX ix_ucs_pub_load_posts ON fl_user_company_settings (public_load_posts);


CREATE INDEX ix_uid ON authnet_trans (user_company_id, user_id, arb_id, submitTimeUTC);


CREATE INDEX ix_user_bad_email_date ON user_info (bad_email_date);


CREATE INDEX ix_user_cell_phone ON user_info (cell_phone, cell_phone_verified);


CREATE INDEX ix_user_email ON user_info (email);


CREATE INDEX ix_user_emails_daily ON user_emails (to_user_id, date_added, email_category_id, sent);


CREATE INDEX ix_user_emails_load_search ON user_emails (load_id, user_id, site_id, date_added);


CREATE INDEX ix_user_lat ON user_info (latitude);


CREATE INDEX ix_user_long ON user_info (longitude);


CREATE INDEX ix_user_state_country ON user_info (state, country);


CREATE INDEX ix_washout_state ON washouts (origin_state);


CREATE INDEX ixcity ON cities (city);


CREATE INDEX ixcountry ON cities (country);


CREATE INDEX ixname ON cities (name);


CREATE INDEX ixstate ON cities (state);


CREATE INDEX ixzip ON cities (zip);


CREATE INDEX `last name` ON user_info (last_name);


CREATE INDEX last_reply ON forum_post (last_reply);


CREATE INDEX `lat long` ON scales (latitude, longitude);


CREATE INDEX `load security` ON loads (load_security);


CREATE INDEX load_assignments_trailer_user_company_equipment_id ON load_assignments (trailer_user_company_equipment_id);


CREATE INDEX load_assignments_truck_user_company_equipment_id ON load_assignments (truck_user_company_equipment_id);


CREATE INDEX load_id ON load_equipment (load_id);


CREATE INDEX mc ON user_company (mc_num);


CREATE INDEX membership_end_date ON bl_user_company_settings (membership_end_date);


CREATE INDEX message_group_id_idx ON message_group_users (group_id);


CREATE INDEX message_id_idx ON message_files (message_id);


CREATE INDEX non_payment ON ratings (non_payment);


CREATE INDEX `origin lat` ON loads (origin_lat);


CREATE INDEX `origin lat` ON trucks (origin_lat);


CREATE INDEX `origin long` ON loads (origin_long);


CREATE INDEX `origin long` ON trucks (origin_long);


CREATE INDEX `origin state` ON loads (origin_state);


CREATE INDEX `origin state` ON trucks (origin_state);


CREATE INDEX origin_city ON loads (origin_city);


CREATE INDEX origincity ON mileages (origin_city);


CREATE INDEX originstate ON mileages (origin_state);


CREATE INDEX overall_experience ON ratings (overall_experience);


CREATE INDEX `phone 1` ON user_info (user_phone_1);


CREATE INDEX `phone 2` ON user_info (user_phone_2);


CREATE INDEX `rater user company id` ON ratings (rater_user_company_id);


CREATE INDEX `rater user id` ON ratings (rater_user_id);


CREATE INDEX `rating id` ON rating_comment (rating_id);


CREATE INDEX sender ON email_queue (sender_user_id);


CREATE INDEX shotID ON sched_tasks (shot_id DESC);


CREATE INDEX `site id` ON forum_post (site_id);


CREATE INDEX `site visibility` ON forum_post (visibility_site_ids);


CREATE INDEX state ON dot_info (dot_info_state);


CREATE INDEX state ON scales_static (state);


CREATE INDEX state ON states (state);


CREATE INDEX `to` ON email_queue (to_user_id);


CREATE INDEX to_email ON email_queue (to_email);


CREATE INDEX to_email_idx ON messages (to_email);


CREATE INDEX to_group_id_idx ON messages (to_group_id);


CREATE INDEX to_phone_idx ON messages (to_phone);


CREATE INDEX to_user_id_idx ON messages (to_user_id);


CREATE INDEX `trailer type` ON trucks (trailer_type);


CREATE INDEX type_id ON site_updates (site_updates_type_id);


CREATE INDEX `unsub marketing` ON deletebad_email (unsubscribed_marketing);


CREATE INDEX `user company` ON loads (user_company_id);


CREATE INDEX `user company` ON ratings (user_company_id);


CREATE INDEX `user company id` ON commodity_listing (user_company_id);


CREATE INDEX `user company id` ON trucks (user_company_id);


CREATE INDEX `user id` ON commodity_listing (user_id);


CREATE INDEX `user id` ON forum_post (user_id);


CREATE INDEX `user id` ON load_search (user_id, site_id, search_type, date);


CREATE INDEX `user id` ON truck_search (user_id);


CREATE INDEX `user id` ON user_comment (user_id);


CREATE INDEX `user id` ON user_followup (user_id);


CREATE INDEX `user_company id` ON user_company_comment (user_company_id);


CREATE INDEX user_company_id ON invoices (user_company_id);


CREATE INDEX user_company_id ON orders (user_company_id);


CREATE INDEX user_company_id ON subscriptions (user_company_id);


CREATE INDEX user_id ON invoices (user_id);


CREATE INDEX user_id ON logins (user_id, login_id);


CREATE INDEX user_id ON orders (user_id);


CREATE INDEX user_id ON subscriptions (user_id);


CREATE INDEX user_id ON user_company (company_owner_id);


CREATE INDEX user_id_idx ON message_group_users (user_id);


CREATE INDEX user_info_last_load_assignments_truck_user_company_equipment_id ON user_info (last_truck_user_company_equipment_id);


CREATE INDEX user_info_last_trailer_user_company_equipment_id ON user_info (last_trailer_user_company_equipment_id);


CREATE INDEX username ON user_info (username);


CREATE INDEX weight ON banners (banner_weight);


ALTER TABLE sched_tasks
    ADD CONSTRAINT SCHED_SHOTS_shot_id FOREIGN KEY (shot_id) REFERENCES sched_tasks_shots (shot_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE ab_user_group_ab_users
    ADD CONSTRAINT ab_user_group_ab_users_ibfk_1 FOREIGN KEY (ab_user_group_id) REFERENCES ab_user_groups (ab_user_group_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE ab_user_group_ab_users
    ADD CONSTRAINT ab_user_group_ab_users_ibfk_2 FOREIGN KEY (ab_user_id) REFERENCES ab_users (ab_user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE ab_user_roles_ref
    ADD CONSTRAINT ab_user_roles_ref_ibfk_1 FOREIGN KEY (ab_user_role_id) REFERENCES ab_user_roles (ab_user_role_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE ab_user_roles_ref
    ADD CONSTRAINT ab_user_roles_ref_ibfk_2 FOREIGN KEY (ab_user_id) REFERENCES ab_users (ab_user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE ab_users
    ADD CONSTRAINT ab_users_ibfk_1 FOREIGN KEY (ab_company_id) REFERENCES ab_companies (ab_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE activities
    ADD CONSTRAINT activities_ibfk_1 FOREIGN KEY (activity_type_id) REFERENCES activity_types (activity_type_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE activities
    ADD CONSTRAINT activities_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE api_key_endpoints
    ADD CONSTRAINT api_key_endpoints_ibfk_1 FOREIGN KEY (api_endpoint_id) REFERENCES api_endpoints (api_endpoint_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE api_key_endpoints
    ADD CONSTRAINT api_key_endpoints_ibfk_2 FOREIGN KEY (api_key_id) REFERENCES api_keys (api_key_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE api_key_integration_permissions
    ADD CONSTRAINT api_key_integration_permissions_ibfk_1 FOREIGN KEY (api_key_id) REFERENCES api_keys (api_key_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE api_key_integration_permissions
    ADD CONSTRAINT api_key_integration_permissions_ibfk_2 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE app_load_surcharges
    ADD CONSTRAINT app_load_surcharges_ibfk_1 FOREIGN KEY (load_id) REFERENCES app_loads (load_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE app_recipient_history
    ADD CONSTRAINT app_recipient_history_ibfk_1 FOREIGN KEY (rec_id) REFERENCES app_recipients (rec_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE bl_user_company_settings
    ADD CONSTRAINT bl_user_company_settings_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE bl_user_settings
    ADD CONSTRAINT bl_user_settings_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE bl_user_settings
    ADD CONSTRAINT bl_user_settings_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE blocked_companies
    ADD CONSTRAINT blocked_companies_ibfk_1 FOREIGN KEY (blocked_user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE blocked_companies
    ADD CONSTRAINT blocked_companies_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE blocked_errors
    ADD CONSTRAINT blocked_errors_ibfk_1 FOREIGN KEY (blocked_history_id) REFERENCES blocked_history (blocked_history_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE blocked_requests
    ADD CONSTRAINT blocked_requests_ibfk_1 FOREIGN KEY (blocked_history_id) REFERENCES blocked_history (blocked_history_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE carrier_profile_equipment_run
    ADD CONSTRAINT carrier_profile_equipment_run_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE carrier_profile_files
    ADD CONSTRAINT carrier_profile_files_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE carrier_profile_files
    ADD CONSTRAINT carrier_profile_files_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE carrier_profile_states_run
    ADD CONSTRAINT carrier_profile_states_run_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE commodity_listing
    ADD CONSTRAINT commodity_listing_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE commodity_listing
    ADD CONSTRAINT commodity_listing_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE company_finder_criteria_search
    ADD CONSTRAINT company_finder_criteria_search_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE company_finder_radius_search
    ADD CONSTRAINT company_finder_radius_search_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE company_finder_views
    ADD CONSTRAINT company_finder_views_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE company_searches
    ADD CONSTRAINT company_searches_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE deleteload_analytics_views
    ADD CONSTRAINT deleteload_analytics_views_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE email_queue
    ADD CONSTRAINT email_queue_ibfk_1 FOREIGN KEY (email_category_id) REFERENCES email_categories (email_category_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE favorite_companies
    ADD CONSTRAINT favorite_companies_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE favorite_companies
    ADD CONSTRAINT favorite_companies_ibfk_2 FOREIGN KEY (site_id) REFERENCES sites (id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE favorite_companies
    ADD CONSTRAINT favorite_companies_ibfk_3 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE favorite_fmcsa
    ADD CONSTRAINT favorite_fmcsa_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE favorite_lanes
    ADD CONSTRAINT favorite_lanes_ibfk_1 FOREIGN KEY (site_id) REFERENCES sites (id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE favorite_lanes
    ADD CONSTRAINT favorite_lanes_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE favorite_loads
    ADD CONSTRAINT favorite_loads_ibfk_1 FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE favorite_loads
    ADD CONSTRAINT favorite_loads_ibfk_2 FOREIGN KEY (site_id) REFERENCES sites (id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE favorite_loads
    ADD CONSTRAINT favorite_loads_ibfk_3 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE file_views
    ADD CONSTRAINT file_views_ibfk_1 FOREIGN KEY (file_id) REFERENCES files (file_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE ab_company_files
    ADD CONSTRAINT fk_ab_company_files_ab_company_id FOREIGN KEY (ab_company_id) REFERENCES ab_companies (ab_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE commodities
    ADD CONSTRAINT fk_commodities_user_company_id FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE commodity_equipments
    ADD CONSTRAINT fk_commodity_equip_comm_id FOREIGN KEY (commodity_id) REFERENCES commodities (commodity_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE contracts
    ADD CONSTRAINT fk_contracts_c_id FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE contracts
    ADD CONSTRAINT fk_contracts_commodity_id FOREIGN KEY (commodity_id) REFERENCES commodities (commodity_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE external_rate_product_categories
    ADD CONSTRAINT fk_erpc_user_company_id FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_assignments
    ADD CONSTRAINT fk_la_load_id FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE loads
    ADD CONSTRAINT fk_load_commodity_id FOREIGN KEY (commodity_id) REFERENCES commodities (commodity_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE schedules_log_sync_mcp_status
    ADD CONSTRAINT fk_schedules_log FOREIGN KEY (schedules_log_id) REFERENCES schedules_log (schedules_log_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE fl_user_company_settings
    ADD CONSTRAINT fl_user_company_settings_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE fl_user_settings
    ADD CONSTRAINT fl_user_settings_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE fl_user_settings
    ADD CONSTRAINT fl_user_settings_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE fmcsa_emails
    ADD CONSTRAINT fmcsa_emails_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE fmcsa_emails
    ADD CONSTRAINT fmcsa_emails_ibfk_2 FOREIGN KEY (site_id) REFERENCES sites (id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE forum_files
    ADD CONSTRAINT forum_files_ibfk_1 FOREIGN KEY (forum_id) REFERENCES forum_post (forum_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE forum_files
    ADD CONSTRAINT forum_files_ibfk_2 FOREIGN KEY (reply_id) REFERENCES forum_reply (reply_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE forum_opinions
    ADD CONSTRAINT forum_opinions_ibfk_1 FOREIGN KEY (forum_id) REFERENCES forum_post (forum_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE forum_opinions
    ADD CONSTRAINT forum_opinions_ibfk_2 FOREIGN KEY (reply_id) REFERENCES forum_reply (reply_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE forum_post
    ADD CONSTRAINT forum_post_ibfk_1 FOREIGN KEY (site_id) REFERENCES sites (id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE forum_post
    ADD CONSTRAINT forum_post_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE forum_reply
    ADD CONSTRAINT forum_reply_ibfk_1 FOREIGN KEY (forum_id) REFERENCES forum_post (forum_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE forum_reply
    ADD CONSTRAINT forum_reply_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE forum_subscription
    ADD CONSTRAINT forum_subscription_ibfk_1 FOREIGN KEY (forum_id) REFERENCES forum_post (forum_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE forum_subscription
    ADD CONSTRAINT forum_subscription_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE invoice_files
    ADD CONSTRAINT invoice_files_ibfk_1 FOREIGN KEY (invoice_id) REFERENCES invoices (invoice_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE invoice_items
    ADD CONSTRAINT invoice_items_ibfk_1 FOREIGN KEY (invoice_id) REFERENCES invoices (invoice_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE invoice_items
    ADD CONSTRAINT invoice_items_ibfk_2 FOREIGN KEY (invoice_item_type_id) REFERENCES invoice_item_types (invoice_item_type_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE invoice_notes
    ADD CONSTRAINT invoice_notes_ibfk_1 FOREIGN KEY (invoice_id) REFERENCES invoices (invoice_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE invoice_payments
    ADD CONSTRAINT invoice_payments_ibfk_1 FOREIGN KEY (invoice_id) REFERENCES invoices (invoice_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_carrier_comments
    ADD CONSTRAINT lo_carrier_comments_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_carrier_comments
    ADD CONSTRAINT lo_carrier_comments_ibfk_2 FOREIGN KEY (lo_carrier_id) REFERENCES lo_carriers (lo_carrier_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_carrier_equipment
    ADD CONSTRAINT lo_carrier_equipment_ibfk_1 FOREIGN KEY (lo_carrier_id) REFERENCES lo_carriers (lo_carrier_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_carrier_loads
    ADD CONSTRAINT lo_carrier_loads_ibfk_1 FOREIGN KEY (lo_carrier_id) REFERENCES lo_carriers (lo_carrier_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE lo_carrier_loads
    ADD CONSTRAINT lo_carrier_loads_ibfk_2 FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_carrier_states_run
    ADD CONSTRAINT lo_carrier_states_run_ibfk_1 FOREIGN KEY (lo_carrier_id) REFERENCES lo_carriers (lo_carrier_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_carriers
    ADD CONSTRAINT lo_carriers_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_user_company_settings
    ADD CONSTRAINT lo_user_company_settings_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_user_settings
    ADD CONSTRAINT lo_user_settings_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE lo_user_settings
    ADD CONSTRAINT lo_user_settings_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_alert_notifications
    ADD CONSTRAINT load_alert_notifications_ibfk_1 FOREIGN KEY (load_alert_id) REFERENCES load_alerts (load_alert_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_alerts
    ADD CONSTRAINT load_alerts_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_alerts_user_load_log
    ADD CONSTRAINT load_alerts_user_load_log_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_analytics_emails
    ADD CONSTRAINT load_analytics_emails_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_analytics_search
    ADD CONSTRAINT load_analytics_search_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_assignment_confirmation_log
    ADD CONSTRAINT load_assignment_confirmation_log_ibfk_1 FOREIGN KEY (load_assignment_id) REFERENCES load_assignments (load_assignment_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_assignment_confirmation_log
    ADD CONSTRAINT load_assignment_confirmation_log_ibfk_2 FOREIGN KEY (log_by_user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_assignment_files
    ADD CONSTRAINT load_assignment_files_ibfk_1 FOREIGN KEY (file_id) REFERENCES files (file_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_assignment_files
    ADD CONSTRAINT load_assignment_files_ibfk_2 FOREIGN KEY (load_assignment_id) REFERENCES load_assignments (load_assignment_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_assignment_geo_history
    ADD CONSTRAINT load_assignment_geo_history_ibfk_1 FOREIGN KEY (load_assignment_id) REFERENCES load_assignments (load_assignment_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_assignment_surcharges
    ADD CONSTRAINT load_assignment_surcharges_ibfk_1 FOREIGN KEY (load_assignment_id) REFERENCES load_assignments (load_assignment_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_ibfk_1 FOREIGN KEY (hiring_ab_user_id) REFERENCES ab_users (ab_user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_ibfk_3 FOREIGN KEY (to_ab_user_id) REFERENCES ab_users (ab_user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_ibfk_4 FOREIGN KEY (to_load_id) REFERENCES loads (load_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_ibfk_5 FOREIGN KEY (to_user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_ibfk_6 FOREIGN KEY (to_user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_ibfk_7 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_ibfk_8 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_trailer_user_company_equipment_id FOREIGN KEY (trailer_user_company_equipment_id) REFERENCES user_company_equipments (user_company_equipment_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE load_assignments
    ADD CONSTRAINT load_assignments_truck_user_company_equipment_id FOREIGN KEY (truck_user_company_equipment_id) REFERENCES user_company_equipments (user_company_equipment_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE load_emails
    ADD CONSTRAINT load_emails_ibfk_1 FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_emails
    ADD CONSTRAINT load_emails_ibfk_2 FOREIGN KEY (site_id) REFERENCES sites (id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE load_emails
    ADD CONSTRAINT load_emails_ibfk_3 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_equipment
    ADD CONSTRAINT load_equipment_ibfk_1 FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE load_equipment
    ADD CONSTRAINT load_equipment_ibfk_2 FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_flags
    ADD CONSTRAINT load_flags_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_invoice_items
    ADD CONSTRAINT load_invoice_items_ibfk_1 FOREIGN KEY (load_invoice_id) REFERENCES load_invoices (load_invoice_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_invoice_items
    ADD CONSTRAINT load_invoice_items_ibfk_2 FOREIGN KEY (load_assignment_id) REFERENCES load_assignments (load_assignment_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_invoice_notes
    ADD CONSTRAINT load_invoice_notes_ibfk_1 FOREIGN KEY (load_invoice_id) REFERENCES load_invoices (load_invoice_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_invoice_payments_old
    ADD CONSTRAINT load_invoice_payments_old_ibfk_1 FOREIGN KEY (load_invoice_id) REFERENCES load_invoices (load_invoice_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_invoices
    ADD CONSTRAINT load_invoices_ibfk_1 FOREIGN KEY (bill_to_ab_company_id) REFERENCES ab_companies (ab_company_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_invoices
    ADD CONSTRAINT load_invoices_ibfk_2 FOREIGN KEY (hiring_ab_user_id) REFERENCES ab_users (ab_user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_invoices
    ADD CONSTRAINT load_invoices_ibfk_3 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_invoices
    ADD CONSTRAINT load_invoices_ibfk_4 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE load_leads
    ADD CONSTRAINT load_leads_ibfk_1 FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_leads
    ADD CONSTRAINT load_leads_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_offers
    ADD CONSTRAINT load_offers_ibfk_1 FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE load_offers
    ADD CONSTRAINT load_offers_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE load_search
    ADD CONSTRAINT load_search_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_views
    ADD CONSTRAINT load_views_ibfk_1 FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE load_views
    ADD CONSTRAINT load_views_ibfk_2 FOREIGN KEY (site_id) REFERENCES sites (id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE load_views
    ADD CONSTRAINT load_views_ibfk_3 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE loads
    ADD CONSTRAINT loads_ibfk_1 FOREIGN KEY (rate_product_category_id) REFERENCES rate_product_categories (rate_product_category_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE logins
    ADD CONSTRAINT logins_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE message_email_metrics
    ADD CONSTRAINT message_email_metrics_ibfk_1 FOREIGN KEY (email_metrics_id) REFERENCES email_metrics (email_metrics_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE message_email_metrics
    ADD CONSTRAINT message_email_metrics_ibfk_2 FOREIGN KEY (message_id) REFERENCES messages (message_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE message_files
    ADD CONSTRAINT message_files_ibfk_1 FOREIGN KEY (message_id) REFERENCES messages (message_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE message_group_users
    ADD CONSTRAINT message_group_users_ibfk_1 FOREIGN KEY (group_id) REFERENCES message_groups (group_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE message_group_users
    ADD CONSTRAINT message_group_users_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE messages
    ADD CONSTRAINT messages_ibfk_1 FOREIGN KEY (to_group_id) REFERENCES message_groups (group_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE offer_recipients
    ADD CONSTRAINT offer_recipients_ibfk_1 FOREIGN KEY (ab_user_group_id) REFERENCES ab_user_groups (ab_user_group_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE offer_recipients
    ADD CONSTRAINT offer_recipients_ibfk_2 FOREIGN KEY (ab_user_id) REFERENCES ab_users (ab_user_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE offer_recipients
    ADD CONSTRAINT offer_recipients_ibfk_3 FOREIGN KEY (offer_id) REFERENCES offers (offer_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE pc_route_stops
    ADD CONSTRAINT pc_route_stops_ibfk_1 FOREIGN KEY (cityID) REFERENCES cities (ID) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE pc_route_stops
    ADD CONSTRAINT pc_route_stops_ibfk_2 FOREIGN KEY (routeID) REFERENCES pc_routes (routeID) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE pc_routes
    ADD CONSTRAINT pc_routes_ibfk_1 FOREIGN KEY (endCityID) REFERENCES cities (ID) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE pc_routes
    ADD CONSTRAINT pc_routes_ibfk_2 FOREIGN KEY (startCityID) REFERENCES cities (ID) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE pc_searches
    ADD CONSTRAINT pc_searches_ibfk_1 FOREIGN KEY (routeid) REFERENCES pc_routes (routeID) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE rate_product_category_equipments
    ADD CONSTRAINT rate_product_category_equipments_ibfk_1 FOREIGN KEY (rate_product_category_id) REFERENCES rate_product_categories (rate_product_category_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE rate_search
    ADD CONSTRAINT rate_search_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE ratings
    ADD CONSTRAINT ratings_ibfk_1 FOREIGN KEY (rater_user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE reminder_queue
    ADD CONSTRAINT reminder_queue_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE sfc_user_company_settings
    ADD CONSTRAINT sfc_user_company_settings_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE sfc_user_settings
    ADD CONSTRAINT sfc_user_settings_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE sfc_user_settings
    ADD CONSTRAINT sfc_user_settings_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE sff_prospects
    ADD CONSTRAINT sff_prospects_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE site_updates
    ADD CONSTRAINT site_updates_ibfk_1 FOREIGN KEY (site_updates_type_id) REFERENCES site_updates_type (site_updates_type_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE subscriptions
    ADD CONSTRAINT subscriptions_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE truck_emails
    ADD CONSTRAINT truck_emails_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE truck_search
    ADD CONSTRAINT truck_search_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE truck_views
    ADD CONSTRAINT truck_views_ibfk_1 FOREIGN KEY (truck_id) REFERENCES trucks (truck_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE truck_views
    ADD CONSTRAINT truck_views_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_comment
    ADD CONSTRAINT user_comment_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_company_comment
    ADD CONSTRAINT user_company_comment_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_company_mcp_integrations
    ADD CONSTRAINT user_company_mcp_integrations_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_company_user_types
    ADD CONSTRAINT user_company_user_types_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_devices
    ADD CONSTRAINT user_devices_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_email_categories
    ADD CONSTRAINT user_email_categories_ibfk_1 FOREIGN KEY (email_category_id) REFERENCES email_categories (email_category_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_email_categories
    ADD CONSTRAINT user_email_categories_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_email_schedules
    ADD CONSTRAINT user_email_schedules_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_emails
    ADD CONSTRAINT user_emails_ibfk_1 FOREIGN KEY (load_id) REFERENCES loads (load_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_emails
    ADD CONSTRAINT user_emails_ibfk_2 FOREIGN KEY (site_id) REFERENCES sites (id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE user_emails
    ADD CONSTRAINT user_emails_ibfk_3 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_files
    ADD CONSTRAINT user_files_ibfk_1 FOREIGN KEY (file_id) REFERENCES files (file_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE user_files
    ADD CONSTRAINT user_files_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_followup
    ADD CONSTRAINT user_followup_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_geo_history
    ADD CONSTRAINT user_geo_history_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_geo_tracking
    ADD CONSTRAINT user_geo_tracking_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE user_groups
    ADD CONSTRAINT user_groups_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_groups
    ADD CONSTRAINT user_groups_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE user_groups_ref
    ADD CONSTRAINT user_groups_ref_ibfk_1 FOREIGN KEY (user_group_id) REFERENCES user_groups (user_group_id) ON UPDATE CASCADE ON DELETE RESTRICT;


ALTER TABLE user_groups_ref
    ADD CONSTRAINT user_groups_ref_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_info_app
    ADD CONSTRAINT user_info_app_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE user_info
    ADD CONSTRAINT user_info_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE user_info
    ADD CONSTRAINT user_info_last_load_assignments_truck_user_company_equipment_id FOREIGN KEY (last_truck_user_company_equipment_id) REFERENCES user_company_equipments (user_company_equipment_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE user_info
    ADD CONSTRAINT user_info_last_trailer_user_company_equipment_id FOREIGN KEY (last_trailer_user_company_equipment_id) REFERENCES user_company_equipments (user_company_equipment_id) ON UPDATE RESTRICT ON DELETE RESTRICT;


ALTER TABLE user_role_permissions
    ADD CONSTRAINT user_role_permissions_ibfk_1 FOREIGN KEY (user_permission_id) REFERENCES user_permissions (user_permission_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_role_permissions
    ADD CONSTRAINT user_role_permissions_ibfk_2 FOREIGN KEY (user_role_id) REFERENCES user_roles (user_role_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_roles_ref
    ADD CONSTRAINT user_roles_ref_ibfk_1 FOREIGN KEY (user_role_id) REFERENCES user_roles (user_role_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_roles_ref
    ADD CONSTRAINT user_roles_ref_ibfk_2 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE user_vars
    ADD CONSTRAINT user_vars_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE RESTRICT ON DELETE CASCADE;


ALTER TABLE verifications_email
    ADD CONSTRAINT verifications_email_ibfk_1 FOREIGN KEY (user_id) REFERENCES user_info (user_id) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE verifications
    ADD CONSTRAINT verifications_ibfk_1 FOREIGN KEY (user_company_id) REFERENCES user_company (user_company_id) ON UPDATE RESTRICT ON DELETE CASCADE;

-- changeset system:1729142640065-2 endDelimiter:/
-- routines
create function bearing(lat1 double, lng1 double, lat2 double, lng2 double) returns double
BEGIN
    DECLARE dLng DECIMAL(30, 15);
    DECLARE y DECIMAL(30, 15);
    DECLARE x DECIMAL(30, 15);
    DECLARE bearing DECIMAL(30, 15);
    SET dLng = RADIANS(lng2) - RADIANS(lng1);
    SET y = SIN(dLng) * COS(RADIANS(lat2));
    SET x = (COS(RADIANS(lat1)) * SIN(RADIANS(lat2))) - (SIN(RADIANS(lat1)) * COS(RADIANS(lat2)) * COS(dLng));
    SET bearing = DEGREES(ATAN2(y, x));
    RETURN bearing;
END
/

create function bearing_direction(bearing double) returns varchar(2)
BEGIN
    IF (bearing is NULL) THEN
        RETURN '';
    ELSEIF bearing > -22.5 and bearing <= 22.5 THEN
        return 'N';
    ELSEIF bearing > 22.5 and bearing <= 67.5 THEN
        return 'NE';
    ELSEIF bearing > 67.5 and bearing <= 112.5 THEN
        return 'E';
    ELSEIF bearing > 112.5 and bearing <= 157.5 THEN
        return 'SE';
    ELSEIF bearing > 157.5 and bearing <= 180.0 THEN
        return 'S';
    ELSEIF bearing > -180.0 and bearing <= -157.5 THEN
        return 'S';
    ELSEIF bearing > -157.5 and bearing <= -112.5 THEN
        return 'SW';
    ELSEIF bearing > -112.5 and bearing <= -67.5 THEN
        return 'W';
    ELSEIF bearing > -67.5 and bearing <= -22.5 THEN
        return 'NW';
    ELSE
        RETURN '';
    END IF;
END
/

create function CAP_FIRST(input varchar(255)) returns varchar(255)
    deterministic
BEGIN
    DECLARE len INT;
    DECLARE i INT;
    SET len = CHAR_LENGTH(input);
    SET input = LOWER(input);
    SET i = 0;
    WHILE (i < len)
        DO
            IF (MID(input, i, 1) = ' ' OR i = 0) THEN
                IF (i < len) THEN
                    SET input = CONCAT(
                            LEFT(input, i),
                            UPPER(MID(input, i + 1, 1)),
                            RIGHT(input, len - i - 1)
                                );
                END IF;
            END IF;
            SET i = i + 1;
        END WHILE;
    RETURN input;
END
/

CREATE FUNCTION natural_sort(s VARCHAR(255)) RETURNS VARCHAR(1024)
    DETERMINISTIC
    NO SQL
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE result VARCHAR(1024) DEFAULT '';
    DECLARE len INT;
    DECLARE ch CHAR(1);
    DECLARE num_str VARCHAR(255) DEFAULT '';
    DECLARE in_number BOOLEAN DEFAULT FALSE;

    SET len = CHAR_LENGTH(s);
    WHILE i <= len
        DO
            SET ch = SUBSTRING(s, i, 1);
            IF ch BETWEEN '0' AND '9' THEN
                SET num_str = CONCAT(num_str, ch);
                SET in_number = TRUE;
            ELSE
                IF in_number THEN
                    SET result = CONCAT(result, LPAD(num_str, 20, '0'));
                    SET num_str = '';
                    SET in_number = FALSE;
                END IF;
                SET result = CONCAT(result, ch);
            END IF;
            SET i = i + 1;
        END WHILE;
    -- Append any remaining numbers at the end of the string
    IF in_number THEN
        SET result = CONCAT(result, LPAD(num_str, 20, '0'));
    END IF;
    RETURN result;
END
/

create procedure RotateNotificationDevices(IN newPartValue datetime)
BEGIN
    -- Setup
    DECLARE keepStmt VARCHAR(2000) DEFAULT @stmt;
    DECLARE partitionToDrop VARCHAR(64);
    -- Split the pMAX partition to 2
    SET @stmt = CONCAT('ALTER TABLE notification_devices
							REORGANIZE PARTITION pMAX INTO (
							PARTITION
							p', DATE_FORMAT(newPartValue, '%Y%m'),
                       ' VALUES LESS THAN (TO_DAYS(\'',
                       DATE_FORMAT(newPartValue, '%Y-%m-%d'),
                       '\'
                       )),
                       PARTITION pMAX VALUES LESS THAN MAXVALUE
                       )');

    PREPARE pStmt FROM @stmt;
    EXECUTE pStmt;
    DEALLOCATE PREPARE pStmt;
    -- Find and drop the first partition in the table.
    SELECT partition_name
    INTO partitionToDrop
    FROM INFORMATION_SCHEMA.PARTITIONS
    WHERE table_name = 'notification_devices'
      AND partition_ordinal_position = 1;
    SET @stmt = CONCAT('ALTER TABLE notification_devices DROP PARTITION ',
                       partitionToDrop);
    PREPARE pStmt FROM @stmt;
    EXECUTE pStmt;
    DEALLOCATE PREPARE pStmt;
    -- Cleanup
    SET @stmt = keepStmt;
END
/

create procedure RotateNotifications(IN newPartValue datetime)
BEGIN
    -- Setup
    DECLARE keepStmt VARCHAR(2000) DEFAULT @stmt;
    DECLARE partitionToDrop VARCHAR(64);
    -- Split the pMAX partition to 2
    SET @stmt = CONCAT('ALTER TABLE notifications
							REORGANIZE PARTITION pMAX INTO (
							PARTITION
							p', DATE_FORMAT(newPartValue, '%Y%m'),
                       ' VALUES LESS THAN (TO_DAYS(\'',
                       DATE_FORMAT(newPartValue, '%Y-%m-%d'),
                       '\'
                       )),
                       PARTITION pMAX VALUES LESS THAN MAXVALUE
                       )');

    PREPARE pStmt FROM @stmt;
    EXECUTE pStmt;
    DEALLOCATE PREPARE pStmt;
    -- Find and drop the first partition in the table.
    SELECT partition_name
    INTO partitionToDrop
    FROM INFORMATION_SCHEMA.PARTITIONS
    WHERE table_name = 'notifications'
      AND partition_ordinal_position = 1;
    SET @stmt = CONCAT('ALTER TABLE notifications DROP PARTITION ',
                       partitionToDrop);
    PREPARE pStmt FROM @stmt;
    EXECUTE pStmt;
    DEALLOCATE PREPARE pStmt;
    -- Cleanup
    SET @stmt = keepStmt;
END
/

create procedure RotateRequestLog(IN newPartValue datetime)
BEGIN
    -- Setup
    DECLARE keepStmt VARCHAR(2000) DEFAULT @stmt;
    DECLARE partitionToDrop VARCHAR(64);
    -- Add a new partition using the input date for a value limit.
    SET @stmt = CONCAT('ALTER TABLE request_log ADD PARTITION (PARTITION p',
                       DATE_FORMAT(newPartValue, '%Y%m%d'),
                       ' VALUES LESS THAN (TO_DAYS(\'',
                       DATE_FORMAT(newPartValue, '%Y-%m-%d'),
                       '\')))');
    PREPARE pStmt FROM @stmt;
    EXECUTE pStmt;
    DEALLOCATE PREPARE pStmt;
    -- Find and drop the first partition in the table.
    SELECT partition_name
    INTO partitionToDrop
    FROM INFORMATION_SCHEMA.PARTITIONS
    WHERE table_name = 'request_log'
      AND partition_ordinal_position = 1;
    SET @stmt = CONCAT('ALTER TABLE request_log DROP PARTITION ',
                       partitionToDrop);
    PREPARE pStmt FROM @stmt;
    EXECUTE pStmt;
    DEALLOCATE PREPARE pStmt;
    -- Cleanup
    SET @stmt = keepStmt;
END
/

-- changeset system:1729142640065-3
-- events
create event rotate_notification_devices_job on schedule
    every '1' MONTH
        starts '2023-01-01 00:00:00'
    enable
    do
    call RotateNotificationDevices(curdate() + interval 1 MONTH);

create event rotate_notifications_job on schedule
    every '1' MONTH
        starts '2023-01-01 00:00:00'
    enable
    do
    call RotateNotifications(curdate() + interval 1 MONTH);

create event rotate_request_log_job on schedule
    every '1' DAY
        starts '2024-02-07 18:23:07'
    enable
    do
    call RotateRequestLog(curdate() + interval 2 DAY);