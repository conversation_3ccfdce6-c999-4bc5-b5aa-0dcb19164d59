-- liquibase formatted sql

-- changeset theo:20250319-1200
insert into api_endpoints(category, method, path, description)
values ('ELD', 'DELETE', '/eld/authorization', 'Remove ELD provider authorization');

insert into api_key_endpoints(api_key_id, api_endpoint_id)
select k.api_key_id, e.api_endpoint_id
from api_keys k
         join api_endpoints e
where k.api_key_id in (1, 2)
  and method = 'DELETE'
  and path = '/eld/authorization'
  and e.api_endpoint_id not in (select api_endpoint_id
                                from api_key_endpoints
                                where api_key_id in (1, 2))
order by k.api_key_id, e.api_endpoint_id;

-- changeset theo:20250320-0900
update api_endpoints
set path = '/eld/vehicle_locations'
where path = '/eld/vehicle_location';