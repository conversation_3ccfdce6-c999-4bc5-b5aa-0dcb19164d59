-- liquibase formatted sql

-- changeset john:20250116

ALTER TABLE facilities

    ADD COLUMN `total_reviews` INT NOT NULL DEFAULT 0 AFTER `facility_mailing_location`,
    ADD COLUMN `avr_rating` DOUBLE NULL DEFAULT null AFTER `total_reviews`,
    ADD COLUMN `equipment_ids` VARCHAR(50) NOT NULL DEFAULT '' AFTER `avr_rating`,
    ADD COLUMN `equipment_names` VARCHAR(300) NOT NULL DEFAULT '' AFTER `equipment_ids`,
    ADD COLUMN `view_count` INT NOT NULL DEFAULT 0 AFTER `equipment_names`,
    ADD COLUMN `added_date` DATETIME NOT NULL DEFAULT NOW() AFTER `view_count`,
    ADD COLUMN `added_by_user_id` INT NOT NULL AFTER `added_date`,
    ADD COLUMN `updated_date` DATETIME AFTER `added_by_user_id`,
    ADD COLUMN `updated_by_user_id` INT AFTER `updated_date`,
    ADD COLUMN `deleted_by_user_id` INT AFTER `updated_by_user_id`,
    ADD COLUMN `public_restrooms` TINYINT(1) NOT NULL DEFAULT 0 AFTER `deleted_by_user_id`,
    ADD COLUMN `overnight_parking` TINYINT(1) NOT NULL DEFAULT 0 AFTER `public_restrooms`,
    ADD COLUMN `driver_lounge` TINYINT(1) NOT NULL DEFAULT 0 AFTER `overnight_parking`,
    ADD COLUMN `onsite_scale` TINYINT(1) NOT NULL DEFAULT 0 AFTER `driver_lounge`,
    ADD COLUMN `appointment_required` TINYINT(1) NOT NULL DEFAULT 0 AFTER `onsite_scale`,
    ADD COLUMN `washout_required` TINYINT(1) NOT NULL DEFAULT 0 AFTER `appointment_required`,
    ADD COLUMN `hours_of_operation` VARCHAR(1000) NOT NULL DEFAULT '' AFTER `washout_required`;

ALTER TABLE facilities
    change column `facility_outside_record_id` `outside_record_id` int DEFAULT NULL COMMENT 'ID from charles or barchar',
    change column `facility_outside_source` `outside_source` varchar(50) NOT NULL DEFAULT '' COMMENT 'company where we got the data from',
    change column `facility_name` `name` varchar(255) NOT NULL DEFAULT '',
    change column `facility_location_name` `location_name` varchar(255) NOT NULL DEFAULT '' COMMENT 'Common name of the facility, might not match city,st',
    change column `facility_type` `type` varchar(255) NOT NULL DEFAULT '',
    change column `facility_city` `city` varchar(255) NOT NULL DEFAULT '',
    change column `facility_state` `state` varchar(25) NOT NULL DEFAULT '',
    change column `facility_address` `address` varchar(1000) NOT NULL DEFAULT '',
    change column `facility_zip` `zip` varchar(25) NOT NULL DEFAULT '',
    change column `facility_county` `county` varchar(100) NOT NULL DEFAULT '',
    change column `facility_longitude` `longitude` double DEFAULT NULL,
    change column `facility_latitude` `latitude` double DEFAULT NULL,
    change column `facility_website` `website` varchar(255) NOT NULL DEFAULT '',
    change column `facility_phone_1` `phone_1` varchar(50) NOT NULL DEFAULT '',
    change column `facility_fax` `fax` varchar(50) NOT NULL DEFAULT '',
    change column `facility_email` `email` varchar(255) NOT NULL DEFAULT '',
    change column `facility_contact_name` `contact_name` varchar(255) NOT NULL DEFAULT '',
    change column `facility_contact_title` `contact_title` varchar(255) NOT NULL DEFAULT '',
    change column `facility_mailing_address` `mailing_address` varchar(1000) NOT NULL DEFAULT '',
    change column `facility_mailing_city` `mailing_city` varchar(255) NOT NULL DEFAULT '',
    change column `facility_mailing_state` `mailing_state` varchar(25) NOT NULL DEFAULT '',
    change column `facility_mailing_zip` `mailing_zip` varchar(25) NOT NULL DEFAULT '',
    change column `facility_storage_amount` `storage_amount` int DEFAULT NULL,
    change column `facility_railroad` `railroad` varchar(50) NOT NULL DEFAULT '',
    change column `facility_user_company_id` `user_company_id` int DEFAULT NULL COMMENT 'user_company_id of the company that entered this facility data',
    change column `facility_is_private` `is_private` tinyint NOT NULL DEFAULT '0' COMMENT 'if 1 then this facility should not be shown to any other companies besides the company that added it',
    change column `facility_address_for_coordinates` `address_for_coordinates` varchar(500) NOT NULL DEFAULT '' COMMENT 'address used for coordinates by charles data',
    change column `facility_deleted` `deleted` tinyint NOT NULL DEFAULT '0',
    change column `facility_location` `location` varchar(255) NOT NULL DEFAULT '',
    change column `facility_country` `country` varchar(100) NOT NULL DEFAULT '',
    change column `facility_mailing_country` `mailing_country` varchar(100) NOT NULL DEFAULT '',
    change column `facility_mailing_location` `mailing_location` varchar(255) NOT NULL DEFAULT '';


CREATE TABLE facility_equipments (
    facility_id INT NOT NULL,
    equipment_id INT NOT NULL,
    PRIMARY KEY (facility_id, equipment_id)
);

CREATE TABLE facility_searches (
    facility_search_id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    search_term VARCHAR(255) NOT NULL,
    search_date DATETIME NOT NULL,
    PRIMARY KEY (facility_search_id)
);

CREATE TABLE facility_reviews (
    facility_review_id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    facility_id INT NOT NULL,
    review TEXT NOT NULL,
    rating INT NOT NULL,
    review_date DATETIME NOT NULL,
    PRIMARY KEY (facility_review_id)
);

CREATE TABLE facility_wait_times (
    facility_wait_time_id INT NOT NULL AUTO_INCREMENT,
    facility_id INT NOT NULL,
    load_or_unload ENUM('load', 'unload') NOT NULL DEFAULT 'load',
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    wait_duration_minutes INT NOT NULL,
    by_appointment TINYINT(1) NOT NULL DEFAULT 0,
    comment TEXT NOT NULL,
    added_date DATETIME NOT NULL,
    added_by_user_id INT NOT NULL,
    PRIMARY KEY (facility_wait_time_id)
);

CREATE TABLE facility_photos (
    facility_id INT NOT NULL,
    file_id INT NOT NULL,
    PRIMARY KEY (facility_id, file_id)
);
-- changeset john:20250116-2
alter table facilities
    change column avr_rating avg_rating double null default null;

-- changeset john:20250116-3
alter table facilities
    ADD COLUMN `deleted_date` DATETIME AFTER `deleted`;

-- changeset john:20250116-4
ALTER TABLE facility_equipments
    CHANGE COLUMN equipment_id equipment_id VARCHAR(4) NOT NULL;

-- changeset john:20250116-5
ALTER TABLE facility_photos
    RENAME TO facility_files;

-- changeset john:20250116-6
ALTER TABLE facility_searches
    CHANGE COLUMN search_term location VARCHAR(255) NOT NULL ;

-- changeset john:20250116-7
INSERT INTO `api_endpoints`
(`category`,`method`,`path`,`description`)
VALUES
    ('Facilities', 'GET', '/facilities/searches', 'Facility Searches');

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2) -- TMS
  and
    (
        method = 'GET' and path = '/facilities/searches'
        )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2) -- TMS
)
order by k.api_key_id, e.api_endpoint_id;

CREATE TABLE facility_review_files (
     facility_review_id INT NOT NULL,
     file_id INT NOT NULL,
     PRIMARY KEY (facility_review_id, file_id)
);

alter table facility_wait_times
    ADD COLUMN `used_timer` tinyint NOT NULL DEFAULT '0' AFTER `comment`;

-- changeset john:20250116-8
INSERT INTO `api_endpoints`
(`category`,`method`,`path`,`description`)
VALUES
    ('Facilities', 'GET', '/facilities/{int}/wait_times', 'Facility Wait Times'),
    ('Facilities', 'POST', '/facilities/{int}/wait_times', 'Add Facility Wait Time')
;

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2)
  and
    (
        method = 'GET' and path = '/facilities/{int}/wait_times' or
        method = 'POST' and path = '/facilities/{int}/wait_times'
        )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2)
)
order by k.api_key_id, e.api_endpoint_id;