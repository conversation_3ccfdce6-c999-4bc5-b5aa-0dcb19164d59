-- liquibase formatted sql

-- changeset andreas:20250813-2300

ALTER TABLE file_fields
    ADD COLUMN external_grade_code VARCHAR(10) DEFAULT NULL AFTER grade_id,
    ADD COLUMN is_unmatched_external_grade TINYINT(1) NOT NULL DEFAULT 0 AFTER external_grade_code,
    ADD COLUMN is_grade TINYINT(1) NOT NULL DEFAULT 0 AFTER is_unmatched_external_grade;

set sql_safe_updates = 0;

update agtrax_scale_tickets ag
    JOIN load_assignments la ON ag.load_assignment_id = la.load_assignment_id
    JOIN agtrax_scale_ticket_factors fact USING (agtrax_scale_ticket_id)
    JOIN file_fields ff ON ff.file_id = la.loading_ticket_file_id
SET
    ff.external_grade_code = fact.factor_code,
    ff.is_unmatched_external_grade = (ff.field_name = fact.factor_code)
WHERE (
    -- direct match
    ff.field_name = fact.factor_code
   OR
    -- via external_grades → grades → file_field_definitions
    EXISTS (
        SELECT 1
        FROM external_grades ex
            JOIN grades g ON ex.grade_id = g.grade_id
            JOIN file_field_definitions ffd ON ffd.grade_id = g.grade_id
        WHERE ex.external_grade_code = fact.factor_code
            AND ex.user_company_id   = ag.user_company_id
            AND ffd.field_name = ff.field_name
    )
);


-- is_grade = 1 when external_grade_code is not null or grade_id is not null
UPDATE file_fields
SET is_grade = 1
WHERE external_grade_code IS NOT NULL
   OR grade_id IS NOT NULL;
