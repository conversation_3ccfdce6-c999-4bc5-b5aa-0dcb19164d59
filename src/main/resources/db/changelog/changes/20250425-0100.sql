-- liquibase formatted sql

-- changeset andreas :20250425-0100

ALTER TABLE `user_info`
    ADD FULLTEXT INDEX `ix_user_full_text` (`first_name`, `last_name`, `email`);

ALTER TABLE `user_company`
    ADD FULLTEXT INDEX `ix_user_company_full_text` (`company_name`);

-- changeset andreas :20250425-0200

CALL createEndpointAndPermissions(
        'Users',
        'GET',
        '/users/auto',
        'Get users for autosuggest',
        '1,2'
     );

CALL createEndpointAndPermissions(
        'Companies',
        'GET',
        '/companies/auto',
        'Get companies for autosuggest',
        '1,2'
     );

