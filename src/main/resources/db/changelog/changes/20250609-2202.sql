-- liquibase formatted sql

-- changeset john:20250609-2202

INSERT INTO `api_endpoints`
(`category`,`method`,`path`,`description`)
VALUES
    ('File', 'GET', '/assignments/ocr/{int}', 'Get OCR data for a load assignment'),
    ('File', 'PUT', '/assignments/ocr/{int}', 'Update OCR data for a load assignment')
;

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2)
  and (
        method = 'GET' and path = '/assignments/ocr/{int}'
     or method = 'PUT' and path = '/assignments/ocr/{int}'
  )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2)
)
order by k.api_key_id, e.api_endpoint_id;