-- liquibase formatted sql

-- changeset theo:20250116

create table oauth2_authorized_client
(
    client_registration_id  varchar(100)                            not null,
    principal_name          varchar(200)                            not null,
    access_token_type       varchar(100)                            not null,
    access_token_value      blob                                    not null,
    access_token_issued_at  timestamp                               not null,
    access_token_expires_at timestamp                               not null,
    access_token_scopes     varchar(1000) default null,
    refresh_token_value     blob          default null,
    refresh_token_issued_at datetime      default null,
    created_at              datetime      default current_timestamp not null,
    primary key (client_registration_id, principal_name)
);

create table eld_user_info
(
    id              int auto_increment not null,
    eld_provider_id varchar(100)       not null default '',
    external_id     varchar(100)       not null default '',
    first_name      varchar(100)       not null default '',
    last_name       varchar(100)       not null default '',
    email           varchar(100)       not null default '',
    username        varchar(100)       not null default '',
    notes           varchar(100)       not null default '',
    status          varchar(100)       not null default '',
    phone_1         varchar(100)       not null default '',
    timezone        varchar(100)       not null default '',
    updated_at      datetime                    default null,
    sync_user_id    int                         default null,
    user_id         int                         default null,
    user_company_id int                         default null,
    is_active       tinyint            not null default 1,
    primary key (id),
    unique (eld_provider_id, external_id),
    index idx_user_id (user_id),
    index idx_user_company_id (user_company_id)
);

create table eld_user_company_equipments
(
    id                        int auto_increment not null,
    eld_provider_id           varchar(100)       not null default '',
    external_id               varchar(100)       not null default '',
    status                    varchar(100)       not null default '',
    license_plate_number      varchar(100)       not null default '',
    make                      varchar(100)       not null default '',
    model                     varchar(100)       not null default '',
    notes                     varchar(100)       not null default '',
    vin                       varchar(100)       not null default '',
    model_year                varchar(100)       not null default '',
    updated_at                datetime                    default null,
    sync_user_id              int                         default null,
    user_company_equipment_id int                         default null,
    user_company_id           int                         default null,
    is_active                 tinyint            not null default 1,
    primary key (id),
    unique (eld_provider_id, external_id),
    index idx_user_company_equipment_id (user_company_equipment_id),
    index idx_user_company_id (user_company_id)
);

insert into api_endpoints(category, method, path, description)
values ('ELD', 'GET', '/eld/authorization/motive', 'Authorize with Motive Api - Exposed'),
       ('ELD', 'GET', '/eld/authorization/samsara', 'Authorize with Samsara Api - Exposed'),
       ('ELD', 'GET', '/oauth2/authorization/motive', 'Authorize with Motive Api'),
       ('ELD', 'GET', '/oauth2/authorization/samsara', 'Authorize with Samsara Api'),
       ('ELD', 'GET', '/login/oauth2/code/motive', 'Callback from Motive Api'),
       ('ELD', 'GET', '/login/oauth2/code/samsara', 'Callback from Samsara Api'),
       ('ELD', 'GET', '/eld/users/{int}', 'Get ELD user by id'),
       ('ELD', 'GET', '/eld/user_company_equipments/{int}', 'Get ELD user company equipment by id'),
       ('ELD', 'GET', '/eld/users', 'Get all ELD users associated with provider and user company id'),
       ('ELD', 'GET', '/eld/user_company_equipments', 'Get all ELD user company equipment associated with provider and user company id'),
       ('ELD', 'PUT', '/eld/users/{int}/link/{int}', 'Link ELD user to user'),
       ('ELD', 'PUT', '/eld/user_company_equipments/{int}/link/{int}', 'Link ELD user company equipment to user company equipment'),
       ('ELD', 'DELETE', '/eld/users/{int}/unlink', 'Unlink ELD user from user'),
       ('ELD', 'DELETE', '/eld/user_company_equipments/{int}/unlink', 'Unlink ELD user company equipment from user company equipment'),
       ('ELD', 'PUT', '/eld/users/sync', 'Trigger ELD users sync'),
       ('ELD', 'PUT', '/eld/user_company_equipments/sync', 'Trigger ELD user company equipments sync');

insert into api_key_endpoints(api_key_id, api_endpoint_id)
select k.api_key_id, e.api_endpoint_id
from api_keys k
         join api_endpoints e
where k.api_key_id in (1, 2)
  and (
    method = 'GET' and path = '/eld/authorization/motive'
        or method = 'GET' and path = '/eld/authorization/samsara'
        or method = 'GET' and path = '/oauth2/authorization/motive'
        or method = 'GET' and path = '/oauth2/authorization/samsara'
        or method = 'GET' and path = '/login/oauth2/code/motive'
        or method = 'GET' and path = '/login/oauth2/code/samsara'
        or method = 'GET' and path = '/eld/users/{int}'
        or method = 'GET' and path = '/eld/user_company_equipments/{int}'
        or method = 'GET' and path = '/eld/users'
        or method = 'GET' and path = '/eld/user_company_equipments'
        or method = 'PUT' and path = '/eld/users/{int}/link/{int}'
        or method = 'PUT' and path = '/eld/user_company_equipments/{int}/link/{int}'
        or method = 'DELETE' and path = '/eld/users/{int}/unlink'
        or method = 'DELETE' and path = '/eld/user_company_equipments/{int}/unlink'
        or method = 'PUT' and path = '/eld/users/sync'
        or method = 'PUT' and path = '/eld/user_company_equipments/sync'
    )
  and e.api_endpoint_id not in (select api_endpoint_id
                                from api_key_endpoints
                                where api_key_id in (1, 2))
order by k.api_key_id, e.api_endpoint_id;

alter table bl_user_company_settings
    add column eld_provider_id varchar(25) not null default '';
