package com.bulkloads.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import lombok.Data;

@Data
public class ExcelTable {

  private List<String> headers;
  private List<Map<String, XSSFCell>> data;

  private static final int STOP_ON_EMPTY_ROWS = 10;

  public ExcelTable(InputStream excelFile) throws IOException {
    parseExcel(excelFile);
  }

  private void parseExcel(InputStream excelFile) throws IOException {
    Workbook workbook = new XSSFWorkbook(excelFile);
    Sheet sheet = workbook.getSheetAt(0);

    List<Map<String, XSSFCell>> newData = new ArrayList<>();

    // Get the header row (first row) to determine the column names
    Row headerRow = sheet.getRow(0);
    int columnCount = headerRow.getLastCellNum(); // Number of columns

    List<String> newHeaders = new ArrayList<>();
    for (int columnIndex = 0; columnIndex < columnCount; columnIndex++) {
      newHeaders.add(headerRow.getCell(columnIndex).getStringCellValue().trim());
    }

    // Iterate through each row (starting from the second row, as first is header)
    int emptyRowsInARow = 0;
    for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
      if (emptyRowsInARow >= STOP_ON_EMPTY_ROWS) {
        break; // Stop parsing if too many empty rows
      }

      Row currentRow = sheet.getRow(rowIndex);
      if (currentRow == null) {
        emptyRowsInARow++;
        continue; // Handle empty rows
      }

      // Create a map to store the row data
      Map<String, XSSFCell> rowMap = new HashMap<>();

      // Iterate through each cell in the row
      for (int columnIndex = 0; columnIndex < columnCount; columnIndex++) {
        XSSFCell currentCell = (XSSFCell) currentRow.getCell(columnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
        String columnName = headerRow.getCell(columnIndex).getStringCellValue().trim();


        // Get the cell value based on its type
        /*
        switch (currentCell.getCellType()) {
          case STRING:
            rowMap.put(columnName, currentCell.getStringCellValue());
            break;
          case NUMERIC:
            if (DateUtil.isCellDateFormatted(currentCell)) {
              rowMap.put(columnName, currentCell.getDateCellValue());
            } else {
              rowMap.put(columnName, currentCell.getNumericCellValue());
            }
            break;
          case BOOLEAN:
            rowMap.put(columnName, currentCell.getBooleanCellValue());
            break;
          case BLANK:
            rowMap.put(columnName, ""); // Handle blank cells
            break;
          default:
            rowMap.put(columnName, ""); // Default case
        }
        */
        rowMap.put(columnName, currentCell);

      }
      // check if all cells are empty
      // the following line doesn't work. the values() should be from ((XSSFCell) currentCell).getRawValue() not the currentCell
      // boolean allEmpty = rowMap.values().stream().allMatch(v -> v == null || v.isEmpty());
      // fix it:
      boolean allEmpty = rowMap.values().stream().allMatch(v -> v == null || (v).getRawValue() == null || (v).getRawValue().isEmpty());
      if (allEmpty) {
        emptyRowsInARow++;
        continue; // Handle empty rows
      } else {
        emptyRowsInARow = 0;
      }

      // Add the row map to the list
      newData.add(rowMap);
    }

    setHeaders(newHeaders);
    setData(newData);

  }

  public void filterByHeaders(List<String> headers) {
    List<Map<String, XSSFCell>> newData = new ArrayList<>();
    for (Map<String, XSSFCell> row : data) {
      Map<String, XSSFCell> newRow = new HashMap<>();
      for (String header : headers) {
        newRow.put(header, row.get(header));
      }
      newData.add(newRow);
    }
    setData(newData);
    setHeaders(headers);
  }

}
