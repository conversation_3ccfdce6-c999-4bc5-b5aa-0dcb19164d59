package com.bulkloads.web.equipment.repository;

import static com.bulkloads.web.equipment.repository.template.GetEquipmentsQueryTemplate.GET_EQUIPMENTS_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.equipment.service.dto.EquipmentListResponse;
import com.bulkloads.web.equipment.service.dto.transformer.EquipmentListResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class EquipmentQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final EquipmentListResponseTransformer equipmentListResponseTransformer;

  public List<EquipmentListResponse> getEquipments(
      String term,
      String order
  ) {
    Map<String, Object> params = new HashMap<>();
    params.put("equipmentIds", null);
    params.put("term", term);
    params.put("order", order);

    return jpaNativeQueryService.query(
        GET_EQUIPMENTS_QUERY_TEMPLATE,
        params,
        equipmentListResponseTransformer
    );
  }

}
