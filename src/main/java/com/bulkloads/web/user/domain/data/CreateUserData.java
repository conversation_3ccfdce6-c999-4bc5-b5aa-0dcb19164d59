package com.bulkloads.web.user.domain.data;

import java.util.Optional;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreateUserData {

  private Optional<String> firstName;
  private Optional<String> lastName;
  private Optional<String> companyName;

  @NotNull
  @Email
  private String email;

  private Optional<String> emailToken;
  private Optional<String> accountingEmail;
  private Optional<String> accountingEmailToken;
  private Optional<String> accountingEmailCcMe;
  private Optional<String> cellPhone;
  private Optional<String> cellPhoneToken;
  private Optional<String> phone1;
  private Optional<String> phone;
  private Optional<String> phoneToken;
  private Optional<String> password;
  private Optional<String> userRoleIds;
  private Optional<String> userGroupIds;
  private Optional<String> defaultDriverRate;
  private Optional<String> defaultDriverRateType;
  private Optional<String> appMode;
  private Optional<String> externalUserId;
}
