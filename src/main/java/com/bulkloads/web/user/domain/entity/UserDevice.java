package com.bulkloads.web.user.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "user_devices")
@Getter
@Setter
public class UserDevice {

  @EmbeddedId
  private UserDeviceId userDeviceId;

  @MapsId("userId")
  @ManyToOne
  @JoinColumn(name = "user_id", insertable = false, updatable = false)
  private User user;

  @Column(name = "device_id", insertable = false, updatable = false)
  private String deviceId;

  @NotNull
  @Size(max = 500, message = "Up to 500 chars")
  @Column(name = "notification_token")
  private String notificationToken = "";

  @NotNull
  @Column(name = "push_enabled")
  private Boolean pushEnabled = false;

  @NotNull
  @Column(name = "sms_enabled")
  private Boolean smsEnabled = false;

  @NotNull
  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();

  @Column(name = "date_updated")
  private Instant dateUpdated;
}