package com.bulkloads.web.user.repository.template;

import org.intellij.lang.annotations.Language;

public class GetUsersAutoQueryTemplate {

  @Language("SQL")
  public static final String GET_USERS_AUTO_QUERY_TEMPLATE = """
      <% params.put("searchTerm", searchTerm) %>
      SELECT
          user_info.user_id,
          CONCAT(first_name, ' ', last_name, ' (', company_name, ')') as value,
          first_name,
          last_name,
          company_name,
          MATCH(first_name, last_name, email) AGAINST(:searchTerm IN BOOLEAN MODE) as score
      FROM user_info
          INNER JOIN user_company USING(user_company_id)
          INNER JOIN bl_user_settings us ON user_info.user_id = us.user_id
      WHERE user_company.merged_to IS NULL
          AND us.deletion_date IS NULL
          AND us.show_in_company_finder = 1
          AND us.app_mode != 'driver'
          AND MATCH(first_name, last_name, email) AGAINST(:searchTerm IN BOOLEAN MODE)
      ORDER BY score DESC
      LIMIT 15
      """;
}