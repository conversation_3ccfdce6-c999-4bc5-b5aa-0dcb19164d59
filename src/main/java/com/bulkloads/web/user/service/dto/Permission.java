package com.bulkloads.web.user.service.dto;

import com.bulkloads.web.user.domain.entity.UserPermission;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class Permission {

  private Integer permissionId;
  private String permission;
  private String category;

  public static Permission fromUserPermission(UserPermission userPermission) {
    return Permission.builder()
        .permissionId(userPermission.getUserPermissionId())
        .permission(userPermission.getPermission())
        .category(userPermission.getCategory())
        .build();
  }

}
