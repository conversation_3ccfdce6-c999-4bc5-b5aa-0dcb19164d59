package com.bulkloads.web.notification.service;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.web.load.service.LoadService.GENERAL;
import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import com.bulkloads.web.notification.domain.NotificationDomainService;
import com.bulkloads.web.notification.domain.data.NotificationData;
import com.bulkloads.web.notification.domain.entity.Notification;
import com.bulkloads.web.notification.mapper.NotificationMapper;
import com.bulkloads.web.notification.repository.NotificationRepository;
import com.bulkloads.web.notification.service.dto.NotificationCountResponse;
import com.bulkloads.web.notification.service.dto.NotificationMessageDto;
import com.bulkloads.web.notification.service.dto.NotificationRequest;
import com.bulkloads.web.notification.service.dto.NotificationResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class NotificationService {

  private final NotificationMapper notificationMapper;
  private final NotificationRepository notificationRepository;
  private final NotificationDomainService notificationDomainService;
  private final MessageQueueSender queueSender;
  private final AppProperties appProperties;
  private final EntityManager entityManager;

  public NotificationResponse create(NotificationRequest request) {
    final NotificationData notificationData = notificationMapper.requestToData(request);
    final Result<Notification> notificationResult = notificationDomainService.create(notificationData);
    Notification entity = notificationResult.orElseThrow();
    entity = notificationRepository.save(entity);
    entityManager.flush();

    final Integer notificationId = entity.getNotificationId();
    final int userId = entity.getUser().getUserId();

    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
      @Override
      public void afterCommit() {
        sendNotification(userId, notificationId);
      }
    });

    return notificationRepository.getNotification(userId, notificationId);
  }

  public void markNotificationsAsRead(List<Integer> notificationIds, List<Integer> emailCategoryIds) {
    if (isEmpty(notificationIds) && isEmpty(emailCategoryIds)) {
      throw new ValidationException(GENERAL, "notification_ids or email_category_ids is required");
    }
    final int userId = UserUtil.getUserIdOrThrow();

    if (isEmpty(notificationIds)) {
      notificationIds = null;
    }
    if (isEmpty(emailCategoryIds)) {
      emailCategoryIds = null;
    }

    int read = notificationRepository.markNotificationsAsRead(userId, notificationIds, emailCategoryIds);
    log.debug("Marked as read {} notifications", read);
  }

  public void markNotificationsAsUnread(final List<Integer> notificationIds) {
    if (isEmpty(notificationIds)) {
      throw new ValidationException(GENERAL, "notification_ids is required");
    }
    final int userId = UserUtil.getUserIdOrThrow();
    int unread = notificationRepository.markNotificationsAsUnread(userId, notificationIds);
    log.debug("Marked as unread {} notifications", unread);
  }

  public NotificationResponse getNotification(final int notificationId) {
    final int userId = UserUtil.getUserIdOrThrow();
    return notificationRepository.getNotification(userId, notificationId);
  }

  public List<NotificationResponse> getNotifications(
      final List<Integer> notificationIds,
      final Integer emailCategoryId,
      final Integer notificationTypeId,
      final String deviceId,
      final Integer isRead,
      final int skip,
      final int limit) {

    final int userId = UserUtil.getUserIdOrThrow();
    return notificationRepository.getNotifications(userId, notificationIds, emailCategoryId, notificationTypeId, deviceId, isRead, skip, limit);
  }

  public NotificationCountResponse getNotificationCount(final Boolean isRead,
                                                        final Integer emailCategoryId,
                                                        final Integer notificationTypeId) {
    final int userId = UserUtil.getUserIdOrThrow();
    return notificationRepository.getNotificationCount(userId, isRead, emailCategoryId, notificationTypeId);
  }

  private void sendNotification(final int userId,
                                final int notificationId) {

    String queue = appProperties.getNotification().getQueueName();
    final NotificationMessageDto notificationMessageDto = NotificationMessageDto.builder()
        .userId(userId)
        .notificationId(notificationId).build();
    queueSender.send(queue, notificationMessageDto);
  }
}