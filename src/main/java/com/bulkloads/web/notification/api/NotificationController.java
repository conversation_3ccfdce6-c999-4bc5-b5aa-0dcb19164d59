package com.bulkloads.web.notification.api;

import java.util.List;
import com.bulkloads.common.Parsers;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.notification.service.NotificationService;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/notifications")
@Tag(name = "Notifications")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class NotificationController {

  private final NotificationService notificationService;


  @Operation(summary = "Mark notifications as read")
  @PreAuthorize("hasRole('USER')")
  @PostMapping(path = "/mark_read", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
  public ApiResponse<Void, Void> markNotificationsAsRead(
      @Parameter(description = "a comma-delimited list of notification_id", required = false)
      @RequestParam(value = "notification_ids", required = false) String notificationIdsStr,
      @Parameter(description = "Filter by email_category_ids")
      @RequestParam(value = "email_category_ids", required = false) String emailCategoryIdsStr) {

    List<Integer> notificationIds = Parsers.parseIntegerCsvToList(notificationIdsStr);
    List<Integer> emailCategoryIds = Parsers.parseIntegerCsvToList(emailCategoryIdsStr);

    log.debug("Marking notifications as read: notificationIds={}, emailCategoryIds={}", notificationIds, emailCategoryIds);
    notificationService.markNotificationsAsRead(notificationIds, emailCategoryIds);

    return ApiResponse.<Void, Void>builder()
        .message("Notifications marked as read")
        .build();
  }

  @Operation(summary = "Mark notifications as unread")
  @PreAuthorize("hasRole('USER')")
  @PostMapping(path = "/mark_unread", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
  public ApiResponse<Void, Void> markNotificationsAsUnread(
      @Parameter(description = "a comma-delimited list of notification_id")
      @RequestParam(value = "notification_ids", required = false) String notificationIds) {
    List<Integer> ids = Parsers.parseIntegerCsvToList(notificationIds);
    log.debug("Marking notifications as unread: notificationIds={}", ids);

    notificationService.markNotificationsAsUnread(ids);

    return ApiResponse.<Void, Void>builder()
        .message("Notifications marked as unread")
        .build();
  }

}
