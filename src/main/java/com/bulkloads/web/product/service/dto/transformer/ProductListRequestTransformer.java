package com.bulkloads.web.product.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.product.service.dto.ProductListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class ProductListRequestTransformer implements TupleTransformer<ProductListResponse> {


  @Override
  public ProductListResponse transformTuple(Object[] columns, String[] aliases) {
    ProductListResponse view = new ProductListResponse();
    QueryParts parts = new QueryParts(columns, aliases);
    view.setProduct(parts.asString("product"));
    view.setDefRateType(parts.asString("def_rate_type"));
    return view;
  }
}
