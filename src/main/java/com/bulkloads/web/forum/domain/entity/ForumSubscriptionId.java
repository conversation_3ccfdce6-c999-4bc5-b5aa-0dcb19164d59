package com.bulkloads.web.forum.domain.entity;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class ForumSubscriptionId implements Serializable {

  @Column(name = "forum_id")
  Integer forumId;

  @Column(name = "user_id")
  Integer userId;

}
