package com.bulkloads.web.forum.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.user.domain.entity.User;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "forum_reply")
public class ForumReply {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "reply_id")
  private Integer replyId;

  @Column(name = "approved")
  private Boolean approved;

  @Column(name = "date_added")
  private Instant dateAdded;

  @Column(name = "parent_reply_id")
  private Integer parentReplyId;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", referencedColumnName = "user_id")
  private User user;

  @Column(name = "content")
  private String content;

  @Column(name = "origin_site_id")
  private Integer originSiteId = 1;

  @Column(name = "thumb_path")
  private String thumbPath = "";


  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "forum_id")
  private ForumPost forumPost;

  @OneToMany(cascade = {CascadeType.ALL},
      orphanRemoval = true, fetch = FetchType.LAZY)
  @JoinColumn(name = "reply_id")
  private List<ForumFile> files = new ArrayList<>();

}
