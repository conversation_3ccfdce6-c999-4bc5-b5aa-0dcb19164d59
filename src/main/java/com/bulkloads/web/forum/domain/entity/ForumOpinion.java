package com.bulkloads.web.forum.domain.entity;

import java.time.Instant;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "forum_opinions")
public class ForumOpinion {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "forum_opinion_id")
  private Integer forumOpinionId;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @Join<PERSON>olumn(name = "forum_id")
  private ForumPost forumPost;

  @Column(name = "opinion")
  private Integer opinion;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "reply_id")
  private ForumReply forumReply;

  @Column(name = "user_id")
  private Integer userId;

  @Column(name = "date_added")
  private Instant dateAdded;
}
