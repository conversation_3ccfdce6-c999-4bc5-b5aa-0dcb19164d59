package com.bulkloads.web.forum.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.forum.service.dto.ForumDetailResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class QueryPostDetailsResponseTransformer implements TupleTransformer<ForumDetailResponse> {

  @Override
  public ForumDetailResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return ForumDetailResponse.builder()
        .forumId(parts.asInteger("forum_id"))
        .postTypeId(parts.asInteger("post_type_id"))
        .postType(parts.asString("post_type"))
        .forumTitle(parts.asString("forum_title"))
        .forumContent(parts.asString("forum_content"))
        .forumContentHtml(parts.asString("forum_content_html"))
        .thumbUrl(parts.asString("thumb_url"))
        .dateAdded(parts.asInstant("date_added"))
        .lastPost(parts.asInstant("last_post"))
        .views(parts.asInteger("views"))
        .likes(parts.asInteger("likes"))
        .dislikes(parts.asInteger("dislikes"))
        .messageCount(parts.asInteger("message_count"))
        .premiumOnly(parts.asInteger("premium_only"))
        .isfeatured(parts.asInteger("isfeatured"))
        .showInDays(parts.asInteger("show_in_days"))
        .showDate(parts.asLocalDate("show_date"))
        .forumLink(parts.asString("forum_link"))
        .youtubeVideoId(parts.asString("youtube_video_id"))
        .approved(parts.asBoolean("approved"))
        .active(parts.asInteger("active"))
        .alias(parts.asString("alias"))
        .forumUrl(parts.asString("forum_url"))
        .userId(parts.asInteger("user_id"))
        .firstName(parts.asString("first_name"))
        .lastName(parts.asString("last_name"))
        .iLike(parts.asInteger("i_like"))
        .iDislike(parts.asInteger("i_dislike"))
        .iSubscribe(parts.asInteger("i_subscribe"))
        .companyName(parts.asString("company_name"))
        .email(parts.asString("email"))
        .city(parts.asString("city"))
        .state(parts.asString("state"))
        .userCommentCount(parts.asInteger("user_comment_count"))
        .userLikes(parts.asInteger("user_likes"))
        .userDislikes(parts.asInteger("user_dislikes"))
        .signUpDate(parts.asLocalDate("sign_up_date"))
        .avatarThumbUrl(parts.asString("avatar_thumb_url"))
        .userId(parts.asInteger("user_id"))
        .build();
  }
}
