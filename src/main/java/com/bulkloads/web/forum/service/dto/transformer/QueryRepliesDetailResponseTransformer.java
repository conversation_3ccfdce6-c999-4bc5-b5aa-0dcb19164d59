package com.bulkloads.web.forum.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.forum.service.dto.ReplyResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class QueryRepliesDetailResponseTransformer implements TupleTransformer<ReplyResponse> {

  @Override
  public ReplyResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return ReplyResponse.builder()
      .dislikes(parts.asInteger("dislikes"))
      .state(parts.asString("state"))
      .userCommentCount(parts.asInteger("user_comment_count"))
      .userId(parts.asInteger("user_id"))
      .email(parts.asString("email"))
      .lastName(parts.asString("last_name"))
      .idislike(parts.asInteger("i_dislike"))
      .likes(parts.asInteger("likes"))
      .firstName(parts.asString("first_name"))
      .parentReplyId(parts.asInteger("parent_reply_id"))
      .userLikes(parts.asInteger("user_likes"))
      .content(parts.asString("content"))
      .contentHtml(parts.asString("content_html"))
      .thumbUrl(parts.asString("thumb_url"))
      .dateAdded(parts.asInstant("date_added"))
      .companyName(parts.asString("company_name"))
      .userDislikes(parts.asInteger("user_dislikes"))
      .city(parts.asString("city"))
      .signUpDate(parts.asLocalDate("sign_up_date"))
      .avatarThumbUrl(parts.asString("avatar_thumb_url"))
      .replyId(parts.asInteger("reply_id"))
      .approved(parts.asInteger("approved"))
      .iLike(parts.asInteger("i_like"))
      .build();
  }
}
