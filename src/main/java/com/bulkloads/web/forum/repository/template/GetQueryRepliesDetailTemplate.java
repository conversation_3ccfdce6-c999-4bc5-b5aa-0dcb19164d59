package com.bulkloads.web.forum.repository.template;

public class GetQueryRepliesDetailTemplate {

  @SuppressWarnings("checkstyle:FileTabCharacter")
  public static final String GET_QUERY_REPLIES_DETAIL_TEMPLATE = """
      
         SELECT r.*,
            IF(r.thumb_path IS NOT NULL AND r.thumb_path <> '',
                                           CONCAT('<% print(classifieds_dir) %>/thumbs/', r.thumb_path),
                                           '') AS thumb_url,
         			p.post_type_id,
         			p.premium_only,
      
         			first_name, last_name, company_name, email, city, state, user_phone_1,
         			avatar_small,
         			concat('<% print(profiles_dir) %>', '/thumbs/', avatar_small) as avatar_thumb_url,
         			sign_up_date,
         			ifnull(
         			(SELECT count(1)
         			FROM forum_post
         			WHERE active = 1 and approved = 1 and user_id = r.user_id)
         			  , 0) +
         			ifnull(
         			(SELECT count(1)
         			FROM forum_reply
         			WHERE user_id = r.user_id)
         				, 0)
         					as user_comment_count,
      
         			(SELECT count(1)
         			FROM forum_opinions
         			where opinion = 1
         				AND user_id = r.user_id) as user_likes,
         			(SELECT count(1)
         			FROM forum_opinions
         			where opinion = 0
         				AND user_id = r.user_id) as user_dislikes,
      
         			(SELECT COUNT(opinion)
         			FROM forum_opinions
         			WHERE opinion = 1
         				AND forum_id = r.forum_id AND reply_id = r.reply_id) as likes,
         			(SELECT COUNT(opinion)
         			FROM forum_opinions
         			WHERE opinion = 0
         				AND forum_id = r.forum_id AND reply_id = r.reply_id) as dislikes,
      
         			(SELECT COUNT(forum_file_id)
         			FROM forum_files
         			WHERE reply_id = r.reply_id) as file_count,
      
         			u.sign_up_date,
         			<% if (paramExistsAdd("u_id")) { %>
         			(SELECT COUNT(opinion)
         			FROM forum_opinions
         			WHERE opinion = 1
         					AND forum_id = r.forum_id AND reply_id = r.reply_id AND user_id = :u_id) as i_like,
         				(SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 0
         					AND forum_id = r.forum_id AND reply_id = r.reply_id AND user_id = :u_id) as i_dislike
         			<% } else { %>
         				0 as i_like,
         				0 as i_dislike
         			 <% } %>
      
         		FROM forum_reply r
         			inner join forum_post p using(forum_id)
         			INNER JOIN user_info u on r.user_id = u.user_id
         			LEFT JOIN user_company uc on u.user_company_id = uc.user_company_id
         		WHERE 1=1
      
         		 <% if (paramExistsAdd("forum_id")) { %>
         			AND r.forum_id = :forum_id
         		<% } %>
      
         		<% if (paramExistsAdd("reply_id")) { %>
         			AND r.reply_id = :reply_id
         		<% } %>
      
                <% if (!paramIsTrue("is_site_admin")) { %>
                  AND (r.approved = 1
                  <% if (paramExistsAdd("u_id")) { %>
                      OR r.user_id = :u_id
                  <% } %>
                  )
                <% } %>

                <% if (paramIsTrue("include_premium")) { %>
                <% } else { %>
                    AND p.premium_only = 0
                <% } %>
      
         		ORDER BY date_added asc
      
      """;
}
