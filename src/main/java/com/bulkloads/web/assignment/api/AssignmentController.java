package com.bulkloads.web.assignment.api;

import static com.bulkloads.config.AppConstants.UserPermission.CREATE_LOAD_ASSIGNMENT;
import static com.bulkloads.config.AppConstants.UserPermission.DELETE_LOAD_ASSIGNMENT;
import static com.bulkloads.config.AppConstants.UserPermission.UPDATE_LOAD_ASSIGNMENT;
import com.bulkloads.web.assignment.api.dto.AssignmentApiResponse;
import com.bulkloads.web.assignment.service.AssignmentService;
import com.bulkloads.web.assignment.service.dto.AssignmentPreviewResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentResponse;
import com.bulkloads.web.assignment.service.dto.CancelAssignmentRequest;
import com.bulkloads.web.assignment.service.dto.CreateLoadAssignmentRequest;
import com.bulkloads.web.assignment.service.dto.UpdateLoadAssignmentRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest/loads")
@Tag(name = "Load Assignments (Shipper)")
@RequiredArgsConstructor
@Validated
public class AssignmentController {

  private final AssignmentService assignmentService;

  @PreAuthorize("hasAuthority('" + CREATE_LOAD_ASSIGNMENT + "')")
  @Operation(summary = "Create load assignment")
  @PostMapping("my_loads/{load_id}/assignments")
  public AssignmentApiResponse createAssignment(
      @Parameter(name = "load_id", description = "load_id", required = true, in = ParameterIn.PATH)
      @Positive(message = "Load id should be positive")
      @PathVariable("load_id") final int loadId,
      @Parameter(name = "body", required = true)
      @Valid @RequestBody final CreateLoadAssignmentRequest dto) {
    final AssignmentResponse response = assignmentService.create(loadId, dto);
    return AssignmentApiResponse.builder()
        .loadAssignmentIds(response.getIds())
        .message("Load assignments created successfully")
        .build();
  }

  @PreAuthorize("hasAuthority('" + UPDATE_LOAD_ASSIGNMENT + "')")
  @Operation(summary = "Update load assignment")
  @PutMapping("my_loads/{load_id}/assignments/{load_assignment_id}")
  public AssignmentApiResponse updateAssignment(
      @Parameter(name = "load_id", description = "load_id", required = true, in = ParameterIn.PATH)
      @Positive(message = "Load id should be positive")
      @PathVariable("load_id") final int loadId,
      @Parameter(name = "load_assignment_id", description = "load_assignment_id", required = true, in = ParameterIn.PATH)
      @Positive(message = "Load assignment id should be positive")
      @PathVariable("load_assignment_id") final int loadAssignmentId,
      @Parameter(name = "body", required = true)
      @Valid @RequestBody final UpdateLoadAssignmentRequest dto) {
    assignmentService.update(loadAssignmentId, dto);
    return AssignmentApiResponse.builder()
        .message("Assignment details updated")
        .build();
  }

  @PreAuthorize("hasAuthority('" + DELETE_LOAD_ASSIGNMENT + "')")
  @Operation(summary = "Cancel load assignment")
  @PostMapping("/my_loads/{load_id}/assignments/{load_assignment_id}/cancel")
  public AssignmentApiResponse cancelAssignment(
      @Parameter(name = "load_id", description = "load_id", required = true, in = ParameterIn.PATH)
      @Positive(message = "Load id should be positive")
      @PathVariable("load_id") final int loadId,
      @Parameter(name = "load_assignment_id", description = "load_assignment_id", required = true, in = ParameterIn.PATH)
      @Positive(message = "Load assignment id should be positive")
      @PathVariable("load_assignment_id") final int loadAssignmentId,
      @Parameter(name = "body", description = "Cancel Load Assignment Message")
      @Valid @RequestBody(required = false) final CancelAssignmentRequest dto) {

    assignmentService.cancel(loadId, loadAssignmentId, dto);
    return AssignmentApiResponse.builder()
        .message("Assignment canceled")
        .build();
  }

  @PreAuthorize("hasAuthority('" + CREATE_LOAD_ASSIGNMENT + "')")
  @Operation(summary = "Preview of create load assignment")
  @PostMapping("my_loads/{load_id}/assignments/preview")
  public AssignmentPreviewResponse createAssignmentPreview(
      @Parameter(name = "load_id", description = "load_id", required = true, in = ParameterIn.PATH)
      @Positive(message = "Load id should be positive")
      @PathVariable("load_id") final int loadId,
      @Parameter(name = "body", required = true)
      @Valid @RequestBody final CreateLoadAssignmentRequest dto) {

    final AssignmentPreviewResponse response = assignmentService.createAssignmentPreview(loadId, dto);
    return AssignmentPreviewResponse.builder()
        .content(response.getContent())
        .message("Load assignments created successfully")
        .build();
  }

}
