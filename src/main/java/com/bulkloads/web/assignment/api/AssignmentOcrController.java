package com.bulkloads.web.assignment.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.web.assignment.service.AssignmentOcrService;
import com.bulkloads.web.assignment.service.dto.AssignmentOcrReadyResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentOcrRequestResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentOcrUpdateResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping(path = "/rest/assignments/ocr")
@Tag(name = "File")
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class AssignmentOcrController {

  private final AssignmentOcrService assignmentOcrService;

  @GetMapping("/{load_assignment_id}")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Get OCR data for a load assignment")
  public AssignmentOcrRequestResponse getAssignmentOcrData(
      @Parameter(name = "load_assignment_id", required = true, in = ParameterIn.PATH)
      @PathVariable("load_assignment_id")
      @Positive(message = "Load assignment id should be positive") final int loadAssignmentId) {
    return assignmentOcrService.getAssignmentOcrData(loadAssignmentId);
  }

  @GetMapping("/ready")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Get Assignments with OCR files ready for approval")
  public List<AssignmentOcrReadyResponse> getAssignmentsOcrReady() {
    return assignmentOcrService.getAssignmentsOcrReady();
  }

  @GetMapping("/waiting")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Get Assignments with OCR files that have missing or unreadable images")
  public List<AssignmentOcrReadyResponse> getAssignmentsOcrWaiting() {
    return assignmentOcrService.getAssignmentsOcrWaiting();
  }

  @PutMapping("/{load_assignment_id}")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Update OCR data for a load assignment")
  public AssignmentOcrUpdateResponse updateAssignmentOcrData(
      @Parameter(name = "load_assignment_id", required = true, in = ParameterIn.PATH)
      @PathVariable("load_assignment_id")
      @Positive(message = "Load assignment id should be positive") final int loadAssignmentId,
      @Valid @RequestBody final AssignmentOcrRequestResponse request) {
    return assignmentOcrService.updateAssignmentOcrData(loadAssignmentId, request);

  }
}