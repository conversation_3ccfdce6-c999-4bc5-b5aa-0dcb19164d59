package com.bulkloads.web.assignment.api;

import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.assignment.api.dto.LoadBookingRequest;
import com.bulkloads.web.assignment.service.BookingService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest/loads")
@Tag(name = "Load Bookings (Carrier)")
@RequiredArgsConstructor
@Validated
public class BookingController {

  private final BookingService bookingService;

//  @PreAuthorize("hasAuthority('" + UPDATE_LOAD_BOOKING_GENERAL + "')")
  @Operation(summary = "Update Booking",
      description = "Carrier updates assignment status, loading weight, unload weight, files")
  @PutMapping("/my_loads/bookings/{load_assignment_id}")
  public ApiResponse<Void, Void> updateBookings(
      @Parameter(name = "load_assignment_id", description = "load_assignment_id", required = true, in = ParameterIn.PATH)
      @Positive(message = "Load assignment id should be positive")
      @PathVariable("load_assignment_id") final int loadAssignmentId,
      @Parameter(name = "body", description = "Load Assignment Status Details", required = true)
      @Valid @RequestBody final LoadBookingRequest request) {
    final String message = bookingService.update(loadAssignmentId, request);

    return ApiResponse.<Void, Void>builder()
        .message(message)
        .build();
  }
}
