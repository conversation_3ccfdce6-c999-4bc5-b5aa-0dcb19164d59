package com.bulkloads.web.assignment.api.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.assignment.service.dto.AssignmentFileRequest;
import com.bulkloads.web.assignment.service.dto.AssignmentSurchargeRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class LoadBookingRequest {

  @Schema(name = "assignment_status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> assignmentStatus;
  @Schema(name = "loaded_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> loadedWeight;
  @Schema(name = "unload_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> unloadWeight;
  @Schema(name = "loaded_volume", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> loadedVolume;
  @Schema(name = "unload_volume", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> unloadVolume;
  @Schema(name = "share_driver_location", description = "1 for true, 0 for false", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> shareDriverLocation;
  @Schema(name = "bill_weight_use", description = "either loaded_weight or unload_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> billWeightUse;
  @Schema(name = "bill_miles", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> billMiles;
  @Schema(name = "bill_hours", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> billHours;
  @Schema(name = "to_payment", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> toPayment;
  @Schema(name = "to_payment_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> toPaymentNotes;
  @Schema(name = "to_paid", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> toPaid;
  @Schema(name = "pickup_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> pickupNotes;
  @Schema(name = "drop_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> dropNotes;
  @Schema(name = "inside_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> insideNotes;
  @Schema(name = "shared_with_hiring_company", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> sharedWithHiringCompany;
  @Schema(name = "loading_ticket_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loadingTicketNumber;
  @Schema(name = "unloading_ticket_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> unloadingTicketNumber;
  @Schema(name = "bol_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> bolNumber;
  @Schema(name = "hauled_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> hauledNotes;
  @Schema(name = "hauled_date", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<LocalDate> hauledDate;
  @Schema(name = "bill_to_ab_company_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> billToAbCompanyId;
  @Schema(name = "bill_to_ab_user_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> billToAbUserId;
  @Schema(name = "auto_invoice", description = "1 to send an invoice immediately . Must be 'delivered'.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> autoInvoice;
  @Schema(name = "ready_to_invoice", description = "1 to move the booking to Ready To Invoice to be invoiced manually If both auto_invoice "
      + "and ready_to_invoice are 0, no invoice will be sent.Must be 'delivered'.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> readyToInvoice;

  @Valid
  @Schema(name = "files", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<List<@Valid AssignmentFileRequest>> files;

  @Valid
  @Schema(name = "surcharges", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<List<@Valid AssignmentSurchargeRequest>> surcharges;
}