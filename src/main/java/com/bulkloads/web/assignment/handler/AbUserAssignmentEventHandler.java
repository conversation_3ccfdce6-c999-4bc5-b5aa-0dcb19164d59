package com.bulkloads.web.assignment.handler;

import static com.bulkloads.common.Parsers.parseIntegerCsvToList;
import static com.bulkloads.config.AppConstants.AssignmentAction.ASSIGNMENT_CREATE;
import com.bulkloads.web.addressbook.abuser.event.AbUserEmailUpdatedEvent;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.infra.confirmation.ConfirmationService;
import com.bulkloads.web.infra.confirmation.dto.ConfirmationDto;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AbUserAssignmentEventHandler {

  private final AssignmentRepository assignmentRepository;
  private final ConfirmationService confirmationService;

  @TransactionalEventListener(
      classes = AbUserEmailUpdatedEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT
  )
  public void handleAbUserEmailUpdatedEvent(final AbUserEmailUpdatedEvent event) {
    log.debug("Sending confirmartion due to {}", event);

    /* Here we should check that the confirmation was
    sent to the shipper now that the email has been corrected
    but the code has not been added to re-send the confirmation, only
    the invoice gets resent */

    assignmentRepository.findPendingLoadAssignmentConfirmations(event.getUserCompanyId(),
                                                                event.getAbUser())
        .forEach(e -> {
          confirmationService.sendAssignmentConfirmation(
              ConfirmationDto.builder()
                  .loadAssignmentIds(parseIntegerCsvToList(e.getLoadAssignmentIds()))
                  .action(ASSIGNMENT_CREATE)
                  .build());
        });
  }

}
