package com.bulkloads.web.assignment.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.load.repository.projections.LoadAssignmentConfirmationProjection;
import org.hibernate.cfg.AvailableSettings;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.LockModeType;
import jakarta.persistence.QueryHint;

@Repository
public interface AssignmentRepository extends JpaRepository<Assignment, Integer>, AssignmentQueryRepository {

  @QueryHints(@QueryHint(name = AvailableSettings.JAKARTA_LOCK_TIMEOUT, value = "-2")) //LockOptions.SKIP_LOCKED
  @Lock(LockModeType.PESSIMISTIC_WRITE)
  @Query("""
      select la from Assignment la
      left join Assignment lb on la.parentLoadAssignment.loadAssignmentId = lb.loadAssignmentId
      where la.load.loadId = :loadId
      and la.userCompany.userCompanyId = :userCompanyId
      and la.assignmentStatus = 'Unassigned'
      and la.deleted = false
      and (lb.deleted is null or lb.deleted = false)
      and (:#{#loadAssignmentIds.isEmpty()} = true or la.loadAssignmentId in (:loadAssignmentIds))
      order by la.loadAssignmentId""")
  List<Assignment> findBlankLoadAssignmentsLocking(
      @Param("loadId") final int loadId,
      @Param("userCompanyId") final int userCompanyId,
      @Param("loadAssignmentIds") final List<Integer> loadAssignmentIds,
      final Pageable pageable);

  @Query("""
      select la from Assignment la
      left join Assignment lb on la.parentLoadAssignment.loadAssignmentId = lb.loadAssignmentId
      where la.load.loadId = :loadId
      and la.userCompany.userCompanyId = :userCompanyId
      and la.assignmentStatus = 'Unassigned'
      and la.deleted = false
      and (lb.deleted is null or lb.deleted = false)
      and (:#{#loadAssignmentIds.isEmpty()} = true or la.loadAssignmentId in (:loadAssignmentIds))
      order by la.loadAssignmentId""")
  List<Assignment> findBlankLoadAssignments(
      @Param("loadId") final int loadId,
      @Param("userCompanyId") final int userCompanyId,
      @Param("loadAssignmentIds") final List<Integer> loadAssignmentIds,
      final Pageable pageable);

  Optional<Assignment> findByLoadAssignmentIdAndUserCompanyUserCompanyIdAndDeletedFalse(
      final int loadAssignmentId,
      final int userCompanyId);

  @QueryHints(@QueryHint(name = AvailableSettings.JAKARTA_LOCK_TIMEOUT, value = "-2")) //LockOptions.SKIP_LOCKED
  @Lock(LockModeType.PESSIMISTIC_WRITE)
  Optional<Assignment> findByLoadAssignmentIdAndUserCompanyUserCompanyId(
      final int loadAssignmentId,
      final int userCompanyId);

  List<Assignment> findAllByConfirmationFileFileIdAndConfirmationFileCodeAndDeletedIsFalse(
      final int confirmationFileId,
      final String confirmationFileCode);

  @Query("""
      select
        la from Assignment la
        join Load l on l.loadId = la.load.loadId
        where la.load.loadId = :loadId
        and la.deleted = false
        order by la.assignmentStatus desc , la.loadAssignmentId asc
        limit :noOfLoads
      """)
  List<Assignment> findAllByLoadIdAndDeletedIsFalse(@Param("loadId") final int loadId,
                                                    @Param("noOfLoads") final int noOfLoads);

  @Query("""
      select
        la from Assignment la
        join Load l on l.loadId = la.toLoad.loadId
        where la.load.loadId = :loadId
        and la.deleted = false
        and la.isIntraCompany = false
        order by la.loadAssignmentId asc
        limit :noOfLoads
      """)
  List<Assignment> findAllByToLoadIdAndDeletedIsFalseAndIntraCompanyIsFalse(@Param("loadId") final int loadId,
                                                                            @Param("noOfLoads") final int noOfLoads);

  @Query(value = """
      select
        GROUP_CONCAT(la.load_assignment_id) as loadAssignmentIds,
        la.confirmation_file_id as confirmationFileId,
        u.user_id as userId,
        u.user_company_id as userCompanyId
      from
        loads l
        inner join load_assignments la ON l.load_id = la.load_id
        inner join user_info u ON u.user_id = la.user_id
        inner join ab_users abu ON abu.ab_user_id = la.to_ab_user_id
      where
            l.deleted = 0
        and la.deleted = 0
        and la.user_company_id = :userCompanyId
        and la.to_ab_user_id = :abUserId
        and la.confirmation_email_status NOT IN ('sent', 'delivered', 'opened')
        and la.confirmation_sent_date IS NOT NULL
        and la.confirmation_sent_date > CURDATE() - INTERVAL 30 DAY
      group by la.confirmation_file_id
      """, nativeQuery = true)
  List<LoadAssignmentConfirmationProjection> findPendingLoadAssignmentConfirmations(
      @Param("userCompanyId") Integer userCompanyId,
      @Param("abUserId") Integer abUserId);

  List<Assignment> findAllByUnloadingTicketFileId(int fileId);

  List<Assignment> findAllByLoadingTicketFileId(int fileId);

  List<Assignment> findByLoadInvoiceId(int loadInvoiceId);

  @Modifying
  @Transactional
  @Query(value = """
        select
            la.*,
            max(ffd.field_name is null) has_unmatched
        from loads l
          inner join load_assignments la using(load_id)
          inner join files f on la.loading_ticket_file_id = f.file_id
          inner join file_fields ff on ff.file_id = f.file_id
          left join file_field_definitions ffd using(field_name)
        where l.active = 1
          and la.has_unmatched_external_grades = 1
          and f.ocr_processed = 1
          and f.ocr_approved = 0
          and l.user_company_id = :userCompanyId
        group by la.load_assignment_id
        having has_unmatched = 0
      """, nativeQuery = true)
  List<Assignment> findAssignmentsNoLongerWithUnmatchedExternalGrades(@Param("userCompanyId") Integer userCompanyId);

  List<Assignment> findAllByLoadingTicketFileIdOrUnloadingTicketFileId(Integer loadingTicketFileId, Integer unloadingTicketFileId);
}
