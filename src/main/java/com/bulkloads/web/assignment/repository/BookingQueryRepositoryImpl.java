package com.bulkloads.web.assignment.repository;

import static com.bulkloads.web.assignment.repository.template.GetBookingsQueryTemplate.GET_BOOKINGS_QUERY_TEMPLATE;
import static com.bulkloads.web.assignment.repository.template.GetBookingsV2QueryTemplate.GET_BOOKINGS_RTI_QUERY_TEMPLATE_V2;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.api.TotalResponse;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.common.transformer.TotalResponseTransformer;
import com.bulkloads.web.assignment.service.dto.AssignmentBookingListResponse;
import com.bulkloads.web.assignment.service.dto.BookingRtiV2Response;
import com.bulkloads.web.assignment.service.dto.BookingSearchRequest;
import com.bulkloads.web.assignment.service.dto.transformer.AssignmentBookingListResponseTransformer;
import com.bulkloads.web.assignment.service.dto.transformer.BookingRtiV2ListResponseTransformer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class BookingQueryRepositoryImpl implements BookingQueryRepository {

  final JpaNativeQueryService jpaNativeQueryService;
  final AssignmentBookingListResponseTransformer assignmentBookingListResponseTransformer;
  final TotalResponseTransformer totalResponseTransformer;
  final ObjectMapper objectMapper;
  final BookingRtiV2ListResponseTransformer bookingRtiV2ListResponseTransformer;

  public List<AssignmentBookingListResponse> getBookings(
      final Boolean count,
      final List<Integer> toUIds,
      final Integer toCId,
      final Integer toAbUserId,
      final BookingSearchRequest bookingSearchRequest,
      final String orderBy,
      final Integer skip,
      final Integer limit) {

    Map<String, Object> params = new HashMap<>();

    params.put("count", count == null ? Boolean.FALSE : count);
    params.put("to_u_ids", toUIds);
    params.put("to_c_id", toCId);
    params.put("to_ab_user_id", toAbUserId);

    Map<String, Object> searchParams = objectMapper.convertValue(bookingSearchRequest, Map.class);
    params.putAll(searchParams);

    params.put("order_by", orderBy);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(
        GET_BOOKINGS_QUERY_TEMPLATE,
        params,
        assignmentBookingListResponseTransformer);
  }

  @Override
  public List<BookingRtiV2Response> findBookingsRtiV2(
      final int cId,
      final QueryParams queryParams,
      final Integer skip,
      final Integer limit) {

    Map<String, Object> params = queryParams.buildParamsMap();

    params.put("c_id", cId);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(
        GET_BOOKINGS_RTI_QUERY_TEMPLATE_V2,
        params,
        bookingRtiV2ListResponseTransformer);
  }

  @Override
  public TotalResponse findBookingsRtiTotalsV2(final int cId) {

    Map<String, Object> params = new HashMap<>();

    params.put("count", true);
    params.put("c_id", cId);

    return jpaNativeQueryService.queryForObject(
        GET_BOOKINGS_RTI_QUERY_TEMPLATE_V2,
        params,
        totalResponseTransformer);
  }
}
