package com.bulkloads.web.assignment.repository;

import java.util.HashMap;
import java.util.List;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.assignment.service.dto.AssignmentOcrReadyResponse;
import com.bulkloads.web.assignment.service.dto.transformer.AssignmentOcrReadyResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class AssignmentOcrQueryRepositoryImpl implements AssignmentOcrQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final AssignmentOcrReadyResponseTransformer assignmentOcrReadyResponseTransformer;

  @Override
  public List<AssignmentOcrReadyResponse> getAssignmentsOcrReady() {
    String query = """
        select
            la.load_assignment_id,
            la.agtrax_integration,
            la.has_unmatched_external_grades,
            c.company_name,
        
            if(la.to_ab_company_id is not null, to_abc.company_name, to_c.company_name) as to_company_name,
            if(la.to_ab_company_id is not null, to_abu.first_name, to_u.first_name) as to_first_name,
            if(la.to_ab_company_id is not null, to_abu.last_name, to_u.last_name) as to_last_name,
            if(la.to_ab_company_id is not null, to_abu.phone_1, to_u.cell_phone) as to_phone,
        
            if (la.is_rerouted and la.reroute_pickup_drop = 'pickup', reroute_abc.company_name, pickup_c.company_name) as pickup_company_name,
            if (la.is_rerouted and la.reroute_pickup_drop = 'pickup', reroute_abc.city, pickup_c.city) as pickup_city,
            if (la.is_rerouted and la.reroute_pickup_drop = 'pickup', reroute_abc.state, pickup_c.state) as pickup_state,
            if (la.is_rerouted and la.reroute_pickup_drop = 'drop', reroute_abc.company_name, drop_c.company_name) as drop_company_name,
            if (la.is_rerouted and la.reroute_pickup_drop = 'drop', reroute_abc.city, drop_c.city) as drop_city,
            if (la.is_rerouted and la.reroute_pickup_drop = 'drop', reroute_abc.state, drop_c.state) as drop_state,
            l.lo_commodity,
        
            la.assigned_date,
            la.delivered_date,
            la.assignment_status,
            origin_f.file_url as origin_file_url,
            destination_f.file_url as destination_file_url
        
        from loads l
            inner join load_assignments la on l.load_id = la.load_id
            inner join user_company c on la.user_company_id = c.user_company_id
        
            inner join ab_companies pickup_c on l.pickup_ab_company_id = pickup_c.ab_company_id
            inner join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
        
            inner join files origin_f on la.loading_ticket_file_id = origin_f.file_id
            inner join files destination_f on la.unloading_ticket_file_id = destination_f.file_id
        
            -- reroute info
            left join ab_companies reroute_abc on la.reroute_ab_company_id = reroute_abc.ab_company_id
        
            -- external user
            left join ab_companies to_abc on la.to_ab_company_id = to_abc.ab_company_id
            left join ab_users to_abu on la.to_ab_user_id = to_abu.ab_user_id
            -- internal user
            left join user_company to_c on la.to_user_company_id = to_c.user_company_id
            left join user_info to_u on la.to_user_id = to_u.user_id
        
        where l.user_company_id in (select user_company_id from api_key_integration_permissions inner join api_keys using(api_key_id) where app_name = 'agtrax')
            and la.assignment_status in ('delivered', 'completed')
            and origin_f.ocr_processed = 1
            and destination_f.ocr_processed = 1
            and (origin_f.ocr_approved = 0 or destination_f.ocr_approved = 0)
            and origin_f.is_unreadable = 0
            and destination_f.is_unreadable = 0
          order by la.load_assignment_id desc
        limit 100
        """;

    return jpaNativeQueryService.query(query, new HashMap<>(), assignmentOcrReadyResponseTransformer);
  }

  @Override
  public List<AssignmentOcrReadyResponse> getAssignmentsOcrWaiting() {
    String query = """
        select
            la.load_assignment_id,
            la.agtrax_integration,
            la.has_unmatched_external_grades,
            c.company_name,
        
            if(la.to_ab_company_id is not null, to_abc.company_name, to_c.company_name) as to_company_name,
            if(la.to_ab_company_id is not null, to_abu.first_name, to_u.first_name) as to_first_name,
            if(la.to_ab_company_id is not null, to_abu.last_name, to_u.last_name) as to_last_name,
            if(la.to_ab_company_id is not null, to_abu.phone_1, to_u.cell_phone) as to_phone,
        
            if (la.is_rerouted and la.reroute_pickup_drop = 'pickup', reroute_abc.company_name, pickup_c.company_name) as pickup_company_name,
            if (la.is_rerouted and la.reroute_pickup_drop = 'pickup', reroute_abc.city, pickup_c.city) as pickup_city,
            if (la.is_rerouted and la.reroute_pickup_drop = 'pickup', reroute_abc.state, pickup_c.state) as pickup_state,
            if (la.is_rerouted and la.reroute_pickup_drop = 'drop', reroute_abc.company_name, drop_c.company_name) as drop_company_name,
            if (la.is_rerouted and la.reroute_pickup_drop = 'drop', reroute_abc.city, drop_c.city) as drop_city,
            if (la.is_rerouted and la.reroute_pickup_drop = 'drop', reroute_abc.state, drop_c.state) as drop_state,
            l.lo_commodity,
        
            la.assigned_date,
            la.delivered_date,
            la.assignment_status,
            origin_f.file_url as origin_file_url,
            destination_f.file_url as destination_file_url,
            origin_f.is_unreadable as origin_file_is_unreadable,
            destination_f.is_unreadable as destination_file_is_unreadable
        
        
        from loads l
            inner join load_assignments la on l.load_id = la.load_id
            inner join user_company c on la.user_company_id = c.user_company_id
        
            inner join ab_companies pickup_c on l.pickup_ab_company_id = pickup_c.ab_company_id
            inner join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
        
            left join files origin_f on la.loading_ticket_file_id = origin_f.file_id
            left join files destination_f on la.unloading_ticket_file_id = destination_f.file_id
        
            -- reroute info
            left join ab_companies reroute_abc on la.reroute_ab_company_id = reroute_abc.ab_company_id
        
            -- external user
            left join ab_companies to_abc on la.to_ab_company_id = to_abc.ab_company_id
            left join ab_users to_abu on la.to_ab_user_id = to_abu.ab_user_id
            -- internal user
            left join user_company to_c on la.to_user_company_id = to_c.user_company_id
            left join user_info to_u on la.to_user_id = to_u.user_id
        
        where l.user_company_id in (select user_company_id from api_key_integration_permissions inner join api_keys using(api_key_id) where app_name = 'agtrax')
            and  la.assignment_status in ('delivered', 'completed')
            and (
              (la.loading_ticket_file_id is null or (origin_f.is_unreadable = 1 and origin_f.ocr_processed = 1))
              OR
              (la.unloading_ticket_file_id is null or (destination_f.is_unreadable = 1 and destination_f.ocr_processed = 1))
            )
        order by la.load_assignment_id desc
        limit 100
        """;

    return jpaNativeQueryService.query(query, new HashMap<>(), assignmentOcrReadyResponseTransformer);
  }
}
