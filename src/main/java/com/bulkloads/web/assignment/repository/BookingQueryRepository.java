package com.bulkloads.web.assignment.repository;

import java.util.List;
import com.bulkloads.common.api.TotalResponse;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.web.assignment.service.dto.AssignmentBookingListResponse;
import com.bulkloads.web.assignment.service.dto.BookingRtiV2Response;
import com.bulkloads.web.assignment.service.dto.BookingSearchRequest;

public interface BookingQueryRepository {

  List<AssignmentBookingListResponse> getBookings(
      final Boolean count,
      final List<Integer> toUIds,
      final Integer toCId,
      final Integer toAbUserId,
      final BookingSearchRequest bookingSearchRequest,
      final String orderBy,
      final Integer skip,
      final Integer limit);

  List<BookingRtiV2Response> findBookingsRtiV2(
      final int cId,
      final QueryParams queryParams,
      final Integer skip,
      final Integer limit);

  TotalResponse findBookingsRtiTotalsV2(final int cId);
}
