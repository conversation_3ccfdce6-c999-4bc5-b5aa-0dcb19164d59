package com.bulkloads.web.assignment.repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import org.hibernate.cfg.AvailableSettings;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import jakarta.persistence.LockModeType;
import jakarta.persistence.QueryHint;

@Repository
public interface BookingRepository extends JpaRepository<Assignment, Integer>, BookingQueryRepository {

  Optional<Assignment> findByLoadAssignmentIdAndToUserCompanyUserCompanyIdAndToDeletedFalse(
      final int loadAssignmentId,
      final int userCompanyId);

  Optional<Assignment> findByLoadAssignmentIdAndToAbUserAbUserIdAndToDeletedFalse(int loadAssignmentId, int abUserId);

  @Query("""
        select la from Assignment la
        where
            la.toUserCompany.userCompanyId = :userCompanyId
        and la.toDeleted = false
        and (:#{#loadAssignmentIds.isEmpty()} = true or la.loadAssignmentId in (:loadAssignmentIds))
        order by la.loadAssignmentId
      """)
  List<Assignment> findLoadBookings(
      @Param("userCompanyId") final int userCompanyId,
      @Param("loadAssignmentIds") final List<Integer> loadAssignmentIds,
      final Pageable pageable);

  @Query("""
        select la from Assignment la
        where
            la.toUserCompany.userCompanyId = :userCompanyId
        and la.deleted = false
        and (:#{#loadAssignmentIds.isEmpty()} = true or la.loadAssignmentId in (:loadAssignmentIds))
        order by la.loadAssignmentId
      """)
  @Lock(LockModeType.PESSIMISTIC_WRITE)
  @QueryHints(@QueryHint(name = AvailableSettings.JAKARTA_LOCK_TIMEOUT, value = "10000"))
  List<Assignment> findLoadBookingsLocking(
      @Param("userCompanyId") final int userCompanyId,
      @Param("loadAssignmentIds") final List<Integer> loadAssignmentIds,
      final Pageable pageable);

  @Modifying
  @Query("UPDATE Assignment a SET a.loadInvoiceId = :invoiceId, a.modifiedDate = :modifiedDate WHERE a.loadAssignmentId IN (:loadAssignmentIds)")
  int updateLoadInvoiceIds(@Param("loadAssignmentIds") List<Integer> loadAssignmentIds,
                           @Param("invoiceId") int invoiceId,
                           @Param("modifiedDate") Instant modifiedDate);

  @Query("""
      select a from Assignment a
      where a.loadingTicketNumber = :loadingTicketNumber
      and a.toUser.userId = :userId
      and a.loadAssignmentId not in (:loadAssignmentIds)
      and a.createdDate >= :createdDateAfter
      and a.toDeleted = false
      """)
  List<Assignment> findByLoadingTicketNumberAndUserAndCreatedDateAfter(
      @Param("loadingTicketNumber") String loadingTicketNumber,
      @Param("userId") int userId,
      @Param("loadAssignmentIds") List<Integer> loadAssignmentIds,
      @Param("createdDateAfter") Instant createdDateAfter
  );
}
