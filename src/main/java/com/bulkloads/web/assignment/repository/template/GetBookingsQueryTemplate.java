package com.bulkloads.web.assignment.repository.template;

import org.intellij.lang.annotations.Language;

public class GetBookingsQueryTemplate {

  @Language("SQL")
  public static final String GET_BOOKINGS_QUERY_TEMPLATE = """
      SELECT
          <% if (paramIsTrue("count")) { %>
              count(*) as count
          <% } else { %>
              -- load_assignments
              la.load_id,
              la.to_load_id,
              la.load_assignment_id,
              la.assignment_status,
              la.assigned_date,
              la.created_date,
              la.dispatched_date,
              la.loading_date,
              la.enroute_date,
              la.unloading_date,
              la.delivered_date,
              la.completed_date,
              la.pickup_number,
              la.pickup_notes,
              la.drop_number,
              la.drop_notes,
              la.work_order_number,
              la.inside_notes,
              la.personal_message,
              la.loaded_weight,
              la.unload_weight,
              la.loading_ticket_number,
              la.unloading_ticket_number,
              la.bol_number,
              la.load_assignment_number,
              la.hauled_notes,
              la.scheduled_hauled_date,
              la.scheduled_pickup_date,
              pickup_c.timezone as pickup_timezone,
              la.scheduled_drop_date,
              drop_c.timezone as drop_timezone,
              la.hauled_date,
              la.mileage,
              la.number_of_files,
              la.total_files_size,
              la.needs_attention,
      
              la.truck_user_company_equipment_id,
              la.trailer_user_company_equipment_id,
      
              case
                  when la.truck_user_company_equipment_id is null then ''
                  else trim(concat(
                      ifnull(equipment_trucks.external_equipment_id, ''), ' - ',
                      ifnull(equipment_trucks.model_year, ''),
                      ' ',
                      ifnull(equipment_trucks.make, '')
                  ))
              end as truck_user_company_equipment,
      
              case
                  when la.trailer_user_company_equipment_id is null then ''
                  else trim(concat(
                      ifnull(equipment_trailers.external_equipment_id, ''), ' - ',
                      ifnull(equipment_trailers.model_year, ''),
                      ' ',
                      ifnull(equipment_trailers.make, '')
                  ))
              end as trailer_user_company_equipment,
      
              la.original_rate,
              la.original_rate_type,
              la.original_rate_visible,
              la.original_rate_percentage,
      
              la.rate,
              la.rate_type,
              la.est_weight,
              la.est_miles,
              la.est_hours,
              la.est_quantity,
              la.est_subtotal,
              la.est_surcharges,
              la.est_total,
              la.est_rate_per_mile,
              la.bill_weight_use,
              la.bill_weight,
              la.bill_miles,
              la.bill_hours,
              la.bill_quantity,
              la.bill_subtotal,
              la.bill_surcharges,
              la.bill_total,
              la.bill_rate_per_mile,
      
              -- payments
              la.payment,
              la.payment_notes,
              la.payment_date,
              la.payment_approved,
              la.payment_approved_date,
              la.paid,
              la.paid_date,
              la.to_payment,
              la.to_payment_notes,
              la.to_payment_date,
              la.to_paid,
              la.to_paid_date,
      
              -- confirmation fields
              la.confirmation_file_id,
              la.confirmation_file_code,
              conf_files.file_url as confirmation_file_url,
              conf_files.thumb_url as confirmation_thumb_url,
              la.confirmation_sent_date,
              la.confirmation_sent_method,
              la.confirmation_revised_date,
              la.confirmation_opened_date,
              la.confirmation_confirmed_date,
              la.confirmation_confirmed_method,
              la.confirmation_email_status,
              la.confirmation_to_ab_user_ids,
              la.confirmation_cc_others,
      
      
              -- load fields
              l.active,
              l.rate_product_category_id,
              l.rate_product_category as product,
              l.commodity_id,
              l.lo_commodity,
              l.ship_from,
              l.ship_to,
              l.is_hazmat,
              l.estimated_miles,
              l.load_bearing,
              l.load_bearing_direction,
              l.lo_contract_number,
              l.post_date,
              l.user_id as dispatcher_user_id,
              l.equipment_names,
              l.load_access,
              l.number_of_loads,
              l.number_of_delivered_loads,
              l.number_of_available_loads,
              l.is_managed,
              l.equipment_ids,
      
              pickup_c.ab_company_id as pickup_ab_company_id,
              pickup_c.company_name as pickup_company_name,
              pickup_c.census_num as pickup_census_num,
              pickup_c.mc_num as pickup_mc_num,
      
              pickup_c.address as pickup_address,
              pickup_c.city as pickup_city,
              pickup_c.state as pickup_state,
              pickup_c.zip as pickup_zip,
              pickup_c.country as pickup_country,
              pickup_c.location as pickup_location,
              pickup_c.latitude as pickup_latitude,
              pickup_c.longitude as pickup_longitude,
              pickup_c.company_phone as pickup_company_phone,
              pickup_c.receiving_hours as pickup_receiving_hours,
              pickup_c.directions as pickup_directions,
              pickup_c.company_notes as pickup_company_notes,
              pickup_c.appt_required as pickup_appt_required,
              l.pickup_po,
      
              drop_c.ab_company_id as drop_ab_company_id,
              drop_c.company_name as drop_company_name,
              drop_c.census_num as drop_census_num,
              drop_c.mc_num as drop_mc_num,
      
              drop_c.address as drop_address,
              drop_c.city as drop_city,
              drop_c.state as drop_state,
              drop_c.zip as drop_zip,
              drop_c.country as drop_country,
              drop_c.location as drop_location,
              drop_c.latitude as drop_latitude,
              drop_c.longitude as drop_longitude,
      
              drop_c.company_phone as drop_company_phone,
              drop_c.receiving_hours as drop_receiving_hours,
              drop_c.directions as drop_directions,
              drop_c.company_notes as drop_company_notes,
              drop_c.appt_required as drop_appt_required,
              l.drop_po,
      
              ifnull(hiring_l.originals_required, l.originals_required) as originals_required,
              ifnull(hiring_l.washout_required, l.washout_required) as washout_required,
              ifnull(hiring_l.required_file_type_ids, l.required_file_type_ids) as required_file_type_ids,
              ifnull(hiring_l.required_file_types, l.required_file_types) as required_file_types,
              ifnull(hiring_l.required_files_note, l.required_files_note) as required_files_note,
      
              -- created company
              la.created_by_user_id,
              la.created_by_user_company_id,
      
              -- hiring company
      
              la.hiring_ab_user_id,
              la.user_id,
              ifnull(hiring_abu.first_name, u.first_name) as first_name,
              ifnull(hiring_abu.last_name, u.last_name) as last_name,
              ifnull(hiring_abu.email, u.email) as email,
              ifnull(hiring_abu.phone_1, u.cell_phone) as phone_1,
      
              la.hiring_ab_company_id,
              la.user_company_id,
              ifnull(hiring_abc.company_name, c.company_name) as company_name,
              ifnull(hiring_abc.user_types, c.user_types) as user_types,
      
              c.avg_rating,
              c.rating_count,
              c.company_logo_url,
              c.company_logo_thumb_url,
      
              -- hired company
      
              la.to_ab_user_id,
              la.to_user_id,
      
              ifnull(to_u.first_name, to_abu.first_name) as to_first_name,
              ifnull(to_u.last_name, to_abu.last_name) as to_last_name,
              ifnull(to_u.email, to_abu.email) as to_email,
              ifnull(to_u.cell_phone, to_abu.phone_1) as to_phone_1,
      
              la.to_ab_company_id,
              la.to_user_company_id,
              ifnull(to_c.company_name, to_abc.company_name) as to_company_name,
              ifnull(to_c.user_types, to_abc.user_types) as to_user_types,
      
              to_c.avg_rating as to_avg_rating,
              to_c.rating_count as to_rating_count,
              to_c.company_logo_url as to_company_logo_url,
              to_c.company_logo_thumb_url as to_company_logo_thumb_url,
      
              -- bill to company
              la.bill_to_ab_company_id,
              if(la.bill_to_ab_company_id is null, c.company_name, bill_to_abc.company_name) as bill_to_company_name,
              la.bill_to_ab_user_id,
              if(la.bill_to_ab_company_id is null, u.first_name, bill_to_abu.first_name) as bill_to_first_name,
              if(la.bill_to_ab_company_id is null, u.last_name, bill_to_abu.last_name) as bill_to_last_name,
              if(la.bill_to_ab_company_id is null, u.email, bill_to_abu.email) as bill_to_email,
      
              -- sharing fields
              la.shared_with_hiring_company,
              la.shared_with_hiring_company_response,
              la.shared_with_hired_company,
              la.shared_with_hired_company_response,
      
              (la.to_load_id is null and la.shared_with_hired_company = 1 and la.shared_with_hired_company_response = 'Pending') as new_share,
      
              -- chain fields
              la.child_load_assignment_id,
              la.parent_load_assignment_id,
              la.chain_load_assignment_id,
      
              -- invoice_fields
              la.auto_invoice,
              la.ready_to_invoice,
              0 as parent_ready_to_invoice,
              la.load_invoice_id,
              la.load_invoice_id is not null as invoiced,
              0 as parent_invoiced,
      
              li.invoice_date,
              li.email_status as invoice_email_status,
              li.email_status_date as invoice_email_status_date,
              lif.file_url as invoice_file_url,
              lif.thumb_url as invoice_thumb_url,
              li.bill_to_user_id,
              li.bill_to_user_company_id,
              ifnull(li.archived,0) as archived,
              ifnull(li.bill_to_archived,0) as bill_to_archived,
      
              "" as settlement_file_url,
      
              -- geo fields
              la.geo_share_location,
              la.geo_request_status,
              la.geo_request_date,
              la.geo_tracking_enabled,
              la.geo_tracking_stop_date,
              la.geo_tracking_until,
              la.geo_latitude,
              la.geo_longitude,
              la.geo_speed,
              la.geo_heading,
              la.geo_updated_date,
      
      
              -- external fields
              l.external_load_id,
              pickup_c.external_ab_company_id as pickup_external_ab_company_id,
              drop_c.external_ab_company_id as drop_external_ab_company_id,
              hiring_abc.external_ab_company_id as hiring_external_ab_company_id,
              hiring_abu.external_ab_user_id as hiring_external_ab_user_id,
              cast(null as char) as to_external_ab_company_id,
              cast(null as char) as to_external_ab_user_id,
              bill_to_abc.external_ab_company_id as bill_to_external_ab_company_id,
              bill_to_abu.external_ab_user_id as bill_to_external_ab_user_id,
      
              -- parent booking number of files
              cast(null as decimal) as parent_number_of_files,
      
              -- exports
              cast(null as char) as export_date,
      
              -- offers
              la.booked_from_offer_recipient_id,
      
              -- parent hiring info not visible for bookings
              cast(null as char) as parent_hiring_company_name,
              cast(null as decimal) as lo_rate,
              cast(null as char) as lo_rate_type,
      
              1 as is_booking,
              la.is_rerouted,
              la.is_reassigned,
              la.is_intra_company,
              la.is_driver,
              la.modified_date,
      
              -- Reroutes, reroute request
              la.reroute_request,
              la.reroute_request_reason,
              la.reroute_request_date,
              -- Reroutes, rerouted start
              la.reroute_date,
              la.reroute_by_user_id,
              la.reroute_reason,
              la.reroute_contract_id,
              la.reroute_contract_number,
              la.previous_load_assignment_number,
              la.previous_pickup_number,
              la.previous_drop_number,
              la.previous_work_order_number,
              la.previous_rate,
              la.previous_rate_type,
              la.previous_bill_subtotal,
              la.previous_bill_surcharges,
              la.previous_bill_total,
              la.previous_bill_rate_per_mile,
              la.reroute_pickup_drop,
      
              -- reroute_c rerouted to company
              reroute_c.ab_company_id as reroute_ab_company_id,
              reroute_c.external_ab_company_id as reroute_external_ab_company_id,
              reroute_c.company_name as reroute_company_name,
              reroute_c.census_num as reroute_census_num,
              reroute_c.mc_num as reroute_mc_num,
      
              reroute_c.address as reroute_address,
              reroute_c.city as reroute_city,
              reroute_c.state as reroute_state,
              reroute_c.zip as reroute_zip,
              reroute_c.country as reroute_country,
              reroute_c.location as reroute_location,
              reroute_c.latitude as reroute_latitude,
              reroute_c.longitude as reroute_longitude,
      
              reroute_c.company_phone as reroute_company_phone,
              reroute_c.receiving_hours as reroute_receiving_hours,
              reroute_c.directions as reroute_directions,
              reroute_c.company_notes as reroute_company_notes,
              reroute_c.appt_required as reroute_appt_required,
              -- l.reroute_po,
      
              -- deletes
              0 as parent_deleted,
              '' as parent_deleted_message,
              la.deleted,
              la.deleted_message,
              la.to_deleted_message,
              <% if (paramIsTrue("include_deleted")) { %>
                  l.deleted OR la.to_deleted as to_deleted
              <% } else { %>
                  0 as to_deleted
              <% } %>
      
              , u.accounting_email
              , hiring_l.contract_id as hiring_contract_id
            <% } %>
      
        FROM load_assignments la
      
            inner join loads l on l.load_id = ifnull(la.to_load_id,la.load_id) -- needed for new shared bookings that don't have a to_load_id yet
            left join loads hiring_l on hiring_l.load_id = la.load_id
            left join ab_companies pickup_c on l.pickup_ab_company_id = pickup_c.ab_company_id
            left join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
            left join files conf_files on la.confirmation_file_id = conf_files.file_id
      
            -- reroutes
            left join ab_companies reroute_c on ifnull(la.reroute_to_ab_company_id,la.reroute_ab_company_id) = reroute_c.ab_company_id
      
            -- hiring ab
            left join ab_users hiring_abu on la.hiring_ab_user_id = hiring_abu.ab_user_id
            left join ab_companies hiring_abc on la.hiring_ab_company_id = hiring_abc.ab_company_id
      
            -- hiring user_info
            left join user_info u on u.user_id = la.user_id
            left join user_company c on u.user_company_id = c.user_company_id
      
      
            -- hired ab
            left join ab_users to_abu on la.to_ab_user_id = to_abu.ab_user_id
            left join ab_companies to_abc on la.to_ab_company_id = to_abc.ab_company_id
      
            -- hired user_info
            left join user_info to_u on to_u.user_id = la.to_user_id
            left join user_company to_c on to_u.user_company_id = to_c.user_company_id
            -- left join user_info bl_u on to_abu.bl_user_id = bl_u.user_id extra condition for shares
      
            -- bill_to company
            left join ab_companies bill_to_abc on bill_to_abc.ab_company_id = la.bill_to_ab_company_id
            left join ab_users bill_to_abu on bill_to_abu.ab_user_id = la.bill_to_ab_user_id
      
            -- invoices
            left join load_invoices li on la.load_invoice_id = li.load_invoice_id
            left join files  lif on li.invoice_file_id = lif.file_id
      
            -- user company equipments
            left join user_company_equipments equipment_trucks on la.truck_user_company_equipment_id = equipment_trucks.user_company_equipment_id
            left join user_company_equipments equipment_trailers on la.trailer_user_company_equipment_id = equipment_trailers.user_company_equipment_id
      
      
        WHERE 1=1
      
            <% if (paramIsTrue("include_deleted")) { %>
              AND l.deleted = 0
              AND la.to_deleted = 0
            <% } %>
      
            <% if (paramExistsAdd("to_ab_user_id")) { %>
                and la.to_ab_user_id = :to_ab_user_id
            <% } else { %>
              <% if (paramExistsAdd("to_c_id")) { %>
                and la.to_user_company_id = :to_c_id
              <% } %>
      
              <% if (paramExists("tms_active")) { %>
                AND <% if (!tms_active) { %>NOT<% } %>
                (
                  l.active
                  <% if (paramExistsAdd("to_u_ids")) { %>
                      AND la.to_user_id in (:to_u_ids)
                  <% } %>
      
                  OR la.load_invoice_id is null and la.ready_to_invoice = 1
                  OR la.load_invoice_id is not null AND li.archived = 0
                )
              <% } else { %>
                <% if (paramExistsAdd("to_u_ids")) { %>
                    AND la.to_user_id in (:to_u_ids)
                <% } %>
              <% } %>
      
            <% } %>
      
            <% if (paramExists("new_shares")) { %>
              <% if (new_shares) { %>
                and la.to_load_id is null
                and la.shared_with_hired_company = 1
                and la.confirmation_sent_date is not null
                and la.shared_with_hired_company_response = 'Pending'
              <% } else { %>
                and (
                  la.to_load_id is not NULL
                  OR
                  la.shared_with_hired_company = 1
                  and la.confirmation_sent_date is not null
                  and la.shared_with_hired_company_response = 'Accepted'
                )
              <% } %>
            <% } else { %>
              -- both
              AND (
                la.to_load_id is not NULL
                OR
                la.shared_with_hired_company = 1
                and la.confirmation_sent_date is not null
                -- AND la.shared_with_hired_company_response <> 'Dismissed'
              )
            <% } %>
      
            <% if (paramExistsAdd("active")) { %>
              AND l.active = :active
            <% } %>
      
            <% if (paramExists("completed")) { %>
              <% if (completed) { %>
                AND la.assignment_status+0 >= 7 -- delivered & completed
              <% } else { %>
                AND la.assignment_status+0 < 7 -- Unassigned, Assigned, Dispatched, Loading, En Route
              <% } %>
            <% } %>
      
            <% if (paramExists("invoiced")) { %>
                <% if (invoiced) { %>
                    AND la.load_invoice_id is not null
                <% } else { %>
                    AND la.load_invoice_id is null
                <% } %>
            <% } %>
      
            <% if (paramExistsAdd("load_invoice_id")) { %>
                and la.load_invoice_id = :load_invoice_id
            <% } %>
      
            <% if (paramExists("archived")) { %>
              <% if (archived) { %>
                AND li.archived = 1
              <% } else { %>
                AND li.archived = 0
              <% } %>
            <% } %>
      
            <% if (paramExists("auto_invoice")) { %>
              <% if (auto_invoice) { %>
                AND la.auto_invoice = 1
              <% } else { %>
                AND la.auto_invoice = 0
              <% } %>
            <% } %>
      
            <% if (paramExists("ready_to_invoice")) { %>
              <% if (ready_to_invoice) { %>
                AND la.ready_to_invoice = 1
              <% } else { %>
                AND la.ready_to_invoice = 0
              <% } %>
            <% } %>
      
            <% if (paramExists("intra_company")) { %>
              <% if (intra_company) { %>
                AND la.is_intra_company = 1
              <% } else { %>
                AND la.is_intra_company = 0
              <% } %>
            <% } %>
      
      
            <% if (paramExists("to_paid")) { %>
                AND la.to_paid = <% if (to_paid) { %>1<% } else { %>0<% } %>
            <% } %>
      
            <% if (paramExists("has_child")) { %>
                AND la.child_load_assignment_id <% if (has_child) { %>is not null<% } else { %>is null<% } %>
            <% } %>
      
            <% if (paramExistsAdd("last_modified_date")) { %>
                and la.modified_date >= :last_modified_date
            <% } %>
      
            <% if (paramExistsAdd("load_id")) { %>
                AND la.load_id = :load_id
            <% } %>
      
            <% if (paramExistsAdd("to_load_id")) { %>
                AND la.to_load_id = :to_load_id
            <% } %>
      
            <% if (paramExistsAdd("load_assignment_id")) { %>
                AND la.load_assignment_id = :load_assignment_id
            <% } %>
      
            <% if (paramExistsAdd("load_assignment_ids")) { %>
                AND la.load_assignment_id in (:load_assignment_ids)
            <% } %>
      
            <% if (paramExistsAdd("confirmation_file_id")) { %>
                AND la.confirmation_file_id = :confirmation_file_id
            <% } %>
      
            -- radius search
            <% if (paramExistsAdd("radius")) { %>
              <% if ( pickup_latitude != null && pickup_longitude != null) { %>
                <% params.put("pickup_latitude", pickup_latitude) %>
                <% params.put("pickup_longitude", pickup_longitude) %>
      
                AND st_distance_sphere(
                      point(:pickup_longitude, :pickup_latitude),
                      point(pickup_c.longitude, pickup_c.latitude))/1609.34
                      <= :radius
              <% } %>
      
              <% if (drop_latitude != null &&  drop_longitude != null) { %>
                <% params.put("drop_longitude", drop_longitude) %>
                <% params.put("drop_latitude", drop_latitude) %>
      
                AND st_distance_sphere(
                    point(:drop_longitude, :drop_latitude),
                    point(ifnull(reroute_c.longitude, drop_c.longitude), ifnull(reroute_c.latitude, drop_c.latitude)))/1609.34
                    <= :radius
              <% } %>
            <% } %>
      
            -- shipping dates search. loads range must be fully included in the search range
            <% if (paramExistsAdd("ship_from")) { %>
                AND l.ship_from >= :ship_from
            <% } %>
      
            <% if (paramExistsAdd("ship_to")) { %>
                AND l.ship_to <= :ship_to
            <% } %>
      
            <% if (paramExistsAdd("lo_contract_number")) { %>
                AND l.lo_contract_number = :lo_contract_number
            <% } %>
      
            <% if (paramExistsAdd("load_assignment_number")) { %>
                AND la.load_assignment_number = :load_assignment_number
            <% } %>
      
            <% if (paramExists("search_term")) { %>
              <% params.put("search_term_param", search_term + "%") %>
              AND (
                l.lo_contract_number like :search_term_param
                or la.load_assignment_number like :search_term_param
                or la.load_invoice_id like :search_term_param
                or hiring_abc.company_name like :search_term_param
              )
            <% } %>
      
      
          -- Sorting
          <% if (!paramIsTrue("count")) { %>
            <% if (paramExists("order_by")) { %>
              ORDER BY <% print(order_by) %>
            <% } %>
      
            <% if (paramExistsAdd("limit")) { %>
              LIMIT
              <% if (paramExistsAdd("skip")) { %>
                  :skip,
              <% } %>
              :limit
            <% } %>
          <% } %>
      """;
}
