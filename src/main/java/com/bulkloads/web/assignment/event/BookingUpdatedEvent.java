package com.bulkloads.web.assignment.event;

import static com.bulkloads.config.AppConstants.AssignmentAction.BOOKING_UPDATE;
import java.util.List;
import java.util.Set;
import lombok.Getter;

@Getter
public class BookingUpdatedEvent extends AssignmentBookingEvent {

  public BookingUpdatedEvent(final List<Integer> loadAssignmentId, final Set<String> affectedFields) {
    super(loadAssignmentId, BOOKING_UPDATE, affectedFields);
  }
}
