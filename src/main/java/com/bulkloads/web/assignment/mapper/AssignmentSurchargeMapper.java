package com.bulkloads.web.assignment.mapper;

import static java.util.Objects.isNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import com.bulkloads.web.assignment.domain.data.AssignmentSurchargeData;
import com.bulkloads.web.assignment.domain.entity.AssignmentSurcharge;
import com.bulkloads.web.assignment.domain.entity.SurchargeType;
import com.bulkloads.web.assignment.repository.SurchargeTypeRepository;
import com.bulkloads.web.assignment.service.dto.AssignmentSurchargeRequest;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class AssignmentSurchargeMapper {

  @Autowired
  SurchargeTypeRepository surchargeTypeRepository;

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "loadAssignment", ignore = true)
  public abstract AssignmentSurcharge dataToEntity(final AssignmentSurchargeData data);

  public abstract List<AssignmentSurcharge> dataToEntity(final List<AssignmentSurchargeData> data);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "loadAssignment", ignore = true)
  public abstract void dataToEntity(final AssignmentSurchargeData data, @MappingTarget final AssignmentSurcharge entity);

  protected void dataToEntity(final List<AssignmentSurchargeData> datas, @MappingTarget final List<AssignmentSurcharge> entities) {
    if (isNull(datas)) {
      return;
    }

    final List<AssignmentSurcharge> newEntities = new ArrayList<>();

    datas.forEach(d -> {
      if (isNull(d.getSurcharge())) {
        return;
      }
      final List<AssignmentSurcharge> matching = entities.stream()
          .filter(e -> e.getSurchargeType().equals(d.getSurchargeType())).toList();
      if (matching.isEmpty()) {
        final AssignmentSurcharge surcharge = dataToEntity(d);
        newEntities.add(surcharge);
      } else {
        matching.forEach(e -> {
          dataToEntity(d, e);
          newEntities.add(e);
        });
      }
    });

    entities.clear();
    entities.addAll(newEntities);
  }

  public Optional<List<AssignmentSurchargeData>> optionalSurchargeRequestsToOptionalSurcharges(
      final Optional<List<AssignmentSurchargeRequest>> srs) {

    if (isNull(srs)) {
      return null;
    }

    if (srs.isEmpty()) {
      return Optional.empty();
    }

    final List<AssignmentSurchargeRequest> requests = srs.get();
    final List<Integer> ids = collectSurchargeTypeIds(requests);
    final Map<Integer, SurchargeType> surchargeTypeById = collectSurchargeTypeById(ids);

    return Optional.of(requests.stream().map(asr -> new AssignmentSurchargeData()
        .setSurcharge(asr.getSurcharge())
        .setSurchargeType(surchargeTypeById.get(asr.getSurchargeTypeId()))).toList());
  }

  private Map<Integer, SurchargeType> collectSurchargeTypeById(final List<Integer> ids) {
    return surchargeTypeRepository.findAllById(ids).stream().collect(Collectors.toMap(SurchargeType::getId, t -> t));
  }

  private List<Integer> collectSurchargeTypeIds(final List<AssignmentSurchargeRequest> requests) {
    return requests.stream().map(AssignmentSurchargeRequest::getSurchargeTypeId).toList();
  }
}
