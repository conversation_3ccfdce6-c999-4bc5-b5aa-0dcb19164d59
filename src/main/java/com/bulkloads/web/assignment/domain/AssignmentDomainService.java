package com.bulkloads.web.assignment.domain;

import static com.bulkloads.common.validation.ValidationMethod.CREATE;
import static com.bulkloads.common.validation.ValidationMethod.UPDATE;
import static com.bulkloads.common.validation.ValidationUtils.exists;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsEmpty;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsFalse;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsTrue;
import static com.bulkloads.common.validation.ValidationUtils.hasChange;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static com.bulkloads.config.AppConstants.AbUserRole.DRIVER;
import static com.bulkloads.config.AppConstants.AssignmentStatus.UNASSIGNED;
import static com.bulkloads.config.AppConstants.ContactMethod.EMAIL;
import static com.bulkloads.config.AppConstants.RateType.TWO_K;
import static com.bulkloads.config.AppConstants.SharedWithResponse.ACCEPTED;
import static com.bulkloads.config.AppConstants.SharedWithResponse.PENDING;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.addressbook.abcompany.domain.AbCompanyDomainService;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.assignment.domain.data.AssignmentData;
import com.bulkloads.web.assignment.domain.data.BlankAssignmentData;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.mapper.AssignmentMapper;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.rate.repository.RateTypeRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AssignmentDomainService extends AbstractAssignmentBookingDomainService {

  private final AssignmentMapper assignmentMapper;
  private final AbCompanyDomainService abCompanyDomainService;
  private final UserService userService;
  private final RateTypeRepository rateTypeRepository;

  public Result<Assignment> create(final Assignment entity, final AssignmentData data) {
    return super.validate(entity, null, data, CREATE);
  }

  public Result<Assignment> createBlankAssignment(final Load load, final BlankAssignmentData data) {
    final int userId = UserUtil.getUserIdOrThrow();
    final User user = userService.findById(userId);
    final Assignment entity = assignmentMapper.blankAssignmentDataToEntity(data);
    entity.setUser(user);
    entity.setCreatedDate(Instant.now());
    entity.setUserCompany(user.getUserCompany());
    entity.setCreatedByUserId(userId);
    entity.setCreatedByUserCompanyId(user.getUserCompany().getUserCompanyId());
    entity.setLoad(load);
    entity.setRateType(findRateTypeById(TWO_K));
    entity.setPreviousRateType(findRateTypeById(TWO_K));
    entity.setBillWeightUse(load.getDefaultBillWeightUse());

    final Result<Assignment> assignmentResult = new Result<>(entity);
    assignmentResult.validate(validator, entity);
    return assignmentResult;
  }

  public Result<Assignment> update(final Assignment entity, final AssignmentData data) {
    return super.validate(entity, null, data, UPDATE);
  }

  @Override
  public void validateDataAndMapToEntity(final Result<Assignment> result,
                                         final Assignment entity,
                                         final Assignment existing,
                                         final Object dataObject,
                                         final ValidationMethod method) {

    final AssignmentData data = (AssignmentData) dataObject;

    final int userId = UserUtil.getUserIdOrThrow();
    final User user = userService.findById(userId);
    final UserCompany userCompany = user.getUserCompany();
    final int userCompanyId = userCompany.getUserCompanyId();

    final Load load = entity.getLoad();
    final Instant now = Instant.now();

    if (!load.getIsManaged()) {
      throw new ValidationException(LOAD_ID, "The load isn't managed and doesn't allow assignments to be created. "
          + "Set the load to managed to enable asssignments POST /loads/{load_id}/set_managed");
    }

    if (CREATE.equals(method)) {
      validateCreate(result, data);
    } else {
      validateUpdate(result, data);
    }

    entity.setModifiedDate(now);

    if (existsAndIsEmpty(data.getOriginalRateType())) {
      data.setOriginalRateType(rateTypeRepository.findById(TWO_K));
    }

    //if paid, require payment amount
    if (existsAndIsTrue(data.getPaid())) {
      if (isMissingOrIsEmpty(data.getPayment())) {
        result.addError(PAYMENT, "Enter a valid number");
      }

      if (existsAndIsFalse(data.getPaymentApproved()) || userCompany.getApproveLoadsForPayment()) {
        result.addError(PAID, "To mark a load as paid, set Payment Approved or disable the setting 'Approve loads for payment'");
      }
    }

    if (userCompany.getApproveLoadsForPayment()
        && (existsAndIsTrue(data.getPaymentApproved()) && isMissingOrIsEmpty(data.getPayment()))) {
      result.addError(PAYMENT, "Enter a valid number");
    }

    final Integer defaultBillToCompanyId = user.getDefaultBillToCompanyId();

    // external assignment
    validateToAbCompany(result, entity, data, defaultBillToCompanyId, user, userCompanyId);

    // driver assignment
    validateToUser(result, entity, data, userCompany, load);

    if (isMissingOrIsEmpty(data.getConfirmationSentMethod())) {
      data.setConfirmationSentMethod(Optional.of(EMAIL));
    }

    //validate that passed ab_user_ids for sending the confirmation to belong to your address book and to the right company
    //ab_user_ids should belong to the to_ab_company (TODO: or bill_to_company?)

    if (existsAndIsEmpty(data.getConfirmationToAbUsers())
        && !data.getConfirmationToAbUsers().get().stream()
        .allMatch(abUser -> abUser.getUserCompany().getUserCompanyId()
            .equals(userCompany.getUserCompanyId()))) {
      final List<String> abUserIds = data.getConfirmationToAbUsers().get().stream().map(AbUser::getAbUserId).map(Object::toString).toList();
      final String join = String.join(",", abUserIds);
      throw new BulkloadsException("Confirmation ab users [%s] must belong to your company [%s]".formatted(join, userCompanyId));
    }

    if (isMissingOrIsEmpty(data.getEstMiles())) {
      Optional.ofNullable(entity.getMileage()).map(Optional::of).ifPresent(data::setEstMiles);
    }

    super.validateSurcharges(data, result);

    super.validateOcrFields(result, entity, data.getHauledDate(),
        data.getLoadingTicketNumber(), data.getLoadedVolume(), data.getLoadedWeight(),
        data.getUnloadingTicketNumber(), data.getUnloadVolume(), data.getUnloadWeight());

    // keep scheduledHauledDate and scheduledPickupDate in sync
    syncHauledDateAndPickupDate(result, data);

    if (result.hasErrors()) {
      throw new ValidationException(result.getErrors());
    }
  }

  private static void syncHauledDateAndPickupDate(Result<Assignment> result, AssignmentData data) {
    if (exists(data.getScheduledPickupDate()) && isMissingOrIsEmpty(data.getScheduledHauledDate())) {
      if (isEmpty(data.getScheduledPickupDate())) {
        data.setScheduledHauledDate(Optional.empty());
      } else {
        data.setScheduledHauledDate(Optional.of(data.getScheduledPickupDate().get().atZone(ZoneId.systemDefault()).toLocalDate()));
      }
    }
    // use deprecated scheduledHauledDate for the scheduledPickupDate
    if (exists(data.getScheduledHauledDate()) && isMissingOrIsEmpty(data.getScheduledPickupDate())) {
      if (isEmpty(data.getScheduledHauledDate())) {
        data.setScheduledPickupDate(Optional.empty());
      } else {
        data.setScheduledPickupDate(Optional.of(data.getScheduledHauledDate().get().atStartOfDay(ZoneId.systemDefault()).toInstant()));
      }
    }

    // validate scheduled pickup/drop time
    if (existsAndIsNotEmpty(data.getScheduledPickupDate()) && existsAndIsNotEmpty(data.getScheduledDropDate())) {
      if (data.getScheduledDropDate().get().isBefore(data.getScheduledPickupDate().get())) {
        result.addError(SCHEDULED_DROP_DATE, "Drop time cannot be before pickup time");
      }
    }
  }

  private static void validateToUser(Result<Assignment> result, Assignment entity, AssignmentData data, UserCompany userCompany, Load load) {
    if (existsAndIsNotEmpty(data.getToUser())) {
      final int carrierUserCompanyId = data.getToUser().get().getUserCompany().getUserCompanyId();
      if (carrierUserCompanyId == userCompany.getUserCompanyId()) {
        entity.setToLoad(load);
        entity.setIsIntraCompany(true);
        entity.setIsDriver(true);
        entity.setToUserCompany(data.getToUser().get().getUserCompany());
      } else {
        if (isNull(data.getToAbUser())) {
          result.addError(TO_USER_ID, "The driver was not found in your company");
        }
      }
    }
  }

  private void validateToAbCompany(Result<Assignment> result, Assignment entity, AssignmentData data, Integer defaultBillToCompanyId, User user,
                                   int userCompanyId) {
    if (existsAndIsNotEmpty(data.getToAbCompany())) {
      final AbCompany toAbCompany = data.getToAbCompany().get();
      final int carriersUserCompanyId = toAbCompany.getUserCompany().getUserCompanyId();

      if (nonNull(defaultBillToCompanyId) && carriersUserCompanyId == defaultBillToCompanyId) {
        final AbCompany replicatedAbCompany = abCompanyDomainService.replicate(user, toAbCompany);
        data.setToAbCompany(Optional.of(replicatedAbCompany));
      } else if (carriersUserCompanyId != userCompanyId) {
        result.addError(TO_AB_COMPANY_ID, "The company was not found in your address book");
      }

      if (existsAndIsNotEmpty(data.getToAbUser())) {
        final AbUser toAbUser = data.getToAbUser().get();
        final User blUser = toAbUser.getBlUser();
        if (nonNull(blUser)) {
          data.setToUser(Optional.of(blUser));
          entity.setToUserCompany(blUser.getUserCompany());
        }

        final boolean isDriver = toAbUser.getAbUserRoles().stream()
            .anyMatch(role -> role.getAbUserRole().equals(DRIVER));

        entity.setIsDriver(isDriver);
      }
    }
  }

  private void validateCreate(final Result<Assignment> result, final AssignmentData assignmentData) {
    final Assignment assignment = result.getEntity();
    final int userId = UserUtil.getUserIdOrThrow();
    final User user = userService.findById(userId);
    final UserCompany userCompany = user.getUserCompany();
    final int userCompanyId = userCompany.getUserCompanyId();
    final Load load = assignment.getLoad();
    final Instant now = Instant.now();

    assignment.setCreatedByUserId(userId);
    assignment.setCreatedByUserCompanyId(userCompanyId);
    assignment.setUser(user);
    assignment.setUserCompany(userCompany);
    assignment.setAssignedDate(now);
    assignment.setAssignedByUserId(userId);

    if (nonNull(assignment.getParentLoadAssignment())) {
      assignment.getParentLoadAssignment().setIsReassigned(true);
    }

    if (load.getNumberOfAvailableLoads() < assignmentData.getNumberOfLoads().get()) {
      result.addError(NUMBER_OF_LOADS, "Only %s loads remain to be assigned".formatted(load.getNumberOfAvailableLoads()));
    }

    if (isMissingOrIsEmpty(assignmentData.getNumberOfLoads())) {
      assignmentData.setNumberOfLoads(Optional.of(1));
    }

    Optional.ofNullable(load.getPickupDropMiles()).ifPresent(assignment::setMileage);

    if (existsAndIsTrue(assignmentData.getSharedWithHiredCompany())) {
      assignmentData.setSharedWithHiredCompany(Optional.of(true));
      assignment.setSharedWithHiredCompanyResponse(PENDING);
    }

    //either the to_ab_company_id OR the to_user_id is required
    if (exists(assignmentData.getAssignmentStatus()) && !assignmentData.getAssignmentStatus().get().equals(UNASSIGNED)
        && (isMissingOrIsEmpty(assignmentData.getToAbCompany()) && isMissingOrIsEmpty(assignmentData.getToUser()))) {
      result.addError(TO_AB_COMPANY_ID, "Company was not specified");
      result.addError(TO_USER_ID, "Driver was not specified");
    }

    if (isMissingOrIsEmpty(assignmentData.getEstWeight())) {
      assignmentData.setEstWeight(Optional.of(52000d));
    }

    if (isMissingOrIsEmpty(assignmentData.getEstVolume())) {
      assignmentData.setEstVolume(Optional.of(5000d));
    }

    if (isMissingOrIsEmpty(assignmentData.getSharedWithHiredCompany())) {
      assignmentData.setSharedWithHiredCompany(Optional.of(true));
      assignment.setSharedWithHiredCompanyResponse(PENDING);
    }

  }

  private void validateUpdate(final Result<Assignment> result, final AssignmentData assignmentData) {
    final Assignment assignment = result.getEntity();
    final int userId = UserUtil.getUserIdOrThrow();
    final User user = userService.findById(userId);
    final UserCompany userCompany = user.getUserCompany();
    final int userCompanyId = userCompany.getUserCompanyId();
    final Instant now = Instant.now();

    if (assignment.getToDeleted()) {
      throw new ValidationException(LOGIN, "The load assignment has been rejected. Please Unassign the load.");
    }

    if (nonNull(assignment.getParentLoadAssignment()) && assignment.getParentLoadAssignment().getDeleted()) {
      throw new ValidationException(LOGIN, "The load was deleted. Please Unassign the load and Delete it.");
    }

    validateLoadCannotBeReassigned(assignment, assignmentData);

    if (userCompanyId != assignment.getCreatedByUserCompanyId()) {
      checkEditPermissions(result, assignment, assignmentData);
    }

    if (existsAndIsFalse(assignmentData.getSharedWithHiredCompany())
        && assignment.getSharedWithHiredCompany()
        && ACCEPTED.equals(assignment.getSharedWithHiredCompanyResponse())) {
      result.addError(SHARED_WITH_HIRING_COMPANY, "The hired company has already accepted the share and it cannot be un-shared");
    }

    if (existsAndIsNotEmpty(assignmentData.getAssignmentStatus())) {
      validateAssignmentStatusBacktracking(result, assignment, assignmentData.getAssignmentStatus().get());
    }

    if (assignment.getPaid() && hasChange(assignment.getPayment(), assignmentData.getPayment())) {
      result.addError(PAYMENT, "The payment cannot be changed on a paid load");
    }

    assignment.setEditDate(now);
    assignment.setEditByUserId(userId);

    // default estWeight and estVolume
    if (isNull(assignment.getEstWeight())) {
      assignment.setEstWeight(52000d);
    }
    if (isNull(assignment.getEstVolume())) {
      assignment.setEstVolume(5000d);
    }

  }

  @Override
  public void mapToEntityAuto(Object data, final Assignment entity) {

    assignmentMapper.assignmentDataToEntity((AssignmentData) data, entity);
  }

  @Override
  public void validateEntity(final Result<Assignment> result, final Assignment entity) {
    if (nonNull(entity.getToAbCompany()) && isNull(entity.getRate())) {
      result.addError(RATE, "Enter the rate");
    }

    super.validateEntity(result, entity);
  }

  private void validateLoadCannotBeReassigned(final Assignment entity, final AssignmentData data) {
    // Check ToAbUser changes
    if (hasChange(entity.getToAbUser(), data.getToAbUser())) {
      throw new ValidationException(LOAD_ASSIGNMENT_ID, "The load cannot be re-assigned. Please first Unassign.");
    }

    // Check ToAbCompany changes
    if (hasChange(entity.getToAbCompany(), data.getToAbCompany())) {
      throw new ValidationException(LOAD_ASSIGNMENT_ID, "The load cannot be re-assigned. Please first Unassign.");
    }

    // Check ToUser changes
    if (hasChange(entity.getToUser(), data.getToUser())) {
      throw new ValidationException(LOAD_ASSIGNMENT_ID, "The load cannot be re-assigned. Please first Unassign.");
    }
  }

  private void checkEditPermissions(final Result<Assignment> result, final Assignment entity, final AssignmentData data) {
    //don't allow shares to a hiring company to be edited by the hiring company
    checkEditPermission(result, data.getAssignmentStatus(), entity.getAssignmentStatus(), ASSIGNMENT_STATUS);
    checkEditPermission(result, data.getPickupNumber(), entity.getPickupNumber(), PICKUP_NUMBER);
    checkEditPermission(result, data.getPickupNotes(), entity.getPickupNotes(), PICKUP_NOTES);
    checkEditPermission(result, data.getDropNumber(), entity.getDropNumber(), DROP_NUMBER);
    checkEditPermission(result, data.getDropNotes(), entity.getDropNotes(), DROP_NOTES);
    checkEditPermission(result, data.getWorkOrderNumber(), entity.getWorkOrderNumber(), WORK_ORDER_NUMBER);
    checkEditPermission(result, data.getInsideNotes(), entity.getInsideNotes(), INSIDE_NOTES);
    checkEditPermission(result, data.getOriginalRate(), entity.getOriginalRate(), ORIGINAL_RATE);
    checkEditPermission(result, data.getOriginalRateType(), entity.getOriginalRateType(), ORIGINAL_RATE_TYPE);
    checkEditPermission(result, data.getOriginalRatePercentage(), entity.getOriginalRatePercentage(), ORIGINAL_RATE_PERCENTAGE);
    checkEditPermission(result, data.getRate(), entity.getRate(), RATE);
    checkEditPermission(result, data.getRateType(), entity.getRateType(), RATE_TYPE);
    checkEditPermission(result, data.getEstWeight(), entity.getEstWeight(), EST_WEIGHT);
    checkEditPermission(result, data.getEstVolume(), entity.getEstVolume(), EST_VOLUME);
    checkEditPermission(result, data.getEstMiles(), entity.getEstMiles(), EST_MILES);
    checkEditPermission(result, data.getEstHours(), entity.getEstHours(), EST_HOURS);
    checkEditPermission(result, data.getSurcharges(), entity.getSurcharges(), SURCHARGES);
    checkEditPermission(result, data.getLoadedWeight(), entity.getLoadedWeight(), LOADED_WEIGHT);
    checkEditPermission(result, data.getUnloadWeight(), entity.getUnloadWeight(), UNLOAD_WEIGHT);
    checkEditPermission(result, data.getLoadedVolume(), entity.getLoadedVolume(), LOADED_VOLUME);
    checkEditPermission(result, data.getUnloadVolume(), entity.getUnloadVolume(), UNLOAD_VOLUME);
    checkEditPermission(result, data.getLoadingTicketNumber(), entity.getLoadingTicketNumber(), LOADING_TICKET_NUMBER);
    checkEditPermission(result, data.getBolNumber(), entity.getBolNumber(), BOL_NUMBER);
    checkEditPermission(result, data.getLoadAssignmentNumber(), entity.getLoadAssignmentNumber(), LOAD_ASSIGNMENT_NUMBER);
    checkEditPermission(result, data.getHauledNotes(), entity.getHauledNotes(), HAULED_NOTES);
    checkEditPermission(result, data.getHauledDate(), entity.getHauledDate(), HAULED_DATE);
    checkEditPermission(result, data.getBillWeightUse(), entity.getBillWeightUse(), BILL_WEIGHT_USE);
    checkEditPermission(result, data.getBillMiles(), entity.getBillMiles(), BILL_MILES);
    checkEditPermission(result, data.getBillHours(), entity.getBillHours(), BILL_HOURS);
    checkEditPermission(result, data.getSharedWithHiredCompany(), entity.getSharedWithHiredCompany(), SHARED_WITH_HIRING_COMPANY);
    checkEditPermission(result, data.getConfirmationToAbUsers(), entity.getConfirmationToAbUserIds(), CONFIRMATION_TO_AB_USER_IDS);
  }

  private RateType findRateTypeById(final String rateTypeId) {
    return rateTypeRepository.findById(rateTypeId)
        .orElseThrow(() -> new ValidationException(RATE_TYPE, "Could not find id %s".formatted(rateTypeId)));
  }

}
