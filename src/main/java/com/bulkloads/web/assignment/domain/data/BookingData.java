package com.bulkloads.web.assignment.domain.data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BookingData {

  //UPDATE
  private Optional<Integer> loadAssignmentId;
  private Optional<BigDecimal> originalRate;
  private Optional<BigDecimal> rate;
  private Optional<RateType> rateType;
  private Optional<RateType> originalRateType;
  private Optional<BigDecimal> originalRatePercentage;
  private Optional<Double> estWeight;
  private Optional<Double> estVolume;
  private Optional<BigDecimal> estMiles;
  private Optional<BigDecimal> estHours;
  private Optional<BigDecimal> estQuantity;
  private Optional<String> pickupNotes;
  private Optional<String> dropNotes;
  private Optional<String> insideNotes;
  private Optional<Boolean> sharedWithHiredCompany;
  private Optional<AbCompany> toAbCompany;
  private Optional<AbUser> toAbUser;
  private Optional<String> assignmentStatus;
  private Optional<Double> loadedWeight;
  private Optional<Double> unloadWeight;
  private Optional<Double> billWeight;
  private Optional<Double> loadedVolume;
  private Optional<Double> unloadVolume;
  private Optional<Double> billVolume;
  private Optional<BigDecimal> billMiles;
  private Optional<BigDecimal> billHours;
  private Optional<String> hauledNotes;
  private Optional<LocalDate> hauledDate;
  private Optional<String> loadingTicketNumber;
  private Optional<String> unloadingTicketNumber;
  private Optional<String> bolNumber;
  private Optional<AbCompany> billToAbCompany;
  private Optional<AbUser> billToAbUser;
  private Optional<BigDecimal> toPayment;
  private Optional<String> toPaymentNotes;
  private Optional<Boolean> toPaid;
  private Optional<BigDecimal> mileage;
  private Optional<Boolean> intraCompany;
  private Optional<Boolean> isBillable;
  private Optional<Boolean> isDriver;
  private Optional<User> toUser;
  private Optional<UserCompany> toUserCompany;
  private Optional<BigDecimal> estTotal;
  private Optional<BigDecimal> estSubtotal;
  private Optional<BigDecimal> estSurcharges;
  private Optional<String> billWeightUse;
  private Optional<BigDecimal> billQuantity;
  private Optional<BigDecimal> billSubtotal;
  private Optional<BigDecimal> billSurcharges;
  private Optional<Boolean> originalRateVisible;
  private Optional<Integer> createdByUserCompanyId;
  private Optional<Load> toLoad;
  private Optional<List<AssignmentFileData>> files;

  private Optional<Boolean> readyToInvoice;
  private Optional<Boolean> autoInvoice;
  private Optional<Boolean> shareDriverLocation;
  private Optional<Boolean> sharedWithHiringCompany;

  //CREATE
  private Optional<AbCompany> hiringAbCompany;
  private Optional<AbUser> hiringAbUser;
  private Optional<BigDecimal> loRate;
  private Optional<RateType> loRateType;
  private Optional<BigDecimal> loEstimatedWeight;
  private Optional<BigDecimal> loEstimatedVolume;
  private Optional<BigDecimal> loEstMiles;
  private Optional<BigDecimal> loEstHours;
  private Optional<String> defaultBillWeightUse;
  private Optional<List<AssignmentSurchargeData>> surcharges;
  private Optional<String> loadAssignmentNumber;
  private Optional<String> pickupNumber;
  private Optional<String> dropNumber;
  private Optional<String> workOrderNumber;
}
