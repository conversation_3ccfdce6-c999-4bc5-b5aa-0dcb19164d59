package com.bulkloads.web.assignment.domain;

import static com.bulkloads.common.validation.ValidationMethod.CREATE;
import static com.bulkloads.common.validation.ValidationMethod.UPDATE;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsFalse;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.common.validation.ValidationUtils.hasChange;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static com.bulkloads.config.AppConstants.AssignmentStatus.ASSIGNED;
import static com.bulkloads.config.AppConstants.AssignmentStatus.COMPLETED;
import static com.bulkloads.config.AppConstants.AssignmentStatus.DELIVERED;
import static com.bulkloads.config.AppConstants.ContactMethod.EMAIL;
import static com.bulkloads.config.AppConstants.RateType.GALLON;
import static com.bulkloads.config.AppConstants.RateType.HOUR;
import static com.bulkloads.config.AppConstants.RateType.LITER;
import static com.bulkloads.config.AppConstants.RateType.MILE;
import static com.bulkloads.config.AppConstants.RateType.TWO_K;
import static com.bulkloads.config.AppConstants.SharedWithResponse.ACCEPTED;
import static com.bulkloads.config.AppConstants.UserPermission.UPDATE_LOAD_BOOKING_DRIVER;
import static com.bulkloads.config.AppConstants.UserPermission.UPDATE_LOAD_BOOKING_GENERAL;
import static com.bulkloads.config.AppConstants.UserPermission.UPDATE_LOAD_BOOKING_STATUS;
import static com.bulkloads.web.load.domain.LoadDomainService.HIRING_AB_COMPANY_ID;
import static com.bulkloads.web.load.domain.LoadDomainService.HIRING_AB_USER_ID;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.security.Actor;
import com.bulkloads.web.addressbook.abcompany.domain.AbCompanyDomainService;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.assignment.domain.data.BookingData;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.mapper.AssignmentMapper;
import com.bulkloads.web.rate.repository.RateTypeRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class BookingDomainService extends AbstractAssignmentBookingDomainService {

  private final UserService userService;
  private final AbCompanyDomainService abCompanyDomainService;
  private final RateTypeRepository rateTypeRepository;
  private final AssignmentMapper assignmentMapper;

  public Result<Assignment> create(final BookingData data) {
    return super.validate(new Assignment(), null, data, CREATE);
  }

  public Result<Assignment> update(final Assignment entity, final BookingData data) {
    return super.validate(entity, null, data, UPDATE);
  }

  @Override
  public void validateDataAndMapToEntity(final Result<Assignment> result,
                                         final Assignment entity,
                                         final Assignment existing,
                                         final Object dataObject,
                                         final ValidationMethod method) {

    final BookingData data = (BookingData) dataObject;

    if (CREATE.equals(method)) {
      validateCreate(result, data);
    } else {
      validateUpdate(result, data);
    }

    if (UserUtil.getUserId().isPresent()) {
      validateHiringAbCompany(result, data);
      validateBillToAbCompany(result, data);
      validateHiringBillToChange(result, data);
    }

    super.validateOcrFields(result, entity, data.getHauledDate(),
        data.getLoadingTicketNumber(), data.getLoadedVolume(), data.getLoadedWeight(),
        data.getUnloadingTicketNumber(), data.getUnloadVolume(), data.getUnloadWeight());
  }

  @Override
  public void mapToEntityAuto(final Object data, final Assignment entity) {
    assignmentMapper.bookingDataToEntity((BookingData) data, entity);
  }

  @Override
  public void validateEntity(final Result<Assignment> result, final Assignment entity) {
    super.validateEntity(result, entity);

    //ready to invoice validation
    validateAutoInvoice(result);
  }

  private void validateHiringBillToChange(final Result<Assignment> result, final BookingData bookingData) {
    //do NOT allow changing the hiring/bill_to after it's been invoiced
    final Assignment entity = result.getEntity();
    if (nonNull(entity.getLoadInvoiceId())) {
      if (hasChange(entity.getHiringAbCompany(), bookingData.getHiringAbCompany())) {
        result.addError(HIRING_AB_COMPANY_ID, "You cannot change the hiring company after the load has been invoiced. "
            + "Delete the invoice first to make edits");
      }
      if (hasChange(entity.getHiringAbUser(), bookingData.getHiringAbUser())) {
        result.addError(HIRING_AB_USER_ID, "You cannot change the hiring user after the load has been invoiced. "
            + "Delete the invoice first to make edits");
      }

      if (hasChange(entity.getBillToAbUser(), bookingData.getBillToAbUser())) {
        result.addError(BILL_TO_AB_USER_ID, "You cannot change the bill to user after the load has been invoiced. "
            + "Delete the invoice first to make edits");
      } else if (hasChange(entity.getBillToAbCompany(), bookingData.getBillToAbCompany())) {
        result.addError(BILL_TO_AB_COMPANY_ID, "You cannot change the bill to company after the load has been invoiced. "
            + "Delete the invoice first to make edits");
      }
    }
  }

  private void validateHiringAbCompany(final Result<Assignment> result, final BookingData bookingData) {
    final User user = userService.getLoggedInUser();
    final int loggedInUserCompanyId = user.getUserCompany().getUserCompanyId();
    final Integer defaultBillToCompanyId = user.getDefaultBillToCompanyId();
    //ready to invoice validation
    if (existsAndIsNotEmpty(bookingData.getHiringAbCompany())) {
      AbCompany hiringAbCompany = bookingData.getHiringAbCompany().get();
      final Integer userCompanyId = hiringAbCompany.getUserCompany().getUserCompanyId();
      if (userCompanyId != loggedInUserCompanyId) {
        result.addError(HIRING_AB_COMPANY_ID, "The company is not in your address book");
      } else {
        if (userCompanyId.equals(defaultBillToCompanyId)) {
          hiringAbCompany = abCompanyDomainService.replicate(user, hiringAbCompany);
          bookingData.setHiringAbCompany(Optional.of(hiringAbCompany));

          final Optional<AbUser> hiringAbUser = bookingData.getHiringAbUser();
          if (existsAndIsNotEmpty(hiringAbUser)) {
            final AbUser replicatedAbUser = hiringAbCompany.getAbUsers().stream()
                .filter(abu ->
                    abu.getFirstName().equals(hiringAbUser.get().getFirstName())
                        && abu.getLastName().equals(hiringAbUser.get().getLastName())
                        && abu.getEmail().equals(hiringAbUser.get().getEmail())
                        && abu.getPhone1().equals(hiringAbUser.get().getPhone1())
                )
                .findFirst()
                .orElseThrow(() -> new ValidationException(HIRING_AB_USER_ID, "The user is not in the hiring company"));
            bookingData.setHiringAbUser(Optional.of(replicatedAbUser));
          }

        }
        final Optional<AbUser> hiringAbUser = bookingData.getHiringAbUser();
        if (existsAndIsNotEmpty(hiringAbUser)) {
          if (loggedInUserCompanyId != hiringAbUser.get().getUserCompany().getUserCompanyId()) {
            result.addError(HIRING_AB_USER_ID, "The user is not in your address book");
          } else if (!hiringAbUser.get().getAbCompany().equals(hiringAbCompany)) {
            result.addError(HIRING_AB_USER_ID, "The user is not in your address book");
          } else {
            final User blUser = hiringAbUser.get().getBlUser();
            if (nonNull(blUser)) {
              result.getEntity().setUser(blUser);
              result.getEntity().setUserCompany(blUser.getUserCompany());
            }
          }
        }
      }
    }
  }

  private void validateBillToAbCompany(final Result<Assignment> result, final BookingData bookingData) {
    final User user = userService.getLoggedInUser();
    final int loggedInUserCompanyId = user.getUserCompany().getUserCompanyId();
    final Integer defaultBillToCompanyId = user.getDefaultBillToCompanyId();

    if (existsAndIsNotEmpty(bookingData.getBillToAbCompany())) {
      AbCompany billToAbCompany = bookingData.getBillToAbCompany().get();
      final Integer userCompanyId = billToAbCompany.getUserCompany().getUserCompanyId();
      if (userCompanyId != loggedInUserCompanyId) {
        result.addError(BILL_TO_AB_COMPANY_ID, "The company is not in your address book");
      } else {
        if (userCompanyId.equals(defaultBillToCompanyId)) {
          billToAbCompany = abCompanyDomainService.replicate(user, billToAbCompany);
          bookingData.setBillToAbCompany(Optional.of(billToAbCompany));

          final Optional<AbUser> billToAbUser = bookingData.getBillToAbUser();
          if (existsAndIsNotEmpty(billToAbUser)) {
            final AbUser replicatedAbUser = billToAbCompany.getAbUsers().stream()
                .filter(abu ->
                    abu.getFirstName().equals(billToAbUser.get().getFirstName())
                        && abu.getLastName().equals(billToAbUser.get().getLastName())
                        && abu.getEmail().equals(billToAbUser.get().getEmail())
                        && abu.getPhone1().equals(billToAbUser.get().getPhone1())
                )
                .findFirst()
                .orElseThrow(() -> new ValidationException(BILL_TO_AB_USER_ID, "The user is not in the hiring company"));
            bookingData.setHiringAbUser(Optional.of(replicatedAbUser));
          }

        }
        final Optional<AbUser> billToAbUser = bookingData.getBillToAbUser();
        if (existsAndIsNotEmpty(billToAbUser)) {
          if (loggedInUserCompanyId != billToAbUser.get().getUserCompany().getUserCompanyId()) {
            result.addError(BILL_TO_AB_USER_ID, "The user is not in your address book");
          } else if (!billToAbUser.get().getAbCompany().equals(billToAbCompany)) {
            result.addError(BILL_TO_AB_USER_ID, "The user is not in your address book");
          }
        }
      }
    }
  }

  private void validateCreate(final Result<Assignment> result, final BookingData bookingData) {
    final Assignment booking = result.getEntity();

    final int userId = UserUtil.getUserIdOrThrow();
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final User user = userService.getLoggedInUser();

    booking.setAssignmentStatus(ASSIGNED);
    booking.setAutoInvoice(false);
    booking.setReadyToInvoice(false);
    booking.setSharedWithHiredCompany(true);
    booking.setConfirmationSentMethod(EMAIL);
    booking.setCreatedByUserId(userId);
    booking.setCreatedByUserCompanyId(userCompanyId);
    booking.setToUser(user);
    booking.setToUserCompany(user.getUserCompany());
    booking.setCreatedDate(Instant.now());
    booking.setPreviousRateType(rateTypeRepository.getReferenceById(TWO_K));
    booking.setBillWeightUse(bookingData.getToLoad().get().getDefaultBillWeightUse());

    //extra validation besides validateBooking
    if (isMissingOrIsEmpty(bookingData.getToLoad())) {
      result.addError("to_load_id", "A to_load_id was not passed to create the booking");
    }

  }

  private void validateUpdate(final Result<Assignment> result, final BookingData bookingData) {
    final Assignment booking = result.getEntity();

    validatebooking(booking);
    checkEditPermissions(result, booking, bookingData);
    checkPermissions(result, booking, bookingData);
    //don't allow unsharing for accepted responses
    if (existsAndIsFalse(bookingData.getSharedWithHiredCompany())
        && booking.getSharedWithHiredCompany() && ACCEPTED.equals(booking.getSharedWithHiredCompanyResponse())) {
      result.addError("shared_with_hired_company", "The hired company has already accepted the share and it cannot be un-shared");
    }

    validateFiles(result, booking, bookingData);

    //when the status is to change from delivered to something before delivered,
    //check the whole chain to see if any bookings have been invoiced and if so don't allow.
    if (existsAndIsNotEmpty(bookingData.getAssignmentStatus())) {
      validateAssignmentStatusBacktracking(result, booking, bookingData.getAssignmentStatus().get());
    }

    // validation only for BL users (non-guest)
    if (existsAndIsNotEmpty(bookingData.getToLoad()) && UserUtil.getUserId().isPresent()) {
      bookingData.setMileage(Optional.of(bookingData.getToLoad().get().getPickupDropMiles()));
    }
  }

  private void validateFiles(final Result<Assignment> result, final Assignment booking, final BookingData data) {
    if (existsAndIsNotEmpty(data.getAssignmentStatus())
        && (Stream.of(DELIVERED, COMPLETED).anyMatch(status -> status.equals(data.getAssignmentStatus().get())))) {
      boolean requireDocumentsForCompletingLoad = false;
      if (UserUtil.getUserId().isPresent()) {
        final User user = userService.findById(UserUtil.getUserId().get());
        requireDocumentsForCompletingLoad = user.getUserCompany().getRequireDocumentsForCompletingLoad();
      }

      int numFiles = booking.getNumberOfFiles();
      if (existsAndIsNotEmpty(data.getFiles())) {
        numFiles += data.getFiles().get().size();
      }

      if (requireDocumentsForCompletingLoad && numFiles == 0) {
        result.addError("assignment_status", "Attaching files is required for completing this load");
      }
    }
  }

  private void checkEditPermissions(final Result<Assignment> result, final Assignment booking, final BookingData data) {
    final Optional<Integer> userCompanyIdOpt = UserUtil.getUserCompanyId();
    //don't allow the shared hired company to edit some fields
    if (userCompanyIdOpt.isEmpty() || !booking.getCreatedByUserId().equals(userCompanyIdOpt.get())) {

      checkEditPermission(result, data.getOriginalRate(), booking.getOriginalRate(), ORIGINAL_RATE);
      checkEditPermission(result, data.getOriginalRateType(), booking.getOriginalRateType(), ORIGINAL_RATE_TYPE);
      checkEditPermission(result, data.getOriginalRatePercentage(), booking.getOriginalRatePercentage(), ORIGINAL_RATE_PERCENTAGE);
      checkEditPermission(result, data.getRate(), booking.getRate(), RATE);
      checkEditPermission(result, data.getRateType(), booking.getRateType(), RATE_TYPE);
      checkEditPermission(result, data.getEstWeight(), BigDecimal.valueOf(booking.getEstWeight()), EST_WEIGHT);
      checkEditPermission(result, data.getEstVolume(), BigDecimal.valueOf(booking.getEstVolume()), EST_VOLUME);
      checkEditPermission(result, data.getEstMiles(), booking.getEstMiles(), EST_MILES);
      checkEditPermission(result, data.getEstHours(), booking.getEstHours(), EST_HOURS);
      checkEditPermission(result, data.getToAbCompany(), booking.getToAbCompany(), TO_AB_COMPANY_ID);
      checkEditPermission(result, data.getToAbUser(), booking.getToAbUser(), TO_AB_USER_ID);
      checkEditPermission(result, data.getLoadAssignmentNumber(), booking.getLoadAssignmentNumber(), LOAD_ASSIGNMENT_NUMBER);
      checkEditPermission(result, data.getPickupNumber(), booking.getPickupNumber(), PICKUP_NUMBER);
      checkEditPermission(result, data.getPickupNotes(), booking.getPickupNotes(), PICKUP_NOTES);
      checkEditPermission(result, data.getDropNumber(), booking.getDropNumber(), DROP_NUMBER);
      checkEditPermission(result, data.getDropNotes(), booking.getDropNotes(), DROP_NOTES);
      checkEditPermission(result, data.getWorkOrderNumber(), booking.getWorkOrderNumber(), WORK_ORDER_NUMBER);
      checkEditPermission(result, data.getInsideNotes(), booking.getInsideNotes(), INSIDE_NOTES);
      checkEditPermission(result, data.getSharedWithHiredCompany(), booking.getSharedWithHiredCompany(), SHARED_WITH_HIRING_COMPANY);
      checkEditPermission(result, data.getBillWeightUse(), booking.getBillWeightUse(), BILL_WEIGHT_USE);
    }
  }

  private void validatebooking(final Assignment booking) {
    final Optional<Integer> abUserIdOpt = UserUtil.getAbUserId();
    final Optional<Integer> userCompanyIdOpt = UserUtil.getUserCompanyId();
    if (userCompanyIdOpt.isPresent() && (nonNull(booking.getToUserCompany()) && !booking.getToUserCompany().getUserCompanyId().equals(userCompanyIdOpt.get()))
        || abUserIdOpt.isPresent() && (nonNull(booking.getToAbUser()) && !booking.getToAbUser().getAbUserId().equals(abUserIdOpt.get()))) {
      throw new ValidationException(LOGIN, "You don't have permission to edit the load booking");
    }
  }

  private void checkPermissions(final Result<Assignment> result, final Assignment booking, final BookingData bookingData) {
    final Actor actor = UserUtil.getActorOrThrow();
    // TODO guest permissions
    if (actor.isGuest()) {
      return;
    }

    final List<String> roles = actor.getRoles().stream().map(GrantedAuthority::getAuthority).toList();
    final String errorMessage = "Your user role " + roles + " doesn't have the %s permission";

    //STATUS/driver
    if (hasChange(booking.getAssignmentStatus(), bookingData.getAssignmentStatus())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(ASSIGNMENT_STATUS, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    if (hasChange(booking.getLoadedWeight(), bookingData.getLoadedWeight())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(LOADED_WEIGHT, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    if (hasChange(booking.getUnloadWeight(), bookingData.getUnloadWeight())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(UNLOAD_WEIGHT, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    if (hasChange(booking.getBillWeight(), bookingData.getBillWeight())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(BILL_WEIGHT, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    if (hasChange(booking.getLoadedVolume(), bookingData.getLoadedVolume())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(LOADED_VOLUME, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    if (hasChange(booking.getUnloadVolume(), bookingData.getUnloadVolume())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(UNLOAD_VOLUME, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    if (hasChange(booking.getBillVolume(), bookingData.getBillVolume())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(BILL_VOLUME, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    // billMiles
    if (hasChange(booking.getBillMiles(), bookingData.getBillMiles())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(BILL_MILES, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    // billHours
    if (hasChange(booking.getBillHours(), bookingData.getBillHours())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_STATUS)) {
      result.addError(BILL_HOURS, errorMessage.formatted(UPDATE_LOAD_BOOKING_STATUS));
    }

    // hauledNotes
    if (hasChange(booking.getHauledNotes(), bookingData.getHauledNotes())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_DRIVER)) {
      result.addError(HAULED_NOTES, errorMessage.formatted(UPDATE_LOAD_BOOKING_DRIVER));
    }

    // hauledDate
    if (hasChange(booking.getHauledDate(), bookingData.getHauledDate())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_DRIVER)) {
      result.addError(HAULED_DATE, errorMessage.formatted(UPDATE_LOAD_BOOKING_DRIVER));
    }

    //GENERAL
    if (hasChange(booking.getPickupNumber(), bookingData.getPickupNumber())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(PICKUP_NUMBER, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getPickupNotes(), bookingData.getPickupNotes())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(PICKUP_NOTES, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getDropNumber(), bookingData.getDropNumber())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(DROP_NUMBER, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getDropNotes(), bookingData.getDropNotes())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(DROP_NOTES, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getWorkOrderNumber(), bookingData.getWorkOrderNumber())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(WORK_ORDER_NUMBER, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getInsideNotes(), bookingData.getInsideNotes())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(INSIDE_NOTES, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getSharedWithHiredCompany(), bookingData.getSharedWithHiredCompany())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(SHARED_WITH_HIRING_COMPANY, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getLoadingTicketNumber(), bookingData.getLoadingTicketNumber())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(LOADING_TICKET_NUMBER, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getUnloadingTicketNumber(), bookingData.getUnloadingTicketNumber())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(UNLOADING_TICKET_NUMBER, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getBolNumber(), bookingData.getBolNumber())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(BOL_NUMBER, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getLoadAssignmentNumber(), bookingData.getLoadAssignmentNumber())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(LOAD_ASSIGNMENT_NUMBER, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getBillToAbCompany(), bookingData.getBillToAbCompany())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(BILL_TO_AB_COMPANY_ID, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getRate(), bookingData.getRate())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(RATE, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getRateType(), bookingData.getRateType())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(RATE_TYPE, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getToPayment(), bookingData.getToPayment())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(TO_PAYMENT, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getToPaymentNotes(), bookingData.getToPaymentNotes())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(TO_PAYMENT_NOTES, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

    if (hasChange(booking.getToPaid(), bookingData.getToPaid())
        && !actor.hasPermission(UPDATE_LOAD_BOOKING_GENERAL)) {
      result.addError(TO_PAID, errorMessage.formatted(UPDATE_LOAD_BOOKING_GENERAL));
    }

  }

  private void validateAutoInvoice(final Result<Assignment> result) {

    final Assignment entity = result.getEntity();
    if (entity.getAutoInvoice() || entity.getReadyToInvoice()) {

      if (entity.getAutoInvoice() && entity.getReadyToInvoice()) {
        result.addError(AUTO_INVOICE, "auto_invoice and ready_to_invoice cannot both be 1");
      }

      if (!DELIVERED.equals(entity.getAssignmentStatus()) && !COMPLETED.equals(entity.getAssignmentStatus())) {
        result.addError(AUTO_INVOICE, "The assignment_status must be delivered|completed");
      }

      //If no bill_to company is specified, set it to the hiring company
      if (isNull(entity.getBillToAbCompany()) && nonNull(entity.getHiringAbCompany())) {
        entity.setBillToAbCompany(entity.getHiringAbCompany());
      }

      //If the bill company is the same as the hiring and no bill user is specified, use the hiring user
      if (nonNull(entity.getBillToAbCompany()) && entity.getBillToAbCompany().equals(entity.getHiringAbCompany())
          && isNull(entity.getBillToAbUser()) && nonNull(entity.getHiringAbUser())) {
        entity.setBillToAbUser(entity.getHiringAbUser());
      }

      //If there is no address book billing, bill the user_company_id, typically your own company through a driver assignment
      if (nonNull(entity.getBillToAbCompany()) && isNull(entity.getBillToAbUser())
          || isNull(entity.getBillToAbCompany()) && isNull(entity.getUser())) {
        result.addError(AUTO_INVOICE, "The invoice recipient isn't specified");
        result.addError(BILL_TO_AB_USER_ID, "Enter the invoice recipient");
      }

      if (isNull(entity.getRate())) {
        result.addError(AUTO_INVOICE, "There is no rate specified in order to invoice this load");
        result.addError(RATE, "There is no rate specified in order to invoice this load");
      }

      if (nonNull(entity.getRateType()) && NumberUtils.isParsable(entity.getRateType().getRateType())) {
        if (LOADED_WEIGHT.equals(entity.getBillWeightUse())) {
          if (isNull(entity.getLoadedWeight()) || entity.getLoadedWeight() < 0) {
            result.addError(LOADED_WEIGHT, "Enter the weight");
          }
        } else {
          if (isNull(entity.getUnloadWeight()) || entity.getUnloadWeight() < 0) {
            result.addError(UNLOAD_WEIGHT, "Enter the weight");
          }
        }
      } else if (GALLON.equals(entity.getRateType().getRateType()) || LITER.equals(entity.getRateType().getRateType())) {
        if (LOADED_WEIGHT.equals(entity.getBillWeightUse())) {
          if (isNull(entity.getLoadedVolume()) || entity.getLoadedVolume() < 0) {
            result.addError(LOADED_VOLUME, "Enter the volume");
          }
        } else {
          if (isNull(entity.getUnloadVolume()) || entity.getUnloadVolume() < 0) {
            result.addError(UNLOAD_VOLUME, "Enter the volume");
          }
        }
      } else if (MILE.equals(entity.getRateType().getRateType())) {
        if (isNull(entity.getBillMiles()) || entity.getBillMiles().compareTo(BigDecimal.ZERO) < 0) {
          result.addError(BILL_MILES, "Enter a valid number");
        }
      } else if (HOUR.equals(entity.getRateType().getRateType())
          && (isNull(entity.getBillHours()) || entity.getBillHours().compareTo(BigDecimal.ZERO) < 0)) {
        result.addError(BILL_HOURS, "Enter a valid number");
      }
    }

    //if paid, require payment amount
    if (entity.getToPaid() && isNull(entity.getToPayment())) {
      result.addError(TO_PAYMENT, "Enter a valid number");
    }
  }
}
