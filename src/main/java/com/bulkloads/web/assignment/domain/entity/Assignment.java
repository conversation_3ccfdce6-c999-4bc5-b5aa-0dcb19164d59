package com.bulkloads.web.assignment.domain.entity;

import static com.bulkloads.common.validation.ValidationUtils.equal;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.jpa.CsvListSize;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.common.event.DomainEvent;
import com.bulkloads.web.common.jpa.converter.CsvIntegerListConverter;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.data.domain.AbstractAggregateRoot;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@DynamicUpdate
@Table(name = "load_assignments")
@EntityListeners(AssignmentEntityListener.class)
public class Assignment extends AbstractAggregateRoot<Assignment> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "load_assignment_id")
  private Integer loadAssignmentId;

  @NotNull
  @Size(max = 50, message = "Up to 50 chars")
  @Column(name = "external_load_assignment_id")
  private String externalLoadAssignmentId = "";

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "load_id")
  private Load load;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "to_ab_company_id")
  private AbCompany toAbCompany;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "to_ab_user_id")
  private AbUser toAbUser;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "to_user_id")
  private User toUser;

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "to_user_company_id")
  private UserCompany toUserCompany;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "to_load_id")
  private Load toLoad;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "hiring_ab_company_id")
  private AbCompany hiringAbCompany;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "hiring_ab_user_id")
  private AbUser hiringAbUser;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "bill_to_ab_company_id")
  private AbCompany billToAbCompany;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "bill_to_ab_user_id")
  private AbUser billToAbUser;

  @Column(name = "load_invoice_id")
  private Integer loadInvoiceId;

  @Column(name = "auto_invoice")
  private Boolean autoInvoice = false;

  @Column(name = "ready_to_invoice")
  private Boolean readyToInvoice = false;

  @Column(name = "default_dispatcher_user_id")
  private Integer defaultDispatcherUserId;

  @NotNull
  @Column(name = "assignment_status")
  private String assignmentStatus = "Unassigned";

  @NotNull
  @Size(max = 25, message = "Up to 25 chars")
  @Column(name = "assignment_phone")
  private String assignmentPhone = "";

  @Size(max = 100)
  @NotNull
  @Column(name = "assignment_email")
  private String assignmentEmail = "";

  @Size(max = 45)
  @NotNull
  @Column(name = "contract_number")
  private String contractNumber = "";

  @Size(max = 150, message = "150 chars max")
  @NotNull
  @Column(name = "pickup_number")
  private String pickupNumber = "";

  @Column(name = "pickup_notes")
  private String pickupNotes = "";

  @Size(max = 150, message = "150 chars max")
  @NotNull
  @Column(name = "drop_number")
  private String dropNumber = "";

  @Column(name = "drop_notes")
  private String dropNotes = "";

  @Size(max = 150, message = "150 chars max")
  @NotNull
  @Column(name = "work_order_number")
  private String workOrderNumber = "";

  @Column(name = "inside_notes")
  private String insideNotes = "";

  @Column(name = "personal_message")
  private String personalMessage = "";

  @NotNull
  @Column(name = "created_date")
  private Instant createdDate;

  @Column(name = "created_by_user_id")
  private Integer createdByUserId;

  @Column(name = "created_by_user_company_id")
  private Integer createdByUserCompanyId;

  @Column(name = "assigned_date")
  private Instant assignedDate;

  @Column(name = "assigned_by_user_id")
  private Integer assignedByUserId;

  @Column(name = "dispatched_date")
  private Instant dispatchedDate;

  @Column(name = "dispatched_by_user_id")
  private Integer dispatchedByUserId;

  @Column(name = "dispatched_by_ab_user_id")
  private Integer dispatchedByAbUserId;

  @Column(name = "loading_date")
  private Instant loadingDate;

  @Column(name = "loading_by_user_id")
  private Integer loadingByUserId;

  @Column(name = "loading_by_ab_user_id")
  private Integer loadingByAbUserId;

  @Column(name = "enroute_date")
  private Instant enrouteDate;

  @Column(name = "enroute_by_user_id")
  private Integer enrouteByUserId;

  @Column(name = "enroute_by_ab_user_id")
  private Integer enrouteByAbUserId;

  @Column(name = "unloading_date")
  private Instant unloadingDate;

  @Column(name = "unloading_by_user_id")
  private Integer unloadingByUserId;

  @Column(name = "unloading_by_ab_user_id")
  private Integer unloadingByAbUserId;

  @Column(name = "delivered_date")
  private Instant deliveredDate;

  @Column(name = "delivered_by_user_id")
  private Integer deliveredByUserId;

  @Column(name = "delivered_by_ab_user_id")
  private Integer deliveredByAbUserId;

  @Column(name = "completed_date")
  private Instant completedDate;

  @Column(name = "completed_by_user_id")
  private Integer completedByUserId;

  @Column(name = "completed_by_ab_user_id")
  private Integer completedByAbUserId;

  @Column(name = "shared_with_hired_company")
  private Boolean sharedWithHiredCompany = false;

  @Column(name = "shared_with_hired_company_response")
  private String sharedWithHiredCompanyResponse;

  @Column(name = "shared_with_hiring_company")
  private Boolean sharedWithHiringCompany = false;

  @Column(name = "shared_with_hiring_company_response")
  private String sharedWithHiringCompanyResponse;

  @Column(name = "edit_date")
  private Instant editDate;

  @Column(name = "edit_by_user_id")
  private Integer editByUserId;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  @Column(name = "deleted_by_user_id")
  private Integer deletedByUserId;

  @Column(name = "deleted")
  private Boolean deleted = false;

  @NotNull
  @Size(max = 1000, message = "Up to 1000 chars")
  @Column(name = "deleted_message")
  private String deletedMessage = "";

  @Column(name = "to_deleted_date")
  private Instant toDeletedDate;

  @Column(name = "to_deleted_by_user_id")
  private Integer toDeletedByUserId;

  @Column(name = "to_deleted")
  private Boolean toDeleted = false;

  @Size(max = 1000, message = "Up to 1000 chars")
  @Column(name = "to_deleted_message")
  private String toDeletedMessage = "";

  @Column(name = "booked_from_offer_recipient_id")
  private Integer bookedFromOfferRecipientId;

  @Column(name = "number_of_files")
  private int numberOfFiles = 0;

  @Column(name = "total_files_size")
  private int totalFilesSize = 0;

  @Column(name = "needs_attention")
  private Boolean needsAttention = false;

  @Column(name = "needs_attention_date")
  private Instant needsAttentionDate;

  @NotNull
  @Column(name = "assignment_confirmation_ready_to_send")
  private Boolean assignmentConfirmationReadyToSend = false;

  @NotNull
  @Column(name = "booking_confirmation_ready_to_send")
  private Boolean bookingConfirmationReadyToSend = false;

  @NotNull
  @Column(name = "send_confirmation")
  private Boolean sendConfirmation = true;

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "confirmation_file_id")
  private File confirmationFile;

  @NotNull
  @Size(max = 50, message = "Up to 50 chars")
  @Column(name = "confirmation_file_code")
  private String confirmationFileCode = "";

  @Column(name = "confirmation_sent_date")
  private Instant confirmationSentDate;

  @NotNull
  @Size(max = 50, message = "Up to 50 chars")
  @Column(name = "confirmation_sent_method")
  private String confirmationSentMethod = "";

  @Column(name = "confirmation_sent_by_user_id")
  private Integer confirmationSentByUserId;

  @Column(name = "confirmation_revised_date")
  private Instant confirmationRevisedDate;

  @Column(name = "confirmation_opened_date")
  private Instant confirmationOpenedDate;

  @Column(name = "confirmation_confirmed_date")
  private Instant confirmationConfirmedDate;

  @NotNull
  @Size(max = 50, message = "Up to 50 chars")
  @Column(name = "confirmation_confirmed_method")
  private String confirmationConfirmedMethod = "";

  @Column(name = "confirmation_confirmed_by_user_id")
  private Integer confirmationConfirmedByUserId;

  @Column(name = "confirmation_confirmed_by_ab_user_id")
  private Integer confirmationConfirmedByAbUserId;

  @NotNull
  @Size(max = 25, message = "Up to 25 chars")
  @Column(name = "confirmation_email_status")
  private String confirmationEmailStatus = "";

  @Column(name = "confirmation_email_queue_id")
  private Integer confirmationEmailQueueId;

  @CsvListSize(max = 100)
  @Convert(converter = CsvIntegerListConverter.class)
  @Column(name = "confirmation_to_ab_user_ids")
  private List<Integer> confirmationToAbUserIds = new ArrayList<>();

  @Size(max = 150, message = "Up to 150 chars")
  @NotNull
  @Column(name = "confirmation_cc_others")
  private String confirmationCcOthers = "";

  @Column(name = "mileage")
  private BigDecimal mileage;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 200000, message = "Up to 200,000lbs")
  @Column(name = "loaded_weight")
  private Double loadedWeight;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 80000, message = "Up to 80,000")
  @Column(name = "loaded_volume")
  private Double loadedVolume;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 200000, message = "Up to 200,000lbs")
  @Column(name = "unload_weight")
  private Double unloadWeight;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 80000, message = "Up to 80,000")
  @Column(name = "unload_volume")
  private Double unloadVolume;

  @Size(max = 50, message = "50 chars max")
  @NotNull
  @Column(name = "loading_ticket_number")
  private String loadingTicketNumber = "";

  @Column(name = "loading_ticket_file_id")
  private Integer loadingTicketFileId;

  @Column(name = "loading_ticket_number_ocr")
  private String loadingTicketNumberOcr = "";

  @Column(name = "loaded_weight_ocr")
  private Double loadedWeightOcr;

  @Column(name = "loaded_volume_ocr")
  private Double loadedVolumeOcr;

  @Column(name = "unloading_ticket_file_id")
  private Integer unloadingTicketFileId;

  @Column(name = "unloading_ticket_number_ocr")
  private String unloadingTicketNumberOcr = "";

  @Column(name = "unload_weight_ocr")
  private Double unloadWeightOcr;

  @Column(name = "unload_volume_ocr")
  private Double unloadVolumeOcr;

  @Column(name = "hauled_date_ocr")
  private LocalDate hauledDateOcr;

  @Size(max = 50, message = "50 chars max")
  @NotNull
  @Column(name = "unloading_ticket_number")
  private String unloadingTicketNumber = "";

  @Size(max = 50, message = "50 chars max")
  @NotNull
  @Column(name = "bol_number")
  private String bolNumber = "";

  @Size(max = 150, message = "150 chars max")
  @NotNull
  @Column(name = "load_assignment_number")
  private String loadAssignmentNumber = "";

  @Column(name = "hauled_notes")
  private String hauledNotes = "";

  @Column(name = "scheduled_hauled_date")
  private LocalDate scheduledHauledDate;

  @Column(name = "scheduled_pickup_date")
  private Instant scheduledPickupDate;

  @Column(name = "scheduled_drop_date")
  private Instant scheduledDropDate;

  @Column(name = "hauled_date")
  private LocalDate hauledDate;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 99999, message = "Up to $99,999")
  @Column(name = "original_rate")
  private BigDecimal originalRate;

  @ManyToOne
  @JoinColumn(name = "original_rate_type")
  private RateType originalRateType;

  @Column(name = "original_rate_visible")
  private Boolean originalRateVisible = false;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 100, message = "Up to 100%")
  @Column(name = "original_rate_percentage")
  private BigDecimal originalRatePercentage;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 99999, message = "Up to $99,999")
  @Column(name = "rate")
  private BigDecimal rate;

  @NotNull(message = "Select a rate type")
  @ManyToOne
  @JoinColumn(name = "rate_type")
  private RateType rateType;

  @Min(value = 0, message = "Enter a positive number")
  @Column(name = "est_weight")
  private Double estWeight = 52000d;

  @Min(value = 0, message = "Enter a positive number")
  @Column(name = "est_volume")
  private Double estVolume = 5000d;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 10000, message = "Up to 10,000")
  @Column(name = "est_miles")
  private BigDecimal estMiles;

  @Min(value = 0, message = "Enter a positive number")
  @Max(value = 1000, message = "Up to 1,000")
  @Column(name = "est_hours")
  private BigDecimal estHours;

  @Column(name = "est_quantity")
  private BigDecimal estQuantity;

  @Column(name = "est_subtotal")
  private BigDecimal estSubtotal;

  @NotNull
  @Column(name = "est_surcharges")
  private BigDecimal estSurcharges = BigDecimal.ZERO;

  @Column(name = "est_total")
  private BigDecimal estTotal;

  @Column(name = "est_rate_per_mile")
  private BigDecimal estRatePerMile;

  @NotNull
  @Lob
  @Column(name = "bill_weight_use")
  private String billWeightUse = "loaded_weight";

  @Column(name = "bill_weight")
  private Double billWeight;

  @Column(name = "bill_volume")
  private Double billVolume;

  @Column(name = "bill_miles")
  private BigDecimal billMiles;

  @Column(name = "bill_hours")
  private BigDecimal billHours;

  @Column(name = "bill_quantity")
  private BigDecimal billQuantity;

  @Column(name = "bill_subtotal")
  private BigDecimal billSubtotal;

  @NotNull
  @Column(name = "bill_surcharges")
  private BigDecimal billSurcharges = BigDecimal.ZERO;

  @Column(name = "bill_total")
  private BigDecimal billTotal;

  @Column(name = "bill_rate_per_mile")
  private BigDecimal billRatePerMile;

  @Column(name = "payment")
  private BigDecimal payment = BigDecimal.ZERO;

  @Column(name = "payment_date")
  private Instant paymentDate;

  @Column(name = "payment_by_user_id")
  private Integer paymentByUserId;

  @Size(max = 1000, message = "The payment notes must be less than 1000 chars")
  @NotNull
  @Column(name = "payment_notes")
  private String paymentNotes = "";

  @NotNull
  @Column(name = "payment_approved")
  private Boolean paymentApproved = false;

  @Column(name = "payment_approved_date")
  private Instant paymentApprovedDate;

  @Column(name = "payment_approved_by_user_id")
  private Integer paymentApprovedByUserId;

  @Column(name = "paid")
  private Boolean paid = false;

  @Column(name = "paid_date")
  private Instant paidDate;

  @Column(name = "paid_by_user_id")
  private Integer paidByUserId;

  @Column(name = "bill_to_payment")
  private BigDecimal billToPayment;

  @Column(name = "bill_to_payment_date")
  private Instant billToPaymentDate;

  @Column(name = "bill_to_payment_by_user_id")
  private Integer billToPaymentByUserId;

  @Size(max = 1000, message = "Up to 1000 chars")
  @NotNull
  @Column(name = "bill_to_payment_notes")
  private String billToPaymentNotes = "";

  @Column(name = "to_payment")
  private BigDecimal toPayment = BigDecimal.ZERO;

  @Column(name = "to_payment_date")
  private Instant toPaymentDate;

  @Column(name = "to_payment_by_user_id")
  private Integer toPaymentByUserId;

  @Size(max = 1000, message = "Up to 1000 chars")
  @NotNull
  @Column(name = "to_payment_notes")
  private String toPaymentNotes = "";

  @Column(name = "to_paid")
  private Boolean toPaid = false;

  @Column(name = "to_paid_date")
  private Instant toPaidDate;

  @Column(name = "to_paid_by_user_id")
  private Integer toPaidByUserId;

  @Column(name = "geo_share_location")
  private Boolean geoShareLocation = true;

  @Lob
  @Column(name = "geo_request_status")
  private String geoRequestStatus = "";

  @NotNull
  @Lob
  @Column(name = "geo_request_method")
  private String geoRequestMethod = "";

  @Column(name = "geo_request_date")
  private Instant geoRequestDate;

  @Column(name = "geo_response_date")
  private Instant geoResponseDate;

  @Column(name = "geo_tracking_enabled")
  private Boolean geoTrackingEnabled = false;

  @Column(name = "geo_tracking_start_date")
  private Instant geoTrackingStartDate;

  @Column(name = "geo_tracking_stop_date")
  private Instant geoTrackingStopDate;

  @Column(name = "geo_updated_date")
  private Instant geoUpdatedDate;

  @Column(name = "geo_updated_by_user_id")
  private Integer geoUpdatedByUserId;

  @Column(name = "geo_updated_by_ab_user_id")
  private Integer geoUpdatedByAbUserId;

  @Column(name = "geo_tracking_until")
  private Instant geoTrackingUntil;

  @Column(name = "geo_latitude")
  private Double geoLatitude;

  @Column(name = "geo_longitude")
  private Double geoLongitude;

  @Column(name = "geo_accuracy")
  private Float geoAccuracy;

  @Column(name = "geo_speed")
  private Float geoSpeed;

  @Column(name = "geo_heading")
  private Float geoHeading;

  @Column(name = "geo_altitude")
  private Float geoAltitude;

  @Column(name = "reroute_request")
  private Boolean rerouteRequest = false;

  @Column(name = "reroute_request_date")
  private Instant rerouteRequestDate;

  @Size(max = 100, message = "Up to 100 chars")
  @NotNull
  @Column(name = "reroute_request_reason")
  private String rerouteRequestReason = "";

  @Column(name = "reroute_date")
  private Instant rerouteDate;

  @Column(name = "reroute_by_user_id")
  private Integer rerouteByUserId;

  @NotNull
  @Size(max = 1000, message = "Up to 1000 chars")
  @Column(name = "reroute_reason")
  private String rerouteReason = "";

  @Column(name = "reroute_contract_id")
  private Integer rerouteContractId;

  @Size(max = 45, message = "Up to 45 chars")
  @NotNull
  @Column(name = "reroute_contract_number")
  private String rerouteContractNumber = "";

  @Size(max = 150, message = "Up to 150 chars")
  @NotNull
  @Column(name = "previous_load_assignment_number")
  private String previousLoadAssignmentNumber = "";

  @Size(max = 150, message = "Up to 150 chars")
  @NotNull
  @Column(name = "previous_pickup_number")
  private String previousPickupNumber = "";

  @Size(max = 150, message = "Up to 150 chars")
  @NotNull
  @Column(name = "previous_drop_number")
  private String previousDropNumber = "";

  @Size(max = 150, message = "Up to 150 chars")
  @NotNull
  @Column(name = "previous_work_order_number")
  private String previousWorkOrderNumber = "";

  @Column(name = "previous_rate")
  private BigDecimal previousRate;

  @ManyToOne
  @JoinColumn(name = "previous_rate_type")
  private RateType previousRateType;

  @Column(name = "previous_bill_subtotal")
  private BigDecimal previousBillSubtotal;

  @NotNull
  @Column(name = "previous_bill_surcharges")
  private BigDecimal previousBillSurcharges = BigDecimal.ZERO;

  @Column(name = "previous_bill_total")
  private BigDecimal previousBillTotal;

  @Column(name = "previous_bill_rate_per_mile")
  private BigDecimal previousBillRatePerMile;

  @NotNull
  @Lob
  @Column(name = "reroute_pickup_drop")
  private String reroutePickupDrop = "drop";

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "reroute_ab_company_id")
  private AbCompany rerouteAbCompany;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "reroute_to_ab_company_id")
  private AbCompany rerouteToAbCompany;

  @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "parent_load_assignment_id")
  private Assignment parentLoadAssignment;

  @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "child_load_assignment_id")
  private Assignment childLoadAssignment;

  @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "chain_load_assignment_id")
  private Assignment chainLoadAssignment;

  @NotNull
  @Column(name = "is_rerouted")
  private Boolean isRerouted = false;

  @NotNull
  @Column(name = "is_reassigned")
  private Boolean isReassigned = false;

  @NotNull
  @Column(name = "is_intra_company")
  private Boolean isIntraCompany = false;

  @NotNull
  @Column(name = "is_driver")
  private Boolean isDriver = false;

  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "load_assignment_export_id")
  private Integer loadAssignmentExportId;

  @Column(name = "load_booking_export_id")
  private Integer loadBookingExportId;

  @Column(name = "load_paid_export_id")
  private Integer loadPaidExportId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "truck_user_company_equipment_id")
  private UserCompanyEquipment truckUserCompanyEquipment;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "trailer_user_company_equipment_id")
  private UserCompanyEquipment trailerUserCompanyEquipment;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "loadAssignment", orphanRemoval = true)
  private List<AssignmentSurcharge> surcharges = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "loadAssignment", orphanRemoval = true)
  private List<AssignmentFile> assignmentFiles = new ArrayList<>();

  @NotNull
  @Column(name = "has_unmatched_external_grades")
  private Boolean hasUnmatchedExternalGrades = false;

  @NotNull
  @Column(name = "agtrax_integration")
  private Boolean agtraxIntegration = false;

  public String getToCompanyName() {
    return toAbCompany != null ? toAbCompany.getCompanyName() :
        toUserCompany != null ? toUserCompany.getCompanyName() : "";
  }

  public String getToFirstName() {
    return toAbUser != null ? toAbUser.getFirstName() :
        toUser != null ? toUser.getFirstName() : "";
  }

  public String getToLastName() {
    return toAbUser != null ? toAbUser.getLastName() :
        toUser != null ? toUser.getLastName() : "";
  }

  public String getToEmail() {
    return toAbUser != null ? toAbUser.getEmail() :
        toUser != null ? toUser.getEmail() : "";
  }

  public String getToPhone1() {
    return toAbUser != null ? toAbUser.getPhone1() :
        toUser != null ? toUser.getCellPhone() : "";
  }

  public String getCompanyName() {
    return hiringAbCompany != null ? hiringAbCompany.getCompanyName() :
        userCompany != null ? userCompany.getCompanyName() : "";
  }

  public List<Integer> getLoadAssignmentIdsInChain() {
    // go through parent, child and find all ids
    List<Integer> chainIds = new ArrayList<>();

    // Add current assignment ID
    chainIds.add(this.loadAssignmentId);

    // Traverse up to find parent IDs
    Assignment currentParent = this.parentLoadAssignment;
    while (currentParent != null) {
      chainIds.add(currentParent.getLoadAssignmentId());
      currentParent = currentParent.getParentLoadAssignment();
    }

    // Traverse down to find child IDs
    Assignment currentChild = this.childLoadAssignment;
    while (currentChild != null) {
      chainIds.add(currentChild.getLoadAssignmentId());
      currentChild = currentChild.getChildLoadAssignment();
    }

    return chainIds;
  }

  public void registerDomainEvent(final DomainEvent event) {
    registerEvent(event);
  }

  public boolean hasStatus(final String anotherStatus) {
    return equal(assignmentStatus, anotherStatus);
  }

}
