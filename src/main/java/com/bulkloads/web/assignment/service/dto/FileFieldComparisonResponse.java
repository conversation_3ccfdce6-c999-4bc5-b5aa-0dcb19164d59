package com.bulkloads.web.assignment.service.dto;

import com.bulkloads.web.file.service.dto.FileFieldResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class FileFieldComparisonResponse {

  @Schema(description = "External grade code")
  String externalGradeCode;

  @Schema(description = "Is Unmatched External Grade")
  Boolean isUnmatchedExternalGrade;

  @Schema(description = "Field name")
  FileFieldResponse origin;

  @Schema(description = "Field name")
  FileFieldResponse destination;

  @Schema(description = "Difference between loading and unloading values")
  Double diff;
}