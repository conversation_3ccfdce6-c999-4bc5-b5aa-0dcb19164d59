package com.bulkloads.web.assignment.service.dto;

import java.time.Instant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class AssignmentOcrReadyResponse {
  @Schema(description = "Load assignment ID")
  Integer loadAssignmentId;

  @Schema(description = "Indicates if AgTrax integration is enabled")
  Boolean agtraxIntegration;

  @Schema(description = "Indicates if there are unmatched external grades")
  Boolean hasUnmatchedExternalGrades;

  @Schema(description = "Company name")
  String companyName;

  @Schema(description = "Carrier company name")
  String toCompanyName;

  @Schema(description = "Driver first name")
  String toFirstName;

  @Schema(description = "Driver last name")
  String toLastName;

  @Schema(description = "Driver phone")
  String toPhone;

  @Schema(description = "Pickup company name")
  String pickupCompanyName;

  @Schema(description = "Pickup city")
  String pickupCity;

  @Schema(description = "Pickup state")
  String pickupState;

  @Schema(description = "Drop company name")
  String dropCompanyName;

  @Schema(description = "Drop city")
  String dropCity;

  @Schema(description = "Drop state")
  String dropState;

  @Schema(description = "Commodity")
  String loCommodity;

  @Schema(description = "Assigned date")
  Instant assignedDate;

  @Schema(description = "Delivered date")
  Instant deliveredDate;

  @Schema(description = "Assignment status")
  String assignmentStatus;

  @Schema(description = "Origin file URL")
  String originFileUrl;

  @Schema(description = "Destination file URL")
  String destinationFileUrl;

  @Schema(description = "Indicates if origin image is unreadable")
  Boolean originFileIsUnreadable;

  @Schema(description = "Indicates if destination image is unreadable")
  Boolean destinationFileIsUnreadable;
}
