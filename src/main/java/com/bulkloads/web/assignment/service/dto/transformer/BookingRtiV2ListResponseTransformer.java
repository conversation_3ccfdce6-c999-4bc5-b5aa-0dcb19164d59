package com.bulkloads.web.assignment.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.assignment.service.dto.BookingRtiV2Response;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class BookingRtiV2ListResponseTransformer implements TupleTransformer<BookingRtiV2Response> {

  @Override
  public BookingRtiV2Response transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    return BookingRtiV2Response.builder()
        .billToGroupId(parts.asString("bill_to_group_id"))
        .loadAssignmentId(parts.asInteger("load_assignment_id"))
        .loadId(parts.asInteger("load_id"))
        .billToCompanyName(parts.asString("bill_to_company_name"))
        .billTo(parts.asString("bill_to"))
        .billToEmail(parts.asString("bill_to_email"))
        .loadAssignmentNumber(parts.asString("load_assignment_number"))
        .loCommodity(parts.asString("lo_commodity"))
        .pickupCityState(parts.asString("pickup_city_state"))
        .dropCityState(parts.asString("drop_city_state"))
        .isRerouted(parts.asBoolean("is_rerouted"))
        .reroutePickupDrop(parts.asString("reroute_pickup_drop"))
        .rerouteCityState(parts.asString("reroute_city_state"))
        .rate(parts.asBigDecimal("rate"))
        .rateType(parts.asString("rate_type"))
        .rateTypeText(parts.asString("rate_type_text"))
        .rateTypeTextAbbr(parts.asString("rate_type_text_abbr"))
        .hauledDate(parts.asLocalDate("hauled_date"))
        .loadedWeight(parts.asDouble("loaded_weight"))
        .unloadWeight(parts.asDouble("unload_weight"))
        .billSubtotal(parts.asBigDecimal("bill_subtotal"))
        .billSurcharges(parts.asBigDecimal("bill_surcharges"))
        .billTotal(parts.asBigDecimal("bill_total"))
        .build();
  }
}
