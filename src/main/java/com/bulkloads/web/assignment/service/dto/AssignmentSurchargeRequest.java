package com.bulkloads.web.assignment.service.dto;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class AssignmentSurchargeRequest {

  @Schema(name = "surcharge_type_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Integer surchargeTypeId;

  @Schema(name = "surcharge", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  BigDecimal surcharge;
}
