package com.bulkloads.web.assignment.service.dto;

import java.time.Instant;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class AssignmentOcrRequestResponse {
  @Schema(description = "Unique identifier for the shipment")
  Integer loadAssignmentId;

  @Schema(description = "Dispatching company")
  String dispatchedBy;

  @Schema(description = "Driver company")
  String driverCompany;

  @Schema(description = "Driver name")
  String driverName;

  @Schema(description = "Driver phone")
  String driverPhone;

  @Schema(description = "Commodity")
  String commodity;

  @Schema(description = "Origin facility")
  String pickupCompanyName;

  @Schema(description = "Destination facility")
  String dropCompanyName;

  @Schema(description = "Origin Ticket Url")
  String loadingTicketFileUrl;

  @Schema(description = "Destination Ticket Url")
  String unloadingTicketFileUrl;

  @Schema(description = "Status of the load assignment")
  String assignmentStatus;

  @Schema(description = "Creation date of the load assignment")
  //@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  Instant createdAt;

  @Schema(description = "Indicates if Agtrax integration is enabled")
  Boolean isAgtraxIntegration;

  @Schema(description = "Indicates if there are unmatched grades")
  Boolean hasUnmatchedExternalGrades;

  @Schema(description = "Unmatched external grade details")
  List<UnmatchedExternalGradeResponse> unmatchedExternalGrades;

  @Schema(description = "General field comparisons")
  List<FileFieldComparisonResponse> generalFields;

  @Schema(description = "Grade field comparisons")
  List<FileFieldComparisonResponse> gradeFields;
}