package com.bulkloads.web.assignment.service;

import static com.bulkloads.common.Parsers.parseIntegerCsvToList;
import static com.bulkloads.common.StringUtil.parseDouble;
import static com.bulkloads.common.UserUtil.getUserCompanyIdOrThrow;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.config.AppConstants.ActivityType.UPDATE_LOAD_ASSIGNMENT;
import static com.bulkloads.config.AppConstants.AssignmentStatus.COMPLETED;
import static com.bulkloads.config.AppConstants.AssignmentStatus.UNASSIGNED;
import static com.bulkloads.config.AppConstants.SharedWithResponse.PENDING;
import static com.bulkloads.web.assignment.domain.AbstractAssignmentBookingDomainService.ASSIGNMENTS;
import static com.bulkloads.web.assignment.domain.AbstractAssignmentBookingDomainService.LOAD_ASSIGNMENT_ID;
import static com.bulkloads.web.assignment.domain.AssignmentDomainService.ASSIGNMENT_STATUS;
import static com.bulkloads.web.assignment.domain.AssignmentDomainService.NUMBER_OF_LOADS;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.springframework.util.CollectionUtils.isEmpty;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BinaryOperator;
import java.util.stream.IntStream;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.api.TotalResponse;
import com.bulkloads.common.jpa.JpaUtils;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.common.validation.Result;
import com.bulkloads.config.AppConstants;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.security.AuthService;
import com.bulkloads.web.assignment.domain.AbstractAssignmentBookingDomainService;
import com.bulkloads.web.assignment.domain.AssignmentDomainService;
import com.bulkloads.web.assignment.domain.data.AssignmentData;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.event.AssignmentCancelledEvent;
import com.bulkloads.web.assignment.event.AssignmentCreatedEvent;
import com.bulkloads.web.assignment.event.AssignmentUpdatedEvent;
import com.bulkloads.web.assignment.mapper.AssignmentMapper;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.assignment.repository.BookingRepository;
import com.bulkloads.web.assignment.service.dto.AssignmentBookingListResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentByCarrierV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByDestinationV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByDriverV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByOriginV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentPreviewResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentSearchRequest;
import com.bulkloads.web.assignment.service.dto.AssignmentTicketItemResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentV2ListResponse;
import com.bulkloads.web.assignment.service.dto.CancelAssignmentRequest;
import com.bulkloads.web.assignment.service.dto.CreateLoadAssignmentRequest;
import com.bulkloads.web.assignment.service.dto.CreateLoadAssignmentSubRequest;
import com.bulkloads.web.assignment.service.dto.UpdateLoadAssignmentRequest;
import com.bulkloads.web.assignment.service.template.CreateAssignmentTemplateBuilder;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.service.FileOcrService;
import com.bulkloads.web.infra.dynlink.DynamicLinkService;
import com.bulkloads.web.load.domain.template.DynamicLink;
import com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.repository.UserRepository;
import com.bulkloads.web.user.service.UserService;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class AssignmentService extends AssignmentBookingService {

  private static final String ASSIGNMENT_URL_TEMPLATE = "%s/tms/mybookedloads/%s%s";
  private static final String BOOKING_URL_TEMPLATE = "%s/tms/SOMETHINGELSE/%s%s";
  private static final BinaryOperator<Map<String, String>> REDUCE_OPERATOR = (a, b) -> {
    a.putAll(b);
    return a;
  };

  private static final Map<String, String> SORT_OPTIONS =
      Map.ofEntries(Map.entry("Newest", "la.load_assignment_id desc"), Map.entry("Oldest", "la.load_assignment_id asc"),
          Map.entry("Delivered Date Desc", "la.delivered_date desc, la.load_assignment_id desc"),
          Map.entry("Loads Asc - Oldest", "la.load_id, la.load_assignment_id asc"),
          Map.entry("Loads Asc - Newest", "la.load_id, la.load_assignment_id desc"),
          Map.entry("Status", "la.assignment_status asc, la.load_assignment_id asc")
      );

  private final AssignmentDomainService assignmentDomainService;
  private final AssignmentRepository assignmentRepository;
  private final BookingRepository bookingRepository;
  private final UserRepository userRepository;
  private final UserService userService;
  private final AssignmentMapper assignmentMapper;
  private final AuthService authService;
  private final DynamicLinkService dynamicLinkService;
  private final AppProperties appProperties;
  private final JpaUtils jpaUtils;
  private final CreateAssignmentTemplateBuilder createAssignmentTemplateBuilder;
  private final FileRepository fileRepository;
  private final FileOcrService fileOcrService;

  public List<AssignmentBookingListResponse> getAssignments(final AssignmentSearchRequest assignmentSearchRequest, final String order, final Integer skip,
                                                            final Integer limit) {
    final int uId = UserUtil.getUserIdOrThrow();
    final int cId = UserUtil.getUserCompanyIdOrThrow();

    final List<Integer> uIds = getUserIds(assignmentSearchRequest.getUserIds(), assignmentSearchRequest.getUserGroupIds(), cId, uId);

    final String orderText = Optional.ofNullable(order).orElse("");
    final String orderBy = SORT_OPTIONS.getOrDefault(orderText, "la.load_id, la.load_assignment_id");

    return assignmentRepository.getAssignments(false, uIds, cId, assignmentSearchRequest, orderBy, skip, limit);
  }

  public List<AssignmentByDestinationV2Response> getAssignmentsByDestination(Boolean active, String userIds,
                                                                             QueryParams queryParams) {
    final int cId = getUserCompanyIdOrThrow();
    return assignmentRepository.findAssignmentsByDestination(cId, active, parseIntegerCsvToList(userIds), queryParams);
  }

  public List<AssignmentByOriginV2Response> getAssignmentsByOrigin(Boolean active, String userIds,
                                                                   QueryParams queryParams) {
    final int cId = getUserCompanyIdOrThrow();
    return assignmentRepository.findAssignmentsByOrigin(cId, active, parseIntegerCsvToList(userIds), queryParams);
  }

  public List<AssignmentByCarrierV2Response> getAssignmentsByCarrier(Boolean active, String userIds,
                                                                     QueryParams queryParams) {
    final int cId = getUserCompanyIdOrThrow();
    return assignmentRepository.findAssignmentsByCarrier(cId, active, parseIntegerCsvToList(userIds), queryParams);
  }

  public List<AssignmentByDriverV2Response> getAssignmentsByDriver(Boolean active, String userIds,
                                                                   QueryParams queryParams) {
    final int cId = getUserCompanyIdOrThrow();
    return assignmentRepository.findAssignmentsByDriver(cId, active, parseIntegerCsvToList(userIds), queryParams);
  }

  public List<AssignmentV2ListResponse> getAssignmentNewSharesV2() {
    final int cId = getUserCompanyIdOrThrow();
    return assignmentRepository.findAssignmentNewSharesV2(cId);
  }

  public List<AssignmentV2ListResponse> getAssignmentsV2(
      Boolean active,
      String userIds,
      Integer loadId,
      Integer finalPickupAbCompanyId,
      Integer finalDropAbCompanyId,
      Integer toAbCompanyId,
      Integer toAbUserId,
      Integer toUserId,
      QueryParams queryParams,
      int skip,
      int limit) {

    return assignmentRepository.findAssignmentsV2(
        getUserCompanyIdOrThrow(),
        active,
        parseIntegerCsvToList(userIds),
        loadId, finalPickupAbCompanyId, finalDropAbCompanyId,
        toAbCompanyId, toAbUserId, toUserId,
        queryParams, skip, limit
    );
  }

  public TotalResponse getAssignmentTotalsV2(
      Boolean active,
      String userIds,
      QueryParams queryParams
  ) {
    final int cId = getUserCompanyIdOrThrow();
    return assignmentRepository.findAssignmentTotalsV2(cId, active, parseIntegerCsvToList(userIds), queryParams);
  }

  List<Assignment> createAssignmentCommon(final int loadId, final CreateLoadAssignmentRequest request, final AssignmentFetcher fetcher) {
    final Map<String, String> errorMap = new HashMap<>();
    final List<Assignment> dbAssignments = validateAndFetchAssignments(loadId, request, fetcher);

    final List<AssignmentData> assignmentData = assignmentMapper.createLoadAssignmentRequestToData(request, errorMap);

    if (!errorMap.isEmpty()) {
      throw new ValidationException(errorMap);
    }

    final List<Result<Assignment>> results =
        IntStream.range(0, dbAssignments.size())
            .mapToObj(index -> assignmentDomainService.create(dbAssignments.get(index), assignmentData.get(index))).toList();

    final Optional<Map<String, String>> errors = results.stream()
        .map(Result::getErrors)
        .reduce(REDUCE_OPERATOR);

    if (errors.isPresent() && !errors.get().isEmpty()) {
      throw new ValidationException(errors.get());
    }
    return results.stream().map(Result::getEntity).toList();
  }

  @Transactional
  public AssignmentResponse create(final int loadId, final CreateLoadAssignmentRequest request) {

    final List<Assignment> assignments = createAssignmentCommon(loadId, request, assignmentRepository::findBlankLoadAssignmentsLocking);

    final Integer bookedFromOfferRecipientId = existsAndIsNotEmpty(request.getBookedFromOfferRecipientId())
        ? request.getBookedFromOfferRecipientId().get() : null;

    assignments.get(0).registerDomainEvent(buildAssignmentCreatedEvent(assignments, bookedFromOfferRecipientId));

    final List<Integer> assignmentIds = assignmentRepository.saveAll(assignments).stream().map(Assignment::getLoadAssignmentId).toList();

    return AssignmentResponse.of(assignmentIds);
  }

  @Transactional(readOnly = true)
  public AssignmentPreviewResponse createAssignmentPreview(final int loadId, final CreateLoadAssignmentRequest request) {

    final List<Assignment> assignments = createAssignmentCommon(loadId, request, assignmentRepository::findBlankLoadAssignments);

    final LoadAssignmentTemplateModel model = assignmentMapper.assignmentsToFmModel(
        assignments,
        List.of(), // don't create dynamic links
        appProperties.getDomainUrl());

    return AssignmentPreviewResponse.builder()
        .message("Load assignment preview")
        .content(createAssignmentTemplateBuilder.getPreviewContent(model))
        .build();
  }

  @Transactional
  public void update(final int loadAssignmentId, final UpdateLoadAssignmentRequest request) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final Assignment existingAssignment = assignmentRepository.findByLoadAssignmentIdAndUserCompanyUserCompanyIdAndDeletedFalse(loadAssignmentId, userCompanyId)
        .orElseThrow(() -> new ValidationException(LOAD_ASSIGNMENT_ID, "Could not find id %s".formatted(loadAssignmentId)));
    final String oldStatus = existingAssignment.getAssignmentStatus();
    final Map<String, String> errors = new HashMap<>();
    final AssignmentData assignmentData = assignmentMapper.updateLoadAssignmentRequestToData(request, errors);
    if (!errors.isEmpty()) {
      throw new ValidationException(errors);
    }

    final Result<Assignment> assignmentResult = assignmentDomainService.update(existingAssignment, assignmentData);
    final Assignment assignment = assignmentResult.orElseThrow();

    final String newStatus = existingAssignment.getAssignmentStatus();

    if (!newStatus.equals(oldStatus)) {
      updateStatusInAssignmentChain(assignment, oldStatus, newStatus, UPDATE_LOAD_ASSIGNMENT);
    }

    handleFileOcr(assignment);

    if (hasSubstantialChanges(existingAssignment, jpaUtils)) {
      assignment.registerDomainEvent(
          buildAssignmentUpdatedEvent(assignment, List.of(assignment.getLoadAssignmentId()), jpaUtils.findDirtyFields(existingAssignment).keySet()));
    }

    assignmentRepository.save(assignment);
  }

  @Transactional
  public void updateLoadInvoiceIds(List<Integer> loadAssignmentIds, int invoiceId) {
    if (isEmpty(loadAssignmentIds)) {
      return;
    }

    bookingRepository.updateLoadInvoiceIds(
        loadAssignmentIds,
        invoiceId,
        Instant.now()
    );
  }

  @Transactional
  public void cancel(final int loadId, final int loadAssignmentId, CancelAssignmentRequest dto) {
    final int userCompanyId = getUserCompanyIdOrThrow();

    dto = Objects.requireNonNullElse(dto, CancelAssignmentRequest.builder().build());

    final Optional<Assignment> assignmentOpt =
        assignmentRepository.findByLoadAssignmentIdAndUserCompanyUserCompanyId(loadAssignmentId, userCompanyId);

    if (assignmentOpt.isEmpty() || assignmentOpt.get().getDeleted()) {
      return;
    }

    final Assignment assignment = assignmentOpt.get();
    validateStatus(assignment.getAssignmentStatus());

    final Assignment parentLoadAssignment = assignment.getParentLoadAssignment();
    final Instant now = Instant.now();
    final List<Assignment> toBeSaved = new ArrayList<>();
    final String deletedMessage = existsAndIsNotEmpty(dto.getDeletedMessage()) ? dto.getDeletedMessage().get() : "";

    deleteAssignment(assignment, deletedMessage, now);
    toBeSaved.add(assignment);

    final Assignment blankAssignment = buildBlankAssignment(assignment, now);
    toBeSaved.add(blankAssignment);
    final Integer blankAssignmentId = blankAssignment.getLoadAssignmentId();

    Integer parentAssignmentId = null;
    if (nonNull(parentLoadAssignment)) {
      blankAssignment.setParentLoadAssignment(parentLoadAssignment);
      blankAssignment.setChainLoadAssignment(parentLoadAssignment);
      parentLoadAssignment.setChildLoadAssignment(blankAssignment);
      parentLoadAssignment.setIsReassigned(false);
      if (nonNull(parentLoadAssignment.getUserCompany())) {
        parentAssignmentId = parentLoadAssignment.getLoadAssignmentId();
      }
      toBeSaved.add(parentLoadAssignment);
    }

    final AssignmentCancelledEvent cancelledEvent = buildAssignmentCancelledEvent(loadAssignmentId, blankAssignmentId, parentAssignmentId);
    assignment.registerDomainEvent(cancelledEvent);

    assignmentRepository.saveAll(toBeSaved);
  }

  public List<AssignmentTicketItemResponse> getAssignmentTickets(final int loadAssignmentId) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final Assignment assignment = assignmentRepository.findByLoadAssignmentIdAndUserCompanyUserCompanyIdAndDeletedFalse(loadAssignmentId, userCompanyId)
        .orElseThrow(() -> new ValidationException(LOAD_ASSIGNMENT_ID, "Could not find id %s".formatted(loadAssignmentId)));

    // for the assignment get from fileFields values for both loading and unloading ticket ids
    Integer loadingTicketFileId = assignment.getLoadingTicketFileId();
    List<FileField> originFields = new ArrayList<>();
    if (nonNull(loadingTicketFileId)) {
      File originFile = fileRepository.findById(loadingTicketFileId).orElseThrow();
      originFields = fileRepository.getFileFields(originFile);
    }

    Integer unloadingTicketFileId = assignment.getUnloadingTicketFileId();
    List<FileField> destinationFields = new ArrayList<>();
    if (nonNull(unloadingTicketFileId)) {
      File destinationFile = fileRepository.findById(unloadingTicketFileId).orElseThrow();
      destinationFields = fileRepository.getFileFields(destinationFile);
    }

    // Create a map of response items keyed by fieldName for easy access
    Map<String, AssignmentTicketItemResponse> responseMap = new HashMap<>();

    // Add all origin fields first
    for (FileField field : originFields) {
      if (field.getFieldName() != null) {
        AssignmentTicketItemResponse item = AssignmentTicketItemResponse.builder()
            .fieldLabel(field.getFieldLabel())
            .fieldName(field.getFieldName())
            .originValue(field.getFieldValue())
            .build();
        responseMap.put(field.getFieldName().toLowerCase(), item);
      }
    }

    // Add destination values to matching fields
    for (FileField field : destinationFields) {
      if (field.getFieldName() != null) {
        String fieldNameLower = field.getFieldName().toLowerCase();
        if (responseMap.containsKey(fieldNameLower)) {
          // Found matching field - update with destination value
          AssignmentTicketItemResponse existingItem = responseMap.get(fieldNameLower);

          // Calculate difference value and percentage if values can be parsed as numbers
          Double differenceValue = null;
          Double differencePercentage = null;

          try {
            String originValue = existingItem.getOriginValue();
            String destinationValue = field.getFieldValue();

            if (originValue != null && !originValue.isEmpty()
                && destinationValue != null && !destinationValue.isEmpty()) {
              Double originDouble = parseDouble(originValue);
              Double destinationDouble = parseDouble(destinationValue);

              if (nonNull(originDouble) && nonNull(destinationDouble) && originDouble != 0) {
                differenceValue = destinationDouble - originDouble;
                differencePercentage = (differenceValue / originDouble) * 100;
              }
            }
          } catch (NumberFormatException e) {
            // Not numeric values, skip difference calculations
          }

          // Create a new item with all values
          AssignmentTicketItemResponse updatedItem = AssignmentTicketItemResponse.builder()
              .fieldLabel(existingItem.getFieldLabel())
              .fieldName(existingItem.getFieldName())
              .originValue(existingItem.getOriginValue())
              .destinationValue(field.getFieldValue())
              .differenceValue(differenceValue)
              .differencePercentage(differencePercentage)
              .build();

          responseMap.put(fieldNameLower, updatedItem);
        }
      }
    }

    // Sort fields in the same order as FileOcrService.getFileFields
    List<AssignmentTicketItemResponse> sortedResponse = new ArrayList<>();
    sortedResponse.add(responseMap.get(FileOcrService.OCR_FIELD_TICKET_NUMBER));
    sortedResponse.add(responseMap.get(FileOcrService.OCR_FIELD_TICKET_DATE));
    sortedResponse.add(responseMap.get(FileOcrService.OCR_FIELD_GROSS_WEIGHT));
    sortedResponse.add(responseMap.get(FileOcrService.OCR_FIELD_TARE_WEIGHT));
    sortedResponse.add(responseMap.get(FileOcrService.OCR_FIELD_VOLUME));

    List<AssignmentTicketItemResponse> response = new ArrayList<>(responseMap.values());
    response.sort((a, b) -> {
      if (a.getFieldLabel() == null) {
        return -1;
      }
      if (b.getFieldLabel() == null) {
        return 1;
      }
      return a.getFieldLabel().compareToIgnoreCase(b.getFieldLabel());
    });

    // add only the ones that have a value in either origin or destination
    for (AssignmentTicketItemResponse item : response) {
      if (!isEmpty(item.getOriginValue()) || !isEmpty(item.getDestinationValue())) {
        sortedResponse.add(item);
      }
    }
    return sortedResponse;
  }

  public List<DynamicLink> createDynamicLinks(final List<Assignment> assignments, final String action) {
    final String urlSuffix = buildUrlSuffix(assignments);
    final String domainUrl = appProperties.getDomainUrl();

    return assignments.stream()
        .limit(3)
        .map(Assignment::getLoadAssignmentId)
        .map(aid -> {
          String url = buildUrl(aid, domainUrl, urlSuffix, action);
          Map<String, String> params = new HashMap<>();
          params.put("load_assignment_id", aid.toString());
          String type = action.contains("booking") ? "assignments" : "bookings";
          return dynamicLinkService.createDynamicLink(url, type, params);
        })
        .toList();

  }

  public AbstractAssignmentBookingDomainService getDomainService() {
    return assignmentDomainService;
  }

  private AssignmentCancelledEvent buildAssignmentCancelledEvent(final int loadAssignmentId, final int blankAssignmentId,
                                                                 final Integer parentAssignmentId) {
    return new AssignmentCancelledEvent(loadAssignmentId, blankAssignmentId, parentAssignmentId);
  }

  private static void validateStatus(final String assignmentStatus) {
    if (UNASSIGNED.equals(assignmentStatus)) {
      throw new ValidationException(ASSIGNMENT_STATUS, "The load is not assigned and cannot be canceled");
    }

    if (COMPLETED.equals(assignmentStatus)) {
      throw new ValidationException(ASSIGNMENT_STATUS, "The load is completed and cannot be canceled");
    }
  }

  private AssignmentCreatedEvent buildAssignmentCreatedEvent(final List<Assignment> assignments, final Integer bookedFromOfferRecipientId) {
    final List<Integer> loadAssignmentIds = assignments.stream().map(Assignment::getLoadAssignmentId).toList();
    return new AssignmentCreatedEvent(loadAssignmentIds, bookedFromOfferRecipientId);
  }

  private AssignmentUpdatedEvent buildAssignmentUpdatedEvent(final Assignment assignment, final List<Integer> ids, final Set<String> affectedFields) {
    final List<Integer> loadAssignmentId = ids.isEmpty() ? List.of(assignment.getLoadAssignmentId()) : ids;
    return new AssignmentUpdatedEvent(loadAssignmentId, affectedFields);
  }

  private Assignment buildBlankAssignment(final Assignment assignment, final Instant now) {
    final User user = userService.getLoggedInUser();
    final Integer userId = user.getUserId();
    final UserCompany userCompany = user.getUserCompany();
    final int userCompanyId = userCompany.getUserCompanyId();

    final Assignment blankAssignment = new Assignment();
    blankAssignment.setUser(user);
    blankAssignment.setUserCompany(userCompany);
    blankAssignment.setCreatedByUserId(userId);
    blankAssignment.setCreatedByUserCompanyId(userCompanyId);

    blankAssignment.setLoad(assignment.getLoad());
    blankAssignment.setLoadAssignmentNumber(assignment.getLoadAssignmentNumber());
    blankAssignment.setPickupNumber(assignment.getPickupNumber());
    blankAssignment.setDropNumber(assignment.getDropNumber());
    blankAssignment.setWorkOrderNumber(assignment.getWorkOrderNumber());
    blankAssignment.setPickupNotes(assignment.getPickupNotes());
    blankAssignment.setDropNotes(assignment.getDropNotes());
    blankAssignment.setConfirmationSentMethod(assignment.getConfirmationSentMethod());

    blankAssignment.setOriginalRateType(assignment.getOriginalRateType());
    blankAssignment.setPreviousRateType(assignment.getPreviousRateType());
    blankAssignment.setRateType(assignment.getRateType());

    blankAssignment.setCreatedDate(now);
    blankAssignment.setCreatedByUserId(userId);
    blankAssignment.setModifiedDate(now);

    if (assignment.getIsRerouted()) {
      blankAssignment.setIsRerouted(true);
      blankAssignment.setRerouteReason(assignment.getRerouteReason());
      blankAssignment.setRerouteDate(assignment.getRerouteDate());
      blankAssignment.setRerouteByUserId(assignment.getRerouteByUserId());
      blankAssignment.setRerouteAbCompany(assignment.getRerouteAbCompany());
      blankAssignment.setPreviousLoadAssignmentNumber(assignment.getLoadAssignmentNumber());
      blankAssignment.setPreviousPickupNumber(assignment.getPickupNumber());
      blankAssignment.setPreviousDropNumber(assignment.getDropNumber());
      blankAssignment.setPreviousWorkOrderNumber(assignment.getWorkOrderNumber());
      blankAssignment.setEstMiles(assignment.getEstMiles());
      blankAssignment.setEstHours(assignment.getEstHours());
      blankAssignment.setBillMiles(assignment.getBillMiles());
      blankAssignment.setBillHours(assignment.getBillHours());
    }
    final Assignment savedAssignment = assignmentRepository.save(blankAssignment);
    savedAssignment.setChainLoadAssignment(savedAssignment);
    return savedAssignment;
  }

  private static void deleteAssignment(final Assignment assignment, final String deletedMessage, final Instant now) {
    final int userId = UserUtil.getUserIdOrThrow();

    assignment.setParentLoadAssignment(null);
    assignment.setDeleted(true);
    assignment.setDeletedDate(now);
    assignment.setDeletedByUserId(userId);
    assignment.setDeletedMessage(deletedMessage);
    assignment.setModifiedDate(now);

    if (isNull(assignment.getToLoad()) && PENDING.equals(assignment.getSharedWithHiredCompanyResponse())) {
      assignment.setToDeleted(true);
      assignment.setToDeletedDate(now);
      assignment.setToDeletedByUserId(userId);
    }
  }

  private List<Assignment> validateAndFetchAssignments(
      final int loadId,
      final CreateLoadAssignmentRequest request,
      final AssignmentFetcher fetcher
  ) {

    final int numberOfLoads;
    if (existsAndIsNotEmpty(request.getNumberOfLoads())) {
      numberOfLoads = request.getNumberOfLoads().get();
    } else {
      throw new ValidationException(NUMBER_OF_LOADS, "You must pass the number of loads to assign");
    }

    if (existsAndIsNotEmpty(request.getAssignments()) && request.getAssignments().get().size() != numberOfLoads) {
      throw new ValidationException(ASSIGNMENTS, "You must pass an assignments array with %s elements".formatted(numberOfLoads));
    }

    final List<CreateLoadAssignmentSubRequest>
        createLoadAssignmentSubRequests = existsAndIsNotEmpty(request.getAssignments()) ? request.getAssignments().get() : List.of();

    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();

    final List<Integer> assignmentIds =
        createLoadAssignmentSubRequests.stream().map(CreateLoadAssignmentSubRequest::getLoadAssignmentId).filter(Objects::nonNull).toList();

    final PageRequest pageRequest = PageRequest.of(0, numberOfLoads);

    final List<Assignment> dbAssignments = fetcher.fetchAssignments(loadId, userCompanyId, assignmentIds, pageRequest);

    if (dbAssignments.size() != numberOfLoads) {
      throw new ValidationException(NUMBER_OF_LOADS, "Only %s unassigned loads remain to be assigned".formatted(dbAssignments.size()));
    }

    return dbAssignments;
  }

  @NotNull
  private List<Integer> getUserIds(List<Integer> userIds, List<Integer> userGroupIds, Integer cId, Integer uId) {
    List<Integer> uIds = null;

    if (!isEmpty(userGroupIds)) {
      // TODO waiting for user groups
      // get user groups
      // if user groups are not in the company, send error
      // else, append user ids
    }
    if (!isEmpty(userIds)) {
      List<User> users = userRepository.findAllByUserIdInAndUserCompanyUserCompanyId(userIds, cId);
      // set uIds from the list
      uIds = users.stream().map(User::getUserId).toList();
    }
    if (isEmpty(uIds)) {
      uIds = List.of(uId);
    }
    return uIds;
  }

  private String buildUrl(final Integer aid, final String domainUrl, final String urlSuffix, final String action) {
    if (action.contains("booking")) {
      return BOOKING_URL_TEMPLATE.formatted(domainUrl, aid, urlSuffix);
    } else {
      return ASSIGNMENT_URL_TEMPLATE.formatted(domainUrl, aid, urlSuffix);
    }
  }

  private String buildUrlSuffix(final List<Assignment> assignments) {
    final Assignment firstAssignment = assignments.get(0);

    final StringBuilder urlSuffixBuilder = new StringBuilder();

    if (nonNull(firstAssignment.getToAbUser())) {
      final Integer abUserId = firstAssignment.getToAbUser().getAbUserId();
      final String userAuthToken = authService.getUserAuthToken(null, abUserId, Collections.emptyList(), 7);
      urlSuffixBuilder
          .append("?auth_token=").append(userAuthToken);
      if (nonNull(firstAssignment.getToUser())) {
        final Integer userId = firstAssignment.getToUser().getUserId();
        urlSuffixBuilder
            .append("&user_id=").append(userId);
      }
    } else if (nonNull(firstAssignment.getToUser())) {
      final Integer userId = firstAssignment.getToUser().getUserId();
      final List<Integer> assignmentIds = assignments.stream().map(Assignment::getLoadAssignmentId).toList();
      final String userAuthToken = authService.getUserAuthToken(userId, null, assignmentIds, 7);
      urlSuffixBuilder
          .append("?auth_token=").append(userAuthToken)
          .append("&user_id=").append(userId);
    }

    return urlSuffixBuilder.toString();
  }

  private void handleFileOcr(Assignment booking) {
    final UserCompany userCompany = booking.getUserCompany();
    if (nonNull(userCompany) && userCompany.hasIntegration(AppConstants.Integration.AGTRAX) &&
        nonNull(booking.getUnloadingTicketFileId())) {
      File unloadingTicketFile = fileRepository.getReferenceById(booking.getUnloadingTicketFileId());
      if (!unloadingTicketFile.getOcrProcessed()) {
        fileOcrService.ocrFileAsync(unloadingTicketFile.getFileId());
      }
    }
  }

}
