package com.bulkloads.web.assignment.service;

import static com.bulkloads.config.AppConstants.AssignmentStatus.COMPLETED;
import static com.bulkloads.config.AppConstants.AssignmentStatus.DELIVERED;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.ABOVE_REFERENCE_ASSIGNMENT_BOOKING;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.BELOW_REFERENCE_ASSIGNMENT_BOOKING;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.THE_REFERENCE_ASSIGNMENT_BOOKING;
import static java.util.Objects.nonNull;

import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.JpaUtils;
import com.bulkloads.web.assignment.domain.AbstractAssignmentBookingDomainService;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.event.AssignmentBookingStatusChangedEvent;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public abstract class AssignmentBookingService {

  public abstract AbstractAssignmentBookingDomainService getDomainService();

  @Transactional
  public void updateStatusInAssignmentChain(final Assignment rootEntity, final String oldStatus, final String newStatus, final String action) {

    // event for rootEntity
    final AssignmentBookingStatusChangedEvent statusChangedEvent =
        buildStatusUpdatedEvent(rootEntity, oldStatus, newStatus, action, THE_REFERENCE_ASSIGNMENT_BOOKING);
    rootEntity.registerDomainEvent(statusChangedEvent);

    // the rest of the chain will go up to delivered
    String chainStatus = newStatus.equals(COMPLETED) ? DELIVERED : newStatus;

    // iterate parents
    Assignment parent = rootEntity.getParentLoadAssignment();
    while (nonNull(parent)) {
      if (!parent.getAssignmentStatus().equals(chainStatus)
          && !parent.getAssignmentStatus().equals(newStatus)) {
        getDomainService().setAssignmentStatus(parent, chainStatus);
        final AssignmentBookingStatusChangedEvent parentStatusChangedEvent =
            buildStatusUpdatedEvent(parent, oldStatus, chainStatus, action, ABOVE_REFERENCE_ASSIGNMENT_BOOKING);
        rootEntity.registerDomainEvent(parentStatusChangedEvent);
      }
      parent = parent.getParentLoadAssignment();
    }

    // iterate children
    Assignment child = rootEntity.getChildLoadAssignment();
    while (nonNull(child)) {
      if (!child.getAssignmentStatus().equals(chainStatus)
          && !child.getAssignmentStatus().equals(newStatus)) {
        getDomainService().setAssignmentStatus(child, chainStatus);
        final AssignmentBookingStatusChangedEvent childStatusChangedEvent =
            buildStatusUpdatedEvent(child, oldStatus, chainStatus, action, BELOW_REFERENCE_ASSIGNMENT_BOOKING);
        rootEntity.registerDomainEvent(childStatusChangedEvent);

      }
      child = child.getChildLoadAssignment();
    }
  }

  protected AssignmentBookingStatusChangedEvent buildStatusUpdatedEvent(
      final Assignment entity,
      final String oldStatus,
      final String newStatus,
      final String action,
      final String direction) {
    final Integer loadAssignmentId = entity.getLoadAssignmentId();
    return new AssignmentBookingStatusChangedEvent(List.of(loadAssignmentId), oldStatus, newStatus, action, direction);
  }

  protected boolean hasSubstantialChanges(Assignment entity, JpaUtils jpaUtils) {
    final Map<String, Object> dirtyFields = jpaUtils.findDirtyFields(entity);
    return !(dirtyFields.size() == 1 && dirtyFields.containsKey("modifiedDate"));
  }

}
