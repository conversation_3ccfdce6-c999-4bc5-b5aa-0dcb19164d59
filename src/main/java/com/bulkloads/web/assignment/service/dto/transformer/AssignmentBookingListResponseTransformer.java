package com.bulkloads.web.assignment.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.assignment.service.dto.AssignmentBookingListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AssignmentBookingListResponseTransformer implements TupleTransformer<AssignmentBookingListResponse> {

  @Override
  public AssignmentBookingListResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    AssignmentBookingListResponse response = new AssignmentBookingListResponse();
    response.setLoadAssignmentId(parts.asInteger("load_assignment_id"));
    response.setActive(parts.asBoolean("active"));
    response.setAssignmentStatus(parts.asString("assignment_status"));
    response.setAssignedDate(parts.asInstant("assigned_date"));
    response.setAvgRating(parts.asDouble("avg_rating"));
    response.setBillHours(parts.asDouble("bill_hours"));
    response.setBillMiles(parts.asDouble("bill_miles"));
    response.setBillQuantity(parts.asDouble("bill_quantity"));
    response.setBillRatePerMile(parts.asDouble("bill_rate_per_mile"));
    response.setBillSubtotal(parts.asDouble("bill_subtotal"));
    response.setBillSurcharges(parts.asDouble("bill_surcharges"));
    response.setBillToAbCompanyId(parts.asInteger("bill_to_ab_company_id"));
    response.setBillToCompanyName(parts.asString("bill_to_company_name"));
    response.setBillToAbUserId(parts.asInteger("bill_to_ab_user_id"));
    response.setBillToFirstName(parts.asString("bill_to_first_name"));
    response.setBillToLastName(parts.asString("bill_to_last_name"));
    response.setBillToEmail(parts.asString("bill_to_email"));
    response.setBillTotal(parts.asDouble("bill_total"));
    response.setBillVolume(parts.asDouble("bill_volume"));
    response.setBillWeight(parts.asDouble("bill_weight"));
    response.setBillWeightUse(parts.asString("bill_weight_use"));
    response.setPayment(parts.asDouble("payment"));
    response.setPaymentNotes(parts.asString("payment_notes"));
    response.setPaymentDate(parts.asInstant("payment_date"));
    response.setPaymentApproved(parts.asInteger("payment_approved"));
    response.setPaymentApprovedDate(parts.asInstant("payment_approved_date"));
    response.setPaid(parts.asInteger("paid"));
    response.setPaidDate(parts.asInstant("paid_date"));
    response.setToPayment(parts.asDouble("to_payment"));
    response.setToPaymentNotes(parts.asString("to_payment_notes"));
    response.setToPaymentDate(parts.asInstant("to_payment_date"));
    response.setToPaid(parts.asInteger("to_paid"));
    response.setToPaidDate(parts.asInstant("to_paid_date"));
    response.setBolNumber(parts.asString("bol_number"));
    response.setLoadAssignmentNumber(parts.asString("load_assignment_number"));
    response.setHauledNotes(parts.asString("hauled_notes"));
    response.setScheduledHauledDate(parts.asLocalDate("scheduled_hauled_date"));
    response.setScheduledPickupDate(parts.asInstant("scheduled_pickup_date"));
    response.setPickupTimezone(parts.asString("pickup_timezone"));
    response.setScheduledDropDate(parts.asInstant("scheduled_drop_date"));
    response.setDropTimezone(parts.asString("drop_timezone"));
    response.setHauledDate(parts.asLocalDate("hauled_date"));
    response.setCompanyName(parts.asString("company_name"));
    response.setCompanyLogoUrl(parts.asString("company_logo_url"));
    response.setCompanyLogoThumbUrl(parts.asString("company_logo_thumb_url"));
    response.setCompletedDate(parts.asInstant("completed_date"));
    response.setConfirmationConfirmedDate(parts.asInstant("confirmation_confirmed_date"));
    response.setConfirmationConfirmedMethod(parts.asString("confirmation_confirmed_method"));
    response.setConfirmationEmailStatus(parts.asString("confirmation_email_status"));
    response.setConfirmationFileId(parts.asInteger("confirmation_file_id"));
    response.setConfirmationFileUrl(parts.asString("confirmation_file_url"));
    response.setConfirmationOpenedDate(parts.asInstant("confirmation_opened_date"));
    response.setConfirmationSentDate(parts.asInstant("confirmation_sent_date"));
    response.setConfirmationSentMethod(parts.asString("confirmation_sent_method"));
    response.setConfirmationThumbUrl(parts.asString("confirmation_thumb_url"));
    response.setConfirmationCcOther(parts.asString("confirmation_cc_others"));
    response.setConfirmationToAbUserIds(parts.asString("confirmation_to_ab_user_ids"));
    response.setCreatedByUserId(parts.asInteger("created_by_user_id"));
    response.setCreatedByUserCompanyId(parts.asInteger("created_by_user_company_id"));
    response.setCreatedDate(parts.asInstant("created_date"));
    response.setDeletedMessage(parts.asString("deleted_message"));
    response.setDeliveredDate(parts.asInstant("delivered_date"));
    response.setDispatchedDate(parts.asInstant("dispatched_date"));
    response.setDispatcherUserId(parts.asInteger("dispatcher_user_id"));
    response.setDropAbCompanyId(parts.asInteger("drop_ab_company_id"));
    response.setDropAddress(parts.asString("drop_address"));
    response.setDropApptRequired(parts.asInteger("drop_appt_required"));
    response.setDropCensusNum(parts.asInteger("drop_census_num"));
    response.setDropCity(parts.asString("drop_city"));
    response.setDropCompanyName(parts.asString("drop_company_name"));
    response.setDropCompanyPhone(parts.asString("drop_company_phone"));
    response.setDropCountry(parts.asString("drop_country"));
    response.setDropDirections(parts.asString("drop_directions"));
    response.setDropLatitude(parts.asDouble("drop_latitude"));
    response.setDropLocation(parts.asString("drop_location"));
    response.setDropLongitude(parts.asDouble("drop_longitude"));
    response.setDropMcNum(parts.asString("drop_mc_num"));
    response.setDropNotes(parts.asString("drop_notes"));
    response.setDropNumber(parts.asString("drop_number"));
    response.setDropPo(parts.asString("drop_po"));
    response.setDropReceivingHours(parts.asString("drop_receiving_hours"));
    response.setDropState(parts.asString("drop_state"));
    response.setDropZip(parts.asString("drop_zip"));
    response.setEmail(parts.asString("email"));
    response.setEnrouteDate(parts.asInstant("enroute_date"));
    response.setEquipmentNames(parts.asString("equipment_names"));
    response.setEstHours(parts.asDouble("est_hours"));
    response.setEstMiles(parts.asDouble("est_miles"));
    response.setEstQuantity(parts.asDouble("est_quantity"));
    response.setEstRatePerMile(parts.asDouble("est_rate_per_mile"));
    response.setEstSubtotal(parts.asDouble("est_subtotal"));
    response.setEstSurcharges(parts.asDouble("est_surcharges"));
    response.setEstTotal(parts.asDouble("est_total"));
    response.setEstVolume(parts.asDouble("est_volume"));
    response.setEstWeight(parts.asDouble("est_weight"));
    response.setEstimatedMiles(parts.asDouble("estimated_miles"));
    response.setFirstName(parts.asString("first_name"));
    response.setHiringAbCompanyId(parts.asInteger("hiring_ab_company_id"));
    response.setHiringAbUserId(parts.asInteger("hiring_ab_user_id"));
    response.setWorkOrderNumber(parts.asString("work_order_number"));
    response.setInsideNotes(parts.asString("inside_notes"));
    response.setPersonalMessage(parts.asString("personal_message"));
    response.setIsHazmat(parts.asInteger("is_hazmat"));
    response.setIsBooking(parts.asInteger("is_booking"));
    response.setLastName(parts.asString("last_name"));
    response.setCommodityId(parts.asInteger("commodity_id"));
    response.setLoCommodity(parts.asString("lo_commodity"));
    response.setLoContractNumber(parts.asString("lo_contract_number"));
    response.setLoRate(parts.asDouble("lo_rate"));
    response.setLoRateType(parts.asString("lo_rate_type"));
    response.setLoadAccess(parts.asString("load_access"));
    response.setLoadBearingDirection(parts.asString("load_bearing_direction"));
    response.setLoadBearing(parts.asDouble("load_bearing"));
    response.setLoadId(parts.asInteger("load_id"));
    response.setLoadedVolume(parts.asDouble("loaded_volume"));
    response.setLoadedWeight(parts.asDouble("loaded_weight"));
    response.setLoadingDate(parts.asInstant("loading_date"));
    response.setLoadingTicketNumber(parts.asString("loading_ticket_number"));
    response.setMileage(parts.asDouble("mileage"));
    response.setNumberOfFiles(parts.asInteger("number_of_files"));
    response.setTotalFilesSize(parts.asInteger("total_files_size"));
    response.setNeedsAttention(parts.asInteger("needs_attention"));
    response.setNumberOfLoads(parts.asInteger("number_of_loads"));
    response.setNumberOfDeliveredLoads(parts.asInteger("number_of_delivered_loads"));
    response.setNumberOfAvailableLoads(parts.asInteger("number_of_available_loads"));
    response.setOriginalRate(parts.asDouble("original_rate"));
    response.setOriginalRateType(parts.asString("original_rate_type"));
    response.setOriginalRatePercentage(parts.asDouble("original_rate_percentage"));
    response.setOriginalRateVisible(parts.asInteger("original_rate_visible"));
    response.setOriginalsRequired(parts.asInteger("originals_required"));
    response.setPhone1(parts.asString("phone_1"));
    response.setPickupAbCompanyId(parts.asInteger("pickup_ab_company_id"));
    response.setPickupAddress(parts.asString("pickup_address"));
    response.setPickupApptRequired(parts.asInteger("pickup_appt_required"));
    response.setPickupCensusNum(parts.asInteger("pickup_census_num"));
    response.setPickupCity(parts.asString("pickup_city"));
    response.setPickupCompanyName(parts.asString("pickup_company_name"));
    response.setPickupCompanyPhone(parts.asString("pickup_company_phone"));
    response.setPickupCountry(parts.asString("pickup_country"));
    response.setPickupDirections(parts.asString("pickup_directions"));
    response.setPickupLatitude(parts.asDouble("pickup_latitude"));
    response.setPickupLocation(parts.asString("pickup_location"));
    response.setPickupLongitude(parts.asDouble("pickup_longitude"));
    response.setPickupMcNum(parts.asString("pickup_mc_num"));
    response.setPickupNotes(parts.asString("pickup_notes"));
    response.setPickupNumber(parts.asString("pickup_number"));
    response.setPickupPo(parts.asString("pickup_po"));
    response.setPickupReceivingHours(parts.asString("pickup_receiving_hours"));
    response.setPickupState(parts.asString("pickup_state"));
    response.setPickupZip(parts.asString("pickup_zip"));
    response.setPostDate(parts.asInstant("post_date"));
    response.setProduct(parts.asString("product"));
    response.setRateProductCategoryId(parts.asInteger("rate_product_category_id"));
    response.setRateType(parts.asString("rate_type"));
    response.setRate(parts.asDouble("rate"));
    response.setRatingCount(parts.asInteger("rating_count"));
    response.setSharedWithHiredCompanyResponse(parts.asString("shared_with_hired_company_response"));
    response.setSharedWithHiredCompany(parts.asInteger("shared_with_hired_company"));
    response.setSharedWithHiringCompanyResponse(parts.asString("shared_with_hiring_company_response"));
    response.setSharedWithHiringCompany(parts.asInteger("shared_with_hiring_company"));
    response.setNewShare(parts.asInteger("new_share"));
    response.setChildLoadAssignmentId(parts.asInteger("child_load_assignment_id"));
    response.setParentLoadAssignmentId(parts.asInteger("parent_load_assignment_id"));
    response.setChainLoadAssignmentId(parts.asInteger("chain_load_assignment_id"));
    response.setAutoInvoice(parts.asBoolean("auto_invoice"));
    response.setReadyToInvoice(parts.asBoolean("ready_to_invoice"));
    response.setParentReadyToInvoice(parts.asBoolean("parent_ready_to_invoice"));
    response.setInvoiced(parts.asBoolean("invoiced"));
    response.setParentInvoiced(parts.asBoolean("parent_invoiced"));
    response.setParentDeleted(parts.asBoolean("parent_deleted"));
    response.setParentDeletedMessage(parts.asString("parent_deleted_message"));
    response.setLoadInvoiceId(parts.asInteger("load_invoice_id"));
    response.setInvoiceDate(parts.asInstant("invoice_date"));
    response.setInvoiceEmailStatus(parts.asString("invoice_email_status"));
    response.setInvoiceEmailStatusDate(parts.asInstant("invoice_email_status_date"));
    response.setInvoiceFileUrl(parts.asString("invoice_file_url"));
    response.setInvoiceThumbUrl(parts.asString("invoice_thumb_url"));
    response.setSettlementFileUrl(parts.asString("settlement_file_url"));
    response.setBillToUserId(parts.asInteger("bill_to_user_id"));
    response.setBillToUserCompanyId(parts.asInteger("bill_to_user_company_id"));
    response.setArchived(parts.asInteger("archived"));
    response.setBillToArchived(parts.asInteger("bill_to_archived"));
    response.setShipFrom(parts.asLocalDate("ship_from"));
    response.setShipTo(parts.asLocalDate("ship_to"));
    response.setToAbCompanyId(parts.asInteger("to_ab_company_id"));
    response.setToAbUserId(parts.asInteger("to_ab_user_id"));
    response.setToAvgRating(parts.asDouble("to_avg_rating"));
    response.setToCompanyName(parts.asString("to_company_name"));
    response.setToCompanyLogoUrl(parts.asString("to_company_logo_url"));
    response.setToCompanyLogoThumbUrl(parts.asString("to_company_logo_thumb_url"));
    response.setToEmail(parts.asString("to_email"));
    response.setToFirstName(parts.asString("to_first_name"));
    response.setToLastName(parts.asString("to_last_name"));
    response.setToLoadId(parts.asInteger("to_load_id"));
    response.setToPhone1(parts.asString("to_phone_1"));
    response.setToRatingCount(parts.asInteger("to_rating_count"));
    response.setToUserCompanyId(parts.asInteger("to_user_company_id"));
    response.setToUserId(parts.asInteger("to_user_id"));
    response.setToUserTypes(parts.asString("to_user_types"));
    response.setUnloadWeight(parts.asDouble("unload_weight"));
    response.setUnloadVolume(parts.asDouble("unload_volume"));
    response.setUnloadingDate(parts.asInstant("unloading_date"));
    response.setUnloadingTicketNumber(parts.asString("unloading_ticket_number"));
    response.setUserCompanyId(parts.asInteger("user_company_id"));
    response.setUserId(parts.asInteger("user_id"));
    response.setUserTypes(parts.asString("user_types"));
    response.setWashoutRequired(parts.asInteger("washout_required"));
    response.setRequiredFileTypeIds(parts.asIntegerListFromCsv("required_file_type_ids"));
    response.setRequiredFileTypes(parts.asStringListFromCsv("required_file_types"));
    response.setRequiredFilesNote(parts.asString("required_files_note"));
    response.setGeoShareLocation(parts.asInteger("geo_share_location"));
    response.setGeoRequestStatus(parts.asString("geo_request_status"));
    response.setGeoRequestDate(parts.asInstant("geo_request_date"));
    response.setGeoTrackingEnabled(parts.asBoolean("geo_tracking_enabled"));
    response.setGeoTrackingStopDate(parts.asInstant("geo_tracking_stop_date"));
    response.setGeoTrackingUntil(parts.asString("geo_tracking_until"));
    response.setGeoLatitude(parts.asDouble("geo_latitude"));
    response.setGeoLongitude(parts.asDouble("geo_longitude"));
    response.setGeoSpeed(parts.asDouble("geo_speed"));
    response.setGeoHeading(parts.asDouble("geo_heading"));
    response.setGeoUpdatedDate(parts.asInstant("geo_updated_date"));
    response.setExternalLoadId(parts.asString("external_load_id"));
    response.setPickupExternalAbCompanyId(parts.asString("pickup_external_ab_company_id"));
    response.setDropExternalAbCompanyId(parts.asString("drop_external_ab_company_id"));
    response.setHiringExternalAbCompanyId(parts.asString("hiring_external_ab_company_id"));
    response.setHiringExternalAbUserId(parts.asString("hiring_external_ab_user_id"));
    response.setToExternalAbCompanyId(parts.asString("to_external_ab_company_id"));
    response.setToExternalAbUserId(parts.asString("to_external_ab_user_id"));
    response.setBillToExternalAbCompanyId(parts.asString("bill_to_external_ab_company_id"));
    response.setBillToExternalAbUserId(parts.asString("bill_to_external_ab_user_id"));
    response.setParentHiringCompanyName(parts.asString("parent_hiring_company_name"));
    response.setIsReassigned(parts.asInteger("is_reassigned"));
    response.setIsIntraCompany(parts.asInteger("is_intra_company"));
    response.setIsDriver(parts.asInteger("is_driver"));
    response.setIsManaged(parts.asInteger("is_managed"));
    response.setModifiedDate(parts.asInstant("modified_date"));
    response.setDeleted(parts.asBoolean("deleted"));
    response.setExportDate(parts.asInstant("export_date"));
    response.setParentNumberOfFiles(parts.asInteger("parent_number_of_files"));
    response.setIsRerouted(parts.asInteger("is_rerouted"));
    response.setRerouteReason(parts.asString("reroute_reason"));
    response.setRerouteRequest(parts.asBoolean("reroute_request"));
    response.setRerouteRequestReason(parts.asString("reroute_request_reason"));
    response.setRerouteRequestDate(parts.asInstant("reroute_request_date"));
    response.setRerouteDate(parts.asInstant("reroute_date"));
    response.setRerouteByUserId(parts.asInteger("reroute_by_user_id"));
    response.setRerouteContractId(parts.asInteger("reroute_contract_id"));
    response.setRerouteContractNumber(parts.asString("reroute_contract_number"));
    response.setPreviousLoadAssignmentNumber(parts.asString("previous_load_assignment_number"));
    response.setPreviousPickupNumber(parts.asString("previous_pickup_number"));
    response.setPreviousDropNumber(parts.asString("previous_drop_number"));
    response.setPreviousWorkOrderNumber(parts.asString("previous_work_order_number"));
    response.setPreviousRate(parts.asDouble("previous_rate"));
    response.setPreviousRateType(parts.asString("previous_rate_type"));
    response.setPreviousBillSubtotal(parts.asDouble("previous_bill_subtotal"));
    response.setPreviousBillSurcharges(parts.asDouble("previous_bill_surcharges"));
    response.setPreviousBillTotal(parts.asDouble("previous_bill_total"));
    response.setPreviousBillRatePerMile(parts.asDouble("previous_bill_rate_per_mile"));
    response.setReroutePickupDrop(parts.asString("reroute_pickup_drop"));
    response.setRerouteAbCompanyId(parts.asInteger("reroute_ab_company_id"));
    response.setRerouteExternalAbCompanyId(parts.asString("reroute_external_ab_company_id"));
    response.setRerouteCompanyName(parts.asString("reroute_company_name"));
    response.setRerouteCensusNum(parts.asInteger("reroute_census_num"));
    response.setRerouteMcNum(parts.asString("reroute_mc_num"));
    response.setRerouteAddress(parts.asString("reroute_address"));
    response.setRerouteCity(parts.asString("reroute_city"));
    response.setRerouteState(parts.asString("reroute_state"));
    response.setRerouteZip(parts.asString("reroute_zip"));
    response.setRerouteCountry(parts.asString("reroute_country"));
    response.setRerouteLocation(parts.asString("reroute_location"));
    response.setRerouteLatitude(parts.asDouble("reroute_latitude"));
    response.setRerouteLongitude(parts.asDouble("reroute_longitude"));
    response.setRerouteCompanyPhone(parts.asString("reroute_company_phone"));
    response.setRerouteReceivingHours(parts.asString("reroute_receiving_hours"));
    response.setRerouteDirections(parts.asString("reroute_directions"));
    response.setRerouteCompanyNotes(parts.asString("reroute_company_notes"));
    response.setRerouteApptRequired(parts.asBoolean("reroute_appt_required"));
    response.setToDeleted(parts.asBoolean("to_deleted"));
    response.setToDeletedMessage(parts.asString("to_deleted_message"));
    response.setTruckUserCompanyEquipmentId(parts.asInteger("truck_user_company_equipment_id"));
    response.setTrailerUserCompanyEquipmentId(parts.asInteger("trailer_user_company_equipment_id"));
    response.setTruckUserCompanyEquipment(parts.asString("truck_user_company_equipment"));
    response.setTrailerUserCompanyEquipment(parts.asString("trailer_user_company_equipment"));

    return response;
  }
}
