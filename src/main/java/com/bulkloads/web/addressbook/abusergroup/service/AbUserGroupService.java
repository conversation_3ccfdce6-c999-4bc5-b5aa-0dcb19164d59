package com.bulkloads.web.addressbook.abusergroup.service;

import static com.bulkloads.common.UserUtil.getUserCompanyIdOrThrow;
import static com.bulkloads.config.AppConstants.WebSocket.ACTION;
import static com.bulkloads.config.AppConstants.WebSocket.Action.CREATED;
import static com.bulkloads.config.AppConstants.WebSocket.Action.DELETED;
import static com.bulkloads.config.AppConstants.WebSocket.Action.UPDATED;
import static com.bulkloads.config.AppConstants.WebSocket.Channel.AB_USER_GROUPS;
import static com.bulkloads.config.AppConstants.WebSocket.DATA;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.addressbook.abusergroup.domain.AbUserGroupDomainService;
import com.bulkloads.web.addressbook.abusergroup.domain.data.AbUserGroupData;
import com.bulkloads.web.addressbook.abusergroup.domain.entity.AbUserGroup;
import com.bulkloads.web.addressbook.abusergroup.mapper.AbUserGroupMapper;
import com.bulkloads.web.addressbook.abusergroup.repository.AbUserGroupRepository;
import com.bulkloads.web.addressbook.abusergroup.service.dto.AbUserGroupRequest;
import com.bulkloads.web.addressbook.abusergroup.service.dto.AbUserGroupResponse;
import com.bulkloads.web.infra.websocket.WebSocketService;
import com.bulkloads.web.infra.websocket.dto.WebSocketPublishDto;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AbUserGroupService {

  private final AbUserGroupDomainService domainService;
  private final AbUserGroupRepository repository;
  private final AbUserGroupMapper mapper;
  private final WebSocketService webSocketService;

  public List<AbUserGroupResponse> getAbUserGroups() {
    final int userCompanyId = getUserCompanyIdOrThrow();
    return repository.findAllByUserCompanyUserCompanyIdAndDeletedFalse(userCompanyId)
        .stream()
        .map(mapper::entityToResponse)
        .toList();
  }

  public AbUserGroupResponse getAbUserGroup(final int abUserGroupId) {
    final int userCompanyId = getUserCompanyIdOrThrow();
    return repository.findByAbUserGroupIdAndUserCompanyUserCompanyId(abUserGroupId, userCompanyId)
        .map(mapper::entityToResponse)
        .orElseThrow();
  }

  public AbUserGroupResponse create(final AbUserGroupRequest request) {

    final AbUserGroupData data = mapper.requestToData(request);
    final Result<AbUserGroup> result = domainService.create(data);
    AbUserGroup entity = result.orElseThrow();

    entity = repository.save(entity);
    final AbUserGroupResponse response = mapper.entityToResponse(entity);
    //TODO refactor to use domain events
    sendToWebSocket(CREATED, entity.getAbUserGroupId(), response);

    return response;
  }


  public AbUserGroupResponse update(final int abUserGroupId, final AbUserGroupRequest request) {
    final int userCompanyId = getUserCompanyIdOrThrow();
    final AbUserGroup existingEntity = repository.findByAbUserGroupIdAndUserCompanyUserCompanyId(abUserGroupId, userCompanyId)
        .orElseThrow(() -> new ValidationException("ab_user_group_id", "Could not find id %s".formatted(abUserGroupId)));
    final AbUserGroupData abUserGroupData = mapper.requestToData(request);

    final Result<AbUserGroup> abUserGroupResult = domainService.update(existingEntity, abUserGroupData);
    AbUserGroup entity = abUserGroupResult.orElseThrow();

    entity = repository.save(entity);
    final AbUserGroupResponse response = mapper.entityToResponse(entity);
    //TODO refactor to use domain events
    sendToWebSocket(UPDATED, entity.getAbUserGroupId(), response);

    return response;
  }

  public void deleteById(final int abUserGroupId) {
    final int userCompanyId = getUserCompanyIdOrThrow();
    final AbUserGroup abUserGroup = repository.findByAbUserGroupIdAndUserCompanyUserCompanyId(abUserGroupId, userCompanyId)
        .orElseThrow(() -> new ValidationException("ab_user_group_id", "Could not find id %s".formatted(abUserGroupId)));
    abUserGroup.setDeleted(true);
    repository.save(abUserGroup);
    sendToWebSocket(DELETED, abUserGroupId);
  }


  private void sendToWebSocket(final String action, final int abUserGroupId, final AbUserGroupResponse... data) {
    final int userCompanyId = getUserCompanyIdOrThrow();
    final Map<String, Object> message = buildMessage(action, abUserGroupId, data);
    final Map<String, Object> meta = Map.of("user_company_id", userCompanyId);

    final WebSocketPublishDto publishDto = WebSocketPublishDto.builder()
        .channel(AB_USER_GROUPS)
        .message(message)
        .meta(meta)
        .build();

    webSocketService.sendToWebSocket(publishDto);
  }

  private Map<String, Object> buildMessage(final String action, final int abUserGroupId, final AbUserGroupResponse... data) {
    final Map<String, Object> message = new HashMap<>();
    message.put(ACTION, action);
    message.put("ab_user_group_id", abUserGroupId);

    if (data.length > 0) {
      message.put(DATA, data[0]);
    }
    return message;
  }

}
