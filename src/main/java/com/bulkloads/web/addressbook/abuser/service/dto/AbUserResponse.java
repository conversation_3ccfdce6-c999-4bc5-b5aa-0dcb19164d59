package com.bulkloads.web.addressbook.abuser.service.dto;

import java.time.Instant;
import java.util.List;
import com.bulkloads.common.jackson.CsvSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
public class AbUserResponse {

  private Integer abUserId;
  private String externalAbUserId;
  private Integer blUserId;
  private Integer abCompanyId;
  private String externalAbCompanyId;
  private String companyName;
  private String firstName;
  private String lastName;

  @JsonProperty("phone_1")
  private String phone1;
  private String email;
  private String preferredContactMethod;
  private String abUserNotes;
  private String location;

  @JsonSerialize(using = CsvSerializer.class)
  private List<Integer> userTypeIds;
  @JsonSerialize(using = CsvSerializer.class)
  private List<String> userTypes;

  private Boolean active;
  private Instant badEmailDate;
  private String badEmailReason;

  @JsonSerialize(using = CsvSerializer.class)
  private List<Integer> abUserRoleIds;
  @JsonSerialize(using = CsvSerializer.class)
  private List<String> abUserRoles;

  private Instant modifiedDate;
  private Boolean deleted;

  private Integer lastTrailerUserCompanyEquipmentId;
  private Integer lastTruckUserCompanyEquipmentId;
}
