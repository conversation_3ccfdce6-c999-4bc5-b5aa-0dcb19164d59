package com.bulkloads.web.addressbook.abcompany.repository.template;

public class GetAbCompaniesAutoQueryTemplate {

  public static final String GET_AB_COMPANIES_AUTO_QUERY_TEMPLATE = """
      select
          ab_company_id,
          company_name,
          city,
          state,
          location,
          appt_required,
          company_notes,
          ins_liab_exp_date,
          ins_cargo_exp_date,
          ins_work_exp_date,
          user_company_id -- the owner company of the entry
      from
        ab_companies
      where
        (
          <% params.put("userCompanyId", userCompanyId) %>
          user_company_id = :userCompanyId
          <% if (paramExistsAdd("defaultBillToCompanyId")) { %>
            or user_company_id = :defaultBillToCompanyId
          <% } %>
        )
        
        and deleted = 0
        and active = 1

        <% if (paramExists("term")) {
            var wildTerms = term.split("\\s+")
            for (int i = 0; i < wildTerms.length; i++) {
                var part = "wildTerms_"+i
                params.put(part, "%"+wildTerms[i]+"%")
        %>
            and company_name like :<% print(part) %>
        <% } %>
      <% } %>

      <% if (paramExists("userTypeIds")) { %>
        AND (
          <% for (int i = 0; i < userTypeIds.size(); i++) {
              var part = "userTypeId_" + i
              params.put(part, userTypeIds.get(i))
          %>
              find_in_set(:<% print(part) %>, user_type_ids)
              <% if (i != userTypeIds.size() - 1) { %> OR <% } %>
          <% } %>
        )
      <% } %>

      order by company_name

      <% if (paramExistsAdd("limit")) { %>
          LIMIT
          <% if (paramExistsAdd("skip")) { %>
          :skip,
          <% } %>
          :limit
      <% } %>
      """;


}
