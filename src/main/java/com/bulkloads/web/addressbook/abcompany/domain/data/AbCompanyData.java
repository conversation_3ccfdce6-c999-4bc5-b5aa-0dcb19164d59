package com.bulkloads.web.addressbook.abcompany.domain.data;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.file.domain.entity.File;
import lombok.Data;

@Data
public class AbCompanyData {

  private Optional<Boolean> active;
  private Optional<String> address;
  private Optional<Boolean> apptRequired;
  private Optional<Integer> censusNum;
  private Optional<String> city;
  private Optional<String> companyCode;
  private Optional<String> companyEmail;
  private Optional<String> companyName;
  private Optional<String> companyNotes;
  private Optional<String> companyPhone;
  private Optional<String> country;
  private Optional<String> directions;
  private Optional<String> externalAbCompanyId;
  private Optional<String> externalAbCompanyType;
  private Optional<Integer> facilityId;
  private Optional<Integer> insCargoAmount;
  private Optional<String> insCargoCompany;
  private Optional<String> insCargoContact;
  private Optional<LocalDate> insCargoExpDate;
  private Optional<String> insCargoNotes;
  private Optional<String> insCargoPhone;
  private Optional<String> insCargoPolicy;
  private Optional<Boolean> insCargoSameAsLiab;
  private Optional<Integer> insLiabAmount;
  private Optional<String> insLiabCompany;
  private Optional<String> insLiabContact;
  private Optional<LocalDate> insLiabExpDate;
  private Optional<String> insLiabNotes;
  private Optional<String> insLiabPhone;
  private Optional<String> insLiabPolicy;
  private Optional<String> insuranceInfo;
  private Optional<Integer> insWorkAmount;
  private Optional<String> insWorkCompany;
  private Optional<String> insWorkContact;
  private Optional<LocalDate> insWorkExpDate;
  private Optional<String> insWorkNotes;
  private Optional<String> insWorkPhone;
  private Optional<String> insWorkPolicy;
  private Optional<Boolean> insWorkSameAsLiab;
  private Optional<Double> latitude;
  private Optional<String> location;
  private Optional<Double> longitude;
  private Optional<String> mailingAddress;
  private Optional<String> mailingLocation;
  private Optional<String> mailingCity;
  private Optional<String> mailingState;
  private Optional<String> mailingZip;
  private Optional<String> mailingCountry;
  private Optional<Boolean> mailingSameAsPhysical;
  private Optional<String> mcNum;
  private Optional<Boolean> mcpMonitored;
  private Optional<String> privateNotes;
  private Optional<String> receivingHours;
  private Optional<String> riskAssessmentOverall;
  private Optional<String> state;
  private Optional<ZoneId> timezone;
  private Optional<String> zip;
  private Optional<List<Integer>> userTypeIds;

  private Optional<List<File>> files;

  private boolean validateLocation = true;
}
