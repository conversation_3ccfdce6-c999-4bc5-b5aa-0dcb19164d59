package com.bulkloads.web.addressbook.abcompany.service.dto.transformers;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyAutoResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AbCompanyAutoResponseTransformer implements TupleTransformer<AbCompanyAutoResponse> {

  @Override
  public AbCompanyAutoResponse transformTuple(Object[] columns, String[] aliases) {
    return toResponse(columns, aliases);
  }

  static AbCompanyAutoResponse toResponse(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    AbCompanyAutoResponse response = new AbCompanyAutoResponse();
    response.setUserCompanyId(parts.asInteger("user_company_id"));
    response.setAbCompanyId(parts.asInteger("ab_company_id"));
    response.setCompanyName(parts.asString("company_name"));
    response.setCity(parts.asString("city"));
    response.setState(parts.asString("state"));
    response.setLocation(parts.asString("location"));
    response.setApptRequired(parts.asBoolean("appt_required"));
    ;
    response.setCompanyNotes(parts.asString("company_notes"));
    response.setInsLiabExpDate(parts.asInstant("ins_liab_exp_date"));
    response.setInsCargoExpDate(parts.asInstant("ins_cargo_exp_date"));
    response.setInsWorkExpDate(parts.asInstant("ins_work_exp_date"));
    return response;
  }

}
