package com.bulkloads.web.addressbook.abcompany.service.dto.transformers;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyFinderResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AbCompanyFinderResponseTransformer implements TupleTransformer<AbCompanyFinderResponse> {

  @Override
  public AbCompanyFinderResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return toResponse(parts);
  }

  static AbCompanyFinderResponse toResponse(QueryParts parts) {
    AbCompanyFinderResponse response = new AbCompanyFinderResponse();
    response.setCompanyName(parts.asString("company_name"));
    response.setUserTypeIds(parts.asIntegerListFromCsv("user_type_ids"));
    response.setUserTypes(parts.asStringListFromCsv("user_types"));
    response.setFacilityId(parts.asInteger("facility_id"));
    response.setCensusNum(parts.asInteger("census_num"));
    response.setMcNum(parts.asString("mc_num"));
    response.setCompanyPhone(parts.asString("company_phone"));
    response.setCompanyEmail(parts.asString("company_email"));
    response.setAddress(parts.asString("address"));
    response.setLocation(parts.asString("location"));
    response.setCity(parts.asString("city"));
    response.setState(parts.asString("state"));
    response.setZip(parts.asString("zip"));
    response.setCountry(parts.asString("country"));
    response.setLongitude(parts.asDouble("longitude"));
    response.setLatitude(parts.asDouble("latitude"));
    response.setMailingAddress(parts.asString("mailing_address"));
    response.setMailingLocation(parts.asString("mailing_location"));
    response.setMailingCity(parts.asString("mailing_city"));
    response.setMailingState(parts.asString("mailing_state"));
    response.setMailingZip(parts.asString("mailing_zip"));
    response.setMailingCountry(parts.asString("mailing_country"));
    return response;
  }

}
