package com.bulkloads.web.addressbook.abcompany.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.web.addressbook.abcompany.service.AbCompanyService;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyListResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyRequest;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.Value;

@RestController
@RequestMapping("/rest/address_book/companies")
@Tag(name = "Address Book")
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class AbCompanyController {

  private final AbCompanyService abCompanyService;

  @Operation(summary = "Create address book company")
  @PostMapping
  public AbCompanyApiResponse create(@Valid @RequestBody AbCompanyRequest abCompanyRequest) {

    AbCompanyResponse abCompanyResponse = abCompanyService.create(abCompanyRequest);

    return AbCompanyApiResponse.builder()
        .key(abCompanyResponse.getAbCompanyId())
        .abCompanyId(abCompanyResponse.getAbCompanyId())
        .data(abCompanyResponse)
        .message("The company was added to your address book")
        .build();
  }

  @Operation(summary = "Update address book company")
  @PutMapping("{ab_company_id}")
  public AbCompanyApiResponse update(
      @Positive(message = "Ab company id should be positive")
      @PathVariable("ab_company_id") int abCompanyId,
      @Valid @RequestBody AbCompanyRequest abCompanyRequest) {
    AbCompanyResponse abCompanyResponse = abCompanyService.update(abCompanyId, abCompanyRequest);

    return AbCompanyApiResponse.builder()
        .key(abCompanyResponse.getAbCompanyId())
        .abCompanyId(abCompanyResponse.getAbCompanyId())
        .data(abCompanyResponse)
        .message("Company details updated")
        .build();
  }

  @Operation(summary = "Delete address book company")
  @DeleteMapping("{ab_company_id}")
  public AbCompanyApiResponse remove(
      @Positive(message = "Ab company id should be positive")
      @PathVariable("ab_company_id") int abCompanyId) {
    abCompanyService.remove(abCompanyId);

    return AbCompanyApiResponse.builder()
        .message("Company deleted")
        .build();
  }

  @Value
  @Builder
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class AbCompanyApiResponse {

    @Schema(name = "message")
    String message;

    @Schema(name = "ab_company_id")
    Integer abCompanyId;

    @Schema(name = "key")
    Integer key;

    @Schema(name = "data")
    AbCompanyListResponse data;
  }
}
