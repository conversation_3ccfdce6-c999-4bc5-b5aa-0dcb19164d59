package com.bulkloads.web.contracts.repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.contracts.domain.data.ContractSearch;
import com.bulkloads.web.contracts.domain.entity.Contract;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import jakarta.persistence.QueryHint;

@Repository
public interface ContractRepository extends JpaRepository<Contract, Integer> {

  Optional<Contract> findByContractIdAndUserCompanyUserCompanyId(final int contractId,
                                                                 final int userCompanyId);

  @QueryHints(@QueryHint(name = "org.hibernate.flushMode", value = "ALWAYS"))
  @Query(value = """
        select sum(delivered_weight) as delivered_weight
        from (
            select ifnull(sum(la.unload_weight), 0) as delivered_weight
            from loads l
            inner join load_assignments la using (load_id)
            where l.deleted = 0
            and la.deleted = 0
            and la.reroute_contract_id is null
            and (la.assignment_status = 'delivered' or la.assignment_status = 'completed')
            and (unload_weight is not null)
            and l.contract_id = :contractId
      UNION ALL
            select ifnull(sum(la.unload_weight), 0) as delivered_weight
            from load_assignments la
            where la.deleted = 0
            and (la.assignment_status = 'delivered' or la.assignment_status = 'completed')
            and (unload_weight is not null)
            and la.reroute_contract_id = :contractId) delivered_weight;
      """, nativeQuery = true)
  BigDecimal calculateDeliveredWeight(@Param("contractId") final Integer contractId);

  @Query("""
      select c
      from Contract c
      where c.userCompany.userCompanyId = :userCompanyId
      and (:#{#searchParams.userIds.isEmpty()} = true or c.user.userId in :#{#searchParams.userIds})
      and (:#{#searchParams.contractStatus} is null or c.contractStatus =
          (case :#{#searchParams.contractStatus} when 'open' then 'Open' when 'closed' then 'Closed' end))
      and (:#{#searchParams.contractIds.isEmpty()} = true or c.contractId in :#{#searchParams.contractIds})
      and (:#{#searchParams.buySell} is null or c.buySell = :#{#searchParams.buySell})
      and (:#{#searchParams.lastModifiedDate} is null or c.modifiedDate >= :#{#searchParams.lastModifiedDate})
      order by c.addedDate asc
      """)
  List<Contract> findContracts(
      @Param("userCompanyId") final int userCompanyId,
      @Param("searchParams") final ContractSearch searchParams);

  Optional<Contract> findByExternalContractIdAndUserCompanyUserCompanyIdAndDeletedFalse(
      String externalContractId,
      int userCompanyId
  );

  List<Contract> findAllByExternalContractIdInAndUserCompanyUserCompanyIdAndDeletedFalse(
      List<String> externalContractIds,
      int userCompanyId
  );

}
