package com.bulkloads.web.contracts.event;

import java.util.Optional;
import com.bulkloads.web.common.event.DomainEvent;
import com.bulkloads.web.contracts.service.dto.ContractResponse;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class ContractEvent implements DomainEvent {

  private int contractId;
  private String action;
  private ContractResponse response;

  public Optional<ContractResponse> getResponse() {
    return Optional.ofNullable(response);
  }
}
