package com.bulkloads.web.contracts.api;

import java.util.List;
import com.bulkloads.web.contracts.service.ContractService;
import com.bulkloads.web.contracts.service.dto.ContractResponse;
import com.bulkloads.web.contracts.service.dto.ContractSearchRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/contracts")
@Tag(name = "Contracts")
@RequiredArgsConstructor
@Validated
public class ContractQueryController {

  private final ContractService contractService;

  @Operation(summary = "Get contract details")
  @GetMapping("/{contract_id}")
  public ContractResponse getContractById(
      @Parameter(description = "Contract id")
      @PathVariable("contract_id")
      @Positive(message = "Contract id must be positive") final int contractId) {

    return contractService.findContractById(contractId);
  }

  @Operation(summary = "Get all contract details")
  @GetMapping
  public List<ContractResponse> getContracts(
      @ModelAttribute @Valid final ContractSearchRequest requestParams) {

    return contractService.searchContracts(requestParams);
  }
}
