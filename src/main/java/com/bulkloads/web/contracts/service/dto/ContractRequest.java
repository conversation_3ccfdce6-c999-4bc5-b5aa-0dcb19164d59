package com.bulkloads.web.contracts.service.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class ContractRequest {
  Optional<String> externalContractId;
  Optional<String> buySell;
  Optional<String> contractNumber;
  Optional<BigDecimal> rate;
  Optional<String> rateType;
  Optional<BigDecimal> freightRate;
  Optional<String> freightRateType;
  Optional<BigDecimal> freightRatePerMile;
  Optional<Integer> numberOfLoads;
  Optional<BigDecimal> quantity;
  Optional<BigDecimal> weight;
  Optional<Integer> remainingNumberOfLoads;
  Optional<BigDecimal> remainingQuantity;
  Optional<BigDecimal> remainingWeight;
  Optional<Integer> commodityId;
  Optional<String> commodity;
  Optional<LocalDate> shipFrom;
  Optional<LocalDate> shipTo;
  Optional<String> contactInfo;
  Optional<String> notes;
  Optional<Integer> pickupAbCompanyId;
  Optional<Integer> dropAbCompanyId;
}
