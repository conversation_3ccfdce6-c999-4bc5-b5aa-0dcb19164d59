package com.bulkloads.web.eld.domain.entity;

import java.time.Instant;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "eld_user_company_equipments")
public class EldUserCompanyEquipment {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Column(name = "eld_provider_id")
  private String eldProviderId = "";

  @Column(name = "external_id")
  private String externalId = "";

  @Column(name = "status")
  private String status = "";

  @Column(name = "license_plate_number")
  private String licensePlateNumber = "";

  @Column(name = "make")
  private String make = "";

  @Column(name = "model")
  private String model = "";

  @Column(name = "notes")
  private String notes = "";

  @Column(name = "vin")
  private String vin = "";

  @Column(name = "model_year")
  private String modelYear = "";

  @Column(name = "updated_at")
  private Instant updatedAt;

  @Column(name = "sync_user_id")
  private Integer syncUserId;

  @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "user_company_equipment_id")
  private UserCompanyEquipment userCompanyEquipment;

  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Column(name = "is_active")
  private Boolean active;

}
