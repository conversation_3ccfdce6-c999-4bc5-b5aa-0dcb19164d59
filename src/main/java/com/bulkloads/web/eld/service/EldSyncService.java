package com.bulkloads.web.eld.service;

import java.util.Collection;
import java.util.List;
import com.bulkloads.security.ImpersonationService;
import com.bulkloads.security.oauth.BulkloadsJdbcOAuth2AuthorizedClientService;
import com.bulkloads.web.infra.eld.EldProviderFacade;
import com.bulkloads.web.infra.eld.dto.EldDriverDto;
import com.bulkloads.web.infra.eld.dto.EldDriverHosDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleLocationDto;
import com.bulkloads.web.setting.service.SettingService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class EldSyncService {

  private final EldProviderFacade eldProviderFacade;
  private final EldUserInfoService eldUserInfoService;
  private final EldUserCompanyEquipmentService eldUserCompanyEquipmentService;
  private final BulkloadsJdbcOAuth2AuthorizedClientService authorizedClientService;
  private final ImpersonationService impersonationService;
  private final SettingService settingService;

  public void synchronizeDrivers() {
    final String providerId = settingService.getEldProviderId();
    final List<EldDriverDto> dtos = eldProviderFacade.fetchDrivers(providerId);

    if (log.isDebugEnabled()) {
      log.debug("Fetched {} drivers: {}", providerId, dtos);
    }

    eldUserInfoService.syncUsers(providerId, dtos);
  }

  public void synchronizeUserCompanyEquipments() {
    final String providerId = settingService.getEldProviderId();
    final List<EldVehicleDto> dtos = eldProviderFacade.fetchVehicles(providerId);

    if (log.isDebugEnabled()) {
      log.debug("Fetched {} vehicles: {}", providerId, dtos);
    }

    eldUserCompanyEquipmentService.syncUserCompanyEquipments(providerId, dtos);
  }

  public void synchronizeUserCompanyEquipmentLocations() {

    List<OAuth2AuthorizedClient> allEld = authorizedClientService.findAllEld();

    if (allEld.isEmpty()) {
      return;
    }

    final List<EldVehicleLocationDto> allDtos = allEld
        .parallelStream()
        .map(OAuth2AuthorizedClient::getPrincipalName)
        .mapToInt(Integer::parseInt)
        .mapToObj(this::fetchVehicleLocationsForUser)
        .filter(dtos -> !dtos.isEmpty())
        .flatMap(Collection::stream)
        .toList();

    eldUserCompanyEquipmentService.syncUserCompanyEquipmentsLocations(allDtos);

  }

  public void synchronizeDriverHos() {

    List<OAuth2AuthorizedClient> allEld = authorizedClientService.findAllEld();

    if (allEld.isEmpty()) {
      return;
    }

    final List<EldDriverHosDto> allDtos = allEld
        .parallelStream()
        .map(OAuth2AuthorizedClient::getPrincipalName)
        .mapToInt(Integer::parseInt)
        .mapToObj(this::fetchDriverHosForUser)
        .filter(dtos -> !dtos.isEmpty())
        .flatMap(Collection::stream)
        .toList();

    eldUserInfoService.syncDriverHos(allDtos);
  }

  private List<EldVehicleLocationDto> fetchVehicleLocationsForUser(final int userCompanyId) {
    impersonationService.impersonateByUserCompanyId(userCompanyId);
    final String providerId = settingService.getEldProviderId();
    final List<String> externalIds = eldUserCompanyEquipmentService.findMatchedExternalIdsByProviderIdAndUserCompanyId();

    if (externalIds.isEmpty()) {
      if (log.isDebugEnabled()) {
        log.debug("No matched eld user company equipment external ids found for userCompanyId: {}", userCompanyId);
      }
      return List.of();
    }

    final List<EldVehicleLocationDto> dtos = eldProviderFacade.fetchVehicleLocations(providerId, externalIds);

    if (log.isDebugEnabled()) {
      log.debug("Fetched {} vehicle locations for provider: {}", dtos.size(), providerId);
    }
    return dtos;
  }

  private List<EldDriverHosDto> fetchDriverHosForUser(final int userCompanyId) {
    impersonationService.impersonateByUserCompanyId(userCompanyId);
    final String providerId = settingService.getEldProviderId();
    final List<String> externalIds = eldUserInfoService.findMatchedExternalIdsByProviderIdAndUserCompanyId();

    if (externalIds.isEmpty()) {
      if (log.isDebugEnabled()) {
        log.debug("No matched eld user external ids found for userCompanyId: {}", userCompanyId);
      }
      return List.of();
    }

    final List<EldDriverHosDto> dtos = eldProviderFacade.fetchDriverHosStatuses(providerId, externalIds);

    if (log.isDebugEnabled()) {
      log.debug("Fetched {} driver hos for provider: {}", dtos.size(), providerId);
    }
    return dtos;
  }

}
