package com.bulkloads.web.eld.service;

import static org.springframework.util.StringUtils.hasLength;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.eld.repository.EldUserCompanyEquipmentRepository;
import com.bulkloads.web.eld.repository.EldUserInfoRepository;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.usercompany.domain.entity.UserCompanySettings;
import com.bulkloads.web.usercompany.service.UserCompanyService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.security.oauth2.client.JdbcOAuth2AuthorizedClientService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class EldAuthorizationService {

  private final UserCompanyService userCompanyService;
  private final JdbcOAuth2AuthorizedClientService jdbcOAuth2AuthorizedClientService;
  private final EldUserInfoRepository eldUserInfoRepository;
  private final EldUserCompanyEquipmentRepository eldUserCompanyEquipmentRepository;

  public void deleteAuthorization() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final UserCompany userCompany = userCompanyService.getUserCompany(userCompanyId);
    final UserCompanySettings userCompanySettings = userCompany.getUserCompanySettings();
    final String eldProviderId = userCompanySettings.getEldProviderId();
    if (hasLength(eldProviderId)) {
      jdbcOAuth2AuthorizedClientService.removeAuthorizedClient(eldProviderId, String.valueOf(userCompanyId));
      log.info("Removed authorization for userCompanyId {} and provider {}", userCompanyId, eldProviderId);

      eldUserInfoRepository.deactivateByEldProviderIdAndUserCompanyId(eldProviderId, userCompanyId);
      log.info("Deactivated all eld user info records for userCompanyId {} and provider {}", userCompanyId, eldProviderId);

      eldUserCompanyEquipmentRepository.deactivateByEldProviderIdAndUserCompanyId(eldProviderId, userCompanyId);
      log.info("Deactivated all eld user company equipment records for userCompanyId {} and provider {}", userCompanyId, eldProviderId);

    }
    userCompanySettings.setEldProviderId(Strings.EMPTY);
  }
}
