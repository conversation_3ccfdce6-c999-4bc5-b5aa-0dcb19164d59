package com.bulkloads.web.eld.api.openapi;

import java.util.List;
import com.bulkloads.web.eld.api.dto.EldUserCompanyEquipmentResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "ELD UserCompanyEquipment API")
public interface EldUserCompanyEquipmentQueryApiDoc {

  @Operation(summary = "Find ELD User Company Equipment Info by Id")
  EldUserCompanyEquipmentResponse findOne(
      @Parameter(name = "eld_user_company_equipment_id", description = "The id of the ELD User Info", required = true)
      final int eldUserInfoId);

  @Operation(summary = "Find all ELD User Company Equipment")
  List<EldUserCompanyEquipmentResponse> findAllByProviderIdAndUserCompanyId();
}
