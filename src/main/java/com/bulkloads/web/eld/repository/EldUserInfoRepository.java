package com.bulkloads.web.eld.repository;

import java.util.List;
import com.bulkloads.web.eld.domain.entity.EldUserInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EldUserInfoRepository extends JpaRepository<EldUserInfo, Integer> {

  List<EldUserInfo> findByEldProviderIdAndUserCompanyIdAndActiveIsTrue(final String eldProviderId, final int userCompanyId);

  List<EldUserInfo> findByEldProviderIdAndUserCompanyId(final String eldProviderId, final int userCompanyId);

  List<EldUserInfo> findByEldProviderIdAndUserCompanyIdAndActiveIsTrueAndUserIdIn(final String eldProviderId,
                                                                                  final int userCompanyId,
                                                                                  final List<Integer> userIds);

  List<EldUserInfo> findByEldProviderIdAndUserCompanyIdAndUserIdNotNull(final String providerId, final int userCompanyId);

  List<EldUserInfo> findAllByUserIdNotNull();

  @Modifying
  @Query("UPDATE EldUserInfo e SET e.active = false WHERE e.eldProviderId = :eldProviderId AND e.userCompanyId = :userCompanyId")
  void deactivateByEldProviderIdAndUserCompanyId(@Param("eldProviderId") final String eldProviderId,
                                                @Param("userCompanyId") final int userCompanyId);

}
