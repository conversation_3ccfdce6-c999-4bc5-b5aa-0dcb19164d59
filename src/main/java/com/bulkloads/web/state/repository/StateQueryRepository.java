package com.bulkloads.web.state.repository;


import static com.bulkloads.web.state.repository.template.GetStateQueryTemplate.GET_STATES_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.state.service.dto.StateResponse;
import com.bulkloads.web.state.service.dto.transformer.StateResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class StateQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final StateResponseTransformer stateResponseTransformer;

  public List<StateResponse> getStates(
      final String term
  ) {
    Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("term", term);

    return jpaNativeQueryService.query(
        GET_STATES_QUERY_TEMPLATE,
        queryParams,
        stateResponseTransformer
    );
  }


}
