package com.bulkloads.web.washout.repository;

import java.util.List;
import com.bulkloads.web.washout.domain.entity.WashoutComment;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface WashoutCommentRepository extends
    ListCrudRepository<WashoutComment, Integer>,
    WashoutCommentQueryRepository {

  List<WashoutComment> findAllByWashoutWashoutId(int washoutId);

}
