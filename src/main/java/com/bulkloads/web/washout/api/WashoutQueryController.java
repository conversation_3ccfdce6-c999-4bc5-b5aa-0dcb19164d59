package com.bulkloads.web.washout.api;

import java.util.List;
import com.bulkloads.web.washout.service.WashoutService;
import com.bulkloads.web.washout.service.dto.WashoutListResponse;
import com.bulkloads.web.washout.service.dto.WashoutResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/washouts", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Washouts")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class WashoutQueryController {

  private final WashoutService washoutService;

  @GetMapping
  public List<WashoutListResponse> getWashouts(
      @RequestParam(value = "term", required = false) String term,
      @RequestParam(value = "lat", required = false) Double latitude,
      @RequestParam(value = "long", required = false) Double longitude,
      @RequestParam(value = "state", required = false) String state,
      @RequestParam(value = "radius", required = false) Double radius,
      @RequestParam(value = "skip", defaultValue = "0") Integer skip,
      @RequestParam(value = "limit", required = false) Integer limit
  ) {
    return washoutService.getWashouts(term, latitude, longitude, radius, state, skip, limit);
  }

  @GetMapping("{washout_id}")
  public WashoutResponse getWashoutById(@PathVariable(value = "washout_id") Integer washoutId) {
    return washoutService.getWashout(washoutId);

  }

}
