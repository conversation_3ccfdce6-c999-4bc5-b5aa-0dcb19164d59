package com.bulkloads.web.washout.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.washout.service.WashoutCommentService;
import com.bulkloads.web.washout.service.dto.WashoutCommentRequest;
import com.bulkloads.web.washout.service.dto.WashoutCommentResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest/washouts")
@Tag(name = "Washouts")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class WashoutCommentController {

  private final WashoutCommentService washoutCommentService;

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @PostMapping("{washout_id}/comments")
  public ApiResponse<WashoutCommentResponse, Integer> createComment(
      @PathVariable("washout_id") int washoutId, @RequestBody @Valid WashoutCommentRequest dto) {
    WashoutCommentResponse washoutCommentResponse = washoutCommentService.create(washoutId, dto);

    return ApiResponse.<WashoutCommentResponse, Integer>builder()
        .key(washoutCommentResponse.getWashoutCommentId())
        .data(washoutCommentResponse)
        .message("Created successfully")
        .build();
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @PutMapping("{washout_id}/comments/{washout_comment_id}")
  public ApiResponse<WashoutCommentResponse, Integer> updateComment(
      @PathVariable("washout_id") int washoutId,
      @PathVariable("washout_comment_id") int washoutCommentId,
      @RequestBody @Valid WashoutCommentRequest payload) {

    WashoutCommentResponse washoutCommentResponse =
        washoutCommentService.update(washoutId, washoutCommentId, payload);

    return ApiResponse.<WashoutCommentResponse, Integer>builder()
        .key(washoutCommentResponse.getWashoutCommentId())
        .data(washoutCommentResponse)
        .message("Updated successfully")
        .build();
  }

  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @PostMapping("{washout_id}/comments/{washout_comment_id}/approve")
  public void approveComment(
      @PathVariable("washout_id") int washoutId,
      @PathVariable("washout_comment_id") int washoutCommentId) {

    washoutCommentService.approveComment(washoutId, washoutCommentId);
  }

  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @DeleteMapping("{washout_id}/comments/{washout_comment_id}")
  public ApiResponse<WashoutCommentResponse, Integer> removeWashoutComment(
      @PathVariable("washout_id") int washoutId,
      @PathVariable("washout_comment_id") int washoutCommentId
  ) {
    washoutCommentService.remove(washoutId, washoutCommentId);

    return ApiResponse.<WashoutCommentResponse, Integer>builder()
        .message("Removed successfully")
        .build();
  }
}
