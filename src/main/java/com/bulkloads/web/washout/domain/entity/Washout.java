package com.bulkloads.web.washout.domain.entity;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.user.domain.entity.User;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "washouts")
@Getter
@Setter
public class Washout {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "washout_id")
  private Integer washoutId;

  @Size(max = 50, message = "Up to 10 chars")
  @Column(name = "washout_name")
  private String washoutName = "";

  @Size(max = 60, message = "Up to 60 chars")
  @Column(name = "origin_city")
  private String originCity = "";

  @Size(max = 2, message = "Up to 2 chars")
  @Column(name = "origin_state")
  private String originState = "";

  @Size(max = 15, message = "Up to 15 chars")
  @Column(name = "phone_1")
  private String phone1 = "";

  @Size(max = 15, message = "Up to 15 chars")
  @Column(name = "phone_2")
  private String phone2 = "";

  @Size(max = 500, message = "Up to 500 chars")
  @Column(name = "directions")
  private String directions = "";

  @Size(max = 20, message = "Up to 20 chars")
  @Column(name = "cost")
  private String cost = "";

  @Size(max = 150, message = "Up to 150 chars")
  @Column(name = "address")
  private String address = "";

  @Size(max = 1, message = "Should be Y, N or empty")
  @Column(name = "hot_water")
  private String hotWater = "";


  @Column(name = "date_verified")
  private LocalDate dateVerified;

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "day_contact")
  private String dayContact = "";

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "after_hours_contact")
  private String afterHoursContact = "";

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "hours_of_operation")
  private String hoursOfOperation = "";

  @Column(name = "longitude")
  private Double longitude;

  @Column(name = "latitude")
  private Double latitude;

  @Column(name = "posted_date")
  private LocalDate postedDate;

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "notes")
  private String notes = "";

  @Size(max = 3, message = "Up to 3 chars")
  @Column(name = "missing_info")
  private String missingInfo = "";

  @Size(max = 500, message = "Up to 500 chars")
  @Column(name = "public_notes")
  private String publicNotes = "";

  @Column(name = "premium_end_date")
  private LocalDate premiumEndDate;

  @Size(max = 1, message = "Should be Y, N or empty")
  @Column(name = "allow_livestock")
  private String allowLivestock = "0";

  @Size(max = 1, message = "Should be Y, N or empty")
  @Column(name = "allow_tanker")
  private String allowTanker = "";

  @Column(name = "edit_date")
  private Instant editDate;

  @Size(max = 1, message = "Should be Y, N or empty")
  @Column(name = "take_credit_cards")
  private String takeCreditCards = "";

  @Column(name = "location_verified_date")
  private LocalDate locationVerifiedDate;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH,
      CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "edit_by_user_id")
  private User editedBy;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "washout", fetch = FetchType.LAZY)
  private List<WashoutComment> comments = new ArrayList<>();
}