package com.bulkloads.web.washout.domain.data;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Optional;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class WashoutData {

  @NotEmpty
  private String washoutName;

  @NotEmpty
  private String location;

  private Optional<String> phone1;
  private Optional<String> phone2;
  private Optional<String> directions;
  private Optional<String> cost;
  private Optional<String> address;
  private Optional<String> hotWater;
  private Optional<LocalDate> dateVerified;
  private Optional<String> dayContact;
  private Optional<String> afterHoursContact;
  private Optional<String> hoursOfOperation;
  private Optional<Double> latitude;
  private Optional<Double> longitude;
  private Optional<LocalDate> postedDate;
  private Optional<String> missingInfo;
  private Optional<String> publicNotes;
  private Optional<LocalDate> premiumEndDate;
  private Optional<String> allowLivestock;
  private Optional<String> allowTanker;
  private Optional<Instant> editDate;
  private Optional<String> takeCreditCards;
  private Optional<LocalDate> locationVerifiedDate;
}
