package com.bulkloads.web.commoditylisting.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.commoditylisting.service.CommodityListingService;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingRequest;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/commodity_listings")
@Tag(name = "Commodity Listings")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class CommodityListingController {

  private final CommodityListingService commodityListingService;

  @Operation(summary = "Create Commodity Listing")
  @PostMapping
  public ApiResponse<CommodityListingResponse, Integer> createCommodityListing(
      @RequestBody final CommodityListingRequest dto) {
    final CommodityListingResponse response = commodityListingService.create(dto);

    return ApiResponse.<CommodityListingResponse, Integer>builder()
        .key(response.getCommodityListingId())
        .data(response)
        .message("Commodity Listing created")
        .build();
  }

  @Operation(summary = "Update commodity listing")
  @PutMapping("/{commodity_listing_id}")
  public ApiResponse<CommodityListingResponse, Integer> updateCommodityListing(
      @PathVariable("commodity_listing_id") @Positive final int commodityListingId,
      @RequestBody final CommodityListingRequest dto) {
    final CommodityListingResponse response = commodityListingService.update(commodityListingId, dto);
    return ApiResponse.<CommodityListingResponse, Integer>builder()
        .key(response.getCommodityListingId())
        .data(response)
        .message("Commodity Listing updated")
        .build();
  }

  @Operation(summary = "Delete Commodity Listing")
  @DeleteMapping("/{commodity_listing_id}")
  public ApiResponse<CommodityListingResponse, Integer> deleteCommodityListing(
      @PathVariable("commodity_listing_id") @Positive final int commodityListingId) {
    commodityListingService.deleteById(commodityListingId);
    return ApiResponse.<CommodityListingResponse, Integer>builder()
        .key(commodityListingId)
        .message("Commodity Listing deleted")
        .build();
  }
}
