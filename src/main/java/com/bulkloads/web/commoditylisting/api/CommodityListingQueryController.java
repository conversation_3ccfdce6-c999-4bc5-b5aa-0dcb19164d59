package com.bulkloads.web.commoditylisting.api;

import java.util.List;
import com.bulkloads.web.commoditylisting.service.CommodityListingQueryService;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingListResponse;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingResponse;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingsTotalResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/commodity_listings", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Commodity Listings")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class CommodityListingQueryController {

  private final CommodityListingQueryService commodityListingQueryService;

  @GetMapping
  public List<CommodityListingListResponse> getCommodityListings(
      @RequestParam(value = "term", required = false) String term,
      @RequestParam(value = "order", required = false) String order,
      @RequestParam(value = "skip", defaultValue = "0") Integer skip,
      @RequestParam(value = "limit", defaultValue = "500") Integer limit
  ) {
    return commodityListingQueryService.getCommodityListings(term, order, skip, limit);
  }

  @GetMapping("/totals")
  public CommodityListingsTotalResponse getCommodityListingsTotal(
      @RequestParam(value = "term", required = false) String term
  ) {
    return commodityListingQueryService.getCommodityListingsTotal(term);
  }

  @GetMapping("/{commodity_listing_id}")
  public CommodityListingResponse getCommodityListing(
      @PathVariable("commodity_listing_id") Integer commodityListingId
  ) {
    return commodityListingQueryService.getCommodityListing(commodityListingId);
  }
}
