package com.bulkloads.web.activity.repository;

import static com.bulkloads.web.activity.repository.template.GetActivitiesQueryTemplate.GET_ACTIVITIES_QUERY_TEMPLATE;
import static com.bulkloads.web.activity.repository.template.GetActivityTypesQueryTemplate.GET_ACTIVITY_TYPES_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.activity.service.dto.ActivityListResponse;
import com.bulkloads.web.activity.service.dto.ActivityTypeListResponse;
import com.bulkloads.web.activity.service.dto.transformers.ActivityListResponseTransformer;
import com.bulkloads.web.activity.service.dto.transformers.ActivityTypeListResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class ActivityQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final ActivityListResponseTransformer activityListResponseTransformer;
  private final ActivityTypeListResponseTransformer activityTypeListResponseTransformer;

  public List<ActivityTypeListResponse> getActivityTypes(Integer activityTypeId) {

    Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("activityTypeId", activityTypeId);

    return jpaNativeQueryService.query(
        GET_ACTIVITY_TYPES_QUERY_TEMPLATE,
        queryParams,
        activityTypeListResponseTransformer
    );
  }

  public List<ActivityListResponse> getActivities(
      final int userCompanyId,
      final String userIds,
      final Integer activityTypeId,
      final Integer pastDays,
      final Integer skip,
      final Integer limit) {
    final Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("userCompanyId", userCompanyId);
    queryParams.put("userIds", userIds);
    queryParams.put("activityTypeId", activityTypeId);
    queryParams.put("pastDays", pastDays);
    queryParams.put("skip", skip);
    queryParams.put("limit", limit);

    return jpaNativeQueryService.query(GET_ACTIVITIES_QUERY_TEMPLATE, queryParams, activityListResponseTransformer);

  }

}
