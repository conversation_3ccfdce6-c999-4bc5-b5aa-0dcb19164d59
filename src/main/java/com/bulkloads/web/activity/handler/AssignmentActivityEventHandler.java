package com.bulkloads.web.activity.handler;

import static com.bulkloads.config.AppConstants.ActivityType.CANCEL_LOAD_ASSIGNMENT;
import static com.bulkloads.config.AppConstants.ActivityType.CREATE_LOAD_ASSIGNMENT;
import static com.bulkloads.config.AppConstants.ActivityType.UPDATE_LOAD_ASSIGNMENT;
import static com.bulkloads.config.AppConstants.ActivityType.UPDATE_LOAD_BOOKING;
import static com.bulkloads.config.AppConstants.ReroutePickupDrop.DROP;
import static com.bulkloads.config.AppConstants.ReroutePickupDrop.PICKUP;
import static com.bulkloads.config.AppConstants.SharedWithResponse.PENDING;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.ABOVE_REFERENCE_ASSIGNMENT_BOOKING;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.BELOW_REFERENCE_ASSIGNMENT_BOOKING;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.THE_REFERENCE_ASSIGNMENT_BOOKING;
import static com.bulkloads.web.activity.domain.entity.ActivityType.ActivityTypes.LOAD_ASSIGNMENTS_ACTIVITY_ID;
import static com.bulkloads.web.activity.domain.entity.ActivityType.ActivityTypes.LOAD_BOOKINGS_ACTIVITY_ID;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.activity.service.ActivityService;
import com.bulkloads.web.activity.service.dto.ActivityRequest;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.event.AssignmentBookingEvent;
import com.bulkloads.web.assignment.event.AssignmentBookingStatusChangedEvent;
import com.bulkloads.web.assignment.event.AssignmentCancelledEvent;
import com.bulkloads.web.assignment.event.AssignmentCreatedEvent;
import com.bulkloads.web.assignment.event.AssignmentSentEvent;
import com.bulkloads.web.assignment.event.AssignmentUpdatedEvent;
import com.bulkloads.web.assignment.event.BookingUpdatedEvent;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AssignmentActivityEventHandler {

  private final ActivityService activityService;
  private final AssignmentRepository assignmentRepository;
  private final UserService userService;

  @TransactionalEventListener(classes = AssignmentCreatedEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentCreatedActivity(final AssignmentCreatedEvent event) {
    final List<Integer> assignmentIds = event.getLoadAssignmentIds();
    final Assignment firstAssignment = assignmentRepository.getReferenceById(assignmentIds.get(0));
    final Integer loadId = firstAssignment.getLoad().getLoadId();
    final String activityContent = getAssignmentCreatedActivityMessage(firstAssignment, assignmentIds);
    final ActivityRequest activityRequest = buildActivityRequest(firstAssignment.getUser().getUserId(), assignmentIds, loadId, activityContent,
        LOAD_ASSIGNMENTS_ACTIVITY_ID.get(), CREATE_LOAD_ASSIGNMENT);
    activityService.createActivities(List.of(activityRequest));
  }

  @TransactionalEventListener(classes = {
      AssignmentUpdatedEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentUpdatedActivity(final AssignmentBookingEvent event) {
    final List<Integer> assignmentIds = event.getLoadAssignmentIds();
    final Assignment firstAssignment = assignmentRepository.getReferenceById(assignmentIds.get(0));
    final Integer loadId = firstAssignment.getLoad().getLoadId();
    final String activityContent = getAssignmentBookingActivityMessage(firstAssignment, assignmentIds);

    // Get the logged in user who is performing the update
    final int loggedInUserId = userService.getLoggedInUser().getUserId();

    // Create activity for the logged in user
    final ActivityRequest activityRequest =
        buildActivityRequest(loggedInUserId, assignmentIds, loadId, activityContent,
            LOAD_ASSIGNMENTS_ACTIVITY_ID.get(), UPDATE_LOAD_ASSIGNMENT);

    List<ActivityRequest> activityRequests = new ArrayList<>();
    activityRequests.add(activityRequest);

    // If logged in user is different from assignment owner, create activity for assignment owner
    if (loggedInUserId != firstAssignment.getUser().getUserId()) {
      final User loggedInUser = userService.getLoggedInUser();
      final String userFullName = loggedInUser.getFirstName() + " " + loggedInUser.getLastName();

      final ActivityRequest ownerActivityRequest = buildActivityRequest(
          firstAssignment.getUser().getUserId(),
          assignmentIds,
          loadId,
          activityContent + " by " + userFullName,
          LOAD_ASSIGNMENTS_ACTIVITY_ID.get(),
          UPDATE_LOAD_ASSIGNMENT);

      activityRequests.add(ownerActivityRequest);
    }

    // Create activity for toUser (carrier) if toLoad exists
    if (nonNull(firstAssignment.getToLoad()) && nonNull(firstAssignment.getToUser())) {
      final ActivityRequest carrierActivityRequest = buildActivityRequest(
          firstAssignment.getToUser().getUserId(),
          assignmentIds,
          loadId,
          activityContent + " by " + firstAssignment.getUserCompany().getCompanyName(),
          LOAD_BOOKINGS_ACTIVITY_ID.get(),
          UPDATE_LOAD_ASSIGNMENT);

      activityRequests.add(carrierActivityRequest);
    }

    activityService.createActivities(activityRequests);
  }

  @TransactionalEventListener(classes = AssignmentCancelledEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentCancelledActivity(final AssignmentCancelledEvent event) {
    final List<Integer> assignmentIds = event.getLoadAssignmentIds();
    final Assignment firstAssignment = assignmentRepository.getReferenceById(assignmentIds.get(0));
    final Integer loadId = firstAssignment.getLoad().getLoadId();

    final List<ActivityRequest> activityRequests = new ArrayList<>();

    final String activityShipperContent = getAssignmentCanceledActivityMessage(firstAssignment, true);
    final ActivityRequest activityShipperRequest = buildActivityRequest(userService.getLoggedInUser().getUserId(), assignmentIds, loadId,
        activityShipperContent,
        LOAD_ASSIGNMENTS_ACTIVITY_ID.get(), CANCEL_LOAD_ASSIGNMENT);
    activityRequests.add(activityShipperRequest);

    if (nonNull(firstAssignment.getToUser())) {
      final String activityCarrierContent = getAssignmentCanceledActivityMessage(firstAssignment, false);
      final ActivityRequest activityCarrierRequest = buildActivityRequest(
          firstAssignment.getToUser().getUserId(),
          assignmentIds, loadId,
          activityCarrierContent,
          LOAD_BOOKINGS_ACTIVITY_ID.get(),
          CANCEL_LOAD_ASSIGNMENT);
      activityRequests.add(activityCarrierRequest);
    }

    activityService.createActivities(activityRequests);
  }

  @TransactionalEventListener(classes = {
      AssignmentBookingStatusChangedEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentStatusChange(final AssignmentBookingStatusChangedEvent event) {
    final List<Integer> assignmentIds = event.getLoadAssignmentIds();
    final int assignmentId = assignmentIds.get(0);
    final Assignment assignment = assignmentRepository.getReferenceById(assignmentId);

    switch (event.getDirection()) {
      case THE_REFERENCE_ASSIGNMENT_BOOKING -> {
        // no activity, activities are already written on updateAssignment or updateBooking
      }
      case ABOVE_REFERENCE_ASSIGNMENT_BOOKING -> {
        final Load toLoad = assignment.getToLoad();
        if (nonNull(toLoad) && nonNull(assignment.getUser())) {
          final String activityContentAbove = getAssignmentBookingActivityMessage(assignment, assignmentIds);
          final ActivityRequest activityRequest = buildActivityRequest(
              assignment.getUser().getUserId(),
              assignmentIds,
              assignment.getToLoad().getLoadId(),
              activityContentAbove + " by " + assignment.getToUserCompany().getCompanyName(),
              LOAD_ASSIGNMENTS_ACTIVITY_ID.get(),
              event.getAction());

          activityService.createActivities(List.of(activityRequest));
        }
      }
      case BELOW_REFERENCE_ASSIGNMENT_BOOKING -> {
        final String activityContentBelow = getAssignmentBookingActivityMessage(assignment, assignmentIds);
        final Load load = assignment.getLoad();
        if (nonNull(load) && nonNull(assignment.getToUser())) {
          final ActivityRequest activityRequestBelow = buildActivityRequest(
              assignment.getToUser().getUserId(),
              assignmentIds,
              load.getLoadId(),
              activityContentBelow + " by " + assignment.getUserCompany().getCompanyName(),
              LOAD_BOOKINGS_ACTIVITY_ID.get(),
              event.getAction());

          activityService.createActivities(List.of(activityRequestBelow));
        }
      }
      default -> throw new IllegalArgumentException("Invalid direction: " + event.getDirection());
    }

  }

  @TransactionalEventListener(classes = {
      AssignmentSentEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentSent(final AssignmentSentEvent event) {
    final List<Integer> assignmentIds = event.getLoadAssignmentIds();
    final List<Assignment> assignments = assignmentRepository.findAllById(assignmentIds);
    final Assignment firstAssignment = assignments.get(0);
    final List<ActivityRequest> activityRequests = new ArrayList<>();

    final Load load = getLoad(firstAssignment);
    final Integer loadId = load.getLoadId();

    if (nonNull(firstAssignment.getUser())) {
      final String activityContentShipper = getAssignmentSentActivityMessage(assignments, true);
      final ActivityRequest assignmentActivityRequest =
          buildActivityRequest(
              firstAssignment.getUser().getUserId(),
              assignmentIds,
              loadId,
              activityContentShipper,
              LOAD_ASSIGNMENTS_ACTIVITY_ID.get(),
              CREATE_LOAD_ASSIGNMENT);
      activityRequests.add(assignmentActivityRequest);
    }

    if (nonNull(firstAssignment.getToUser())) {
      final String activityContentCarrier = getAssignmentSentActivityMessage(assignments, false);
      final ActivityRequest bookingActivityRequest = buildActivityRequest(
          firstAssignment.getToUser().getUserId(),
          assignmentIds,
          loadId,
          activityContentCarrier,
          LOAD_BOOKINGS_ACTIVITY_ID.get(),
          CREATE_LOAD_ASSIGNMENT);
      activityRequests.add(bookingActivityRequest);
    }

    activityService.createActivities(activityRequests);
  }

  @TransactionalEventListener(classes = {
      BookingUpdatedEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleBookingUpdatedActivity(final AssignmentBookingEvent event) {
    final List<Integer> assignmentIds = event.getLoadAssignmentIds();
    final Assignment firstAssignment = assignmentRepository.getReferenceById(assignmentIds.get(0));
    final String activityContent = getAssignmentBookingActivityMessage(firstAssignment, assignmentIds);

    // Get the logged in user who is performing the update
    final Optional<Integer> loggedInUserIdOpt = UserUtil.getUserId();

    Integer loadId = null;
    if (nonNull(firstAssignment.getToLoad())) {
      loadId = firstAssignment.getToLoad().getLoadId();
    } else if (nonNull(firstAssignment.getLoad())) {
      loadId = firstAssignment.getLoad().getLoadId();
    }

    List<ActivityRequest> activityRequests = new ArrayList<>();

    // Create activity for the logged in user
    if (loggedInUserIdOpt.isPresent()) {
      final ActivityRequest activityRequest =
          buildActivityRequest(loggedInUserIdOpt.get(), assignmentIds, loadId, activityContent,
              LOAD_BOOKINGS_ACTIVITY_ID.get(), UPDATE_LOAD_BOOKING);
      activityRequests.add(activityRequest);
    }

    // If logged in user is different from assigned user, create activity for assigned user
    if (nonNull(firstAssignment.getToUser())
        && (loggedInUserIdOpt.isEmpty() || !loggedInUserIdOpt.get().equals(firstAssignment.getToUser().getUserId()))) {
      final User loggedInUser = userService.getLoggedInUser();
      final String userFullName = loggedInUser.getFirstName() + " " + loggedInUser.getLastName();

      final ActivityRequest ownerActivityRequest = buildActivityRequest(
          firstAssignment.getToUser().getUserId(),
          assignmentIds,
          loadId,
          activityContent + " by " + userFullName,
          LOAD_BOOKINGS_ACTIVITY_ID.get(),
          UPDATE_LOAD_BOOKING);

      activityRequests.add(ownerActivityRequest);
    }

    // Create activity for User (shipper) if load exists
    if (nonNull(firstAssignment.getLoad()) && nonNull(firstAssignment.getUser())) {

      final ActivityRequest shipperActivityRequest = buildActivityRequest(
          firstAssignment.getUser().getUserId(),
          assignmentIds,
          loadId,
          activityContent + " by " + firstAssignment.getToCompanyName(),
          LOAD_ASSIGNMENTS_ACTIVITY_ID.get(),
          UPDATE_LOAD_BOOKING);

      activityRequests.add(shipperActivityRequest);
    }

    activityService.createActivities(activityRequests);
  }

  private String getAssignmentCreatedActivityMessage(final Assignment assignment,
                                                     final List<Integer> assignmentIds) {
    final String pickupDropLabel = getPickupDropLabel(assignment);
    final String toCompanyName = assignment.getToCompanyName();

    return "%s assigned %s load(s) to %s"
        .formatted(pickupDropLabel, assignmentIds.size(), toCompanyName);
  }

  private String getAssignmentSentActivityMessage(final List<Assignment> assignments,
                                                  boolean isShipper) {
    final Assignment assignment = assignments.get(0);
    final String pickupDropLabel = getPickupDropLabel(assignment);
    final String otherCompany = isShipper ? assignment.getToCompanyName() : assignment.getCompanyName();

    String messageTemplate = isShipper
        ? "%s confirmation sent %s load(s) to %s"
        : "%s assigned %s load(s) by %s";

    return messageTemplate.formatted(pickupDropLabel, assignments.size(), otherCompany);
  }

  private String getAssignmentCanceledActivityMessage(final Assignment assignment, boolean isShipper) {

    final String pickupDropLabel = getPickupDropLabel(assignment);
    final String otherCompany = isShipper ? assignment.getToCompanyName() : assignment.getUserCompany().getCompanyName();

    String messageTemplate;
    if (!assignment.getToDeleted() || (isNull(assignment.getToLoad()) && PENDING.equals(assignment.getSharedWithHiredCompanyResponse()))) {
      messageTemplate = isShipper ? "%s canceled 1 load to %s" : "%s canceled by %s";
    } else if (isNull(assignment.getToLoad())) {
      messageTemplate = isShipper ? "%s rejection confirmed to %s" : "%s rejection confirmed by %s";
    } else {
      messageTemplate = isShipper ? "%s cancelation confirmed to %s" : "%s cancelation confirmed by %s";
    }

    return messageTemplate.formatted(pickupDropLabel, otherCompany);
  }

  private String getAssignmentBookingActivityMessage(final Assignment assignment,
                                                     final List<Integer> assignmentIds) {
    final String pickupDropLabel = getPickupDropLabel(assignment);

    return "%s Load %s %s".formatted(pickupDropLabel, "#" + assignment.getLoadAssignmentNumber(), assignment.getAssignmentStatus());
  }

  private String getPickupDropLabel(final Assignment assignment) {
    final boolean isRerouted = assignment.getIsRerouted();
    final String reroutePickupDrop = assignment.getReroutePickupDrop();
    final AbCompany rerouteAbCompany = nonNull(assignment.getRerouteAbCompany())
        ? assignment.getRerouteAbCompany() : assignment.getRerouteToAbCompany();
    final String pickupLabel;
    final String dropLabel;

    final Load load = getLoad(assignment);
    final AbCompany pickupAbCompany = load.getPickupAbCompany();
    final AbCompany dropAbCompany = load.getDropAbCompany();

    if (isRerouted && PICKUP.equals(reroutePickupDrop)) {
      pickupLabel = getLabel(rerouteAbCompany);
    } else {
      pickupLabel = getLabel(pickupAbCompany);
    }

    if (isRerouted && DROP.equals(reroutePickupDrop)) {
      dropLabel = getLabel(rerouteAbCompany);
    } else {
      dropLabel = getLabel(dropAbCompany);
    }
    return "%s to %s".formatted(pickupLabel, dropLabel);
  }

  private Load getLoad(final Assignment assignment) {
    return nonNull(assignment.getLoad()) ? assignment.getLoad() : assignment.getToLoad();
  }

  private String getLabel(final AbCompany abCompany) {
    return nonNull(abCompany.getCity())
        ? abCompany.getCity() + ", " + abCompany.getState()
        : abCompany.getCompanyName();
  }

  private ActivityRequest buildActivityRequest(final int userId, final List<Integer> assignmentIds, final Integer loadId,
                                               final String activityContent, final int activityTypeId, final String activityType) {
    final ActivityRequest activityRequest = new ActivityRequest();
    activityRequest.setUserId(userId);
    activityRequest.setLoadId(Optional.ofNullable(loadId));
    activityRequest.setActivityTypeId(activityTypeId); //LOAD_ASSIGNMENTS_ACTIVITY_ID.get());
    activityRequest.setAction(activityType);
    activityRequest.setActivity(activityContent);
    activityRequest.setData(
        """
            {
              "load_assignment_ids": "%s",
              "load_ids": "%s"
            }""".formatted(Strings.join(assignmentIds, ','), Strings.join(List.of(loadId), ',')));
    return activityRequest;
  }

}
