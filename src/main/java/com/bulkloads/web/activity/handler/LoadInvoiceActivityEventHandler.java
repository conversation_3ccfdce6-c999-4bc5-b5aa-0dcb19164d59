package com.bulkloads.web.activity.handler;

import static com.bulkloads.common.StringUtil.asCsString;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.activity.domain.entity.ActivityType;
import com.bulkloads.web.activity.service.ActivityService;
import com.bulkloads.web.activity.service.dto.ActivityRequest;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoice;
import com.bulkloads.web.loadinvoice.event.LoadInvoiceEvent;
import com.bulkloads.web.loadinvoice.repository.LoadInvoiceRepository;
import com.bulkloads.web.user.service.UserService;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoadInvoiceActivityEventHandler {

  private final UserService userService;
  private final ActivityService activityService;
  private final AssignmentRepository assignmentRepository;
  private final LoadInvoiceRepository loadInvoiceRepository;

  @Order(20)
  @TransactionalEventListener(classes = {
      LoadInvoiceEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleLoadInvoiceCreatedEventActivity(final LoadInvoiceEvent event) {

    LoadInvoice invoice = loadInvoiceRepository.findById(event.getLoadInvoiceId())
        .orElseThrow(() -> new BulkloadsException("Load invoice not found"));

    final List<Assignment> assignments = assignmentRepository.findAllById(event.getLoadAssignmentIds());
    final Assignment assignment = assignments.get(0);
    final Load toLoad = assignments.get(0).getToLoad();

    List<ActivityRequest> activityRequests = new ArrayList<>();
    activityRequests.add(buildActivityForInvoiceIssuer(event, assignments, assignment, toLoad));

    if (invoice.getBillToUser() != null) {
      activityRequests.add(buildActivityForInvoiceRecipient(event, invoice, assignments, assignment, toLoad));
    }
    activityService.createActivities(activityRequests);
  }

  private ActivityRequest buildActivityForInvoiceIssuer(final LoadInvoiceEvent event,
                                                        List<Assignment> assignments,
                                                        Assignment assignment,
                                                        Load toLoad) {
    String activityText = String.format(
        "%s Load %s %s Invoice created",
        getPickupDrop(toLoad),
        assignment.getLoadAssignmentNumber(),
        assignments.size() > 1 ? "(...and " + (assignments.size() - 1) + " more)" : "");

    final ActivityRequest activityRequest = new ActivityRequest();
    activityRequest.setUserId(userService.getLoggedInUser().getUserId());
    activityRequest.setActivityTypeId(ActivityType.ActivityTypes.LOAD_INVOICES_SENT_ACTIVITY_ID.get());
    activityRequest.setActivity(activityText);
    activityRequest.setAction("CREATE_LOAD_INVOICE");
    activityRequest.setLoadId(Optional.of(toLoad.getLoadId()));
    activityRequest.setData(
        """
            {
              "load_invoice_ids": "%s",
              "invoice_file_urls": "%s"
            }""".formatted(asCsString(event.getLoadAssignmentIds()),
            event.getFileUrl()));

    return activityRequest;
  }

  private ActivityRequest buildActivityForInvoiceRecipient(final LoadInvoiceEvent event,
                                                           LoadInvoice invoice,
                                                           List<Assignment> assignments,
                                                           Assignment assignment,
                                                           Load toLoad) {
    String activityText = String.format(
        "Received Invoice: %s Load %s %s",
        getPickupDrop(toLoad),
        assignment.getLoadAssignmentNumber(),
        assignments.size() > 1 ? "(...and " + (assignments.size() - 1) + " more)" : "");

    final ActivityRequest activityRequest = new ActivityRequest();
    activityRequest.setUserId(invoice.getBillToUser().getUserId());
    activityRequest.setActivityTypeId(ActivityType.ActivityTypes.LOAD_INVOICES_RECEIVED_ACTIVITY_ID.get());
    activityRequest.setActivity(activityText);
    activityRequest.setAction("CREATE_LOAD_INVOICE");
    activityRequest.setLoadId(Optional.of(toLoad.getLoadId()));
    activityRequest.setData(
        """
            {
              "load_invoice_ids": "%s",
              "invoice_file_urls": "%s"
            }""".formatted(asCsString(event.getLoadAssignmentIds()),
            event.getFileUrl()));

    return activityRequest;
  }

  static String getPickupDrop(Load load) {
    String pickupCity = load.getPickupAbCompany().getCity();
    String pickupState = load.getPickupAbCompany().getState();
    String dropCity = load.getDropAbCompany().getCity();
    String dropState = load.getDropAbCompany().getState();
    return String.format("%s, %s to %s, %s", pickupCity, pickupState, dropCity, dropState);
  }
}
