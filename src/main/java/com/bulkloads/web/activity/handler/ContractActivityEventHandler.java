package com.bulkloads.web.activity.handler;

import static com.bulkloads.config.AppConstants.ActivityType.CLOSE_CONTRACT;
import static com.bulkloads.config.AppConstants.ActivityType.CREATE_CONTRACT;
import static com.bulkloads.config.AppConstants.ActivityType.DELETE_CONTRACT;
import static com.bulkloads.config.AppConstants.ActivityType.OPEN_CONTRACT;
import static com.bulkloads.config.AppConstants.ActivityType.UPDATE_CONTRACT;
import static com.bulkloads.config.AppConstants.ContractAction.CONTRACT_CLOSED;
import static com.bulkloads.config.AppConstants.ContractAction.CONTRACT_CREATE;
import static com.bulkloads.config.AppConstants.ContractAction.CONTRACT_DELETE;
import static com.bulkloads.config.AppConstants.ContractAction.CONTRACT_OPENED;
import static com.bulkloads.config.AppConstants.ContractAction.CONTRACT_UPDATE;
import static java.util.Objects.nonNull;
import java.util.List;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.activity.domain.entity.ActivityType;
import com.bulkloads.web.activity.service.ActivityService;
import com.bulkloads.web.activity.service.dto.ActivityRequest;
import com.bulkloads.web.contracts.domain.entity.Contract;
import com.bulkloads.web.contracts.event.ContractEvent;
import com.bulkloads.web.contracts.repository.ContractRepository;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class ContractActivityEventHandler {

  private final ActivityService activityService;
  private final ContractRepository contractRepository;
  private final UserService userService;

  @TransactionalEventListener(classes = ContractEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT)
  public void handleActivity(final ContractEvent event) {
    final ActivityRequest activityRequest = buildActivityRequest(event);
    activityService.createActivities(List.of(activityRequest));
  }

  private ActivityRequest buildActivityRequest(final ContractEvent event) {
    final int contractId = event.getContractId();
    final Contract contract = contractRepository.getReferenceById(contractId);

    final String activityMsg = buildActivityMsg(contract, event.getAction());

    final ActivityRequest activityRequest = new ActivityRequest();
    activityRequest.setUserId(userService.getLoggedInUser().getUserId());
    activityRequest.setActivityTypeId(ActivityType.ActivityTypes.CONTRACTS_ACTIVITY_ID.get());
    activityRequest.setAction(mapToActivityAction(event.getAction()));
    activityRequest.setActivity(activityMsg);
    activityRequest.setData(
        """
            {
              "contract_ids": "%s"
            }""".formatted(contractId));
    return activityRequest;
  }

  private String buildActivityMsg(final Contract contract, final String action) {
    final String weightQuantityMsg = getWeightQuantityMessage(contract);
    final String suffix = mapToActionSuffix(action);
    return weightQuantityMsg + " " + suffix;
  }

  private String mapToActionSuffix(final String action) {
    return switch (action) {
      case CONTRACT_CREATE -> "added";
      case CONTRACT_UPDATE -> "updated";
      case CONTRACT_CLOSED -> "closed";
      case CONTRACT_OPENED -> "reopened";
      case CONTRACT_DELETE -> "deleted";
      default -> throw new BulkloadsException("Action %s not supported".formatted(action));
    };
  }

  private String mapToActivityAction(final String action) {
    return switch (action) {
      case CONTRACT_CREATE -> CREATE_CONTRACT;
      case CONTRACT_UPDATE -> UPDATE_CONTRACT;
      case CONTRACT_CLOSED -> CLOSE_CONTRACT;
      case CONTRACT_OPENED -> OPEN_CONTRACT;
      case CONTRACT_DELETE -> DELETE_CONTRACT;
      default -> throw new BulkloadsException("Action %s not supported".formatted(action));
    };
  }

  private String getWeightQuantityMessage(final Contract contract) {
    String action = "";

    final String buySell = contract.getBuySell();
    final String commodity = contract.getCommodityName();
    final Integer numberOfLoads = contract.getNumberOfLoads();
    final String pickupCity = contract.getPickupCity();
    final String pickupState = contract.getPickupState();
    final String dropCity = contract.getDropCity();
    final String dropState = contract.getDropState();
    if (nonNull(buySell)) {
      action = buySell.equals("Buy") ? "Purchase" : "Sale";
    }

    return String.format("%s contract %s %d loads %s, %s to %s, %s",
                         action, commodity, numberOfLoads, pickupCity, pickupState, dropCity, dropState);
  }
}
