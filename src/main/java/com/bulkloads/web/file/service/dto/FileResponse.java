package com.bulkloads.web.file.service.dto;

import com.bulkloads.web.file.domain.entity.File;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.Value;
import lombok.With;

@Data
@Value
@Builder
@With
public class FileResponse {

  @Schema(name = "file_id", description = "Unique identifier for the file")
  Integer fileId;

  @Schema(name = "file_url", description = "URL of the file")
  String fileUrl;

  @Schema(name = "file_type_id", description = "Type ID of the file")
  Integer fileTypeId;

  @Schema(name = "file_type", description = "Type of the file")
  String fileType;

  @Schema(name = "thumb_url", description = "Thumbnail URL of the file")
  String thumbUrl;

  @Schema(name = "mime_type", description = "MIME type of the file")
  String mimeType;

  @Schema(name = "is_image", description = "Indicates if the file is an image")
  Boolean isImage;

  @Schema(name = "is_audio", description = "Indicates if the file is an audio file")
  Boolean isAudio;

  @Schema(name = "size", description = "Size of the file in bytes")
  Long size;

  @Schema(name = "extension", description = "File extension (e.g., jpg, pdf)")
  String extension;

  @Schema(name = "filename", description = "Name of the file")
  String filename;

  @Schema(name = "number_of_pages", description = "Number of pages in the file, if applicable")
  Integer numberOfPages;

  @Schema(name = "caption", description = "Caption or description for the file")
  String caption;

  public static FileResponse fromFile(File file) {
    return FileResponse.builder()
        .fileId(file.getFileId())
        .fileUrl(file.getFileUrl())
        .thumbUrl(file.getThumbUrl())
        .mimeType(file.getMimeType())
        .isImage(file.getIsImage())
        .isAudio(file.getIsAudio())
        .size(file.getSize())
        .extension(file.getExtension())
        .filename(file.getFilename())
        .numberOfPages(file.getNumberOfPages())
        .caption(file.getCaption())
        .build();
  }
}
