package com.bulkloads.web.file.service.dto;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class UpdateFileFieldsRequest {
  @Valid
  @NotEmpty(message = "Fields list cannot be empty")
  @Schema(name = "fields", description = "List of fields to update", requiredMode = Schema.RequiredMode.REQUIRED)
  private List<FileFieldRequest> fields;
}