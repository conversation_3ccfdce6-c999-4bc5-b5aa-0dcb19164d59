package com.bulkloads.web.file.service.dto;

import java.util.Optional;
import org.springframework.web.bind.annotation.BindParam;
import org.springframework.web.multipart.MultipartFile;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Jacksonized
@Builder
public class UploadFileRequest {

  @NotNull(message = "File must not be null")
  @BindParam("file")
  MultipartFile file;

  @BindParam("thumb")
  MultipartFile thumb;

  @BindParam("s3_dir")
  Optional<String> s3Dir;

  @BindParam("number_of_pages")
  Optional<Integer> numberOfPages = Optional.of(1);

  // for OCR we accept only images, for filetypeid should not be present for pdf files
  // say again: fileTypeID only be present for images

  @BindParam("file_type_id")
  Optional<Integer> fileTypeId;
}
