package com.bulkloads.web.file.service;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.text.NumberFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.domain.entity.FileFieldDefinition;
import com.bulkloads.web.file.domain.entity.FileType;
import com.bulkloads.web.file.event.FileFieldsUpdatedEvent;
import com.bulkloads.web.file.mapper.FileFieldMapper;
import com.bulkloads.web.file.mapper.FileMapper;
import com.bulkloads.web.file.repository.FileFieldDefinitionRepository;
import com.bulkloads.web.file.repository.FileOcrProjection;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.service.dto.FileFieldRequest;
import com.bulkloads.web.file.service.dto.FileFieldResponse;
import com.bulkloads.web.file.service.dto.FileOcrDto;
import com.bulkloads.web.file.service.dto.TicketItemResponse;
import com.bulkloads.web.file.service.dto.UnapprovedFileResponse;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.reactive.function.client.WebClient;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class FileOcrService {

  public static final String OCR_FIELD_TICKET_NUMBER = "ticket_number";
  public static final String OCR_FIELD_TICKET_DATE = "ticket_date";
  public static final String OCR_FIELD_GROSS_WEIGHT = "gross";
  public static final String OCR_FIELD_TARE_WEIGHT = "tare";
  public static final String OCR_FIELD_VOLUME = "volume";

  public static final String OCR_SERVICE_TIMEOUT = "The OCR service did not respond in the specified time";

  private final AppProperties appProperties;
  private final WebClient webClient;
  private final AssignmentRepository assignmentRepository;
  private final FileRepository fileRepository;
  private final FileMapper fileMapper;
  private final FileFieldMapper fileFieldMapper;
  private final UserService userService;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final MessageQueueSender queueSender;
  private final FileFieldDefinitionRepository fileFieldDefinitionRepository;
  private final ObjectMapper objectMapper;

  /**
   * Enqueue an OCR request for asynchronous processing once the surrounding transaction
   * successfully commits.
   *
   * @param fileId identifier of the file to be processed
   */
  public void ocrFileAsync(int fileId) {
    log.debug("Emitting file OCR event for file id: {}", fileId);
    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
      @Override
      public void afterCommit() {
        queueSender.send(
            appProperties.getFileOcr().getQueueName(),
            FileOcrDto.builder().fileId(fileId).build());
      }
    });
  }

  public List<UnapprovedFileResponse> findUnapprovedOcrFilesWithDetails() {
    List<FileOcrProjection> results = fileRepository.findUnapprovedOcrFilesWithDetails();
    return fileMapper.projectionsToResponses(results);
  }

  public List<TicketItemResponse> getFileFields(int fileId) {

    List<TicketItemResponse> allFields = new ArrayList<>();

    final File file = fileRepository.findById(fileId).orElseThrow(() -> new BulkloadsException("File not found"));
    if (isNull(file.getFileType())) {
      return allFields;
    }

    final FileType fileType = file.getFileType();
    File originFile = null;
    if (isEmpty(fileType.getOcrModelId())) {
      return allFields;
    }

    if (fileType.getFileTypeId() == 2) {
      // check if fileId exists in load_assignments.loading_ticket_file_id
      List<Assignment> assignments = assignmentRepository.findAllByUnloadingTicketFileId(fileId);
      if (!isEmpty(assignments) && nonNull(assignments.get(0).getLoadingTicketFileId())) {
        originFile = fileRepository.findById(assignments.get(0).getLoadingTicketFileId()).orElseThrow();
      }
    }

    List<FileField> fields = fileRepository.getFileFields(file);
    List<FileField> originFields = originFile != null ? fileRepository.getFileFields(originFile) : null;

    // Special fields
    TicketItemResponse ticketNumber = null;
    TicketItemResponse grossWeight = null;
    TicketItemResponse tareWeight = null;
    TicketItemResponse volume = null;
    TicketItemResponse ticketDate = null;
    List<TicketItemResponse> grades = new ArrayList<>();
    List<TicketItemResponse> gradesWithoutValues = new ArrayList<>();

    // loop fields
    for (FileField field : fields) {
      final FileFieldResponse fileFieldResponse = fileFieldMapper.entityToResponse(field);
      String originValue = "";

      // Find matching origin field
      if (nonNull(originFields) && nonNull(field.getFieldName())) {
        // For special fields (ticket number, tare weight, date), match by field name
        for (FileField originField : originFields) {
          if (nonNull(originField.getFieldName())
              && originField.getFieldName().equalsIgnoreCase(field.getFieldName())) {
            originValue = originField.getFieldValue();
            break;
          }
        }
      }

      TicketItemResponse ticketItem = TicketItemResponse.builder()
          .fileField(fileFieldResponse)
          .originValue(originValue)
          .build();

      if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_TICKET_NUMBER)) {
        ticketNumber = ticketItem;
      } else if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_TICKET_DATE)) {
        ticketDate = ticketItem;
      } else if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_GROSS_WEIGHT)) {
        grossWeight = ticketItem;
      } else if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_TARE_WEIGHT)) {
        tareWeight = ticketItem;
      } else if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_VOLUME)) {
        volume = ticketItem;
      } else { //if (field.getGradeId() != null) {
        if (!isEmpty(field.getFieldValue())) {
          grades.add(ticketItem);
        } else {
          gradesWithoutValues.add(ticketItem);
        }
      }
    }

    // Add fields in the required order
    if (ticketNumber != null) {
      allFields.add(ticketNumber);
    }
    if (ticketDate != null) {
      allFields.add(ticketDate);
    }
    if (grossWeight != null) {
      allFields.add(grossWeight);
    }
    if (tareWeight != null) {
      allFields.add(tareWeight);
    }
    if (volume != null) {
      allFields.add(volume);
    }

    // Add grades with values, sorted alphabetically
    grades.sort((a, b) -> {
      String nameA = a.getFileField().getFieldName();
      String nameB = b.getFileField().getFieldName();
      return nameA.compareToIgnoreCase(nameB);
    });
    allFields.addAll(grades);

    // Add grades without values, sorted alphabetically
    gradesWithoutValues.sort((a, b) -> {
      String nameA = a.getFileField().getFieldName();
      String nameB = b.getFileField().getFieldName();
      return nameA.compareToIgnoreCase(nameB);
    });
    allFields.addAll(gradesWithoutValues);

    return allFields;
  }

  public void updateFileFields(int fileId, List<@Valid FileFieldRequest> request) {
    final File file = fileRepository.findById(fileId)
        .orElseThrow(() -> new BulkloadsException("File not found"));

    if (isEmpty(request)) {
      return;
    }

    // Validate request fields
    Map<String, String> errors = new HashMap<>();

    for (FileFieldRequest fieldRequest : request) {
      if (isMissingOrIsEmpty(fieldRequest.getFieldName())) {
        errors.put("fields", "Field name is required");
      }
    }

    if (!errors.isEmpty()) {
      throw new ValidationException(errors);
    }

    // Get current user ID for auditing
    final User user = userService.getLoggedInUser();
    Integer userId = user.getUserId();
    Instant now = Instant.now();

    // Get existing fields for this file
    List<FileField> existingFields = fileRepository.getFileFields(file);

    for (FileFieldRequest fieldRequest : request) {
      if (isMissingOrIsEmpty(fieldRequest.getFieldName())) {
        continue;
      }

      // Find the existing field to update
      FileField fieldToUpdate = null;

      for (FileField field : existingFields) {
        if (nonNull(field.getFieldName())
            && field.getFieldName().equalsIgnoreCase(fieldRequest.getFieldName().orElse(""))) {
          fieldToUpdate = field;
          break;
        }
      }

      if (nonNull(fieldToUpdate)) {
        // Update the field
        fieldToUpdate.setFieldValue(fieldRequest.getFieldValue().orElse(""));
        fieldToUpdate.setEditByUserId(userId);
        fieldToUpdate.setEditDate(now);
      }
    }

    // Mark the file as OCR approved
    file.setOcrApproved(true);
    file.setOcrApprovedDate(now);
    file.setApprovedByUserId(userId);

    // Save all changes
    fileRepository.save(file);

    // Publish event to notify other components about the updated fields
    applicationEventPublisher.publishEvent(new FileFieldsUpdatedEvent(file, existingFields));
  }

  // sync method
  @Transactional
  public void ocrFile(int fileId) {
    final File file = fileRepository.findById(fileId)
        .orElseThrow(() -> new BulkloadsException("File with file_id=" + fileId + " not found"));

    // idempotence
    if (Boolean.TRUE.equals(file.getOcrProcessed())) {
      return;
    }

    FileType fileType = file.getFileType();
    if (isNull(fileType) || isEmpty(fileType.getOcrModelId())) {
      log.warn("OCR not supported for file id: {}", fileId);
      return;
    }

    ProcessFileResult result = processFile(file.getFileUrl(), fileType);

    file.setFieldsRaw(result.jsonRaw);
    file.setFieldsDate(Instant.now());
    file.setFieldsSubmodelId(result.submodelId);
    file.setOcrProcessed(true);
    file.setFieldsByUserId(file.getAddedByUserId());
    file.setOcrModelId(fileType.getOcrModelId());

    file.getFileFields().clear();
    for (FileField field : result.fields) {
      field.setFile(file);
      file.getFileFields().add(field);
    }
    log.info("OCR file processed {}", file.getFileId());

    fileRepository.save(file);
  }

  private ProcessFileResult processFile(String fileUrl, FileType fileType) {
    JsonNode ocrJson = ocrRequest(fileType.getOcrModelId(), fileUrl);

    JsonNode document = ocrJson.path("analyzeResult").path("documents").get(0);
    String submodelId = document.path("docType").asText("");
    JsonNode incomingFields = document.path("fields");

    List<FileField> fields = new ArrayList<>();
    Iterator<String> names = incomingFields.fieldNames();
    while (names.hasNext()) {
      String sourceName = names.next();
      fileFieldDefinitionRepository.findById(sourceName).ifPresent(def -> {
        JsonNode fieldValue = incomingFields.get(sourceName);
        FileField field = processField(def, fieldValue);
        fields.add(field);
      });
    }

    return new ProcessFileResult(submodelId, ocrJson.toString(), fields);
  }

  private JsonNode ocrRequest(String modelId, String fileUrl) {
    try {
      String analyzeUrl = String.format(
          "%s/%s:analyze?api-version=%s&stringIndexType=textElements",
          appProperties.getFileOcr().getEndpoint(), modelId, appProperties.getFileOcr().getApiVersion());

      // e.g. https://bl-doc-intelligence-east.cognitiveservices.azure.com/documentintelligence/documentModels/2025_3_6/analyzeResults/87e8...fb7
      String operationLocation =
          webClient
              .post()
              .uri(analyzeUrl)
              .contentType(MediaType.APPLICATION_JSON)
              .header("Ocp-Apim-Subscription-Key", appProperties.getFileOcr().getApiKey())
              .bodyValue(Map.of("urlSource", fileUrl))
              .exchangeToMono(
                  response -> {
                    String loc =
                        response.headers().asHttpHeaders().getFirst("operation-location");
                    if (loc == null) {
                      log.error("OCR ocrRequest() error=[Unable to scan file {}] api_url=[{}]", fileUrl,
                          analyzeUrl);
                      return Mono.error(new BulkloadsException("Unable to scan file " + fileUrl));
                    }
                    return Mono.just(loc);
                  })
              .block();

      for (int i = 0; i < 30; i++) {
        ResponseEntity<String> resp = webClient.get()
            .uri(operationLocation)
            .header("Ocp-Apim-Subscription-Key", appProperties.getFileOcr().getApiKey())
            .accept(MediaType.APPLICATION_JSON)
            .exchangeToMono(r -> r.toEntity(String.class))
            .block();

        final HttpStatusCode statusCode = resp.getStatusCode();
        String body = resp.getBody();            // safe: already buffered
        String retryAfter = resp.getHeaders().getFirst("Retry-After");

        if (statusCode.is2xxSuccessful() && body != null) {
          JsonNode node = objectMapper.readTree(body);
          String status = node.path("status").asText();
          if ("succeeded".equalsIgnoreCase(status)) {
            return node;
          }
          if ("failed".equalsIgnoreCase(status)) {
            throw new IllegalStateException("OCR failed: " + body);
          }
        }

        // simple backoff + jitter
        long retrySec = retryAfter != null ? Math.max(2, Long.parseLong(retryAfter)) : 2L;
        long jitterMs = new Random().nextInt(100, 300);

        Thread.sleep(retrySec * 1000 + jitterMs);
        log.info("OCR retrying after {}ms", retrySec * 1000 + jitterMs);

      }

      throw new BulkloadsException(OCR_SERVICE_TIMEOUT + " for file " + fileUrl);
    } catch (BulkloadsException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error calling OCR provider", e);
      throw new BulkloadsException("Error processing file", e);
    }
  }

  private FileField processField(FileFieldDefinition def, JsonNode value) {
    FileField field = new FileField();
    field.setFieldName(def.getFieldName());
    field.setFieldLabel(def.getFieldLabel());
    field.setFieldType(value.path("type").asText("string"));
    field.setFieldValue(""); // default

    if (def.getGradeId() != null) {
      field.setGradeId(def.getGradeId());
      field.setIsGrade(true);
    }

    String type = field.getFieldType();
    if ("string".equalsIgnoreCase(type)) {
      if (value.has("valueString")) {
        field.setFieldValue(value.get("valueString").asText(""));
      }
    } else if ("number".equalsIgnoreCase(type)) {
      if (value.has("content")) {
        String content = value.get("content").asText("");
        try {
          Number number = NumberFormat.getNumberInstance(Locale.US).parse(content);
          field.setFieldValue(String.valueOf(number.doubleValue()));
        } catch (Exception ex) {
          field.setFieldValue(content);
        }
      }
    } else if ("date".equalsIgnoreCase(type)) {
      if (value.has("content")) {
        field.setFieldValue(value.get("content").asText(""));
      }
    } else {
      if (value.has("content")) {
        field.setFieldValue(value.get("content").asText(""));
      }
    }

    if (value.has("confidence")) {
      field.setConfidence(value.get("confidence").asDouble());
    }

    if (value.has("boundingRegions") && !isEmpty(value.get("boundingRegions"))) {
      JsonNode bounding = value.get("boundingRegions").get(0);
      if (bounding.has("pageNumber")) {
        field.setPageNumber(bounding.get("pageNumber").asInt());
      }
      if (bounding.has("polygon") && bounding.get("polygon").isArray()) {
        List<Integer> polygon = new ArrayList<>();
        for (JsonNode n : bounding.get("polygon")) {
          polygon.add(n.asInt());
        }
        field.setPolygon(polygon);
      }
    }

    if (value.has("spans") && !isEmpty(value.get("spans"))) {
      JsonNode span = value.get("spans").get(0);
      if (span.has("offset")) {
        field.setSpanOffset(span.get("offset").asInt());
      }
      if (span.has("length")) {
        field.setSpanLength(span.get("length").asInt());
      }
    }

    return field;
  }

  private record ProcessFileResult(String submodelId, String jsonRaw, List<FileField> fields) {
  }
}