package com.bulkloads.web.file.mapper;

import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.service.dto.FileFieldResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class FileFieldMapper {

  //@Mapping(target = "fieldName", source = "fileField.fileFieldId.fieldName")
  @Mapping(target = "boundingBox", source = "polygon")
  public abstract FileFieldResponse entityToResponse(final FileField fileField);

}
