package com.bulkloads.web.file.domain.entity;

import java.time.Instant;
import java.util.List;
import com.bulkloads.web.common.jpa.converter.JsonListConverter;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "file_fields")
@Getter
@Setter
public class FileField {

  @EmbeddedId
  private FileFieldId id = new FileFieldId();

  @MapsId("fileId")
  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "file_id", nullable = false)
  private File file;

  // the file field definition
  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "field_name", referencedColumnName = "field_name", insertable = false, updatable = false)
  private FileFieldDefinition fileFieldDefinition;

  @Column(name = "field_label")
  private String fieldLabel;

  @Column(name = "field_type")
  private String fieldType;

  @Column(name = "field_value")
  private String fieldValue;

  @Column(name = "confidence")
  private Double confidence;

  @Column(name = "page_number")
  private Integer pageNumber;

  @Column(name = "polygon")
  @Convert(converter = JsonListConverter.class)
  private List<Integer> polygon;

  @Column(name = "span_offset")
  private Integer spanOffset;

  @Column(name = "span_length")
  private Integer spanLength;

  @Column(name = "grade_id")
  private Integer gradeId;

  @Column(name = "external_grade_code")
  private String externalGradeCode;

  @Column(name = "is_unmatched_external_grade")
  private Boolean isUnmatchedExternalGrade = false;

  @Column(name = "is_grade")
  private Boolean isGrade = false;

  @Column(name = "edit_by_user_id")
  private Integer editByUserId;

  @Column(name = "edit_date")
  private Instant editDate;

  // Getter and setter for fieldName that delegates to the embedded id
  public String getFieldName() {
    return id.getFieldName();
  }

  public void setFieldName(String fieldName) {
    id.setFieldName(fieldName);
  }

}