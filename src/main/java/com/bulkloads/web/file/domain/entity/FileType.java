package com.bulkloads.web.file.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "file_types")
@Getter
@Setter
public class FileType {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "file_type_id")
  private Integer fileTypeId;

  @Size(max = 75)
  @NotNull
  @Column(name = "file_type")
  private String fileType;

  @Column(name = "file_type_user_id")
  private Integer fileTypeUserId;

  @Column(name = "file_type_user_company_id")
  private Integer fileTypeUserCompanyId;

  @Size(max = 100)
  @NotNull
  @Column(name = "ocr_model_id")
  private String ocrModelId = "";

}
