package com.bulkloads.web.file.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "files")
@Getter
@Setter
public class File {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "file_id")
  private Integer fileId;

  @NotEmpty
  @Column(name = "file_url")
  private String fileUrl = "";

  @NotEmpty
  @Column(name = "thumb_url")
  private String thumbUrl = "";

  @Column(name = "full_file_url")
  private String fullFileUrl = "";

  @NotEmpty
  @Column(name = "mime_type")
  private String mimeType = "";

  @Column(name = "is_image")
  private Boolean isImage = false;

  @Column(name = "is_audio")
  private Boolean isAudio = false;

  @Column(name = "size")
  private Long size = 0L;

  @PositiveOrZero
  @Column(name = "full_size")
  private Long fullSize = 0L;

  @NotEmpty
  @Column(name = "extension")
  private String extension = "";

  @NotEmpty
  @Column(name = "filename")
  private String filename = "";

  @Positive
  @Column(name = "number_of_pages")
  private Integer numberOfPages;

  @Column(name = "caption")
  private String caption = "";

  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "file_type_id")
  private FileType fileType;

  @Column(name = "fields_raw")
  private String fieldsRaw = "";

  @Column(name = "fields_date")
  private Instant fieldsDate;

  @Column(name = "fields_submodel_id")
  private String fieldsSubmodelId = "";

  @Column(name = "ocr_processed")
  private Boolean ocrProcessed = false;

  @Column(name = "ocr_model_id")
  private String ocrModelId = "";

  @Column(name = "added_by_user_id", insertable = false, updatable = false)
  private Integer addedByUserId;

  @Positive
  @Column(name = "added_by_ab_user_id", insertable = false, updatable = false)
  private Integer addedByAbUserId;

  @Column(name = "ocr_approved")
  private Boolean ocrApproved = false;

  @Column(name = "ocr_approved_date")
  private Instant ocrApprovedDate;

  @Column(name = "fields_by_user_id")
  private Integer fieldsByUserId;

  @Column(name = "ocr_approved_by_user_id")
  private Integer approvedByUserId;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "file", fetch = FetchType.LAZY)
  private List<FilePage> filePages = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "file", fetch = FetchType.LAZY)
  private List<UserFile> userFiles = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "file", fetch = FetchType.LAZY, orphanRemoval = true)
  private List<FileField> fileFields = new ArrayList<>();

}