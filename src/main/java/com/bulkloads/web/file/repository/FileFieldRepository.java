package com.bulkloads.web.file.repository;

import java.util.List;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface FileFieldRepository extends JpaRepository<FileField, Integer> {

  /**
   * Find all file fields for a specific file
   *
   * @param file The file to get fields for
   * @return List of file fields
   */
  List<FileField> findByFile(File file);

  /**
   * Find all file fields for a specific file ID
   *
   * @param fileId The file ID to get fields for
   * @return List of file fields
   */
  @Query("SELECT ff FROM FileField ff WHERE ff.file.fileId = :fileId")
  List<FileField> findByFileId(@Param("fileId") Integer fileId);

  /**
   * When an external grade is matched to a grade, we need to update all file fields for that external grade code
   */
  @Modifying
  @Transactional
  @Query(value = """
        UPDATE loads l
          inner join load_assignments la using(load_id)
          inner join files f on la.loading_ticket_file_id = f.file_id
          inner join file_fields ff on ff.file_id = f.file_id
        set
          ff.field_name = :newFieldName,
          ff.field_label = :newFieldLabel,
          ff.edit_by_user_id = :userId,
          ff.grade_id = :gradeId,
          ff.edit_date = now(),
          ff.is_unmatched_external_grade = 0
        where l.active = 1
          and f.ocr_processed = 1
          and f.ocr_approved = 0
          and l.user_company_id = :userCompanyId
          and ff.field_name = :oldFieldName
      """, nativeQuery = true)
  int updateExternalFileFields(
      @Param("userId") List<Integer> userId,
      @Param("userCompanyId") List<Integer> userCompanyId,
      @Param("oldFieldName") List<String> oldFieldName,
      @Param("newFieldName") List<String> newFieldName,
      @Param("newFieldLabel") List<String> newFieldLabel,
      @Param("gradeId") List<Integer> gradeId
  );
}