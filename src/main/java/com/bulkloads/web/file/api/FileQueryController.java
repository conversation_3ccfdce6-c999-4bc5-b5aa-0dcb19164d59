package com.bulkloads.web.file.api;

import com.bulkloads.web.file.service.FileService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping(path = "/rest/files")
@Tag(name = "File")
@RequiredArgsConstructor
public class FileQueryController {

  private final FileService fileService;



}
