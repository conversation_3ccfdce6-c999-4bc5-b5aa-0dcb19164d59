package com.bulkloads.web.common.validator;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Documented
@Constraint(validatedBy = RateTypeValidator.class)
@Target({ FIELD })
@Retention(RUNTIME)
public @interface RateTypeValid {
  String message() default "The value %s is not a valid rate type";
  Class<?>[] groups() default {};
  Class<? extends Payload>[] payload() default {};
}