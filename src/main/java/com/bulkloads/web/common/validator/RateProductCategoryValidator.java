package com.bulkloads.web.common.validator;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import java.util.Optional;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import com.bulkloads.web.rate.repository.RateProductCategoryRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class RateProductCategoryValidator implements ConstraintValidator<RateProductCategoryValid, Optional<Integer>> {

  private final RateProductCategoryRepository rateProductCategoryRepository;

  private String message;

  @Override
  public void initialize(RateProductCategoryValid constraintAnnotation) {
    this.message = constraintAnnotation.message();
  }

  @Override
  public boolean isValid(Optional<Integer> rateProductCategoryId, ConstraintValidatorContext context) {
    if (existsAndIsNotEmpty(rateProductCategoryId)) {
      Optional<RateProductCategory> rateProductCategory = rateProductCategoryRepository.findById(rateProductCategoryId.get());

      if (rateProductCategory.isEmpty()) {
        String errorMessage = message.formatted(rateProductCategoryId.get());
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(errorMessage)
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}