package com.bulkloads.web.common.jpa.converter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class CsvIntegerListConverter implements AttributeConverter<List<Integer>, String> {

  private static final String DELIMITER = ",";

  @Override
  public String convertToDatabaseColumn(List<Integer> attribute) {
    if (CollectionUtils.isEmpty(attribute)) {
      return Strings.EMPTY;
    }
    return attribute.stream()
        .map(String::valueOf)
        .collect(Collectors.joining(DELIMITER));
  }

  @Override
  public List<Integer> convertToEntityAttribute(String dbData) {
    if (!StringUtils.hasText(dbData)) {
      return new ArrayList<>();
    }
    return Arrays.stream(dbData.split(DELIMITER))
        .map(Integer::parseInt)
        .collect(Collectors.toCollection(ArrayList::new));
  }
}
