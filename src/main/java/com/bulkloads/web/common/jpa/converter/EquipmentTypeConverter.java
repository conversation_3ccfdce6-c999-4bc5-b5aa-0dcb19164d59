package com.bulkloads.web.common.jpa.converter;


import com.bulkloads.web.companyequipment.domain.entity.EquipmentType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class EquipmentTypeConverter implements AttributeConverter<EquipmentType, String> {

  @Override
  public String convertToDatabaseColumn(EquipmentType attribute) {
    if (attribute == null) {
      return null;
    }
    return attribute.toString();
  }

  @Override
  public EquipmentType convertToEntityAttribute(String dbData) {
    if (dbData == null) {
      return null;
    }
    return EquipmentType.fromString(dbData);
  }
}