package com.bulkloads.web.common.jpa.converter;

import static com.bulkloads.common.Converters.asList;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class CsvStringListConverter implements AttributeConverter<List<String>, String> {

  private static final String DELIMITER = ",";


  @Override
  public String convertToDatabaseColumn(List<String> attribute) {
    if (CollectionUtils.isEmpty(attribute)) {
      return Strings.EMPTY;
    }
    return attribute.stream()
        .map(String::valueOf)
        .collect(Collectors.joining(DELIMITER));
  }

  @Override
  public List<String> convertToEntityAttribute(String dbValue) {
    if (!StringUtils.hasText(dbValue)) {
      return new ArrayList<>();
    }


    return Arrays
        .stream(dbValue.split(DELIMITER))
        .collect(asList());
  }
}