package com.bulkloads.web.apikey.repository;

import java.util.Optional;
import com.bulkloads.web.apikey.domain.entity.ApiKey;
import com.bulkloads.web.apikey.projection.ApiKeyResultWithEndpointId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ApiKeyRepository extends JpaRepository<ApiKey, Integer> {

  @Query("""
      select
        new com.bulkloads.web.apikey.projection.ApiKeyResultWithEndpointId(k, e.id)
      from ApiKey k
      left join k.apiEndpoints e with e.method = :method and e.path = :path
      where k.apiKey = :apiKey
      and (k.validUntil is null or k.validUntil >= current_date)
      and k.enabled = true
      and k.numOfRequests <= k.quota
      order by k.id asc
      limit 1
      """)
  Optional<ApiKeyResultWithEndpointId> findApiKeyWithEndpoint(final String apiKey,
                                                              final String method,
                                                              final String path);

  Optional<ApiKey> findByApiKey(final String apiKey);

}
