package com.bulkloads.web.usercompany.api;

import com.bulkloads.web.usercompany.domain.UserCompanyDomainService;
import com.bulkloads.web.usercompany.service.dto.UserCompanyResponse;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest/companies")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class UserCompanyController {

  private final UserCompanyDomainService companyService;

  @Secured({"USER"})
  @GetMapping("/{user_company_id}")
  public UserCompanyResponse getCompanyById(@PathVariable("user_company_id") int userCompanyId) {
    // return UserCompanyResponse.fromCompany(companyService.findById(userCompanyId));
    return null;
  }
}
