package com.bulkloads.web.usercompany.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "user_type")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserType {

  @Id
  @Column(name = "user_type_id")
  private Integer userTypeId;

  @Column(name = "user_type")
  private String userType = "";

  public static UserType of(UserTypeValue value) {
    return new UserType(value.getUserTypeId(), value.getUserType());
  }

  public static UserType of(int userTypeId) {
    return of(UserTypeValue.of(userTypeId));
  }
}