package com.bulkloads.web.usercompany.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.apikey.domain.entity.ApiKeyIntegrationPermission;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.user.domain.entity.User;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "user_company")
@Getter
@Setter
public class UserCompany {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Column(name = "census_num")
  private Integer censusNum;

  @Column(name = "mc_num")
  private String mcNum = "";

  @Column(name = "company_name")
  private String companyName = "";

  @Column(name = "company_name_dba")
  private String companyNameDba = "";

  @Column(name = "deletion_date")
  private Instant deletionDate;

  @Column(name = "merged_to")
  private Integer mergedTo;

  @Column(name = "signup_site")
  private String signupSite = "BulkLoads";

  @Column(name = "order_count")
  private int orderCount = 0;

  @Column(name = "sff_enabled")
  private Integer sffEnabled = 1;

  @Column(name = "auto_generate_work_order_numbers")
  private Integer autoGenerateWorkOrderNumbers = 0;

  @Column(name = "auto_confirm_assignments")
  private Integer autoConfirmAssignments = 0;

  @Column(name = "allow_driver_load_submissions")
  private Integer allowDriverLoadSubmissions = 0;

  @Column(name = "company_logo_url")
  private String companyLogoUrl = "";

  @Column(name = "company_logo_thumb_url")
  private String companyLogoThumbUrl = "";

  @Column(name = "canadian_authority")
  private int canadianAuthority = 0;

  @Column(name = "avg_rating")
  private Double avgRating;

  @Column(name = "rating_count")
  private int ratingCount = 0;

  @Column(name = "avg_days_to_pay")
  private Double avgDaysToPay;

  @Column(name = "non_payment_count")
  private int nonPaymentCount = 0;

  @Column(name = "comment_count")
  private int commentCount = 0;

  @Column(name = "allowed_ips")
  private String allowedIps = "";

  @Column(name = "credit_limit")
  private Double creditLimit = 10000.0;

  @Column(name = "balance")
  private Double balance = 0.00;

  @Column(name = "source_company")
  private String sourceCompany = "";

  @Column(name = "source_company_id")
  private String sourceCompanyId = "";

  @Column(name = "verification_enabled")
  private Boolean verificationEnabled = false;

  @Column(name = "verified")
  private Boolean verified = false;

  @Column(name = "verified_date")
  private Instant verifiedDate;

  @Column(name = "verified_by_user_id")
  private Integer verifiedByUserId;

  @Column(name = "no_census_reason")
  private String noCensusReason = "";

  @Column(name = "number_of_trucks")
  private Integer numberOfTrucks = 0;

  @Column(name = "company_code")
  private String companyCode = "";

  @Column(name = "allow_drivers")
  private Boolean allowDrivers = true;

  @Column(name = "require_documents_for_completing_load")
  private Boolean requireDocumentsForCompletingLoad = false;

  @Column(name = "approve_loads_for_payment")
  private Boolean approveLoadsForPayment = false;

  @Column(name = "use_tms_for_invoices")
  private Boolean useTmsForInvoices = true;

  @Column(name = "use_dispatch")
  private Boolean useDispatch = true;

  @Column(name = "integration_mcp_enabled")
  private Boolean integrationMcpEnabled = false;

  @Column(name = "mcp_checked")
  private Boolean mcpChecked = false;

  @Column(name = "mcp_monitored")
  private Boolean mcpMonitored = false;

  @Column(name = "mcp_error")
  private String mcpError = "";

  @Column(name = "risk_assessment_overall")
  private String riskAssessmentOverall = "";

  @Column(name = "business_start_year")
  private Integer businessStartYear;

  @Column(name = "intra_interstate")
  private String intraInterstate = "";

  @Column(name = "hazmat_certified")
  private Boolean hazmatCertified = false;

  @Column(name = "interested_in")
  private String interestedIn = "";

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "user_company_user_types",
      joinColumns = @JoinColumn(name = "user_company_id"),
      inverseJoinColumns = @JoinColumn(name = "user_type_id"))
  private List<UserType> userTypes = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "userCompany", fetch = FetchType.LAZY)
  private List<User> users = new ArrayList<>();

  @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "company_owner_id", referencedColumnName = "user_id")
  private User owner;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "userCompany", fetch = FetchType.LAZY)
  private List<AbUser> abUsers = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "userCompany", fetch = FetchType.LAZY)
  private List<AbCompany> abCompanies = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "userCompany", fetch = FetchType.LAZY)
  private List<UserCompanyEquipment> equipments = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "userCompany", fetch = FetchType.LAZY)
  private List<Load> loads = new ArrayList<>();

  @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id", referencedColumnName = "user_company_id")
  private UserCompanySettings userCompanySettings;

  public boolean hasType(String userType) {
    if (getUserTypes() == null) {
      return false;
    }
    return getUserTypes().stream().anyMatch(ut -> ut.getUserType().equalsIgnoreCase(userType));
  }

  public boolean isSiteAdmin() {
    return hasType("admin");
  }

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "userCompany", fetch = FetchType.LAZY)
  private List<ApiKeyIntegrationPermission> apiKeyIntegrationPermissions = new ArrayList<>();

  public boolean hasIntegration(String integrationName) {
    return apiKeyIntegrationPermissions.stream()
        .anyMatch(apiKeyIntegrationPermission -> apiKeyIntegrationPermission.getApiKey().getAppName().equalsIgnoreCase(integrationName));
  }

}
