package com.bulkloads.web.rate.service.dto;

import java.time.LocalDate;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.BindParam;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@ParameterObject
public class RateSearchRequest {

  @Parameter(name = "hauled_date", description = "Search for rates hauled on this date")
  @BindParam("hauled_date")
  LocalDate hauledDate;

  @Parameter(name = "origin_lat", description = "Origin latitude")
  @BindParam("origin_lat")
  Double originLat;

  @Parameter(name = "origin_long", description = "Origin longitude")
  @BindParam("origin_long")
  Double originLong;

  @Parameter(name = "origin_radius", description = "Search radius in miles around origin coordinates")
  @BindParam("origin_radius")
  Double originRadius;

  @Parameter(name = "destination_lat", description = "Destination latitude")
  @BindParam("destination_lat")
  Double destinationLat;

  @Parameter(name = "destination_long", description = "Destination longitude")
  @BindParam("destination_long")
  Double destinationLong;

  @Parameter(name = "destination_radius", description = "Search radius in miles around destination coordinates")
  @BindParam("destination_radius")
  Double destinationRadius;
}