package com.bulkloads.web.rate.repository;

import java.util.Optional;
import com.bulkloads.web.rate.domain.entity.RateType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RateTypeRepository extends JpaRepository<RateType, String> {

  Optional<RateType> findByRateTypeAndIsWeightTrue(
      final String rateTypeKey
  );

}