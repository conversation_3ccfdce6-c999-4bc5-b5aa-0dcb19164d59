package com.bulkloads.web.offer.mapper;

import static java.util.Objects.nonNull;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.addressbook.abusergroup.domain.entity.AbUserGroup;
import com.bulkloads.web.addressbook.abusergroup.repository.AbUserGroupRepository;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.load.domain.template.DynamicLink;
import com.bulkloads.web.load.repository.LoadRepository;
import com.bulkloads.web.offer.domain.data.OfferData;
import com.bulkloads.web.offer.domain.data.OfferRecipientData;
import com.bulkloads.web.offer.domain.entity.Offer;
import com.bulkloads.web.offer.domain.entity.OfferRecipient;
import com.bulkloads.web.offer.domain.template.OfferTemplateModel;
import com.bulkloads.web.offer.service.dto.OfferRecipientRequest;
import com.bulkloads.web.offer.service.dto.OfferRequest;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class OfferMapper {

  @Autowired
  private LoadRepository loadRepository;
  @Autowired
  private AbUserGroupRepository abUserGroupRepository;
  
  @Mapping(source = "request.loadId", target = "load", qualifiedByName = "mapMyLoadById")
  public abstract OfferData requestToData(final OfferRequest request, @Context final Map<String, String> errors);

  @Mapping(target = "abUserGroup", source = "abUserGroupId", qualifiedByName = "mapAbUserGroupById")
  @Mapping(target = "abUser", source = "abUserId")
  @Mapping(target = "user", source = "userId")
  public abstract OfferRecipientData mapToOfferRecipientData(OfferRecipientRequest offerRecipientRequest, @Context final Map<String, String> errors);

  @Named("mapAbUserGroupById")
  protected AbUserGroup mapAbUserGroupById(Integer abUserGroupId) {
    if (abUserGroupId == null) {
      return null;
    }
    return abUserGroupRepository.findById(abUserGroupId).orElse(null);
  }

  public abstract List<OfferRecipientData> mapToOfferRecipientDataList(List<OfferRecipientRequest> recipientRequests,
                                                                       @Context final Map<String, String> errors);

  @Named("mapMyLoadById")
  protected Load mapMyLoadById(final Integer id, @Context final Map<String, String> errors) {
    if (nonNull(id)) {
      int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
      final Optional<Load> loadOpt = loadRepository.findByLoadIdAndUserCompanyUserCompanyId(id, userCompanyId);
      if (loadOpt.isPresent()) {
        return loadOpt.get();
      } else {
        errors.put("load", "You don't own this load");
      }
    }
    return null;
  }

  @Mapping(source = "offerRecipient.offerRecipientId", target = "offerRecipientId")
  @Mapping(source = "offerRecipient.offer.offerId", target = "offerId")
  @Mapping(source = "offerRecipient.offer.load.loadId", target = "loadId")
  @Mapping(source = "offerRecipient.offer.user.firstName", target = "fromFirstName")
  @Mapping(source = "offerRecipient.offer.user.lastName", target = "fromLastName")
  @Mapping(source = "offerRecipient.offer.user.userCompany.companyName", target = "fromCompanyName")
  @Mapping(source = "offerRecipient.offer.user.email", target = "fromEmail")
  @Mapping(source = "offerRecipient.offer.user.phone1", target = "fromPhone")
  @Mapping(source = "offerRecipient.offer.user.cellPhone", target = "fromCellPhone")
  @Mapping(source = "offerRecipient.offer.message", target = "message")
  @Mapping(source = "offerRecipient.offer.load.pickupAbCompany.city", target = "pickupCity")
  @Mapping(source = "offerRecipient.offer.load.pickupAbCompany.state", target = "pickupState")
  @Mapping(source = "offerRecipient.offer.load.dropAbCompany.city", target = "dropCity")
  @Mapping(source = "offerRecipient.offer.load.dropAbCompany.state", target = "dropState")
  @Mapping(source = "offerRecipient.offer.load.pickupAbCompany.companyName", target = "pickupCompanyName")
  @Mapping(source = "offerRecipient.offer.load.dropAbCompany.companyName", target = "dropCompanyName")
  @Mapping(source = "offerRecipient.offer.load.pickupDropMiles", target = "pickupDropMiles")
  @Mapping(source = "offerRecipient.offer.load.numberOfAvailableLoads", target = "numberOfAvailableLoads")
  @Mapping(source = "offerRecipient.offer.load.shipFrom", target = "shipFrom")
  @Mapping(source = "offerRecipient.offer.load.shipTo", target = "shipTo")
  @Mapping(source = "offerRecipient.offer.load.loCommodity", target = "loCommodity")
  @Mapping(source = "offerRecipient.offer.offerRate", target = "rate")
  @Mapping(source = "offerRecipient.offer.offerRateType.rateTypeTextMedium", target = "rateTypeTextMedium")
  @Mapping(source = "dynamicLink", target = "link")
  public abstract OfferTemplateModel entityToTemplateModel(OfferRecipient offerRecipient, DynamicLink dynamicLink);

  @Mapping(source = "recipients", target = "recipients", ignore = true)
  public abstract void dataToEntity(final OfferData data, @MappingTarget final Offer entity);

}