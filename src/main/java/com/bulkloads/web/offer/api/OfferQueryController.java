package com.bulkloads.web.offer.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.Arrays;
import java.util.List;
import com.bulkloads.web.offer.api.dto.CarrierOfferResponse;
import com.bulkloads.web.offer.api.dto.SentOfferResponse;
import com.bulkloads.web.offer.service.OfferService;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/offers", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class OfferQueryController {

  private final OfferService offerService;

  @Operation(summary = "Get sent offers")
  @GetMapping("/sent")
  @Tag(name = "Offers (Shipper)")
  public List<SentOfferResponse> getSentOffers(
      @Parameter(description = "Load ID")
      @RequestParam(value = "load_id", required = true)
      Integer loadId,

      @Parameter(description = "Order by. Values: 'Sent Date 1-9', 'Accepted Date 1-9'")
      @RequestParam(value = "order", required = false)
      String order,

      @Parameter(description = "Offer status filter (deprecated - will only return 'Sent' status)")
      @RequestParam(value = "offer_status", required = false)
      List<String> offerStatus,

      @Parameter(description = "Number of records to skip")
      @RequestParam(value = "skip", defaultValue = "0")
      int skip,

      @Parameter(description = "Number of records to return")
      @RequestParam(value = "limit", defaultValue = "100")
      int limit) {

    return offerService.getSentOffers(loadId, order, offerStatus, skip, limit);
  }

  @Operation(summary = "Get carrier offers")
  @GetMapping
  @Tag(name = "Offers (Carrier)")
  public List<CarrierOfferResponse> getCarrierOffers(
      @Parameter(description = "Offer status filter (sent|accepted|dismissed|booked|rejected|deleted)")
      @RequestParam(value = "offer_status", required = false)
      List<String> offerStatus,

      @Parameter(description = "Filter for only offered loads (same as offer_status:'sent,accepted')")
      @RequestParam(value = "only_offered_loads", required = false, defaultValue = "false")
      Boolean onlyOfferedLoads,

      @Parameter(description = "Filter for only past offered loads (same as offer_status:'dismissed|booked|rejected|deleted')")
      @RequestParam(value = "only_past_offered_loads", required = false, defaultValue = "false")
      Boolean onlyPastOfferedLoads,

      @Parameter(description = "Order by")
      @RequestParam(value = "order", required = false)
      String order,

      @Parameter(description = "Number of records to skip")
      @RequestParam(value = "skip", defaultValue = "0")
      int skip,

      @Parameter(description = "Number of records to return")
      @RequestParam(value = "limit", defaultValue = "100")
      int limit) {

    // Handle offer status filters
    List<String> effectiveOfferStatus = offerStatus;
    if (onlyOfferedLoads) {
      effectiveOfferStatus = Arrays.asList("sent", "accepted");
    } else if (onlyPastOfferedLoads) {
      effectiveOfferStatus = Arrays.asList("dismissed", "booked", "rejected", "deleted");
    }

    return offerService.getCarrierOffers(effectiveOfferStatus, order, skip, limit);
  }
}
