package com.bulkloads.web.offer.service.dto;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class OfferRequest {

  @Schema(description = "The load id", example = "1")
  Integer loadId;

  @Schema(description = "The subject", example = "subject")
  String subject;

  @Schema(description = "The message", example = "message")
  String message;

  @Schema(description = "The reply to email", example = "replytoEmail")
  String replytoEmail;

  @Schema(description = "The offer rate", example = "1.0")
  BigDecimal offerRate;

  @Schema(description = "The offer rate type", example = "offerRateType")
  String offerRateType;

  @Schema(description = "The allow auto booking", example = "true")
  Boolean allowAutoBooking;

  @Schema(description = "The list of recipients")
  List<OfferRecipientRequest> recipients;

}
