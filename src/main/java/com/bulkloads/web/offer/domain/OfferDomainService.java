package com.bulkloads.web.offer.domain;

import static com.bulkloads.common.StringUtil.rateFormat;
import static com.bulkloads.common.validation.ValidationUtils.exists;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isValidEmail;
import static com.bulkloads.config.AppConstants.RateType.FLAT;
import static com.bulkloads.config.AppConstants.RateType.GALLON;
import static com.bulkloads.config.AppConstants.RateType.HOUR;
import static com.bulkloads.config.AppConstants.RateType.LITER;
import static com.bulkloads.config.AppConstants.RateType.MILE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.config.AppConstants;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abusergroup.domain.entity.AbUserGroup;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.offer.domain.data.OfferData;
import com.bulkloads.web.offer.domain.data.OfferRecipientData;
import com.bulkloads.web.offer.domain.entity.LoadOffer;
import com.bulkloads.web.offer.domain.entity.Offer;
import com.bulkloads.web.offer.domain.entity.OfferRecipient;
import com.bulkloads.web.offer.mapper.OfferMapper;
import com.bulkloads.web.offer.repository.LoadOfferRepository;
import com.bulkloads.web.rate.repository.RateTypeRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;

@Slf4j
@Component
@RequiredArgsConstructor
public class OfferDomainService extends BaseDomainService<Offer> {

  private final UserService userService;
  private final LoadOfferRepository loadOfferRepository;
  private final OfferMapper offerMapper;
  private final RateTypeRepository rateTypeRepository;

  public Result<Offer> create(OfferData data) {
    return validate(new Offer(), null, data, ValidationMethod.CREATE);
  }

  @Override
  public void validateDataAndMapToEntity(final Result<Offer> result, final Offer entity, final Offer existing, final Object dataObject,
                                         final ValidationMethod method) {

    final OfferData data = (OfferData) dataObject;

    final int userId = UserUtil.getUserIdOrThrow();
    final User user = userService.findById(userId);
    final UserCompany userCompany = user.getUserCompany();
    final int userCompanyId = userCompany.getUserCompanyId();

    final Load load = data.getLoad();

    // set defaults
    if (isNull(data.getAllowAutoBooking())) {
      data.setAllowAutoBooking(false);
    }
    if (isNull(data.getOfferRateType())) {
      data.setOfferRateType(rateTypeRepository.findById(AppConstants.RateType.TWO_K).orElseThrow());
    }

    final Instant now = Instant.now();

    entity.setOfferDate(now);
    entity.setUser(user);

    // Enter at least one recipient
    if (data.getRecipients().isEmpty()) {
      result.addError("recipients", "Enter at least one recipient");
    }

    // iterate over ab_user_id entries first and then over user_id entries
    // loop over recipients
    List<OfferRecipient> recipients = new ArrayList<>();
    List<Integer> abUserIds = new ArrayList<>();
    List<Integer> userIds = new ArrayList<>();
    // make the recipients an object

    boolean hasLeadRecipients = false;

    for (OfferRecipientData recipientData : data.getRecipients()) {

      AbUser abUser = recipientData.getAbUser();
      User toUser = recipientData.getUser();
      AbUserGroup abUserGroup = recipientData.getAbUserGroup();

      boolean autoBook = data.getAllowAutoBooking();
      // has lead recipients
      if (isNull(abUser) && isNull(abUserGroup)) {
        hasLeadRecipients = true;
        // leads cannot auto book. only ab_users can auto book
        autoBook = false;
      }

      // case 1: abUser
      if (nonNull(abUser)) {
        if (abUser.getUserCompany().getUserCompanyId().equals(userCompanyId)) {

          // check if ab_user_id is unique
          if (!abUserIds.contains(abUser.getAbUserId())) {
            // add ab_user_id to unique list
            abUserIds.add(abUser.getAbUserId());

            OfferRecipient newRecipient = buildRecipient(entity, abUser, userIds, data);

            if (nonNull(newRecipient)) {
              recipients.add(newRecipient);
            }
          } else {
            result.addError("recipients", "The recipient " + abUser.getAbUserId() + " was not found in your address book");
          }
        }
      }

      // case 2: user
      if (nonNull(toUser)) {
        // check if user_id is unique
        if (!userIds.contains(toUser.getUserId())) {
          // add user_id to unique list
          userIds.add(toUser.getUserId());

          // create new recipient
          OfferRecipient newRecipient = new OfferRecipient();

          // add user_id to unique list
          newRecipient.setBlUser(toUser);
          newRecipient.setFirstName(toUser.getFirstName());
          newRecipient.setLastName(toUser.getLastName());
          newRecipient.setEmail(toUser.getEmail());
          newRecipient.setPhone1(toUser.getPhone1());
          newRecipient.setCompanyName(toUser.getUserCompany().getCompanyName());
          newRecipient.setOffer(entity);
          newRecipient.setAllowAutoBooking(autoBook);
          newRecipient.setMessageSentDate(Instant.now());
          recipients.add(newRecipient);

        } else {
          result.addError("recipients", "The recipient " + toUser.getUserId() + " was not found");
        }
      }

      // case 3: abUserGroup
      if (nonNull(abUserGroup)) {
        if (abUserGroup.getUserCompany().getUserCompanyId().equals(userCompanyId)) {
          // get all ab_users in the group
          List<AbUser> abUsers = abUserGroup.getAbUsers();

          for (AbUser oneAbUser : abUsers) {
            // check if ab_user_id is unique
            if (!abUserIds.contains(oneAbUser.getAbUserId())) {
              // add ab_user_id to unique list
              abUserIds.add(oneAbUser.getAbUserId());

              // create new recipient
              OfferRecipient newRecipient = buildRecipient(entity, oneAbUser, userIds, data);

              if (nonNull(newRecipient)) {
                // Set the abUserGroup for the recipient
                newRecipient.setAbUserGroup(abUserGroup);
                recipients.add(newRecipient);
              }
            }
          }
        } else {
          result.addError("recipients", "The group " + abUserGroup.getAbUserGroupId() + " was not found");
        }
      }
    }

    entity.setRecipients(recipients);

    // validate fields for Leads
    if (hasLeadRecipients) {
      if (isEmpty(data.getSubject())) {
        result.addError("subject", "Field is required");
      }
      if (isEmpty(data.getMessage())) {
        result.addError("message", "Field is required");
      }
      if (isEmpty(data.getReplytoEmail())) {
        result.addError("replyto_email", "Field is required");
      }
    }

    if (isEmpty(data.getReplytoEmail())) {
      data.setReplytoEmail(user.getEmail());
    }

    if (!isEmpty(data.getReplytoEmail()) && !isValidEmail(data.getReplytoEmail())) {
      result.addError("replyto_email", "Enter a valid reply-to email");
    }

    // rate is required for PRIVATE OFFERS (optional for leads)
    if (data.getRecipients().size() > 0 && !hasLeadRecipients && isNull(data.getOfferRate())) {
      result.addError("offer_rate", "The offer rate is required");
    }

    if (nonNull(data.getOfferRate())) {
      if (data.getOfferRate().compareTo(BigDecimal.ZERO) < 1 || data.getOfferRate().compareTo(new BigDecimal("31000")) > 0) {
        result.addError("offer_rate", "Enter a valid rate");
      }

    }

    // load validation
    if (nonNull(data.getLoad()) && data.getLoad().getNumberOfAvailableLoads() < 1) {
      result.addError("load_id", "No loads are available to be offered");
    }

    if (isEmpty(data.getReplytoEmail())) {
      data.setReplytoEmail(user.getEmail());
    }

    if (isEmpty(data.getSubject())) {
      String rateMessage = rateFormat(data.getOfferRate(), data.getOfferRateType().getRateTypeTextMedium());
      data.setSubject("Load Offer " + data.getLoad().getOriginCity()
          + ", " + data.getLoad().getOriginState() + " to " + data.getLoad().getDestinationCity()
          + ", " + data.getLoad().getDestinationState() + " " + rateMessage);
    }

    // <!--- check if the user has already accepted all the available loads, and if so don't send offer --->
  }

  private OfferRecipient buildRecipient(Offer entity, AbUser abUser, List<Integer> userIds, OfferData data) {
    OfferRecipient newRecipient = new OfferRecipient();
    newRecipient.setOffer(entity);
    newRecipient.setAbUser(abUser);
    newRecipient.setAllowAutoBooking(data.getAllowAutoBooking()); // Add this line
    
    // Set the abUserGroup if it exists in the data
    if (data.getRecipients() != null && !data.getRecipients().isEmpty()) {
      OfferRecipientData recipientData = data.getRecipients().get(0);
      if (recipientData.getAbUserGroup() != null) {
        newRecipient.setAbUserGroup(recipientData.getAbUserGroup());
      }
    }
    
    User blUser = abUser.getBlUser();
    if (exists(blUser)) {
      if (userIds.contains(blUser.getUserId())) {
        return null;
      } else {
        userIds.add(blUser.getUserId());
        newRecipient.setBlUser(blUser);
      }
    }
    
    // Copy other necessary fields from abUser
    newRecipient.setFirstName(abUser.getFirstName());
    newRecipient.setLastName(abUser.getLastName());
    newRecipient.setEmail(abUser.getEmail());
    newRecipient.setPhone1(abUser.getPhone1());
    newRecipient.setCompanyName(abUser.getAbCompany().getCompanyName());
    newRecipient.setMessageSentDate(Instant.now()); // Also ensure this is set
    
    return newRecipient;
  }

  // calculate offerRatePerMile

  private BigDecimal calculateRatePerMile(final @NotNull Offer entity, final Result<Offer> result) {

    BigDecimal ratePerMile = null;

    if (nonNull(entity.getOfferRate())) {

      if (isNull(entity.getLoad().getLoEstimatedWeight()) || nonNull(entity.getLoad().getLoEstimatedVolume())
          || nonNull(entity.getLoad().getEstimatedMiles()) || nonNull(entity.getLoad().getLoEstHours())) {

        BigDecimal estQuantity = null;

        if (entity.getOfferRate().compareTo(BigDecimal.ZERO) <= 0 || entity.getOfferRate().compareTo(new BigDecimal("31000")) > 0) {
          result.addError("rate", "Enter a valid number");
        }

        // add gallons and litters

        String rateType = entity.getOfferRateType().getRateType();

        if (entity.getOfferRateType().getIsWeight()) {
          if (!nonNull(entity.getLoad().getLoEstimatedWeight()) || entity.getLoad().getLoEstimatedWeight().compareTo(BigDecimal.ZERO) <= 0
              || entity.getLoad().getLoEstimatedWeight().compareTo(new BigDecimal("130000")) > 0) {
            result.addError("est_weight", "Enter a valid number");
          } else {
            estQuantity = entity.getLoad().getLoEstimatedWeight()
                .divide(new BigDecimal(entity.getOfferRateType().getRateType()), 6, RoundingMode.HALF_UP);
          }
        } else if (MILE.equals(rateType)) {
          if (!nonNull(entity.getLoad().getEstimatedMiles()) || entity.getLoad().getEstimatedMiles().compareTo(BigDecimal.ZERO) <= 0
              || entity.getLoad().getEstimatedMiles().compareTo(new BigDecimal("10000")) > 0) {
            result.addError("est_miles", "Enter a valid number");
          } else {
            estQuantity = entity.getLoad().getEstimatedMiles();
          }
        } else if (HOUR.equals(rateType)) {
          if (!nonNull(entity.getLoad().getLoEstHours()) || entity.getLoad().getLoEstHours().compareTo(BigDecimal.ZERO) <= 0
              || entity.getLoad().getLoEstHours().compareTo(new BigDecimal("1000")) > 0) {
            result.addError("est_hours", "Enter a valid number");
          } else {
            estQuantity = entity.getLoad().getLoEstHours();
          }
        } else if (FLAT.equals(rateType)) {
          estQuantity = BigDecimal.ONE;
        } else if (GALLON.equals(rateType)) {
          if (nonNull(entity.getLoad().getLoEstimatedVolume()) || entity.getLoad().getLoEstimatedVolume().compareTo(BigDecimal.ZERO) <= 0
              || entity.getLoad().getLoEstimatedVolume().compareTo(new BigDecimal("20000")) > 0) {
            result.addError("est_volume", "Enter a valid number");
          } else {
            estQuantity = entity.getLoad().getLoEstimatedVolume();
          }
        } else if (LITER.equals(rateType)) {
          if (nonNull(entity.getLoad().getLoEstimatedVolume()) || entity.getLoad().getLoEstimatedVolume().compareTo(BigDecimal.ZERO) <= 0
              || entity.getLoad().getLoEstimatedVolume().compareTo(new BigDecimal("80000")) > 0) {
            result.addError("est_volume", "Enter a valid number");
          } else {
            estQuantity = entity.getLoad().getLoEstimatedVolume();
          }
        } else {
          result.addError("rate_type", "Unknown rate type");
        }

        if (nonNull(estQuantity)) {
          BigDecimal estTotal = entity.getOfferRate().multiply(estQuantity).setScale(2, RoundingMode.HALF_UP);

          ratePerMile = BigDecimal.ZERO;

          if (nonNull(entity.getLoad().getEstimatedMiles()) && entity.getLoad().getEstimatedMiles().compareTo(BigDecimal.ZERO) > 0) {
            ratePerMile = estTotal.divide(entity.getLoad().getEstimatedMiles(), 2, RoundingMode.HALF_UP);
          }
        }

      }
    }

    return ratePerMile;
  }

  @Override
  public void mapToEntityAuto(Object data, final Offer entity) {
    offerMapper.dataToEntity((OfferData) data, entity);
  }

  @Override
  public void validateEntity(final Result<Offer> result, final @NotNull Offer entity) {

    final int userId = UserUtil.getUserIdOrThrow();
    final User user = userService.findById(userId);
    final Instant now = Instant.now();

    final List<OfferRecipient> newRecipients = new ArrayList<>();

    // calculate
    // calculate offer_rate_per_mile
    BigDecimal ratePerMile = null;
    if (nonNull(entity.getOfferRate())) {
      ratePerMile = calculateRatePerMile(entity, result);
    }
    entity.setOfferRatePerMile(ratePerMile);

    // exclude recipients that have gotten the same offer rate for the same load
    List<OfferRecipient> recipients = entity.getRecipients();

    for (OfferRecipient recipient : recipients) {
      final Integer loadId = entity.getLoad().getLoadId();
      final Integer abUserId = isNull(recipient.getAbUser()) ? null : recipient.getAbUser().getAbUserId();
      final Integer blUserId = isNull(recipient.getBlUser()) ? null : recipient.getBlUser().getUserId();

      List<LoadOffer> recipientExistingOffers =
          loadOfferRepository.findExistingOffers(loadId, recipient.getOffer().getAllowAutoBooking(), recipient.getOffer().getOfferRate(),
              recipient.getOffer().getOfferRateType().getRateType(), abUserId, blUserId);

      // if the current recipient have NOT gotten the same offer rate for the same load
      if (!isEmpty(recipientExistingOffers)) {
        // recipients.remove(recipient);
      } else {
        // if there are already accepted loads, use it in the insert
        final Integer totalAcceptedLoads = loadOfferRepository.getAcceptedLoadsSum(loadId, abUserId, blUserId);

        recipient.setAlreadyAcceptedLoads(totalAcceptedLoads);

        // check if the user has already accepted all the available loads, and if so don't send offer
        if (totalAcceptedLoads >= entity.getLoad().getNumberOfAvailableLoads()) {
          // recipients.remove(recipient);
        } else {

          List<LoadOffer> existingLoadOffers = loadOfferRepository.getSentAndDismissedLoadOffers(loadId, abUserId, blUserId);

          for (LoadOffer existingLoadOffer : existingLoadOffers) {
            existingLoadOffer.setOfferStatus("Deleted");
            OfferRecipient existingOfferRecipient = existingLoadOffer.getOfferRecipient();
            existingOfferRecipient.setOfferStatus("Deleted");
            existingOfferRecipient.setDeletedByUserId(userId);
            existingOfferRecipient.setDeletedDate(now);
          }

          // load_id, message, user_id, ab_user_id, offer_rate, offer_rate_type, offer_rate_per_mile, offer_date,
          // offer_id, offer_status, offer_recipient_id, allow_auto_booking
          LoadOffer loadOffer = new LoadOffer();

          loadOffer.setLoad(recipient.getOffer().getLoad());
          loadOffer.setMessage(recipient.getOffer().getMessage());
          loadOffer.setUser(recipient.getBlUser());
          loadOffer.setAbUser(recipient.getAbUser());
          loadOffer.setOfferRate(recipient.getOffer().getOfferRate());
          loadOffer.setOfferRateType(recipient.getOffer().getOfferRateType());
          loadOffer.setOfferRatePerMile(ratePerMile);
          loadOffer.setOfferDate(now);
          loadOffer.setOffer(entity);
          loadOffer.setOfferStatus(recipient.getOfferStatus());
          loadOffer.setAllowAutoBooking(recipient.getAllowAutoBooking());
          loadOffer.setOfferRecipient(recipient);
          recipient.setLoadOffers(new ArrayList<>(List.of(loadOffer)));

          newRecipients.add(recipient);

        }

      }

    }

    entity.setRecipients(newRecipients);

  }
}