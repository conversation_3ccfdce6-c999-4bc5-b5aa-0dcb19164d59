package com.bulkloads.web.quickbooks.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.quickbooks.domain.entity.QbLoadAssignmentSurchargeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface QbLoadAssignmentSurchargeTypeRepository extends JpaRepository<QbLoadAssignmentSurchargeType, Integer> {

  List<QbLoadAssignmentSurchargeType> findAllByUserCompanyUserCompanyId(final int userCompanyId);

  Optional<QbLoadAssignmentSurchargeType> findByUserCompanyUserCompanyIdAndChargeType(
      final int userCompanyId,
      final String chargeType);

  Optional<QbLoadAssignmentSurchargeType> findByUserCompanyUserCompanyIdAndSurchargeTypeId(
      final int userCompanyId,
      final int surchargeTypeId);

  @Query(value = """
      select
          st.load_assignment_surcharge_type_id as surchargeTypeId,
          st.load_assignment_surcharge_type as surchargeType,
          qb.qb_item_id as qbItemId
      from load_assignment_surcharge_types st
      left join qb_load_assignment_surcharge_types qb on st.load_assignment_surcharge_type_id = qb.load_assignment_surcharge_type_id
          and qb.user_company_id = :userCompanyId
      union
      select
          0 as surchargeTypeId,
          'Freight' as surchargeType,
          qb.qb_item_id as qbItemId
      from qb_load_assignment_surcharge_types qb
      where charge_type = 'freight'
          and qb.user_company_id = :userCompanyId order by surchargeTypeId
      """, nativeQuery = true)
   List<QbItemSurchargeTypeProjection> findMappings(@Param("userCompanyId") final int userCompanyId);
}
