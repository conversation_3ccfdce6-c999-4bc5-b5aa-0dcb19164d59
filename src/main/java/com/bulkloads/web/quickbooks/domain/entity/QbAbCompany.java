package com.bulkloads.web.quickbooks.domain.entity;

import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MapsId;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "qb_ab_companies")
public class QbAbCompany {

  @Id
  @Column(name = "ab_company_id")
  private Integer abCompanyId;

  @OneToOne
  @MapsId
  @JoinColumn(name = "ab_company_id")
  private AbCompany abCompany;

  @Column(name = "qb_customer_id")
  private String qbCustomerId;

  @Column(name = "qb_vendor_id")
  private String qbVendorId;
}
