package com.bulkloads.web.quickbooks.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.infra.quickbooks.dto.BillResponse;
import com.bulkloads.web.infra.quickbooks.dto.InvoiceBillItemResponse;
import com.bulkloads.web.infra.quickbooks.dto.InvoiceResponse;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoice;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoiceItem;
import com.bulkloads.web.quickbooks.domain.entity.QbAbCompany;
import com.bulkloads.web.quickbooks.domain.entity.QbBill;
import com.bulkloads.web.quickbooks.domain.entity.QbInvoice;
import com.bulkloads.web.quickbooks.repository.QbItemSurchargeTypeProjection;
import org.springframework.stereotype.Component;

@Component
public class QbInvoiceBillMapper {

  public List<BillResponse> mapToBills(final List<LoadInvoice> invoices,
                                          final Map<Integer, QbItemSurchargeTypeProjection> qbItemSurchargeTypeBySurchargeTypeId,
                                          final Map<Integer, QbAbCompany> qbMappingsByAbCompanyId) {
    return invoices.stream()
        .map(loadInvoice -> mapToBill(loadInvoice, qbMappingsByAbCompanyId, qbItemSurchargeTypeBySurchargeTypeId))
        .toList();
  }

  public List<InvoiceResponse> mapToInvoices(final List<LoadInvoice> invoices,
                                             final Map<Integer, QbItemSurchargeTypeProjection> qbItemSurchargeTypeBySurchargeTypeId,
                                             final Map<Integer, QbAbCompany> qbMappingsByAbCompanyId) {
    return invoices.stream()
        .map(loadInvoice -> mapToInvoice(loadInvoice, qbMappingsByAbCompanyId, qbItemSurchargeTypeBySurchargeTypeId))
        .toList();
  }

  private BillResponse mapToBill(final LoadInvoice loadInvoice,
                                    final Map<Integer, QbAbCompany> qbCustomerIdByAbCompanyId,
                                    final Map<Integer, QbItemSurchargeTypeProjection> qbItemSurchargeTypeBySurchargeTypeId) {
    final Optional<QbBill> qbBill = Optional.ofNullable(loadInvoice.getQbBill());
    final List<LoadInvoiceItem> loadInvoiceItems = loadInvoice.getLoadInvoiceItems();
    final String companyName = loadInvoice.getBillToCompanyName();
    final BigDecimal invoiceTotal = loadInvoice.getInvoiceTotal();
    final String billDescription = loadInvoice.getBillDescription();
    final Optional<QbAbCompany> qbAbCompany = Optional.ofNullable(qbCustomerIdByAbCompanyId.get(loadInvoice.getBillToAbCompanyId()));
    final List<InvoiceBillItemResponse> invoiceBillItemResponse = loadInvoiceItems.stream()
        .map(loadInvoiceItem -> mapLoadInvoiceItem(loadInvoiceItem, qbItemSurchargeTypeBySurchargeTypeId))
        .toList();
    return new BillResponse(companyName,
                            invoiceTotal,
                            billDescription,
                            qbAbCompany.map(QbAbCompany::getQbCustomerId).orElse(null),
                            qbBill.map(QbBill::getQbBillId).orElse(null),
                            invoiceBillItemResponse);
  }

  private InvoiceResponse mapToInvoice(final LoadInvoice loadInvoice,
                                       final Map<Integer, QbAbCompany> qbCustomerIdByAbCompanyId,
                                       final Map<Integer, QbItemSurchargeTypeProjection> qbItemSurchargeTypeBySurchargeTypeId) {
    final Optional<QbInvoice> qbInvoice = Optional.ofNullable(loadInvoice.getQbInvoice());
    final List<LoadInvoiceItem> loadInvoiceItems = loadInvoice.getLoadInvoiceItems();
    final String companyName = loadInvoice.getBillToCompanyName();
    final BigDecimal invoiceTotal = loadInvoice.getInvoiceTotal();
    final String billDescription = loadInvoice.getBillDescription();
    final Optional<QbAbCompany> qbAbCompany = Optional.ofNullable(qbCustomerIdByAbCompanyId.get(loadInvoice.getBillToAbCompanyId()));
    final List<InvoiceBillItemResponse> invoiceBillItemResponse = loadInvoiceItems.stream()
        .map(loadInvoiceItem -> mapLoadInvoiceItem(loadInvoiceItem, qbItemSurchargeTypeBySurchargeTypeId))
        .toList();
    return new InvoiceResponse(companyName,
                               invoiceTotal,
                               billDescription,
                               qbAbCompany.map(QbAbCompany::getQbCustomerId).orElse(null),
                               qbInvoice.map(QbInvoice::getQbInvoiceId).orElse(null),
                               invoiceBillItemResponse);
  }

  private InvoiceBillItemResponse mapLoadInvoiceItem(final LoadInvoiceItem loadInvoiceItem,
                                                     final Map<Integer, QbItemSurchargeTypeProjection> qbItemSurchargeTypeBySurchargeTypeId) {
    final Assignment loadAssignment = loadInvoiceItem.getLoadAssignment();
    final Integer loadAssignmentId = loadAssignment.getLoadAssignmentId();
    final String loadAssignmentNumber = loadInvoiceItem.getLoadAssignmentNumber();
    final LocalDate hauledDate = loadInvoiceItem.getHauledDate();
    final BigDecimal itemAmount = loadInvoiceItem.getItemAmount();

    final String pickupCity = loadInvoiceItem.getPickupCity();
    final String pickupState = loadInvoiceItem.getPickupState();
    final String dropCity = loadInvoiceItem.getDropCity();
    final String dropState = loadInvoiceItem.getDropState();
    final Boolean isSurcharge = loadInvoiceItem.getIsSurcharge();

    final Optional<QbItemSurchargeTypeProjection> qbItemSurchargeType = getQbItemSurchargeType(
        loadInvoiceItem, qbItemSurchargeTypeBySurchargeTypeId);

    return new InvoiceBillItemResponse(
        loadAssignmentId,
        loadAssignmentNumber,
        hauledDate,
        itemAmount,
        pickupCity,
        pickupState,
        dropCity,
        dropState,
        isSurcharge,
        qbItemSurchargeType.map(QbItemSurchargeTypeProjection::getSurchargeType).orElse(null),
        qbItemSurchargeType.map(QbItemSurchargeTypeProjection::getQbItemId).orElse(null));
  }

  private Optional<QbItemSurchargeTypeProjection> getQbItemSurchargeType(
      final LoadInvoiceItem loadInvoiceItem,
      final Map<Integer, QbItemSurchargeTypeProjection> qbItemSurchargeTypeBySurchargeTypeId) {

    if (Boolean.TRUE.equals(loadInvoiceItem.getIsSurcharge())) {
      return Optional.of(qbItemSurchargeTypeBySurchargeTypeId.get(loadInvoiceItem.getLoadAssignmentSurchargeTypeId()));
    }
    return Optional.empty();
  }
}
