package com.bulkloads.web.quickbooks.mapper;

import java.util.List;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.quickbooks.api.dto.QbAbCompanyResponse;
import com.bulkloads.web.quickbooks.repository.QbAbCompanyProjection;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    uses = CommonMapper.class)
public abstract class QbAbCompanyMapper {

  public abstract QbAbCompanyResponse map(final QbAbCompanyProjection entity);

  public List<QbAbCompanyResponse> map(final List<QbAbCompanyProjection> entities) {
    return entities.stream().map(this::map).toList();
  }
}
