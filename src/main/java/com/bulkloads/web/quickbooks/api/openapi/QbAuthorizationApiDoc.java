package com.bulkloads.web.quickbooks.api.openapi;

import java.io.IOException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Tag(name = "QuickBooks Authorization")
public interface QbAuthorizationApiDoc {

  @Operation(summary = "Authorize with QuickBooks")
  void authorizeProvider(final HttpServletRequest request,
                         final HttpServletResponse response) throws IOException;

  @Operation(summary = "Remove QuickBooks authorization")
  void deleteAuthorization();
}
