package com.bulkloads.web.companyequipment.domain.entity;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

@Getter
public enum EquipmentType {

  truck,
  trailer;

  @Override
  public String toString() {
    return this.name();
  }

  @JsonCreator
  public static EquipmentType fromString(String type) {
    for (EquipmentType equipmentType : EquipmentType.values()) {
      if (equipmentType.name().equalsIgnoreCase(type)) {
        return equipmentType;
      }
    }
    throw new IllegalArgumentException("No constant with text " + type + " found");
  }
}
