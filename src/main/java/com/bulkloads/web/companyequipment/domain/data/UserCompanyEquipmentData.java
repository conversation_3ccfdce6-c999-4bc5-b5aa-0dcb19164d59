package com.bulkloads.web.companyequipment.domain.data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.companyequipment.domain.entity.EquipmentStatus;
import com.bulkloads.web.companyequipment.domain.entity.EquipmentType;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.user.domain.entity.User;
import lombok.Data;

@Data
public class UserCompanyEquipmentData {

  private Optional<EquipmentType> equipmentType;
  private Optional<String> externalEquipmentId;
  private Optional<String> make;
  private Optional<String> model;
  private Optional<String> trailerType;
  private Optional<String> year;
  private Optional<String> vin;
  private Optional<BigDecimal> value;
  private Optional<Integer> estimatedMileage;
  private Optional<String> licensePlateNumber;
  private Optional<LocalDate> licensePlateExpirationDate;
  private Optional<LocalDate> dotExpirationDate;
  private Optional<LocalDate> insuranceExpirationDate;
  private Optional<String> insuranceCompany;
  private Optional<LocalDate> inspectionExpirationDate;
  private Optional<LocalDate> registrationExpirationDate;
  private Optional<String> notes;
  private Optional<EquipmentStatus> status;
  private Optional<User> defaultAssignedUser;
  private Optional<AbUser> defaultAssignedAbUser;

  private Optional<List<File>> files;
}
