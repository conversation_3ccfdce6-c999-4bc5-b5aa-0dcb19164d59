package com.bulkloads.web.companyequipment.service.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import com.bulkloads.web.file.service.dto.FileResponse;
import lombok.Data;

@Data
public class UserCompanyEquipmentLogResponse {

  private int userCompanyEquipmentLogId;
  private String logType;
  private LocalDate logDate;
  private Integer mileage;
  private String notes;
  private BigDecimal expense;

  List<FileResponse> files;
}
