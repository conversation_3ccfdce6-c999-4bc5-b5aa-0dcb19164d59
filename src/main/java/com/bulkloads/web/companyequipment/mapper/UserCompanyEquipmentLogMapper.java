package com.bulkloads.web.companyequipment.mapper;

import static com.bulkloads.common.Converters.asList;
import java.util.List;
import com.bulkloads.web.companyequipment.domain.data.UserCompanyEquipmentLogData;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentLog;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentLogFile;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogRequest;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogResponse;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.file.service.dto.FileResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class UserCompanyEquipmentLogMapper {

  public abstract UserCompanyEquipmentLogData requestToData(UserCompanyEquipmentLogRequest request);

  @Mapping(source = "userCompanyEquipmentLogFiles", target = "files")
  public abstract UserCompanyEquipmentLogResponse entityToResponse(final UserCompanyEquipmentLog e);

  public abstract void dataToEntity(UserCompanyEquipmentLogData data, @MappingTarget UserCompanyEquipmentLog e);

  @Mapping(target = "userCompanyEquipmentLogFiles", ignore = true)
  public abstract UserCompanyEquipmentLog saveExisting(UserCompanyEquipmentLog e);

  public List<FileResponse> mapFiles(final List<UserCompanyEquipmentLogFile> files) {
    if (files == null) {
      return null;
    }
    return files.stream()
        .filter(e -> !e.getDeleted())
        .map(f -> FileResponse.fromFile(f.getFile()))
        .collect(asList());
  }

}
