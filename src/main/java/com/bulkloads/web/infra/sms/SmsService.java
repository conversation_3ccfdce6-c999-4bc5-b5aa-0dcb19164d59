package com.bulkloads.web.infra.sms;

import static com.bulkloads.web.infra.messaging.consumer.SmsQueueConsumer.ERROR_CODE;
import static java.util.Objects.isNull;

import java.util.Optional;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import com.bulkloads.web.infra.sms.domain.SmsDetails;
import com.bulkloads.web.infra.sms.dto.SmsDto;
import com.bulkloads.web.infra.sms.entity.SmsLogRepository;
import com.bulkloads.web.infra.sms.repository.SmsLog;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Validated
@Transactional
public class SmsService {

  private final MessageQueueSender queueSender;
  private final SmsLogRepository smsLogRepository;
  private final String smsQueueName;
  private final String twilioFromNumber;

  public SmsService(final MessageQueueSender queueSender,
                    final SmsLogRepository smsLogRepository,
                    final AppProperties appProperties) {
    this.queueSender = queueSender;
    this.smsLogRepository = smsLogRepository;
    this.smsQueueName = appProperties.getSms().getQueueName();
    this.twilioFromNumber = appProperties.getTwilio().getFromNumber();
  }

  public Optional<Integer> sendSms(@Valid final SmsDetails smsDetails) {
    final SmsLog smsLog = mapToSmsLog(smsDetails);
    final int id = smsLogRepository.save(smsLog).getId();
    sendToQueue(id);
    return Optional.of(id);
  }

  private SmsLog mapToSmsLog(final SmsDetails smsDetails) {
    final String fromNumber = getFromNumber(smsDetails);
    final String toUserNumber = smsDetails.getToUserNumber();
    final String textMessage = smsDetails.getMessage();

    final SmsLog smsLog = new SmsLog();
    smsLog.setFromUserId(smsDetails.getFromUserId());
    smsLog.setFromPhoneNumber(fromNumber);
    smsLog.setToUserId(smsDetails.getToUserId());
    smsLog.setToPhoneNumber(toUserNumber);
    smsLog.setMessage(textMessage);
    smsLog.setErrorCode(ERROR_CODE);
    return smsLog;
  }

  private String getFromNumber(final SmsDetails smsDetails) {
    return isNull(smsDetails.getFromUserNumber()) ? twilioFromNumber : smsDetails.getFromUserNumber();
  }

  private void sendToQueue(final int smsLogId) {
    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
      @Override
      public void afterCommit() {
        final SmsDto smsDto = SmsDto.builder().smsLogId(smsLogId).build();
        queueSender.send(smsQueueName, smsDto);
      }
    });
  }
}
