package com.bulkloads.web.infra.sms.repository;

import java.math.BigDecimal;
import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "twilio_sms_log")
public class SmsLog {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "twilio_sms_log_id")
  Integer id;

  @Column(name = "from_user_id")
  Integer fromUserId;

  @Size(max = 20)
  @NotNull
  @Column(name = "from_phone_number")
  String fromPhoneNumber = "";

  @Column(name = "to_user_id")
  Integer toUserId;

  @Size(max = 20)
  @NotNull
  @Column(name = "to_phone_number")
  String toPhoneNumber = "";

  @Lob
  @Column(name = "message")
  String message = "";

  @Size(max = 45)
  @NotNull
  @Column(name = "sid")
  String sid = "";

  @Size(max = 20)
  @NotNull
  @Column(name = "status")
  String status = "";

  @Column(name = "date_created")
  Instant dateCreated = Instant.now();

  @Column(name = "date_updated")
  Instant dateUpdated;

  @Column(name = "date_sent")
  Instant dateSent;

  @Column(name = "price")
  BigDecimal price;

  @Size(max = 8)
  @NotNull
  @Column(name = "price_unit")
  String priceUnit = "";

  @Size(max = 45)
  @NotNull
  @Column(name = "error_code")
  String errorCode = "1";

  @Size(max = 256)
  @NotNull
  @Column(name = "error_message")
  String errorMessage = "";
}
