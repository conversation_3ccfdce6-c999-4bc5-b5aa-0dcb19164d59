package com.bulkloads.web.infra.confirmation;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import com.bulkloads.common.PdfUtils;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.mapper.AssignmentMapper;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.assignment.service.template.CreateAssignmentTemplateBuilder;
import com.bulkloads.web.infra.confirmation.dto.ConfirmationDto;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

@Service
public class ConfirmationService {

  private final AssignmentRepository assignmentRepository;
  private final AssignmentMapper assignmentMapper;
  private final CreateAssignmentTemplateBuilder createAssignmentTemplateBuilder;
  private final MessageQueueSender queueSender;
  private final String confirmationQueueName;
  private final String domainUrl;

  public ConfirmationService(final AssignmentRepository assignmentRepository,
                             final AssignmentMapper assignmentMapper,
                             final CreateAssignmentTemplateBuilder createAssignmentTemplateBuilder,
                             final MessageQueueSender queueSender,
                             final @Value("${bulkloads.confirmation.queue-name}") String confirmationQueueName,
                             final @Value("${bulkloads.domain-url}") String domainUrl) {
    this.assignmentRepository = assignmentRepository;
    this.assignmentMapper = assignmentMapper;
    this.createAssignmentTemplateBuilder = createAssignmentTemplateBuilder;
    this.queueSender = queueSender;
    this.confirmationQueueName = confirmationQueueName;
    this.domainUrl = domainUrl;
  }

  @Transactional
  public void sendAssignmentConfirmation(final ConfirmationDto dto) {

    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
      @Override
      public void afterCommit() {
        queueSender.send(confirmationQueueName, dto);
      }
    });
  }

  public void sendBookingConfirmation(final ConfirmationDto dto) {
    queueSender.send(confirmationQueueName, dto);
  }

  public InputStream getConfirmationPdf(final int confirmationFileId,
                                        final String confirmationFileCode) throws IOException {

    return PdfUtils.htmlToPdf(getContent(confirmationFileId, confirmationFileCode));
  }

  private String getContent(final int confirmationFileId, final String confirmationFileCode) {
    final String documentContent;

    final List<Assignment> assignments = assignmentRepository
        .findAllByConfirmationFileFileIdAndConfirmationFileCodeAndDeletedIsFalse(confirmationFileId, confirmationFileCode);

    if (assignments.isEmpty()) {
      documentContent = "<html><br/><h3 align='center'>This document has been deleted by its owner and is no longer available.</h3></html>";
    } else {
      documentContent = buildDocumentContent(assignments);
    }
    return documentContent;
  }

  private String buildDocumentContent(final List<Assignment> assignments) {
    final LoadAssignmentTemplateModel model = assignmentMapper.assignmentsToFmModel(assignments, null, domainUrl);
    return createAssignmentTemplateBuilder.getPdfContent(model);
  }
}