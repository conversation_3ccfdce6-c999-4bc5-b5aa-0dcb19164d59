package com.bulkloads.web.infra.messaging.consumer;

import com.bulkloads.web.file.service.FileOcrService;
import com.bulkloads.web.file.service.dto.FileOcrDto;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Validated
@RequiredArgsConstructor
public class FileOcrQueueConsumer {

  private final FileOcrService fileOcrService;

  @RabbitListener(
      queues = "${bulkloads.file-ocr.queue-name}",
      errorHandler = "bulkloadsRabbitListenerErrorHandler",
      containerFactory = "criticalQueueListenerContainerFactory"
  )
  public void receive(@Valid @Payload final FileOcrDto dto) {
    log.info("OCR file: consumer received {}", dto);
    fileOcrService.ocrFile(dto.getFileId());
  }
}
