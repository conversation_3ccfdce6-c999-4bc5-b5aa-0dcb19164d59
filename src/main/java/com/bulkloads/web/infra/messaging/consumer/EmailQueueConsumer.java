package com.bulkloads.web.infra.messaging.consumer;

import static com.bulkloads.config.AppConstants.Header.RETURN_PATH;
import static com.bulkloads.config.AppConstants.Header.X_PRIORITY;
import static com.bulkloads.config.AppConstants.Header.X_SMTPAPI;
import static org.apache.commons.codec.CharEncoding.UTF_8;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import com.bulkloads.web.infra.email.dto.AttachmentDto;
import com.bulkloads.web.infra.email.dto.EmailMessageDto;
import com.bulkloads.web.infra.email.entity.Email;
import com.bulkloads.web.infra.email.repository.EmailRepository;
import org.jsoup.Jsoup;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.core.io.InputStreamSource;
import org.springframework.core.io.UrlResource;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Validated
@Transactional
@RequiredArgsConstructor
public class EmailQueueConsumer {

  private final JavaMailSender emailSender;
  private final EmailRepository emailRepository;

  @RabbitListener(queues = "${bulkloads.mailing.email-queue-name}", errorHandler = "bulkloadsRabbitListenerErrorHandler")
  public void receive(@Valid @Payload final EmailMessageDto emailMessageDto) throws MessagingException, UnsupportedEncodingException {
    log.trace("Received email queue message " + emailMessageDto);

    final MimeMessage mimeMessage = buildMimeMessage(emailMessageDto);

    emailMessageDto.getEmailId().ifPresent(this::markEmailAsSent);
    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
      @Override
      public void afterCommit() {
        sendEmail(mimeMessage);
      }
    });
  }

  private void sendEmail(final MimeMessage mimeMessage) {
    try {
      emailSender.send(mimeMessage);
    } catch (Exception e) {
      log.error("Error sending email", e);
    }
  }

  private MimeMessage buildMimeMessage(final EmailMessageDto emailMessageDto) throws MessagingException, UnsupportedEncodingException {
    final MimeMessage mimeMessage = emailSender.createMimeMessage();
    final MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, UTF_8);
    final Map<String, String> headers = emailMessageDto.getHeaders();
    final String htmlText = emailMessageDto.getMessage();
    final String plainText = extractPlainText(htmlText);

    final String fromEmail = emailMessageDto.getFromEmail();
    final int index = fromEmail.indexOf("<");
    if (index != -1) {
      final String emailName = fromEmail.substring(0, index).trim();
      final String email = fromEmail.substring(index + 1, fromEmail.length() - 1);
      helper.setFrom(email, emailName);
    } else {
      helper.setFrom(fromEmail);
    }

    helper.setSubject(emailMessageDto.getSubject());
    helper.setText(plainText, htmlText);
    helper.setTo(emailMessageDto.getToEmails().toArray(String[]::new));
    helper.setSentDate(Date.from(emailMessageDto.getSendDate()));
    helper.setReplyTo(emailMessageDto.getReplyToEmail());

    for (AttachmentDto attachment : emailMessageDto.getAttachments()) {
      final String url = attachment.getUrl();
      final InputStreamSource fileUrlInputStream = getFileUrlInputStream(url);
      helper.addAttachment(attachment.getFilename(), fileUrlInputStream);
    }

    final Optional<String> cc = emailMessageDto.getCc();
    if (cc.isPresent()) {
      helper.setCc(cc.get().split(","));
    }

    final Optional<String> bcc = emailMessageDto.getBcc();
    if (bcc.isPresent()) {
      helper.setBcc(bcc.get().split(","));
    }

    if (headers.containsKey(X_PRIORITY)) {
      helper.setPriority(Integer.parseInt(headers.get(X_PRIORITY)));
    }

    if (headers.containsKey(X_SMTPAPI)) {
      mimeMessage.setHeader(X_SMTPAPI, headers.get(X_SMTPAPI));
    }

    mimeMessage.setHeader(RETURN_PATH, emailMessageDto.getFailTo());

    return mimeMessage;
  }

  private void markEmailAsSent(final Integer emailId) {
    final Email email = emailRepository.getReferenceById(emailId);
    email.setSent(1);
    emailRepository.save(email);
  }

  private InputStreamSource getFileUrlInputStream(final String url) {
    final URI uri = URI.create(url);
    return UrlResource.from(uri);
  }

  private String extractPlainText(final String htmlMessage) {
    return Jsoup.parse(htmlMessage).text();
  }
}
