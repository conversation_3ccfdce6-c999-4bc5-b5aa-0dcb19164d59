package com.bulkloads.web.infra.messaging.consumer;

import static java.util.Objects.nonNull;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.config.AppConstants;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.security.AuthService;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.communication.CommunicationService;
import com.bulkloads.web.communication.domain.CommunicationDetails;
import com.bulkloads.web.infra.dto.OfferDto;
import com.bulkloads.web.infra.dynlink.DynamicLinkService;
import com.bulkloads.web.load.domain.template.DynamicLink;
import com.bulkloads.web.offer.domain.entity.Offer;
import com.bulkloads.web.offer.domain.entity.OfferRecipient;
import com.bulkloads.web.offer.domain.template.OfferTemplateModel;
import com.bulkloads.web.offer.mapper.OfferMapper;
import com.bulkloads.web.offer.repository.OfferRepository;
import com.bulkloads.web.offer.service.template.OfferTemplateBuilder;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Validated
@Transactional
@RequiredArgsConstructor

public class OfferQueueConsumer {

  public static final int EMAIL_CATEGORY_ID = 19;

  private final AppProperties appProperties;
  private final OfferRepository offerRepository;
  private final OfferTemplateBuilder offerTemplateBuilder;
  private final OfferMapper offerMapper;
  private final DynamicLinkService dynamicLinkService;
  private final CommunicationService communicationService;
  private final AuthService authService;

  @RabbitListener(queues = "${bulkloads.offer.queue-name}", errorHandler = "bulkloadsRabbitListenerErrorHandler")
  public void receive(@Payload final OfferDto dto) {
    log.trace("Received offer queue message {}", dto);
    handle(dto);
  }

  private void handle(final OfferDto dto) {
    final String domainUrl = appProperties.getDomainUrl();

    Offer offer = offerRepository.findById(dto.getOfferId()).orElseThrow(() -> new BulkloadsException("Offer not found"));

    List<OfferRecipient> recipients = offer.getRecipients();

    for (OfferRecipient recipient : recipients) {

      String urlSuffix = "";
      final AbUser abUser = recipient.getAbUser();
      if (nonNull(abUser)) {
        final String userAuthToken = authService.getUserAuthToken(null, abUser.getAbUserId(), Collections.emptyList(), 7);
        urlSuffix = "?auth_token=" + userAuthToken;

        if (nonNull(abUser.getBlUser())) {
          urlSuffix += "&user_id=" + abUser.getBlUser().getUserId();
        }
      }
      String link = domainUrl + "/tms/myoffers/" + recipient.getOfferRecipientId() + urlSuffix;

      Map<String, String> params = new HashMap<>();
      params.put("offer_recipient_id", recipient.getOfferRecipientId().toString());

      DynamicLink dynamicLink = dynamicLinkService.createDynamicLink(link, "offers", params);

      // create the model that will be used by the offer template
      final OfferTemplateModel model = offerMapper.entityToTemplateModel(recipient, dynamicLink);

      String emailTitle = offerTemplateBuilder.getEmailTitle(model);
      String emailContent = offerTemplateBuilder.getEmailContent(model);
      String notificationTitle = offerTemplateBuilder.getNotificationTitle(model);
      String notificationContent = offerTemplateBuilder.getNotificationContent(model);
      Map<String, Object> pushData = offerTemplateBuilder.getNotificationData(model);

      final CommunicationDetails communicationDetails =
          CommunicationDetails.builder()
              .fromUserId(offer.getUser().getUserId())
              .replyToEmail(offer.getReplytoEmail())
              .toAbUserId(nonNull(abUser) ? abUser.getAbUserId() : null)
              .toUserId(nonNull(recipient.getBlUser()) ? recipient.getBlUser().getUserId() : null)
              .emailCategoryId(EMAIL_CATEGORY_ID)
              .pushTitle(notificationTitle)
              .pushMessage(notificationContent)
              .pushData(pushData)
              .build();

      if (nonNull(abUser)) { // from address book

        String toEmail = abUser.getEmail();

        if (AppConstants.ContactMethod.SMS.equalsIgnoreCase(abUser.getPreferredContactMethod())
            || AppConstants.ContactMethod.BOTH.equalsIgnoreCase(abUser.getPreferredContactMethod())) {
          String smsContent = offerTemplateBuilder.getSmsContent(model);
          communicationDetails.setSmsMessage(smsContent);
        }

        if (AppConstants.ContactMethod.EMAIL.equalsIgnoreCase(abUser.getPreferredContactMethod())
            || AppConstants.ContactMethod.BOTH.equalsIgnoreCase(abUser.getPreferredContactMethod())
            || "".equalsIgnoreCase(abUser.getPreferredContactMethod())) {
          communicationDetails.setEmailTitle(emailTitle);
          communicationDetails.setEmailMessage(emailContent);
        }

      } else { // leads: BL members NOT in address book
        communicationDetails.setEmailTitle(emailTitle);
        communicationDetails.setEmailMessage(emailContent);
        String toEmail = recipient.getBlUser().getEmail();
      }

      communicationService.send(communicationDetails);

    }

  }

}
