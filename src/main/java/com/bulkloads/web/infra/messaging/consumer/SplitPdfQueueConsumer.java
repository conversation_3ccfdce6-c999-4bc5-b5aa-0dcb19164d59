package com.bulkloads.web.infra.messaging.consumer;

import java.io.IOException;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.file.service.SplitPdfService;
import com.bulkloads.web.file.service.dto.SplitPdfDto;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Validated
@RequiredArgsConstructor
public class SplitPdfQueueConsumer {

  final SplitPdfService splitPdfService;

  @RabbitListener(queues = "${bulkloads.split-pdf.queue-name}", errorHandler = "bulkloadsRabbitListenerErrorHandler")
  public void receive(@Valid @Payload final SplitPdfDto dto) {
    try {
      splitPdfService.splitPdf(dto.getFileId());
    } catch (IOException e) {
      log.error("Error splitting PDF", e);
      throw new BulkloadsException("Error splitting PDF");
    }
  }

}
