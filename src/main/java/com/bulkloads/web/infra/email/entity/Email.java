package com.bulkloads.web.infra.email.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.common.jpa.converter.CsvStringListConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "email_queue")
public class Email {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "email_queue_id")
  private Integer id;

  @NotNull
  @Column(name = "sender_user_id")
  private Integer senderUserId;

  @NotNull
  @Column(name = "send_date")
  private Instant sendDate;

  @Size(max = 100)
  @NotNull
  @Column(name = "from_email")
  private String fromEmail = "";

  @Size(max = 100)
  @Column(name = "replyto_email")
  private String replyToEmail = "";

  @Convert(converter = CsvStringListConverter.class)
  @NotNull
  @Column(name = "to_email")
  private List<@jakarta.validation.constraints.Email String> toEmail = new ArrayList<>();

  @Size(max = 500)
  @NotNull
  @Column(name = "subject")
  private String subject = "";

  @NotNull
  @Lob
  @Column(name = "message")
  private String message = "";

  @NotNull
  @Column(name = "sent")
  private int sent;

  @Column(name = "to_user_id")
  private Integer toUserId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "email_category_id")
  private EmailCategory emailCategory;

  @Column(name = "site_id")
  private Integer siteId;

  @Size(max = 500)
  @Column(name = "description")
  private String description = "";

  @Size(max = 100)
  @Column(name = "sg_status")
  private String sgStatus = "";

  @Column(name = "opened_date")
  private Instant openedDate;

  @Size(max = 150)
  @Column(name = "cc")
  private String cc = "";

  @Size(max = 150)
  @Column(name = "bcc")
  private String bcc = "";

}
