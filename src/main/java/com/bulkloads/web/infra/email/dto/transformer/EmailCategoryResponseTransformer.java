package com.bulkloads.web.infra.email.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.setting.service.dto.NotificationSettingResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class EmailCategoryResponseTransformer implements TupleTransformer<NotificationSettingResponse> {

  @Override
  public NotificationSettingResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return NotificationSettingResponse.builder()

      .sendPush(parts.asInteger("send_push"))
      .sendSms(parts.asInteger("send_sms"))
      .emailCategoryId(parts.asInteger("email_category_id"))
      .emailCategory(parts.asString("email_category"))
      .emailFrequency(parts.asString("email_frequency"))
      .emailCategoryDescription(parts.asString("email_category_description"))
      .build();
  }
}
