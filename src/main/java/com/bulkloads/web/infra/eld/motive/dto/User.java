package com.bulkloads.web.infra.eld.motive.dto;

import java.util.List;

public record User(
    long id,
    String email,
    String firstName,
    String lastName,
    List<Long> groupIds,
    String companyReferenceId,
    String phone,
    String phoneCountryCode,
    String phoneExt,
    String timeZone,
    boolean metricUnits,
    String mobileLastActiveAt,
    String mobileCurrentSignInAt,
    String mobileLastSignInAt,
    String webLastActiveAt,
    String role,
    String status,
    String webCurrentSignInAt,
    String webLastSignInAt,
    List<String> externalIds,
    String createdAt,
    String updatedAt) {
}