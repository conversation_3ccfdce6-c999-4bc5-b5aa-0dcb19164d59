package com.bulkloads.web.infra.eld.motive;

import static com.bulkloads.config.AppConstants.EldProviderId.MOTIVE;
import static java.util.Objects.isNull;
import static org.springframework.http.HttpMethod.GET;

import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.infra.eld.EldProviderClient;
import com.bulkloads.web.infra.eld.dto.EldDriverDto;
import com.bulkloads.web.infra.eld.dto.EldDriverHosDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleLocationDto;
import com.bulkloads.web.infra.eld.motive.dto.DriverAvailableTime;
import com.bulkloads.web.infra.eld.motive.dto.DriverAvailableTimeItem;
import com.bulkloads.web.infra.eld.motive.dto.DriverAvailableTimeResponse;
import com.bulkloads.web.infra.eld.motive.dto.User;
import com.bulkloads.web.infra.eld.motive.dto.UserItem;
import com.bulkloads.web.infra.eld.motive.dto.UsersResponse;
import com.bulkloads.web.infra.eld.motive.dto.Vehicle;
import com.bulkloads.web.infra.eld.motive.dto.VehicleItem;
import com.bulkloads.web.infra.eld.motive.dto.VehicleLocation;
import com.bulkloads.web.infra.eld.motive.dto.VehicleLocationItem;
import com.bulkloads.web.infra.eld.motive.dto.VehicleLocationResponse;
import com.bulkloads.web.infra.eld.motive.dto.VehiclesResponse;
import com.bulkloads.web.infra.eld.motive.mapper.MotiveMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.web.reactive.function.client.ServletOAuth2AuthorizedClientExchangeFilterFunction;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MotiveClient extends EldProviderClient {

  public static final String BASE_URL = "https://api.gomotive.com";
  public static final String VEHICLES_ENDPOINT = "/v1/vehicles";
  public static final String USERS_ENDPOINT = "/v1/users";
  public static final String DRIVERS_AVAILABLE_TIME_ENDPOINT = "/v1/available_time";
  public static final String VEHICLES_CURRENT_LOCATION_AND_CURRENT_DRIVER_ENDPOINT = "/v3/vehicle_locations";
  public static final String VEHICLE_IDS = "vehicle_ids";
  public static final String DRIVER_IDS = "driver_ids";
  public static final String PER_PAGE = "per_page";
  public static final String PAGE_NO = "page_no";

  private final MotiveMapper mapper;

  public MotiveClient(final OAuth2AuthorizedClientService authorizedClientService,
                      final OAuth2AuthorizedClientManager authorizedClientManager,
                      final MotiveMapper motiveMapper) {
    super(authorizedClientService, authorizedClientManager);
    this.mapper = motiveMapper;
  }

  @Override
  protected List<EldDriverDto> fetchDrivers() {
    log.info("Fetching users from Motive API");
    final List<User> allUsers = new ArrayList<>();
    int pageIndex = 1;
    final int perPage = 50;
    boolean hasMorePages = true;

    while (hasMorePages) {
      final MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
      queryParams.add(PER_PAGE, String.valueOf(perPage));
      queryParams.add(PAGE_NO, String.valueOf(pageIndex));
      final UsersResponse response = makeApiCall(USERS_ENDPOINT, GET, queryParams, UsersResponse.class);
      final List<User> usersPage = response.users().stream().map(UserItem::user).toList();
      allUsers.addAll(usersPage);

      hasMorePages = usersPage.size() >= perPage
          && (isNull(response.pagination()) || pageIndex < response.pagination().total());
      pageIndex++;
    }

    log.info("Fetched a total of {} users from Motive API", allUsers.size());
    return mapper.mapUsers(allUsers);
  }

  @Override
  protected List<EldVehicleDto> fetchVehicles() {
    log.info("Fetching vehicles from Motive API");
    final List<Vehicle> allVehicles = new ArrayList<>();
    int pageIndex = 1;
    final int perPage = 50;
    boolean hasMorePages = true;

    while (hasMorePages) {
      final MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
      queryParams.add(PER_PAGE, String.valueOf(perPage));
      queryParams.add(PAGE_NO, String.valueOf(pageIndex));
      final VehiclesResponse response = makeApiCall(VEHICLES_ENDPOINT, GET, queryParams, VehiclesResponse.class);
      final List<Vehicle> vehiclesPage = response.vehicles().stream().map(VehicleItem::vehicle).toList();
      allVehicles.addAll(vehiclesPage);

      hasMorePages = vehiclesPage.size() >= perPage
          && (isNull(response.pagination()) || pageIndex < response.pagination().total());
      pageIndex++;
    }

    log.info("Fetched a total of {} vehicles from Motive API", allVehicles.size());
    return mapper.mapVehicles(allVehicles);
  }

  @Override
  protected List<EldVehicleLocationDto> fetchVehicleLocations(final List<String> externalIds) {
    log.info("Fetching vehicle locations from Motive API");
    final List<VehicleLocation> allVehicleLocations = new ArrayList<>();
    int pageIndex = 1;
    final int perPage = 50;
    boolean hasMorePages = true;

    while (hasMorePages) {

      final MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
      queryParams.add(PER_PAGE, String.valueOf(perPage));
      queryParams.add(PAGE_NO, String.valueOf(pageIndex));
      if (externalIds != null && !externalIds.isEmpty()) {
        for (String externalId : externalIds) {
          queryParams.add(VEHICLE_IDS, externalId);
        }
      }

      final VehicleLocationResponse response = makeApiCall(VEHICLES_CURRENT_LOCATION_AND_CURRENT_DRIVER_ENDPOINT,
                                                           GET, queryParams, VehicleLocationResponse.class);
      final List<VehicleLocation> vehicleLocationsPage = response.vehicles().stream().map(VehicleLocationItem::vehicle).toList();
      allVehicleLocations.addAll(vehicleLocationsPage);

      hasMorePages = vehicleLocationsPage.size() >= perPage
          && (isNull(response.pagination()) || pageIndex < response.pagination().total());
      pageIndex++;
    }

    log.info("Fetched a total of {} vehicle locations from Motive API", allVehicleLocations.size());
    return mapper.mapVehicleLocations(allVehicleLocations);

  }

  @Override
  protected List<EldDriverHosDto> fetchDriverHosStatuses(final List<String> externalIds) {
    log.info("Fetching drivers with available time from Motive API. External IDs: {}", externalIds);
    final List<DriverAvailableTime> allDriverAvailableTime = new ArrayList<>();
    int pageIndex = 1;
    final int perPage = 50;
    boolean hasMorePages = true;

    while (hasMorePages) {
      final MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
      queryParams.add(PER_PAGE, String.valueOf(perPage));
      queryParams.add(PAGE_NO, String.valueOf(pageIndex));
      if (externalIds != null && !externalIds.isEmpty()) {
        for (String externalId : externalIds) {
          queryParams.add(DRIVER_IDS, externalId);
        }
      }

      final DriverAvailableTimeResponse response = makeApiCall(DRIVERS_AVAILABLE_TIME_ENDPOINT, GET, queryParams, DriverAvailableTimeResponse.class);
      final List<DriverAvailableTime> driverAvailableTimePage = response.users().stream().map(DriverAvailableTimeItem::user).toList();
      allDriverAvailableTime.addAll(driverAvailableTimePage);

      hasMorePages = driverAvailableTimePage.size() >= perPage
          && (isNull(response.pagination()) || pageIndex < response.pagination().total());
      pageIndex++;
    }

    log.info("Fetched a total of {} drivers with available time from Motive API", allDriverAvailableTime.size());
    return mapper.mapDriverAvailableTime(allDriverAvailableTime);
  }

  @Override
  protected String getClientName() {
    return MOTIVE;
  }

  @Override
  protected WebClient buildWebClient(final OAuth2AuthorizedClientManager authorizedClientManager) {
    final ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    final ExchangeStrategies strategies = ExchangeStrategies.builder()
        .codecs(configurer -> {
          configurer.defaultCodecs().jackson2JsonEncoder(new Jackson2JsonEncoder(objectMapper));
          configurer.defaultCodecs().jackson2JsonDecoder(new Jackson2JsonDecoder(objectMapper));
        })
        .build();

    final ServletOAuth2AuthorizedClientExchangeFilterFunction oauth2
        = new ServletOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager);
    oauth2.setDefaultOAuth2AuthorizedClient(true);

    return WebClient.builder()
        .filter(oauth2)
        .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(5 * 1024 * 1024)) // Set to 5MB

        .exchangeStrategies(strategies)
        .baseUrl(BASE_URL).build();
  }
}
