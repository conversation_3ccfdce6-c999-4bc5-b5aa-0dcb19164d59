package com.bulkloads.web.infra.quickbooks.quickbooks;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bulkloads.config.QuickBooksConfigurationManager;
import com.intuit.ipp.data.CompanyInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Test controller to verify QuickBooks logging functionality
 * This can be removed after testing
 */
@Slf4j
@RestController
@RequestMapping("/rest/quickbooks/test")
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class QbLoggingTestController {

  private final QbRepository qbRepository;
  private final QuickBooksConfigurationManager configurationManager;

  @GetMapping("/logging")
  public String testLogging() {
    try {
      log.info("Testing QuickBooks logging functionality");
      
      // This will trigger the logging wrapper
      qbRepository.findAll(CompanyInfo.class);
      
      return "QuickBooks logging test completed. Check the logs for detailed output.";
    } catch (Exception e) {
      log.error("Error during QuickBooks logging test", e);
      return "QuickBooks logging test failed: " + e.getMessage();
    }
  }

  @GetMapping("/config")
  public String getConfiguration() {
    try {
      return configurationManager.getConfigurationSummary();
    } catch (Exception e) {
      log.error("Error getting QuickBooks configuration", e);
      return "Error getting configuration: " + e.getMessage();
    }
  }

  @GetMapping("/validate")
  public String validateConfiguration() {
    try {
      StringBuilder validation = new StringBuilder();
      validation.append("QuickBooks Configuration Validation:\n\n");

      // Check XML file exists
      try {
        getClass().getClassLoader().getResourceAsStream("intuit-config.xml");
        validation.append("✓ intuit-config.xml file found\n");
      } catch (Exception e) {
        validation.append("✗ intuit-config.xml file not found\n");
      }

      // Check configuration manager
      validation.append("✓ QuickBooksConfigurationManager initialized\n");

      // Add configuration summary
      validation.append("\n").append(configurationManager.getConfigurationSummary());

      return validation.toString();
    } catch (Exception e) {
      log.error("Error validating QuickBooks configuration", e);
      return "Error validating configuration: " + e.getMessage();
    }
  }
}
