package com.bulkloads.web.infra.quickbooks.dto;

import java.math.BigDecimal;
import java.util.List;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Jacksonized
@Builder
public class CreateBillDto {

  int loadInvoiceId;
  int userId;
  @NotEmpty
  String vendorId;
  @NotNull
  BigDecimal totalCostAmount;
  @NotEmpty
  List<@Valid InvoiceItemDto> invoiceItems;
}
