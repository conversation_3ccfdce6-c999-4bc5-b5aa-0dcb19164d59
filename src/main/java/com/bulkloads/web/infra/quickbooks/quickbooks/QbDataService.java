package com.bulkloads.web.infra.quickbooks.quickbooks;

import com.intuit.ipp.services.DataService;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
//TODO Optimise this to be request scoped
//@RequestScope
public class QbDataService {

  private final QbServiceFactory serviceFactory;
//  private DataService dataService;

  public QbDataService(QbServiceFactory serviceFactory) {
    this.serviceFactory = serviceFactory;
  }

  protected DataService getDataService() {
//    if (dataService == null) {
//      dataService = serviceFactory.createDataService();
//    }
//    return dataService;
    return serviceFactory.createDataService();
  }
}
