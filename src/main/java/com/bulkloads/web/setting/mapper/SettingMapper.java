package com.bulkloads.web.setting.mapper;

import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.setting.service.dto.SettingResponse;
import com.bulkloads.web.user.domain.entity.BlUserSettings;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class SettingMapper {

  public abstract SettingResponse entityToResponse(final BlUserSettings entity);
}
