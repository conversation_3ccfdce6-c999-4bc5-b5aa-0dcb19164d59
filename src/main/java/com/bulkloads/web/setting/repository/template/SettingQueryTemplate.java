package com.bulkloads.web.setting.repository.template;

public class SettingQueryTemplate {

  public static final String SETTINGS_QUERY_TEMPLATE = """

      SELECT
       site_settings.*,
       -- company settings
       uc.allow_drivers,
       uc.require_documents_for_completing_load,
       uc.approve_loads_for_payment,
       uc.use_tms_for_invoices,
       uc.use_dispatch,
       uc.sff_enabled,
       u.load_confirmation_footer,
       ifnull(uc.auto_generate_work_order_numbers, 0) as auto_generate_work_order_numbers,
       ifnull(uc.auto_confirm_assignments, 0) as auto_confirm_assignments,
       ifnull(uc.allow_driver_load_submissions, 0) as allow_driver_load_submissions,
       ifnull(la_exp.export_active, 0) as load_export_schedule_active,
       ifnull(la_exp.start_date, curdate()) as load_export_schedule_start_date,
       ifnull(la_exp.start_time, '08:00:00') as load_export_schedule_start_time,
       ifnull(la_exp.export_emails, owner_u.email) as load_export_schedule_emails,
       ifnull(la_exp.last_run_date, '') as load_export_schedule_last_run_date,
       ifnull(la_exp.next_run_date, '') as load_export_schedule_next_run_date,
       ifnull(la_exp.next_run_date_client_tz, '') as load_export_schedule_next_run_date_client_tz,
       ifnull(la_exp.frequency, 'Weekly') as load_export_schedule_frequency,
       ifnull(la_exp.export_master, 1) as load_export_schedule_export_master,
       ifnull(la_exp.export_receivables, 1) as load_export_schedule_export_receivables,
       ifnull(la_exp.export_payables, 1) as load_export_schedule_export_payables,
       ifnull(la_exp.date_only, 0) as load_export_schedule_date_only,
       ifnull(la_exp.first_run, 1) as load_export_schedule_first_nun

      from bl_user_settings site_settings
       inner join user_company uc on site_settings.user_company_id = uc.user_company_id
       inner join user_info u using(user_id)
       inner join user_info owner_u on uc.company_owner_id = owner_u.user_id

       left join load_assignment_export_schedules la_exp on la_exp.user_company_id = u.user_company_id

      WHERE
           1=1

       <% if (paramExistsAdd("user_id")) { %>
            AND u.user_id = :user_id
       <% } %>

      """;
}
