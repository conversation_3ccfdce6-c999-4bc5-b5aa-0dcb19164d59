package com.bulkloads.web.setting.service.dto;

import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class SettingResponse {

  Integer useChat;
  Integer truckMatching;
  Integer truckAvailable;
  Integer truckMatchingMiles;
  Integer showInLoadHistory;
  String loadMessageType;
  Integer showInCompanyFinder;
  Integer allowGroupSend;
  Integer loadMatchingMiles;
  String currentAutoChange;
  Boolean hideMyLoadsFromNonAdmins;
  Integer allowDrivers;
  Integer requireDocumentsForCompletingLoad;
  Integer approveLoadsForPayment;
  Integer useTmsForInvoices;
  Integer useDispatch;
  String appMode;
  String allowedAppModes;
  String loadConfirmationFooter;
  Integer autoGenerateWorkOrderNumbers;
  Integer autoConfirmAssignments;
  Integer allowDriverLoadSubmissions;
  Integer loadExportScheduleActive;
  LocalDate loadExportScheduleStartDate;
  LocalTime loadExportScheduleStartTime;
  String loadExportScheduleEmails;

}
