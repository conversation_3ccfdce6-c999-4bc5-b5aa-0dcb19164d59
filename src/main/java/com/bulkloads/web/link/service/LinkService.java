package com.bulkloads.web.link.service;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import java.math.BigInteger;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.common.StringUtil;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.link.domain.entity.Link;
import com.bulkloads.web.link.mapper.LinkMapper;
import com.bulkloads.web.link.repository.LinkRepository;
import com.bulkloads.web.link.service.dto.LinkRequest;
import com.bulkloads.web.link.service.dto.LinkResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LinkService {

  private final LinkRepository linkRepository;
  private final ObjectMapper objectMapper;
  private final AppProperties appProperties;
  private final LinkMapper linkMapper;

  public Optional<String> getMetadata(String shortCode) {
    try {
      BigInteger id = StringUtil.sqidToBigInteger(shortCode);
      return linkRepository.findById(id)
          .map(Link::getMetadata);
    } catch (IllegalArgumentException e) {
      return Optional.empty();
    }
  }

  @Transactional
  public LinkResponse create(LinkRequest request) {

    if (isEmpty(request.getFullUrl())) {
      throw new ValidationException("full_url", "Full URL is required");
    } else {
      URI uri = URI.create(request.getFullUrl());
      if (!uri.isAbsolute()) {
        throw new ValidationException("full_url", "Full URL must be absolute");
      }
    }

    String fullUrl = request.getFullUrl();
    Map<String, String> additionalParams = request.getAdditionalParameters();

    URI uri = URI.create(fullUrl);
    ObjectNode metadata = objectMapper.createObjectNode();
    metadata.put("fullUrl", fullUrl);
    metadata.put("domain", uri.getHost());
    metadata.put("path", uri.getPath());

    if (!isEmpty(request.getType())) {
      metadata.put("type", request.getType().toLowerCase());
    }

    ObjectNode paramsNode = objectMapper.createObjectNode();
    String query = uri.getQuery();
    if (query != null && !query.isEmpty()) {
      String[] pairs = query.split("&");
      for (String pair : pairs) {
        int idx = pair.indexOf("=");
        if (idx > 0) {
          String key = URLDecoder.decode(pair.substring(0, idx), StandardCharsets.UTF_8);
          String value = URLDecoder.decode(pair.substring(idx + 1), StandardCharsets.UTF_8);
          paramsNode.put(key, value);
        }
      }
    }
    if (additionalParams != null) {
      additionalParams.forEach(paramsNode::put);
    }
    metadata.set("parameters", paramsNode);

    Link link = new Link();
    link.setFullUrl(fullUrl);
    final Instant now = Instant.now();
    link.setAddedDate(now);
    link.setEditDate(now);

    try {
      link.setMetadata(objectMapper.writeValueAsString(metadata));
    } catch (Exception e) {
      throw new BulkloadsException("Failed to serialize metadata for fullUrl: " + fullUrl, e);
    }

    link = linkRepository.save(link);
    String shortCode = StringUtil.bigIntegerToSqid(link.getId());
    String shortUrl = appProperties.getShortDomainUrl() + "/" + shortCode;

    link.setShortCode(shortCode);
    link.setShortUrl(shortUrl);
    link = linkRepository.save(link);

    return linkMapper.entityToResponse(link);
  }

}