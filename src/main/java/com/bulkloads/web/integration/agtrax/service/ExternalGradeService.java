package com.bulkloads.web.integration.agtrax.service;

import java.util.Optional;
import com.bulkloads.web.integration.agtrax.domain.entity.ExternalGrade;
import com.bulkloads.web.integration.agtrax.repository.ExternalGradeRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExternalGradeService {

  private final ExternalGradeRepository externalGradeRepository;

  @Transactional
  public ExternalGrade getOrCreate(String factorCode, String factorDescription, Integer userCompanyId) {
    // Try to find matching external grade
    Optional<ExternalGrade> externalGradeOpt = externalGradeRepository
        .findByExternalGradeCodeAndUserCompanyId(factorCode, userCompanyId);

    if (externalGradeOpt.isPresent()) {
      return externalGradeOpt.get();
    }

    ExternalGrade externalGrade = new ExternalGrade();
    externalGrade.setUserCompanyId(userCompanyId);
    externalGrade.setExternalGradeCode(factorCode);
    externalGrade.setExternalGradeDescription(factorDescription);
    externalGradeRepository.save(externalGrade);
    log.debug("Created new external grade for factor code: {}", factorCode);
    return externalGrade;
  }
}