package com.bulkloads.web.integration.agtrax.dto;

import java.time.Instant;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class UnmatchedOriginTicketResponse {
  Instant date;

  @JsonProperty("discount_schedule_id")
  Integer discountScheduleId;

  @JsonProperty("company_name")
  String companyName;

  @JsonProperty("serial_id")
  Integer serialId;

  String reference1;

  @JsonProperty("agtrax_scale_ticket_id")
  Integer agtraxScaleTicketId;

  String reference2;

  @JsonProperty("ticket_number")
  String ticketNumber;

  @JsonProperty("user_company_id")
  Integer userCompanyId;

  @JsonProperty("commodity_id")
  Integer commodityId;

  String commodity;

  @JsonProperty("tare_weight")
  String tareWeight;
}
