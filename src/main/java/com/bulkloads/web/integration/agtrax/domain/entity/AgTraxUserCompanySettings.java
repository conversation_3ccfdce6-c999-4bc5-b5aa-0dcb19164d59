package com.bulkloads.web.integration.agtrax.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "agtrax_user_company_settings")
@Getter
@Setter
public class AgTraxUserCompanySettings {

  @Id
  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Size(max = 2048)
  @Column(name = "postback_url")
  private String postbackUrl = "";

  @Size(max = 45)
  @Column(name = "postback_pv")
  private String postbackPv = "";

  @Size(max = 45)
  @Column(name = "postback_pvp")
  private String postbackPvp = "";

  @Size(max = 45)
  @Column(name = "postback_u")
  private String postbackU = "";

  @Size(max = 45)
  @Column(name = "postback_p")
  private String postbackP = "";

}