package com.bulkloads.web.integration.agtrax.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.integration.agtrax.dto.GradeResponse;
import com.bulkloads.web.integration.agtrax.service.GradeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest")
@Tag(name = "Grades")
@RequiredArgsConstructor
public class GradeController {

  private final GradeService gradeService;

  @GetMapping("/grades")
  @Operation(summary = "Get Bulkloads grades")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public List<GradeResponse> getGrades() {
    return gradeService.getGrades();
  }

  @PostMapping("/external_grades/{external_grade_id}/link/{grade_id}")
  @Operation(summary = "Match a grade with an external grade")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public ApiResponse<Void, Void> matchGradeWithExternalGrade(
      @PathVariable("external_grade_id") Integer externalGradeId,
      @PathVariable("grade_id") Integer gradeId) {
    gradeService.matchExternalGradeWithGrade(gradeId, externalGradeId);

    return ApiResponse.<Void, Void>builder()
        .message("Grade matched")
        .build();

  }
}