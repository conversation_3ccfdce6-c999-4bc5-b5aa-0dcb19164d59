package com.bulkloads.web.integration.agtrax.repository;

import java.util.HashMap;
import java.util.List;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.integration.agtrax.dto.UnmatchedOriginTicketResponse;
import com.bulkloads.web.integration.agtrax.dto.transformer.UnmatchedOriginTicketResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class AgTraxQueryRepositoryImpl implements AgTraxQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final UnmatchedOriginTicketResponseTransformer unmatchedOriginTicketResponseTransformer;

  @Override
  public List<UnmatchedOriginTicketResponse> getUnmatchedOriginTicketsAdmin() {
    String query = """
        select
            uc.user_company_id,
            uc.company_name,
            st.agtrax_scale_ticket_id,
            st.serial_id,
            st.ticket_number,
            st.reference1,
            st.reference2,
            st.discount_schedule_id,
            st.date,
            st.commodity_id,
            c.commodity,
            st.tare_pounds
        from
            agtrax_scale_tickets st
                inner join user_company uc on st.user_company_id = uc.user_company_id
                left join commodities c on st.commodity_id = c.commodity_id
        where
            st.deleted = 0
            and load_assignment_id is null
        order by
            st.agtrax_scale_ticket_id
        limit 200
        """;
    
    return jpaNativeQueryService.query(query, new HashMap<>(), unmatchedOriginTicketResponseTransformer);
  }
}