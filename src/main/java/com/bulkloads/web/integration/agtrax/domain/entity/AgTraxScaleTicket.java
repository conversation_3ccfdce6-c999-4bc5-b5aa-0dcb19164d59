package com.bulkloads.web.integration.agtrax.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "agtrax_scale_tickets")
public class AgTraxScaleTicket {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "agtrax_scale_ticket_id")
  private Integer agtraxScaleTicketId;

  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Column(name = "serial_id")
  private Integer serialId;

  @Column(name = "date")
  private Instant date;

  @Column(name = "ticket_number")
  private String ticketNumber;

  @Column(name = "branch_id")
  private Integer branchId;

  @Column(name = "commodity_id")
  private Integer commodityId;

  @Column(name = "external_commodity_id")
  private String externalCommodityId;

  @Column(name = "gross_pounds")
  private String grossPounds;

  @Column(name = "tare_pounds")
  private String tarePounds;

  @Column(name = "reference1")
  private String reference1;

  @Column(name = "reference2")
  private String reference2;

  @Column(name = "discount_schedule_id")
  private Integer discountScheduleId;

  @Column(name = "destination_id")
  private Integer destinationId;

  @Column(name = "hauler_id")
  private Integer haulerId;

  @Column(name = "origin_payload")
  private String originPayload;

  @Column(name = "destination_payload")
  private String destinationPayload;

  @Column(name = "post_response")
  private String postResponse;

  @Column(name = "destination_file_id")
  private Integer destinationFileId;

  @Column(name = "load_assignment_id")
  private Integer loadAssignmentId;

  @Column(name = "created_date")
  private Instant createdDate;

  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  @Column(name = "deleted")
  private Boolean deleted;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "agtraxScaleTicket", fetch = FetchType.LAZY)
  @OrderBy("agtraxScaleTicketFactorId ASC")
  private List<AgTraxScaleTicketFactor> agtraxScaleTicketFactors = new ArrayList<>();

  @Transient
  private boolean hasUnmatchedExternalGrades;

}
