package com.bulkloads.web.integration.agtrax.service;

import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_GROSS_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TARE_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_DATE;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_NUMBER;
import static com.bulkloads.web.file.service.FileService.LOADING_TICKET_FILE_TYPE_ID;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.time.Instant;
import java.time.LocalDate;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import com.bulkloads.common.StringUtil;
import com.bulkloads.config.AppConstants;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.security.ImpersonationService;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.assignment.service.AssignmentService;
import com.bulkloads.web.assignment.service.dto.AssignmentFileRequest;
import com.bulkloads.web.assignment.service.dto.UpdateLoadAssignmentRequest;
import com.bulkloads.web.aws.service.AmazonS3Service;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.domain.entity.FileFieldDefinition;
import com.bulkloads.web.file.repository.FileFieldDefinitionRepository;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.service.FileService;
import com.bulkloads.web.file.service.dto.FileResponse;
import com.bulkloads.web.file.service.dto.LocalFileRequest;
import com.bulkloads.web.file.util.ImageUtils;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicket;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicketFactor;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicketLog;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxUserCompanySettings;
import com.bulkloads.web.integration.agtrax.domain.entity.ExternalGrade;
import com.bulkloads.web.integration.agtrax.domain.entity.Grade;
import com.bulkloads.web.integration.agtrax.domain.repository.AgTraxUserCompanySettingsRepository;
import com.bulkloads.web.integration.agtrax.dto.CandidateAssignmentResponse;
import com.bulkloads.web.integration.agtrax.dto.UnmatchedOriginTicketResponse;
import com.bulkloads.web.integration.agtrax.repository.AgTraxQueryRepository;
import com.bulkloads.web.integration.agtrax.repository.AgTraxRepository;
import com.bulkloads.web.integration.agtrax.repository.AgTraxScaleTicketLogRepository;
import com.bulkloads.web.integration.agtrax.repository.GradeRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.utility.service.UtilityService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@AllArgsConstructor
public class AgTraxService {

  public static final String OCR_POSTBACK_URL = "https://patron.agtrax.com/partner/bulkloads/index.asmx/WSWEIGHTSGRADES";

  private final AgTraxRepository agTraxRepository;
  private final AssignmentRepository loadAssignmentRepository;
  private final GradeRepository gradeRepository;
  private final FileRepository fileRepository;
  private final FileFieldDefinitionRepository fileFieldDefinitionRepository;
  private final FileService fileService;
  private final AssignmentService assignmentService;
  private final ImpersonationService impersonationService;
  private final AgTraxQueryRepository agTraxQueryRepository;
  private final UtilityService utilityService;
  private final AgTraxUserCompanySettingsRepository agTraxUserCompanySettingsRepository;
  private final AgTraxScaleTicketLogRepository agTraxScaleTicketLogRepository;
  private final AppProperties appProperties;
  private final ExternalGradeService externalGradeService;

  public List<UnmatchedOriginTicketResponse> getUnmatchedOriginTicketsAdmin() {
    return agTraxQueryRepository.getUnmatchedOriginTicketsAdmin();
  }

  public List<CandidateAssignmentResponse> getCandidateAssignments(Integer agtraxScaleTicketId) {
    // First get the scale ticket details
    Optional<AgTraxScaleTicket> ticketOpt = agTraxRepository.findByAgtraxScaleTicketIdAndDeletedFalse(agtraxScaleTicketId);
    if (ticketOpt.isEmpty()) {
      log.warn("Scale ticket not found with ID: {}", agtraxScaleTicketId);
      return List.of(); // Return empty list instead of throwing exception
    }

    AgTraxScaleTicket ticket = ticketOpt.get();

    // Try to find candidate assignments by ticket number first
    List<CandidateAssignmentResponse> candidates = agTraxRepository.findCandidateAssignmentsByTicketNumber(
            ticket.getUserCompanyId(),
            ticket.getTicketNumber()
        ).stream()
        .map(projection -> CandidateAssignmentResponse.builder()
            .loadAssignmentId(projection.getLoadAssignmentId())
            .loadId(projection.getLoadId())
            .loadingTicketNumber(projection.getLoadingTicketNumber())
            .pickupCompanyName(projection.getPickupCompanyName())
            .dropCompanyName(projection.getDropCompanyName())
            .carrierCompanyName(projection.getCarrierCompanyName())
            .commodity(projection.getCommodity())
            .build())
        .collect(Collectors.toList());

    // If no candidates found by ticket number, try by company IDs
    if (candidates.isEmpty() && ticket.getBranchId() != null
        && ticket.getDestinationId() != null && ticket.getHaulerId() != null) {
      candidates = agTraxRepository.findCandidateAssignmentsByCompanies(
          ticket.getUserCompanyId(),
          ticket.getBranchId(),
          ticket.getDestinationId(),
          ticket.getHaulerId()
      );
    }

    return candidates;
  }

  @Transactional
  public boolean linkAssignmentToScaleTicket(int assignmentId) throws IOException {

    // Find the assignment
    Assignment assignment = loadAssignmentRepository.getReferenceById(assignmentId);

    // use the company from the assignment
    final UserCompany userCompany = assignment.getUserCompany();
    if (isNull(userCompany) || !userCompany.hasIntegration(AppConstants.Integration.AGTRAX)) {
      return false;
    }

    String loadingTicketNumber = assignment.getLoadingTicketNumber();
    Integer userCompanyId = userCompany.getUserCompanyId();

    // Check if there's an existing scale ticket linked to this assignment
    Optional<AgTraxScaleTicket> existingLinkedTicket = agTraxRepository
        .findByUserCompanyIdAndDeletedFalseAndLoadAssignmentId(
            userCompanyId, assignmentId);

    if (existingLinkedTicket.isPresent()) {
      AgTraxScaleTicket linkedTicket = existingLinkedTicket.get();

      // If loading ticket number hasn't changed, no action needed
      if (loadingTicketNumber != null && loadingTicketNumber.equals(linkedTicket.getTicketNumber())) {
        log.debug("Assignment {} already linked to correct scale ticket {}", assignmentId, loadingTicketNumber);
        return true;
      }

      unlinkAssignmentToScaleTicket(linkedTicket, assignment);
    }

    log.debug("Linking assignment {} to scale ticket with number {}", assignmentId, loadingTicketNumber);

    // Find the scale ticket
    Optional<AgTraxScaleTicket> scaleTicketOpt = agTraxRepository.findByTicketNumberAndUserCompanyIdAndDeletedFalseAndLoadAssignmentIdIsNull(
        loadingTicketNumber, userCompanyId);

    if (scaleTicketOpt.isEmpty()) {
      log.warn("No scale ticket found for ticket number: {} from company: {}", loadingTicketNumber, userCompanyId);
      return false;
    }

    AgTraxScaleTicket scaleTicket = scaleTicketOpt.get();

    return linkAssignmentToScaleTicketInternal(assignment, scaleTicket);
  }

  @Transactional
  public boolean linkAssignmentToScaleTicketManual(Integer assignmentId, Integer agtraxScaleTicketId) throws IOException {
    if (assignmentId == null) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Assignment ID is required");
    }

    // Find the assignment
    Assignment assignment = loadAssignmentRepository.findById(assignmentId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
            "Assignment not found with ID: " + assignmentId));

    // Find the scale ticket
    AgTraxScaleTicket scaleTicket = agTraxRepository.findByAgtraxScaleTicketIdAndDeletedFalse(agtraxScaleTicketId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
            "Scale ticket not found with ID: " + agtraxScaleTicketId));

    return linkAssignmentToScaleTicketInternal(assignment, scaleTicket);
  }

  private boolean linkAssignmentToScaleTicketInternal(Assignment assignment, AgTraxScaleTicket scaleTicket) throws IOException {
    // Find the assignment again for the internal method

    // Link the assignment to the scale ticket
    Integer assignmentId = assignment.getLoadAssignmentId();
    scaleTicket.setLoadAssignmentId(assignmentId);
    scaleTicket.setModifiedDate(Instant.now());

    // Set the agtrax_integration flag to true in the assignment
    assignment.setAgtraxIntegration(true);

    List<FileField> fileFieldsToSave = getFileFieldsFromFactors(scaleTicket);

    // see if there are any unmatched grades using the isUnmatchedExternalGrade flag
    boolean hasUnmatchedExternalGrades = fileFieldsToSave.stream().anyMatch(FileField::getIsUnmatchedExternalGrade);
    assignment.setHasUnmatchedExternalGrades(hasUnmatchedExternalGrades);

    // Get the loading ticket file
    File loadingTicketFile;
    if (assignment.getLoadingTicketFileId() != null) {
      loadingTicketFile = fileRepository.findById(assignment.getLoadingTicketFileId())
          .orElseThrow(() -> new BulkloadsException("Loading ticket file not found for assignment: " + assignmentId));
    } else {
      log.warn("No loading ticket file found for assignment: {}", assignmentId);

      // Try to create an image from the scale ticket data
      // Create image and get the new file ID
      Integer fileId = createFileFromText(assignmentId, fileFieldsToSave);
      log.info("Created image from scale ticket data: {}", fileId);

      updateAssignmentWithGeneratedOriginFile(assignment, fileId);

      loadAssignmentRepository.flush();
      assignment = loadAssignmentRepository.findById(assignment.getLoadAssignmentId())
          .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
              "Assignment not found with ID: " + assignmentId));

      loadingTicketFile = fileRepository.findById(assignment.getLoadingTicketFileId())
          .orElse(null);
    }

    overwriteFileFields(loadingTicketFile, fileFieldsToSave);

    // approve file

    log.info("Successfully processed {} factors for scale ticket ID {}",
        fileFieldsToSave.size(), scaleTicket.getAgtraxScaleTicketId());
    return true;
  }

  private void unlinkAssignmentToScaleTicket(AgTraxScaleTicket linkedTicket, Assignment assignment) {
    // Unlink the scale ticket
    linkedTicket.setLoadAssignmentId(null);
    linkedTicket.setModifiedDate(Instant.now());
    assignment.setAgtraxIntegration(false);
    assignment.setHasUnmatchedExternalGrades(false);

    // Clear file fields from loading ticket file if it exists
    if (assignment.getLoadingTicketFileId() != null) {
      File loadingTicketFile = fileRepository.findById(assignment.getLoadingTicketFileId())
          .orElse(null);

      // Remove the loading ticket file from assignment using AssignmentService
      List<AssignmentFileRequest> files = assignment.getAssignmentFiles().stream()
          .filter(f -> !f.getFile().getFileId().equals(assignment.getLoadingTicketFileId()))
          .map(f -> AssignmentFileRequest.builder()
              .fileId(f.getFile().getFileId())
              .build())
          .collect(Collectors.toList());

      impersonationService.impersonate(assignment.getUser().getUserId());

      UpdateLoadAssignmentRequest updateRequest = UpdateLoadAssignmentRequest.builder()
          .files(Optional.of(files))
          .build();

      assignmentService.update(assignment.getLoadAssignmentId(), updateRequest);
    }
  }

  private void overwriteFileFields(File loadingTicketFile, List<FileField> fileFieldsToSave) {
    // Now that we have a file, associate all the previously created file fields with it
    if (loadingTicketFile != null) {
      // Clear existing fields and add new ones
      List<FileField> previousFields = loadingTicketFile.getFileFields();
      for (FileField field : previousFields) {
        field.setFile(null);
      }
      loadingTicketFile.getFileFields().clear();
      fileRepository.saveAndFlush(loadingTicketFile);

      // For each file field we created earlier, update it with the file reference
      for (FileField fileField : fileFieldsToSave) {
        fileField.setFile(loadingTicketFile);
      }
      // Add all fields to the file
      loadingTicketFile.getFileFields().addAll(fileFieldsToSave);
      loadingTicketFile.setOcrProcessed(true);

      // Save the file with its updated fields
      fileRepository.save(loadingTicketFile);
    }
  }

  private void updateAssignmentWithGeneratedOriginFile(Assignment assignment, Integer fileId) {
    // Update the assignment with the new file ID using AssignmentService
    // Gather existing ids and add new one
    List<AssignmentFileRequest> files = assignment.getAssignmentFiles().stream()
        .map(f ->
            AssignmentFileRequest.builder()
                .fileId(f.getFile().getFileId())
                .build()
        )
        .collect(Collectors.toList());
    files.add(AssignmentFileRequest.builder()
        .fileId(fileId)
        .build()
    );

    UpdateLoadAssignmentRequest updateRequest = UpdateLoadAssignmentRequest.builder()
        .files(Optional.of(files))
        .build();

    User user = assignment.getUser();
    impersonationService.impersonate(user.getUserId());
    assignmentService.update(assignment.getLoadAssignmentId(), updateRequest);

  }

  private List<FileField> getFileFieldsFromFactors(AgTraxScaleTicket scaleTicket) {
    // Get the factors for this scale ticket
    List<AgTraxScaleTicketFactor> factors = agTraxRepository.findFactorsByAgtraxScaleTicketId(scaleTicket.getAgtraxScaleTicketId());
    log.debug("Found {} factors for scale ticket {}", factors.size(), scaleTicket.getAgtraxScaleTicketId());

    // Create a list to hold file fields we'll create
    List<FileField> fileFieldsToSave = new ArrayList<>();

    // Track processed factor codes to avoid duplicates
    Set<String> processedFactorCodes = new HashSet<>();

    // add non-grade fields
    fileFieldsToSave.add(createFileField(OCR_FIELD_TICKET_NUMBER, "Ticket Number", scaleTicket.getTicketNumber(), null, null, false, false));
    fileFieldsToSave.add(createFileField(OCR_FIELD_TICKET_DATE, "Ticket Date", scaleTicket.getDate(), null, null, false, false));
    fileFieldsToSave.add(createFileField(OCR_FIELD_GROSS_WEIGHT, "Gross", scaleTicket.getGrossPounds(), null, null, false, false));
    fileFieldsToSave.add(createFileField(OCR_FIELD_TARE_WEIGHT, "Tare", scaleTicket.getTarePounds(), null, null, false, false));

    // Process each factor
    for (AgTraxScaleTicketFactor factor : factors) {
      String factorCode = factor.getFactorCode();
      String factorDescription = factor.getFactorDescription();
      String factorValue = factor.getFactorValue();

      if (factorCode == null || factorValue == null) {
        continue;
      }

      // Skip if we've already processed this factor code
      if (processedFactorCodes.contains(factorCode)) {
        log.debug("Skipping duplicate factor code: {}", factorCode);
        continue;
      }
      processedFactorCodes.add(factorCode);

      // Try to find matching external grade or create new one
      ExternalGrade externalGrade = externalGradeService.getOrCreate(factorCode, factorDescription, scaleTicket.getUserCompanyId());

      // Create file field
      FileField fileField;

      if (nonNull(externalGrade.getGradeId())) {
        // Case 1: Grade has been matched
        Grade grade = gradeRepository.getReferenceById(externalGrade.getGradeId());
        // Get field definition from file_field_definitions table based on grade_id
        FileFieldDefinition fieldDefinition = fileFieldDefinitionRepository.findByGradeId(grade.getGradeId())
            .orElseThrow(() -> new BulkloadsException("Grade " + grade.getGradeName() + " has no file field definition"));

        // Use values from field definition
        fileField = createFileField(
            fieldDefinition.getFieldName(),
            fieldDefinition.getFieldLabel(),
            factorValue,
            grade.getGradeId(),
            externalGrade.getExternalGradeCode(),
            false,
            true
        );

      } else {
        // Case 2: External grade was created (or no grade match)
        fileField = createFileField(
            factorCode,
            factorDescription + " (No Match)",
            factorValue,
            null,
            externalGrade.getExternalGradeCode(),
            true,
            true
        );
      }

      // Add to our list to save later
      fileFieldsToSave.add(fileField);
    }

    return fileFieldsToSave;

  }

  private Integer createFileFromText(Integer assignmentId, List<FileField> fileFields) throws IOException {
    if (assignmentId == null) {
      log.error("Cannot create image from text: assignmentId is null");
      return null;
    }

    // Find the assignment
    Assignment assignment = loadAssignmentRepository.findById(assignmentId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
            "Assignment not found with ID: " + assignmentId));

    // Convert file fields to key-value pairs using AbstractMap.SimpleEntry
    List<Map.Entry<String, String>> keyValuePairs = fileFields.stream()
        .filter(field -> field.getFieldLabel() != null && field.getFieldValue() != null)
        .map(field -> new AbstractMap.SimpleEntry<>(field.getFieldLabel(), field.getFieldValue()))
        .collect(Collectors.toList());

    if (keyValuePairs.isEmpty()) {
      log.warn("No fields found for assignment: {}", assignmentId);
      return null;
    }

    // Create image from key-value pairs
    String title = "Loading Ticket: " + assignment.getLoadingTicketNumber();

    Path outputPath = AppConstants.Paths.TEMP.resolve("Origin_ticket_" + assignmentId + ".png");
    Path generatedFile = ImageUtils.createTextImage(title, keyValuePairs, outputPath);

    // Create a file in the system from the generated image
    LocalFileRequest localFileRequest = LocalFileRequest.builder()
        .localPath(outputPath)
        .caption("Generated ticket image for assignment " + assignmentId)
        .fileTypeId(LOADING_TICKET_FILE_TYPE_ID)
        .build();

    // Save the file to S3 and create a record in the database
    FileResponse fileResponse = fileService.createFromLocalFile(
        localFileRequest,
        false,
        AmazonS3Service.USER_FILES);

    if (fileResponse == null || fileResponse.getFileId() == null) {
      log.error("Failed to create file from image for assignment {}", assignmentId);
      return null;
    }

    return fileResponse.getFileId();
  }

  private FileField createFileField(String fieldName, String fieldLabel, Object value, Integer gradeId, String externalGradeCode,
                                    boolean isUnmatchedExternalGrade, boolean isGrade) {
    FileField fileField = new FileField();
    fileField.setFieldName(fieldName);
    fileField.setFieldLabel(fieldLabel);
    fileField.setFieldType("string");
    fileField.setFieldValue(value != null ? value.toString() : "");
    fileField.setConfidence(1.0);
    fileField.setGradeId(gradeId);
    fileField.setExternalGradeCode(externalGradeCode);
    fileField.setIsUnmatchedExternalGrade(isUnmatchedExternalGrade);
    fileField.setIsGrade(isGrade);
    return fileField;
  }

  @Transactional
  public AgTraxScaleTicket postScaleTicketsByLoadAssignmentId(int loadAssignmentId) {
    // Get the scale ticket ID for the given load assignment ID
    List<AgTraxScaleTicket> scaleTickets = agTraxRepository.findByLoadAssignmentIdAndDeletedFalse(loadAssignmentId);

    if (scaleTickets.isEmpty()) {
      return null;
    }

    AgTraxScaleTicket scaleTicket = scaleTickets.get(0);
    log.info("Reposting to agtrax for agtrax_scale_ticket_id = [{}] and load_assignment_id = [{}]",
        scaleTicket.getAgtraxScaleTicketId(), loadAssignmentId);
    postToAgTrax(scaleTicket);

    return scaleTicket;
  }

  @Transactional
  public AgTraxScaleTicket postToAgTrax(AgTraxScaleTicket scaleTicket) {

    // Generate destination payload
    String xml = generateAgTraxDestinationPayload(scaleTicket);
    scaleTicket.setDestinationPayload(xml);

    // Get postback settings or use defaults
    AgTraxUserCompanySettings settings = agTraxUserCompanySettingsRepository
        .findByUserCompanyId(scaleTicket.getUserCompanyId())
        .orElse(null);

    String postbackUrl = settings != null && !settings.getPostbackUrl().isEmpty()
        ? settings.getPostbackUrl()
        : OCR_POSTBACK_URL;
    String provider = settings != null && !settings.getPostbackPv().isEmpty()
        ? settings.getPostbackPv()
        : appProperties.getAgtrax().getProvider();
    String providerPassword = settings != null && !settings.getPostbackPvp().isEmpty()
        ? settings.getPostbackPvp()
        : appProperties.getAgtrax().getProviderPassword();
    String username = settings != null && !settings.getPostbackU().isEmpty()
        ? settings.getPostbackU()
        : appProperties.getAgtrax().getUsername();
    String password = settings != null && !settings.getPostbackP().isEmpty()
        ? settings.getPostbackP()
        : appProperties.getAgtrax().getPassword();

    // Post to AgTrax
    ResponseEntity response = postPayloadToAgTraxHttpRequest(scaleTicket, postbackUrl, provider,
        providerPassword, username, password);

    // Validate response
    if (response == null) {
      throw new ValidationException("agtrax_response", "Invalid or empty response from AgTrax");
    }
    if (!response.getStatusCode().is2xxSuccessful()) {
      throw new ValidationException("agtrax_response", "Non-200 response from AgTrax: "
          + response.getStatusCode() + ". XML sent: " + xml + ". Response: " + response);
    }

    String responseBody = response.getBody().toString();

    if (responseBody.contains("returnCode=\"0\"")) {
      log.info("AgTrax postback successful");
    } else if (responseBody.contains("returnCode=")) {
      // Parse XML and extract error descriptions
      try {
        Element root = StringUtil.parseXmlToRoot(responseBody);
        String[] errorDescriptions = extractErrorDescriptions(root);

        if (errorDescriptions.length > 0) {
          String errorMsg = "AgTrax validation failed: " + String.join("; ", errorDescriptions);
          throw new ValidationException("agtrax_response", errorMsg);
        } else {
          throw new ValidationException("agtrax_response", "AgTrax validation failed with non-zero return code. Response body: " + responseBody);
        }
      } catch (IllegalArgumentException e) {
        throw new ValidationException("agtrax_response", "AgTrax validation failed with invalid XML response. Response body: " + responseBody);
      }
    } else {
      throw new ValidationException("agtrax_response", "AgTrax validation failed with non-zero return code. Response body: " + responseBody);
    }

    log.info("AgTrax postback result: {}", response);

    // Update scale ticket with destination payload and response
    scaleTicket.setDestinationPayload(xml);
    scaleTicket.setPostResponse(response.toString());
    scaleTicket.setModifiedDate(Instant.now());
    agTraxRepository.save(scaleTicket);

    // Log the transaction
    logAgTraxTransaction(scaleTicket, response);

    return scaleTicket;
  }

  private void logAgTraxTransaction(AgTraxScaleTicket scaleTicket, ResponseEntity response) {
    try {
      AgTraxScaleTicketLog log = new AgTraxScaleTicketLog();
      log.setAgtraxScaleTicketId(scaleTicket.getAgtraxScaleTicketId());
      log.setRequestDate(Instant.now());
      log.setOriginPayload(scaleTicket.getOriginPayload());
      log.setDestinationPayload(scaleTicket.getDestinationPayload());
      log.setPostResponse(response.toString());

      agTraxScaleTicketLogRepository.save(log);
    } catch (Exception e) {
      throw new BulkloadsException("Failed to log AgTrax transaction for scale ticket " +
          scaleTicket.getAgtraxScaleTicketId() + ": " + e.getMessage(), e);
    }
  }

  private String generateAgTraxDestinationPayload(AgTraxScaleTicket ticket) {

    Assignment assignment = loadAssignmentRepository.findById(ticket.getLoadAssignmentId())
        .orElseThrow(() -> new ValidationException("general", "Assignment not found"));

    // Get destination fields as a map for quick lookup
    Map<String, String> destinationFieldValues;
    if (assignment.getUnloadingTicketFileId() != null) {
      File destinationFile = fileRepository.getReferenceById(assignment.getUnloadingTicketFileId());
      List<FileField> destFields = destinationFile.getFileFields();
      destinationFieldValues = destFields.stream()
          .collect(Collectors.toMap(f -> f.getFieldName().toLowerCase(), FileField::getFieldValue, (v1, v2) -> v1));
    } else {
      destinationFieldValues = new HashMap<>();
    }

    // get info from origin ticket
    final Integer serialId = ticket.getSerialId();
    final String reference1 = ticket.getReference1();

    // get general fields from assignment
    BigDecimal perUom = BigDecimal.ZERO;
    BigDecimal perLoad = (assignment.getBillTotal() != null ? assignment.getBillTotal() : assignment.getEstTotal());
    final String ticketNumber = assignment.getUnloadingTicketNumber();

    // get weight fields from destination ticket
    final Integer grossWeight = destinationFieldValues.get(OCR_FIELD_GROSS_WEIGHT) != null
        ? StringUtil.parseInteger(destinationFieldValues.get(OCR_FIELD_GROSS_WEIGHT)) : null;
    final Integer tareWeight = destinationFieldValues.get(OCR_FIELD_TARE_WEIGHT) != null
        ? StringUtil.parseInteger(destinationFieldValues.get(OCR_FIELD_TARE_WEIGHT)) : null;

    // get grades from origin file fields
    Map<String, String> gradeValues = new HashMap<>();

    // Get origin fields from loading ticket file
    if (assignment.getLoadingTicketFileId() != null) {
      File originFile = fileRepository.getReferenceById(assignment.getLoadingTicketFileId());
      List<FileField> originFields = originFile.getFileFields();

      // Process fields that have external grade codes
      originFields.stream()
          .filter(field -> field.getExternalGradeCode() != null && field.getIsGrade())
          .forEach(originField -> {
            String factorCode = originField.getExternalGradeCode();
            String destinationValue = "";
            log.info("Processing external grade code: {}", factorCode);
            destinationValue = destinationFieldValues.get(originField.getFieldName().toLowerCase());
            gradeValues.put(factorCode, destinationValue);
          });
    }

    // Build weight attributes
    StringBuilder weightAttrs = new StringBuilder();
    weightAttrs.append(" gross=\"").append(grossWeight).append("\"");
    weightAttrs.append(" tare=\"").append(tareWeight).append("\"");

    // Build grades
    StringBuilder grades = new StringBuilder();
    gradeValues.forEach((factorCode, fieldValue) -> {
      grades.append("<Grade factorCode=\"").append(factorCode)
          .append("\" factorValue=\"").append(fieldValue).append("\" />");
    });

    String xml = String.format("""
            <?xml version="1.0" encoding="utf-8"?>
            <WSWEIGHTSGRADES xmlns="http://www.agtrax.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.agtrax.com weightsgrades.xsd">
              <Tickets control="obTickets">
                  <Ticket postingType="Both" ticketMasterId="%s">
                      <Reference1 value="%s" />
                      <Reference2 value="%s" />
                      <Freight perUOM="%s" perLOAD="%s" />
                      <Weight>
                          <unloadWeight%s/>
                          <unloadDate value="%s" />
                      </Weight>
                      <Grades>%s</Grades>
                  </Ticket>
              </Tickets>
            </WSWEIGHTSGRADES>""",
        serialId != null ? serialId : "",
        reference1 != null ? reference1 : "",
        ticketNumber != null ? ticketNumber : "",
        perUom,
        perLoad,
        weightAttrs,
        LocalDate.now(),
        grades
    );

    return xml;
  }

  private ResponseEntity postPayloadToAgTraxHttpRequest(AgTraxScaleTicket scaleTicket,
                                                        String postbackUrl, String provider, String providerPassword, String username, String password) {

    ResponseEntity response;

    if (utilityService.isDevMode()) {
      log.info("Development mode: Skipping call to {}", postbackUrl);

      String validXmlResponse = """
          <WSWEIGHTSGRADESResponse xmlns="http://www.agtrax.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.agtrax.com/weightsgradesResponse.xsd">
              <Tickets control="obTickets">
                  <Ticket postingType="Both" ticketMasterId="11224" returnCode="0"></Ticket>
              </Tickets>
          </WSWEIGHTSGRADESResponse>
          """;

      response = new ResponseEntity(validXmlResponse, HttpStatus.OK);

    } else {
      log.info("Posting to AgTrax postback url: {}", postbackUrl);

      try {
        WebClient client = WebClient.builder().build();

        String webClientResponse = client.post()
            .uri(postbackUrl)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .body(BodyInserters
                .fromFormData("passedProvider", provider)
                .with("passedProviderPassword", providerPassword)
                .with("passedUsername", username)
                .with("passedPassword", password)
                .with("passedPostResponse", scaleTicket.getDestinationPayload()))
            .retrieve()
            .bodyToMono(String.class)
            .block();

        response = new ResponseEntity(webClientResponse, HttpStatus.OK);

      } catch (WebClientResponseException e) {
        log.error("Error posting to AgTrax: {}", e.getMessage(), e);
        log.error("Response: {}", e.getResponseBodyAsString());
        log.error("Provider 1: {}", provider);
        response = new ResponseEntity(e.getResponseBodyAsString(), e.getStatusCode());
      } catch (Exception e) {
        log.error("Error posting to AgTrax: {}", e.getMessage(), e);
        log.error("Provider 2: {}", provider);
        response = new ResponseEntity(e, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }

    return response;
  }

  private String[] extractErrorDescriptions(Element root) {
    List<String> descriptions = new ArrayList<>();

    try {
      XPathFactory xPathFactory = XPathFactory.newInstance();
      XPath xpath = xPathFactory.newXPath();

      // XPath to find all Error elements with description attribute
      String expression = "//Tickets/Ticket/Errors/Error/@description";
      NodeList nodes = (NodeList) xpath.evaluate(expression, root, XPathConstants.NODESET);

      for (int i = 0; i < nodes.getLength(); i++) {
        String description = nodes.item(i).getNodeValue();
        if (description != null && !description.trim().isEmpty()) {
          descriptions.add(description);
        }
      }
    } catch (Exception e) {
      log.error("Error extracting error descriptions with XPath: {}", e.getMessage(), e);
    }

    return descriptions.toArray(new String[0]);
  }

}
