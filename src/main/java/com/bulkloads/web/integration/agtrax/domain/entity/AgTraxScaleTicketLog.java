package com.bulkloads.web.integration.agtrax.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "agtrax_scale_tickets_log")
public class AgTraxScaleTicketLog {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "agtrax_scale_tickets_log_id")
  private Integer agtraxScaleTicketsLogId;

  @Column(name = "agtrax_scale_ticket_id")
  private Integer agtraxScaleTicketId;

  @Column(name = "request_date")
  private Instant requestDate;

  @Column(name = "origin_payload", columnDefinition = "json")
  private String originPayload;

  @Column(name = "destination_payload", columnDefinition = "text")
  private String destinationPayload;

  @Column(name = "post_response", columnDefinition = "text")
  private String postResponse;
}