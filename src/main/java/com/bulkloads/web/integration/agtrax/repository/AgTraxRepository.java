package com.bulkloads.web.integration.agtrax.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicket;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicketFactor;
import com.bulkloads.web.integration.agtrax.dto.CandidateAssignmentProjection;
import com.bulkloads.web.integration.agtrax.dto.CandidateAssignmentResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import jakarta.persistence.QueryHint;

@Repository
public interface AgTraxRepository extends JpaRepository<AgTraxScaleTicket, Integer> {

  /**
   * Find a scale ticket by ID that is not deleted
   *
   * @param agtraxScaleTicketId The scale ticket ID
   * @return Optional containing the scale ticket if found
   */
  @Query("SELECT st FROM AgTraxScaleTicket st WHERE st.agtraxScaleTicketId = :agtraxScaleTicketId AND st.deleted = false")
  Optional<AgTraxScaleTicket> findByAgtraxScaleTicketIdAndDeletedFalse(@Param("agtraxScaleTicketId") Integer agtraxScaleTicketId);

  /**
   * Find a scale ticket by ticket number and user company ID
   *
   * @param loadingTicketNumber The ticket number
   * @param userCompanyId       The user company ID
   * @return Optional containing the scale ticket if found
   */
  @Query(value = """
      SELECT st FROM AgTraxScaleTicket st
      WHERE st.ticketNumber = :loadingTicketNumber AND st.userCompanyId = :userCompanyId AND st.deleted = false AND st.loadAssignmentId IS NULL""")
  Optional<AgTraxScaleTicket> findByTicketNumberAndUserCompanyIdAndDeletedFalseAndLoadAssignmentIdIsNull(
      @Param("loadingTicketNumber") String loadingTicketNumber,
      @Param("userCompanyId") Integer userCompanyId);

  /**
   * Find a scale ticket by user company ID and load assignment ID
   *
   * @param userCompanyId    The user company ID
   * @param loadAssignmentId The load assignment ID
   * @return Optional containing the scale ticket if found
   */
  Optional<AgTraxScaleTicket> findByUserCompanyIdAndDeletedFalseAndLoadAssignmentId(
      Integer userCompanyId, Integer loadAssignmentId);

  /**
   * Get candidate assignments for a scale ticket
   */
  @Query(nativeQuery = true, value = """
      select
          la.load_assignment_id,
          la.load_id,
          la.loading_ticket_number,
          pick_c.company_name as pickup_company_name,
          drop_c.company_name as drop_company_name,
          to_abc.company_name as carrier_company_name
      from load_assignments la
      inner join loads l using(load_id)
      inner join ab_companies pick_c on l.pickup_ab_company_id = pick_c.ab_company_id
      inner join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
      inner join ab_companies to_abc on la.to_ab_company_id = to_abc.ab_company_id
      where la.user_company_id = :userCompanyId
      and la.unloading_ticket_file_id is null
      and la.deleted = 0
      and pick_c.external_ab_company_id = :branchId
      and drop_c.external_ab_company_id = :destinationId
      and to_abc.external_ab_company_id = :haulerId
      """)
  List<CandidateAssignmentResponse> findCandidateAssignmentsByCompanies(
      @Param("userCompanyId") Integer userCompanyId,
      @Param("branchId") Integer branchId,
      @Param("destinationId") Integer destinationId,
      @Param("haulerId") Integer haulerId);

  /**
   * Get candidate assignments by ticket number
   *
   * @param userCompanyId The user company ID
   * @param ticketNumber  The ticket number
   * @return List of candidate assignment responses
   */
  @Query(nativeQuery = true, value = """
      select
          la.load_assignment_id as loadAssignmentId,
          la.load_id as loadId,
          la.loading_ticket_number as loadingTicketNumber,
          pick_c.company_name as pickupCompanyName,
          drop_c.company_name as dropCompanyName,
          to_abc.company_name as carrierCompanyName,
          l.lo_commodity as commodity
      from load_assignments la
      inner join loads l using(load_id)
      inner join ab_companies pick_c on l.pickup_ab_company_id = pick_c.ab_company_id
      inner join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
      inner join ab_companies to_abc on la.to_ab_company_id = to_abc.ab_company_id
      where la.user_company_id = :userCompanyId
      and la.loading_ticket_file_id is null
      and la.deleted = 0
      and la.loading_ticket_number = :ticketNumber
      limit 1
      """)
  List<CandidateAssignmentProjection> findCandidateAssignmentsByTicketNumber(
      @Param("userCompanyId") Integer userCompanyId,
      @Param("ticketNumber") String ticketNumber);

  /**
   * Gets factors for a scale ticket
   *
   * @param agtraxScaleTicketId The scale ticket ID
   * @return List of scale ticket factors
   */
  @Query("SELECT f FROM AgTraxScaleTicketFactor f WHERE f.agtraxScaleTicketId = :agtraxScaleTicketId")
  List<AgTraxScaleTicketFactor> findFactorsByAgtraxScaleTicketId(@Param("agtraxScaleTicketId") Integer agtraxScaleTicketId);

  List<AgTraxScaleTicket> findByLoadAssignmentIdAndDeletedFalse(Integer loadAssignmentId);

  @Query("""
      SELECT st FROM AgTraxScaleTicket st
      LEFT JOIN AgTraxUserCompanySettings aucs ON aucs.userCompanyId = st.userCompanyId 
      WHERE st.agtraxScaleTicketId = :agtraxScaleTicketId AND st.deleted = false
      """)
  Optional<AgTraxScaleTicket> findScaleTicketWithPostbackUrl(@Param("agtraxScaleTicketId") Integer agtraxScaleTicketId);

  @QueryHints(@QueryHint(name = "org.hibernate.flushMode", value = "ALWAYS"))
  @Query(nativeQuery = true, value = """
      SELECT DISTINCT
        ag.agtrax_scale_ticket_id,
        COALESCE(la.bill_total, la.est_total) AS total_amount,
        la.unloading_ticket_file_id AS destination_file_id,
        ag.serial_id,
        ag.reference1,
        ag.reference2,
        CASE\s
            WHEN LOCATE('(', destff.field_name) > 0 AND LOCATE(')', destff.field_name) > LOCATE('(', destff.field_name)
            THEN SUBSTRING(
                    destff.field_name,\s
                    LOCATE('(', destff.field_name) + 1,\s
                    LOCATE(')', destff.field_name) - LOCATE('(', destff.field_name) - 1
                 )
            ELSE destff.field_name\s
        END AS external_grade_code,
        destff.field_value,
        COALESCE(la.rate, 0) AS rate,
        COALESCE(la.bill_surcharges, 0) AS bill_surcharges
      FROM
        agtrax_scale_tickets ag
        JOIN load_assignments la ON ag.load_assignment_id = la.load_assignment_id
        JOIN file_fields orff ON orff.file_id = la.loading_ticket_file_id
        JOIN file_fields destff ON destff.file_id = la.unloading_ticket_file_id AND orff.field_name = destff.field_name
        LEFT JOIN file_field_definitions ffd ON ffd.field_name = orff.field_name
        LEFT JOIN external_grades eg ON eg.user_company_id = ag.user_company_id AND eg.grade_id = ffd.grade_id
      WHERE
        ag.agtrax_scale_ticket_id = :agtraxScaleTicketId
        AND ag.deleted = 0
      """)
  List<Object[]> findDestinationPayloadData(@Param("agtraxScaleTicketId") Integer agtraxScaleTicketId);
}
