package com.bulkloads.web.integration.general.service.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class IntegrationContractRequest {
  Optional<String> externalContractId;
  Optional<String> buySell;
  Optional<String> contractNumber;
  Optional<BigDecimal> rate;
  @Schema(name = "rate_type", description = "Allowed values are 100, 1000, 2000, 2204.62, 32, 45, 48, 50, 56, 60, gallon, liter. "
      + "They correspond to: per cwt (100 lbs), per metric ton (kg), per ton, per metric ton (lbs), per bushel (32 lbs), per bushel (45 lbs), per bushel "
      + "(48 lbs), per bushel (50 lbs), per bushel (56 lbs), per bushel (60 lbs), per gallon, per liter.")
  Optional<String> rateType;
  Optional<BigDecimal> freightRate;
  @Schema(name = "freight_rate_type", description = "Allowed values are 100, 1000, 2000, 2204.62, 32, 45, 48, 50, 56, 60, gallon, liter, flat, hour, mile. "
      + "They correspond to: per cwt (100 lbs), per metric ton (kg), per ton, per metric ton (lbs), per bushel (32 lbs), per bushel (45 lbs), per bushel "
      + "(48 lbs), per bushel (50 lbs), per bushel (56 lbs), per bushel (60 lbs), per gallon, per liter, flat rate, per hour, per mile.")
  Optional<String> freightRateType;

  @Schema(
      name = "weight",
      description = "The total weight of the contract in pounds (lbs). This is the primary field for determining contract size. You only need to provide one"
          + " of `weight`, `quantity`, or `numberOfLoads`. The system uses a priority order of `weight` > `quantity` > `numberOfLoads` to "
          + "calculate and validate the other two fields."
  )
  Optional<BigDecimal> weight;
  @Schema(
      name = "quantity",
      description = "The total quantity of the contract (e.g., tons, bushels). This is the secondary field for determining contract size and is used"
          + "if `weight` is not provided."
  )
  Optional<BigDecimal> quantity;
  @Schema(
      name = "number_of_loads",
      description = "The total number of truck loads for the contract. This is the tertiary field for determining contract size and is used if neither"
          + "`weight` nor `quantity` are provided."
  )
  Optional<Integer> numberOfLoads;
  @Schema(
      name = "remaining_quantity",
      description = "The remaining quantity on the contract. Follows the same calculation/validation logic as `quantity` but for the remaining amount. "
          + "Cannot be greater than the total `quantity`."
  )
  Optional<BigDecimal> remainingQuantity;
  @Schema(
      name = "remaining_weight",
      description = "The remaining weight on the contract in pounds (lbs). Follows the same calculation/validation logic as `weight` but for "
          + "the remaining amount. Cannot be greater than the total `weight`."
  )
  Optional<BigDecimal> remainingWeight;
  Optional<IntegrationCommodityRequest> commodity;
  Optional<LocalDate> shipFromDate;
  Optional<LocalDate> shipToDate;
  Optional<String> contactInfo;
  Optional<String> notes;
  Optional<IntegrationAbCompanyRequest> origin;
  Optional<IntegrationAbCompanyRequest> destination;
}



