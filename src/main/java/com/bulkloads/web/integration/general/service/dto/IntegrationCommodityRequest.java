package com.bulkloads.web.integration.general.service.dto;

import java.util.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class IntegrationCommodityRequest {
  @Schema(name = "commodity_id", description = "If provided, will look up the commodity by ID instead of creating/updating")
  Optional<Integer> commodityId;

  @Schema(name = "external_commodity_id")
  Optional<String> externalCommodityId;

  Optional<String> commodity;

  @Schema(name = "commodity_abbr")
  Optional<String> commodityAbbr;

}