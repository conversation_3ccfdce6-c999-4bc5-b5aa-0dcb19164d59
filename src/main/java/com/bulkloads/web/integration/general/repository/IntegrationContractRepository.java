package com.bulkloads.web.integration.general.repository;

import java.time.Instant;
import com.bulkloads.web.contracts.domain.entity.Contract;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IntegrationContractRepository extends JpaRepository<Contract, Integer> {

  @Query("""
      SELECT c
      FROM Contract c
        JOIN FETCH c.pickupAbCompany
        JOIN FETCH c.dropAbCompany
        LEFT JOIN FETCH c.commodity
      WHERE c.userCompany.userCompanyId = :companyId
      AND (:externalContractId IS NULL OR c.externalContractId = :externalContractId)
      AND (:buySell IS NULL OR UPPER(c.buySell) = UPPER(:buySell))
      AND (:contractStatus IS NULL OR
          (UPPER(:contractStatus) = 'OPEN' AND c.contractStatus != 'CLOSED') OR
          (UPPER(:contractStatus) = 'CLOSED' AND c.contractStatus = 'CLOSED'))
      AND (:lastModifiedDate IS NULL OR c.modifiedDate >= :lastModifiedDate)
      AND c.deleted = false
      """)
  Page<Contract> findContracts(
      @Param("companyId") Integer companyId,
      @Param("externalContractId") String externalContractId,
      @Param("buySell") String buySell,
      @Param("contractStatus") String contractStatus,
      @Param("lastModifiedDate") Instant lastModifiedDate,
      Pageable pageable
  );

}