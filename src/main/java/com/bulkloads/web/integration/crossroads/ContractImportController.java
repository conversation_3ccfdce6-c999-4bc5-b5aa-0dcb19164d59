/*
package com.bulkloads.web.integration.crossroads;

import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_CONTRACTS;
import java.io.IOException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/integration/crossroads")
@Tag(name = "Cross Roads Coop")
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasAuthority('" + MANAGE_CONTRACTS + "')")
public class ContractImportController {

  private final ContractImportService contractImportService;
  private final ContractImportJob contractImportJob;

  @Operation(summary = "Import contracts")
  @PostMapping(path = "/contracts/import", consumes = {"multipart/form-data"})
  public ContractImportResponse importContractsFromExcel(
      @RequestParam("file") MultipartFile file) throws IOException {

    return contractImportService.importContractsFromExcel(file.getInputStream());
  }

  @Operation(summary = "Import contracts from ftp")
  @PostMapping(path = "/contracts/import_from_ftp")
  public void importContractsFromExcelFromFtp() {
    contractImportJob.processFiles();
  }

}
*/