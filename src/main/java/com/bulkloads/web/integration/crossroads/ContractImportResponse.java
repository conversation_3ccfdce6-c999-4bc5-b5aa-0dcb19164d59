package com.bulkloads.web.integration.crossroads;

import java.io.FileOutputStream;
import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.stream.Stream;
import com.bulkloads.exception.BulkloadsException;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import lombok.Data;

@Data
public class ContractImportResponse {

  public static final String SUCCESSFUL = "Successful";
  public static final String URL = "Url";
  public static final String MESSAGE = "Message";
  public static final String DATE_PROCESSED = "Date Processed";
  public static final String CONTRACT_ID = "ContractID";

  private String message = "";
  private int totalRecords;
  private int failedRecords;
  private int successRecords;
  private Workbook processedWorkbook;

  // constructor
  public ContractImportResponse(List<String> headers) {
    initProcessedWorkbook(headers);
  }

  public void addRecord(List<XSSFCell> record, boolean successful, String url, String message, Integer contractId) {
    totalRecords++;
    if (successful) {
      incrementSuccessRecords();
    } else {
      incrementFailedRecords();
    }

    // append the data cells
    Workbook workbook = getProcessedWorkbook();
    Sheet sheet = workbook.getSheetAt(0);
    Row row = sheet.createRow(totalRecords);
    addCellsToRow(row, record);

    // append the report cells

    // successful
    Cell cell = row.createCell(record.size());
    cell.setCellValue(successful ? "Yes" : "No");

    // url
    cell = row.createCell(record.size() + 1);
    if (!"".equals(url)) {
      // Create a CreationHelper to help create the hyperlink
      CreationHelper creationHelper = workbook.getCreationHelper();
      // Create the hyperlink
      Hyperlink hyperlink = creationHelper.createHyperlink(HyperlinkType.URL);
      hyperlink.setAddress(url);
      // Attach the hyperlink to the cell
      cell.setHyperlink(hyperlink);
      // Optional: Set the style for the hyperlink cell
      CellStyle hlinkStyle = workbook.createCellStyle();
      Font hlinkFont = workbook.createFont();
      hlinkFont.setUnderline(Font.U_SINGLE);
      hlinkFont.setColor(IndexedColors.BLUE.getIndex());
      hlinkStyle.setFont(hlinkFont);
      cell.setCellStyle(hlinkStyle);

      cell.setCellValue("View");
    }

    // message
    cell = row.createCell(record.size() + 2);
    cell.setCellValue(message);

    // date processed in UTC
    cell = row.createCell(record.size() + 3);
    cell.setCellValue(Instant.now().toString());

    // contract id
    cell = row.createCell(record.size() + 4);
    cell.setCellValue(contractId != null ? String.valueOf(contractId) : "");

  }

  public void saveProcessedFile(String fileName) {
    // save the processed file
    Workbook wb = getProcessedWorkbook();

    // Iterate over each column to auto-size
    /*
    Sheet sheet = wb.getSheetAt(0);
    try {
      for (int i = 0; i < sheet.getRow(0).getLastCellNum(); i++) {
        // Auto-size each column
        sheet.autoSizeColumn(i);
      }
    } catch (Exception e) {
      // may need to ensure that fontconfig is correctly set up.
      // If you're running the application in a Docker container or headless environment, ensure the necessary font packages are installed.
    }
    */

    // Write the output to a file
    try (FileOutputStream fileOut = new FileOutputStream(fileName)) {
      wb.write(fileOut);
    } catch (IOException e) {
      throw new BulkloadsException("Error saving processed file", e);
    }
  }

  private void incrementFailedRecords() {
    failedRecords++;
  }

  private void incrementSuccessRecords() {
    successRecords++;
  }

  private void initProcessedWorkbook(List<String> baseHeaders) {
    Workbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet("Data");

    // append the processed headers
    List<String> headers = List.copyOf(
        Stream.concat(baseHeaders.stream(), Stream.of(SUCCESSFUL, URL, MESSAGE, DATE_PROCESSED, CONTRACT_ID)).toList()
    );

    // Create header row
    Row headerRow = sheet.createRow(0);
    for (int i = 0; i < headers.size(); i++) {
      Cell cell = headerRow.createCell(i);
      cell.setCellValue(headers.get(i));
    }

    setProcessedWorkbook(workbook);
  }

  private void addCellsToRow(Row row, List<XSSFCell> cellList) {
    int colNum = -1;
    for (XSSFCell cell : cellList) {
      colNum++;
      Cell newCell = row.createCell(colNum, cell.getCellType());

      // Copy the value and style from the original cell
      switch (cell.getCellType()) {
        case STRING:
          newCell.setCellValue(cell.getStringCellValue());
          break;
        case NUMERIC:
          if (DateUtil.isCellDateFormatted(cell)) {
            newCell.setCellValue(cell.getDateCellValue());
          } else {
            newCell.setCellValue(cell.getNumericCellValue());
          }
          break;
        case BOOLEAN:
          newCell.setCellValue(cell.getBooleanCellValue());
          break;
        case FORMULA:
          newCell.setCellFormula(cell.getCellFormula());
          break;
        default:
          break;
      }
      // newCell.setCellStyle(cell.getCellStyle());
      // Copy the cell style
      CellStyle newCellStyle = getProcessedWorkbook().createCellStyle();
      newCellStyle.cloneStyleFrom(cell.getCellStyle());
      newCell.setCellStyle(newCellStyle);
    }
  }
}
