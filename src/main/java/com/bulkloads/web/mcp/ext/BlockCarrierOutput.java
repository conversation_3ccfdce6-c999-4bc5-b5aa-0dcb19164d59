package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * BlockCarrierOutput
 */
@JsonPropertyOrder({BlockCarrierOutput.JSON_PROPERTY_RESULT, BlockCarrierOutput.JSON_PROPERTY_MESSAGE})

public class BlockCarrierOutput {

  public static final String JSON_PROPERTY_RESULT = "Result";
  public static final String JSON_PROPERTY_MESSAGE = "Message";
  private Boolean result;
  private String message;

  public BlockCarrierOutput() {
  }

  public BlockCarrierOutput result(Boolean result) {

    this.result = result;
    return this;
  }

  /**
   * Get result
   *
   * @return result
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESULT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getResult() {
    return result;
  }


  @JsonProperty(JSON_PROPERTY_RESULT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResult(Boolean result) {
    this.result = result;
  }


  public BlockCarrierOutput message(String message) {

    this.message = message;
    return this;
  }

  /**
   * Get message
   *
   * @return message
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMessage() {
    return message;
  }


  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMessage(String message) {
    this.message = message;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BlockCarrierOutput blockCarrierOutput = (BlockCarrierOutput) o;
    return Objects.equals(this.result, blockCarrierOutput.result) && Objects.equals(this.message, blockCarrierOutput.message);
  }

  @Override
  public int hashCode() {
    return Objects.hash(result, message);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BlockCarrierOutput {\n");
    sb.append("    result: ").append(toIndentedString(result)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

