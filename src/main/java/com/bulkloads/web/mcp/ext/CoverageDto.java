package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CoverageDto
 */
@JsonPropertyOrder({CoverageDto.JSON_PROPERTY_INSURER_NAME, CoverageDto.JSON_PROPERTY_INSURER_A_M_BEST_RATING, CoverageDto.JSON_PROPERTY_TYPE,
    CoverageDto.JSON_PROPERTY_POLICY_NUMBER, CoverageDto.JSON_PROPERTY_EXPIRATION_DATE, CoverageDto.JSON_PROPERTY_COVERAGE_LIMIT,
    CoverageDto.JSON_PROPERTY_DEDUCTABLE, CoverageDto.JSON_PROPERTY_REFER_BREAKDOWN, CoverageDto.JSON_PROPERTY_REFER_BREAK_DEDUCT})

public class CoverageDto {

  public static final String JSON_PROPERTY_INSURER_NAME = "insurerName";
  public static final String JSON_PROPERTY_INSURER_A_M_BEST_RATING = "insurerAMBestRating";
  public static final String JSON_PROPERTY_TYPE = "type";
  public static final String JSON_PROPERTY_POLICY_NUMBER = "policyNumber";
  public static final String JSON_PROPERTY_EXPIRATION_DATE = "expirationDate";
  public static final String JSON_PROPERTY_COVERAGE_LIMIT = "coverageLimit";
  public static final String JSON_PROPERTY_DEDUCTABLE = "deductable";
  public static final String JSON_PROPERTY_REFER_BREAKDOWN = "referBreakdown";
  public static final String JSON_PROPERTY_REFER_BREAK_DEDUCT = "referBreakDeduct";
  private String insurerName;
  private String insurerAMBestRating;
  private String type;
  private String policyNumber;
  private String expirationDate;
  private String coverageLimit;
  private String deductable;
  private String referBreakdown;
  private String referBreakDeduct;

  public CoverageDto() {
  }

  public CoverageDto insurerName(String insurerName) {

    this.insurerName = insurerName;
    return this;
  }

  /**
   * Get insurerName
   *
   * @return insurerName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSURER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInsurerName() {
    return insurerName;
  }


  @JsonProperty(JSON_PROPERTY_INSURER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInsurerName(String insurerName) {
    this.insurerName = insurerName;
  }


  public CoverageDto insurerAMBestRating(String insurerAMBestRating) {

    this.insurerAMBestRating = insurerAMBestRating;
    return this;
  }

  /**
   * Get insurerAMBestRating
   *
   * @return insurerAMBestRating
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSURER_A_M_BEST_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInsurerAMBestRating() {
    return insurerAMBestRating;
  }


  @JsonProperty(JSON_PROPERTY_INSURER_A_M_BEST_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInsurerAMBestRating(String insurerAMBestRating) {
    this.insurerAMBestRating = insurerAMBestRating;
  }


  public CoverageDto type(String type) {

    this.type = type;
    return this;
  }

  /**
   * Get type
   *
   * @return type
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getType() {
    return type;
  }


  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setType(String type) {
    this.type = type;
  }


  public CoverageDto policyNumber(String policyNumber) {

    this.policyNumber = policyNumber;
    return this;
  }

  /**
   * Get policyNumber
   *
   * @return policyNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POLICY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPolicyNumber() {
    return policyNumber;
  }


  @JsonProperty(JSON_PROPERTY_POLICY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPolicyNumber(String policyNumber) {
    this.policyNumber = policyNumber;
  }


  public CoverageDto expirationDate(String expirationDate) {

    this.expirationDate = expirationDate;
    return this;
  }

  /**
   * Get expirationDate
   *
   * @return expirationDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXPIRATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getExpirationDate() {
    return expirationDate;
  }


  @JsonProperty(JSON_PROPERTY_EXPIRATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExpirationDate(String expirationDate) {
    this.expirationDate = expirationDate;
  }


  public CoverageDto coverageLimit(String coverageLimit) {

    this.coverageLimit = coverageLimit;
    return this;
  }

  /**
   * Get coverageLimit
   *
   * @return coverageLimit
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COVERAGE_LIMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCoverageLimit() {
    return coverageLimit;
  }


  @JsonProperty(JSON_PROPERTY_COVERAGE_LIMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCoverageLimit(String coverageLimit) {
    this.coverageLimit = coverageLimit;
  }


  public CoverageDto deductable(String deductable) {

    this.deductable = deductable;
    return this;
  }

  /**
   * Get deductable
   *
   * @return deductable
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DEDUCTABLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDeductable() {
    return deductable;
  }


  @JsonProperty(JSON_PROPERTY_DEDUCTABLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeductable(String deductable) {
    this.deductable = deductable;
  }


  public CoverageDto referBreakdown(String referBreakdown) {

    this.referBreakdown = referBreakdown;
    return this;
  }

  /**
   * Get referBreakdown
   *
   * @return referBreakdown
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFER_BREAKDOWN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReferBreakdown() {
    return referBreakdown;
  }


  @JsonProperty(JSON_PROPERTY_REFER_BREAKDOWN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReferBreakdown(String referBreakdown) {
    this.referBreakdown = referBreakdown;
  }


  public CoverageDto referBreakDeduct(String referBreakDeduct) {

    this.referBreakDeduct = referBreakDeduct;
    return this;
  }

  /**
   * Get referBreakDeduct
   *
   * @return referBreakDeduct
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFER_BREAK_DEDUCT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReferBreakDeduct() {
    return referBreakDeduct;
  }


  @JsonProperty(JSON_PROPERTY_REFER_BREAK_DEDUCT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReferBreakDeduct(String referBreakDeduct) {
    this.referBreakDeduct = referBreakDeduct;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CoverageDto coverageDto = (CoverageDto) o;
    return Objects.equals(this.insurerName, coverageDto.insurerName) && Objects.equals(this.insurerAMBestRating, coverageDto.insurerAMBestRating)
           && Objects.equals(this.type, coverageDto.type) && Objects.equals(this.policyNumber, coverageDto.policyNumber) && Objects.equals(this.expirationDate,
        coverageDto.expirationDate) && Objects.equals(this.coverageLimit, coverageDto.coverageLimit) && Objects.equals(this.deductable, coverageDto.deductable)
           && Objects.equals(this.referBreakdown, coverageDto.referBreakdown) && Objects.equals(this.referBreakDeduct, coverageDto.referBreakDeduct);
  }

  @Override
  public int hashCode() {
    return Objects.hash(insurerName, insurerAMBestRating, type, policyNumber, expirationDate, coverageLimit, deductable, referBreakdown, referBreakDeduct);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CoverageDto {\n");
    sb.append("    insurerName: ").append(toIndentedString(insurerName)).append("\n");
    sb.append("    insurerAMBestRating: ").append(toIndentedString(insurerAMBestRating)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    policyNumber: ").append(toIndentedString(policyNumber)).append("\n");
    sb.append("    expirationDate: ").append(toIndentedString(expirationDate)).append("\n");
    sb.append("    coverageLimit: ").append(toIndentedString(coverageLimit)).append("\n");
    sb.append("    deductable: ").append(toIndentedString(deductable)).append("\n");
    sb.append("    referBreakdown: ").append(toIndentedString(referBreakdown)).append("\n");
    sb.append("    referBreakDeduct: ").append(toIndentedString(referBreakDeduct)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

