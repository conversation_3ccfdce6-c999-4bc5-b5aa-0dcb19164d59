package com.bulkloads.web.mcp.ext;

import java.time.LocalDateTime;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CustomerAgreementDto
 */
@JsonPropertyOrder({CustomerAgreementDto.JSON_PROPERTY_AGREEMENT_NAME, CustomerAgreementDto.JSON_PROPERTY_CREATED_DATE,
    CustomerAgreementDto.JSON_PROPERTY_CREATED_BY, CustomerAgreementDto.JSON_PROPERTY_BLOB_NAME, CustomerAgreementDto.JSON_PROPERTY_CUSTOMER})

public class CustomerAgreementDto {

  public static final String JSON_PROPERTY_AGREEMENT_NAME = "AgreementName";
  public static final String JSON_PROPERTY_CREATED_DATE = "CreatedDate";
  public static final String JSON_PROPERTY_CREATED_BY = "CreatedBy";
  public static final String JSON_PROPERTY_BLOB_NAME = "BlobName";
  public static final String JSON_PROPERTY_CUSTOMER = "Customer";
  private String agreementName;
  private LocalDateTime createdDate;
  private String createdBy;
  private String blobName;
  private CustomerDto customer;

  public CustomerAgreementDto() {
  }

  public CustomerAgreementDto agreementName(String agreementName) {

    this.agreementName = agreementName;
    return this;
  }

  /**
   * Get agreementName
   *
   * @return agreementName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AGREEMENT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAgreementName() {
    return agreementName;
  }


  @JsonProperty(JSON_PROPERTY_AGREEMENT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAgreementName(String agreementName) {
    this.agreementName = agreementName;
  }


  public CustomerAgreementDto createdDate(LocalDateTime createdDate) {

    this.createdDate = createdDate;
    return this;
  }

  /**
   * Get createdDate
   *
   * @return createdDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getCreatedDate() {
    return createdDate;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedDate(LocalDateTime createdDate) {
    this.createdDate = createdDate;
  }


  public CustomerAgreementDto createdBy(String createdBy) {

    this.createdBy = createdBy;
    return this;
  }

  /**
   * Get createdBy
   *
   * @return createdBy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCreatedBy() {
    return createdBy;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }


  public CustomerAgreementDto blobName(String blobName) {

    this.blobName = blobName;
    return this;
  }

  /**
   * Get blobName
   *
   * @return blobName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BLOB_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBlobName() {
    return blobName;
  }


  @JsonProperty(JSON_PROPERTY_BLOB_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBlobName(String blobName) {
    this.blobName = blobName;
  }


  public CustomerAgreementDto customer(CustomerDto customer) {

    this.customer = customer;
    return this;
  }

  /**
   * Get customer
   *
   * @return customer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CUSTOMER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CustomerDto getCustomer() {
    return customer;
  }


  @JsonProperty(JSON_PROPERTY_CUSTOMER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCustomer(CustomerDto customer) {
    this.customer = customer;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerAgreementDto customerAgreementDto = (CustomerAgreementDto) o;
    return Objects.equals(this.agreementName, customerAgreementDto.agreementName) && Objects.equals(this.createdDate, customerAgreementDto.createdDate)
           && Objects.equals(this.createdBy, customerAgreementDto.createdBy) && Objects.equals(this.blobName, customerAgreementDto.blobName) && Objects.equals(
        this.customer, customerAgreementDto.customer);
  }

  @Override
  public int hashCode() {
    return Objects.hash(agreementName, createdDate, createdBy, blobName, customer);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerAgreementDto {\n");
    sb.append("    agreementName: ").append(toIndentedString(agreementName)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    blobName: ").append(toIndentedString(blobName)).append("\n");
    sb.append("    customer: ").append(toIndentedString(customer)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

