package com.bulkloads.web.mcp.ext;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData
 */
@JsonPropertyOrder({MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData.JSON_PROPERTY_STATUS,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData.JSON_PROPERTY_CERTIFICATES})
@JsonTypeName("MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.CertData")

public class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData {

  public static final String JSON_PROPERTY_STATUS = "Status";
  public static final String JSON_PROPERTY_CERTIFICATES = "Certificates";
  private String status;
  private List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate> certificates;

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData() {
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData status(String status) {

    this.status = status;
    return this;
  }

  /**
   * Get status
   *
   * @return status
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(String status) {
    this.status = status;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData certificates(
      List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate> certificates) {

    this.certificates = certificates;
    return this;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData addCertificatesItem(
      MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate certificatesItem) {
    if (this.certificates == null) {
      this.certificates = new ArrayList<>();
    }
    this.certificates.add(certificatesItem);
    return this;
  }

  /**
   * Get certificates
   *
   * @return certificates
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CERTIFICATES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate> getCertificates() {
    return certificates;
  }


  @JsonProperty(JSON_PROPERTY_CERTIFICATES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCertificates(List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate> certificates) {
    this.certificates = certificates;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData =
        (MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData) o;
    return Objects.equals(this.status, myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData.status) && Objects.equals(this.certificates,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData.certificates);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, certificates);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData {\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    certificates: ").append(toIndentedString(certificates)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

