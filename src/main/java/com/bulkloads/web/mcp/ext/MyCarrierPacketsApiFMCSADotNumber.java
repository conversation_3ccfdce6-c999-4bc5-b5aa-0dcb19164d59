package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSADotNumber
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSADotNumber.JSON_PROPERTY_STATUS, MyCarrierPacketsApiFMCSADotNumber.JSON_PROPERTY_VALUE})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.DotNumber")

public class MyCarrierPacketsApiFMCSADotNumber {

  public static final String JSON_PROPERTY_STATUS = "status";
  public static final String JSON_PROPERTY_VALUE = "Value";
  private String status;
  private String value;

  public MyCarrierPacketsApiFMCSADotNumber() {
  }

  public MyCarrierPacketsApiFMCSADotNumber status(String status) {

    this.status = status;
    return this;
  }

  /**
   * Get status
   *
   * @return status
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(String status) {
    this.status = status;
  }


  public MyCarrierPacketsApiFMCSADotNumber value(String value) {

    this.value = value;
    return this;
  }

  /**
   * Get value
   *
   * @return value
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getValue() {
    return value;
  }


  @JsonProperty(JSON_PROPERTY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValue(String value) {
    this.value = value;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSADotNumber myCarrierPacketsApiFMCSADotNumber = (MyCarrierPacketsApiFMCSADotNumber) o;
    return Objects.equals(this.status, myCarrierPacketsApiFMCSADotNumber.status) && Objects.equals(this.value, myCarrierPacketsApiFMCSADotNumber.value);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, value);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSADotNumber {\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

