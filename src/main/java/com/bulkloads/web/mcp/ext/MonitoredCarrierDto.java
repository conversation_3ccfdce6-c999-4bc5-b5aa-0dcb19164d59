package com.bulkloads.web.mcp.ext;

import java.time.LocalDateTime;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * MonitoredCarrierDto
 */
@JsonPropertyOrder({MonitoredCarrierDto.JSON_PROPERTY_DO_T_NUMBER, MonitoredCarrierDto.JSON_PROPERTY_DOCKET_NUMBER,
    MonitoredCarrierDto.JSON_PROPERTY_INTRASTATE_NUMBER, MonitoredCarrierDto.JSON_PROPERTY_CREATED_DATE, MonitoredCarrierDto.JSON_PROPERTY_CREATED_BY,
    MonitoredCarrierDto.JSON_PROPERTY_LAST_MODIFIED_DATE, MonitoredCarrierDto.JSON_PROPERTY_LAST_MODIFIED_BY})

public class MonitoredCarrierDto {

  public static final String JSON_PROPERTY_DO_T_NUMBER = "DOTNumber";
  public static final String JSON_PROPERTY_DOCKET_NUMBER = "DocketNumber";
  public static final String JSON_PROPERTY_INTRASTATE_NUMBER = "IntrastateNumber";
  public static final String JSON_PROPERTY_CREATED_DATE = "CreatedDate";
  public static final String JSON_PROPERTY_CREATED_BY = "CreatedBy";
  public static final String JSON_PROPERTY_LAST_MODIFIED_DATE = "LastModifiedDate";
  public static final String JSON_PROPERTY_LAST_MODIFIED_BY = "LastModifiedBy";
  private Integer doTNumber;
  private String docketNumber;
  private String intrastateNumber;
  private LocalDateTime createdDate;
  private String createdBy;
  private LocalDateTime lastModifiedDate;
  private String lastModifiedBy;

  public MonitoredCarrierDto() {
  }

  public MonitoredCarrierDto doTNumber(Integer doTNumber) {

    this.doTNumber = doTNumber;
    return this;
  }

  /**
   * Get doTNumber
   *
   * @return doTNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getDoTNumber() {
    return doTNumber;
  }


  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDoTNumber(Integer doTNumber) {
    this.doTNumber = doTNumber;
  }


  public MonitoredCarrierDto docketNumber(String docketNumber) {

    this.docketNumber = docketNumber;
    return this;
  }

  /**
   * Get docketNumber
   *
   * @return docketNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDocketNumber() {
    return docketNumber;
  }


  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDocketNumber(String docketNumber) {
    this.docketNumber = docketNumber;
  }


  public MonitoredCarrierDto intrastateNumber(String intrastateNumber) {

    this.intrastateNumber = intrastateNumber;
    return this;
  }

  /**
   * Get intrastateNumber
   *
   * @return intrastateNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTRASTATE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getIntrastateNumber() {
    return intrastateNumber;
  }


  @JsonProperty(JSON_PROPERTY_INTRASTATE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIntrastateNumber(String intrastateNumber) {
    this.intrastateNumber = intrastateNumber;
  }


  public MonitoredCarrierDto createdDate(LocalDateTime createdDate) {

    this.createdDate = createdDate;
    return this;
  }

  /**
   * Get createdDate
   *
   * @return createdDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getCreatedDate() {
    return createdDate;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedDate(LocalDateTime createdDate) {
    this.createdDate = createdDate;
  }


  public MonitoredCarrierDto createdBy(String createdBy) {

    this.createdBy = createdBy;
    return this;
  }

  /**
   * Get createdBy
   *
   * @return createdBy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCreatedBy() {
    return createdBy;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }


  public MonitoredCarrierDto lastModifiedDate(LocalDateTime lastModifiedDate) {

    this.lastModifiedDate = lastModifiedDate;
    return this;
  }

  /**
   * Get lastModifiedDate
   *
   * @return lastModifiedDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getLastModifiedDate() {
    return lastModifiedDate;
  }


  @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
    this.lastModifiedDate = lastModifiedDate;
  }


  public MonitoredCarrierDto lastModifiedBy(String lastModifiedBy) {

    this.lastModifiedBy = lastModifiedBy;
    return this;
  }

  /**
   * Get lastModifiedBy
   *
   * @return lastModifiedBy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastModifiedBy() {
    return lastModifiedBy;
  }


  @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastModifiedBy(String lastModifiedBy) {
    this.lastModifiedBy = lastModifiedBy;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MonitoredCarrierDto monitoredCarrierDto = (MonitoredCarrierDto) o;
    return Objects.equals(this.doTNumber, monitoredCarrierDto.doTNumber) && Objects.equals(this.docketNumber, monitoredCarrierDto.docketNumber)
           && Objects.equals(this.intrastateNumber, monitoredCarrierDto.intrastateNumber) && Objects.equals(this.createdDate, monitoredCarrierDto.createdDate)
           && Objects.equals(this.createdBy, monitoredCarrierDto.createdBy) && Objects.equals(this.lastModifiedDate, monitoredCarrierDto.lastModifiedDate)
           && Objects.equals(this.lastModifiedBy, monitoredCarrierDto.lastModifiedBy);
  }

  @Override
  public int hashCode() {
    return Objects.hash(doTNumber, docketNumber, intrastateNumber, createdDate, createdBy, lastModifiedDate, lastModifiedBy);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MonitoredCarrierDto {\n");
    sb.append("    doTNumber: ").append(toIndentedString(doTNumber)).append("\n");
    sb.append("    docketNumber: ").append(toIndentedString(docketNumber)).append("\n");
    sb.append("    intrastateNumber: ").append(toIndentedString(intrastateNumber)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    lastModifiedDate: ").append(toIndentedString(lastModifiedDate)).append("\n");
    sb.append("    lastModifiedBy: ").append(toIndentedString(lastModifiedBy)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

