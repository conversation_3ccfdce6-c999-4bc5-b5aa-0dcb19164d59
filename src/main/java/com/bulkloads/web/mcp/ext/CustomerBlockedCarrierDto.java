package com.bulkloads.web.mcp.ext;

import java.time.LocalDateTime;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CustomerBlockedCarrierDto
 */
@JsonPropertyOrder({CustomerBlockedCarrierDto.JSON_PROPERTY_DO_T_NUMBER, CustomerBlockedCarrierDto.JSON_PROPERTY_DOCKET_NUMBER,
    CustomerBlockedCarrierDto.JSON_PROPERTY_INTRASTATE_NUMBER, CustomerBlockedCarrierDto.JSON_PROPERTY_CREATED_DATE,
    CustomerBlockedCarrierDto.JSON_PROPERTY_CREATED_BY, CustomerBlockedCarrierDto.JSON_PROPERTY_LAST_MODIFIED_DATE,
    CustomerBlockedCarrierDto.JSON_PROPERTY_LAST_MODIFIED_BY})

public class CustomerBlockedCarrierDto {

  public static final String JSON_PROPERTY_DO_T_NUMBER = "DOTNumber";
  public static final String JSON_PROPERTY_DOCKET_NUMBER = "DocketNumber";
  public static final String JSON_PROPERTY_INTRASTATE_NUMBER = "IntrastateNumber";
  public static final String JSON_PROPERTY_CREATED_DATE = "CreatedDate";
  public static final String JSON_PROPERTY_CREATED_BY = "CreatedBy";
  public static final String JSON_PROPERTY_LAST_MODIFIED_DATE = "LastModifiedDate";
  public static final String JSON_PROPERTY_LAST_MODIFIED_BY = "LastModifiedBy";
  private Integer doTNumber;
  private String docketNumber;
  private String intrastateNumber;
  private LocalDateTime createdDate;
  private String createdBy;
  private LocalDateTime lastModifiedDate;
  private String lastModifiedBy;

  public CustomerBlockedCarrierDto() {
  }

  public CustomerBlockedCarrierDto doTNumber(Integer doTNumber) {

    this.doTNumber = doTNumber;
    return this;
  }

  /**
   * Get doTNumber
   *
   * @return doTNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getDoTNumber() {
    return doTNumber;
  }


  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDoTNumber(Integer doTNumber) {
    this.doTNumber = doTNumber;
  }


  public CustomerBlockedCarrierDto docketNumber(String docketNumber) {

    this.docketNumber = docketNumber;
    return this;
  }

  /**
   * Get docketNumber
   *
   * @return docketNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDocketNumber() {
    return docketNumber;
  }


  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDocketNumber(String docketNumber) {
    this.docketNumber = docketNumber;
  }


  public CustomerBlockedCarrierDto intrastateNumber(String intrastateNumber) {

    this.intrastateNumber = intrastateNumber;
    return this;
  }

  /**
   * Get intrastateNumber
   *
   * @return intrastateNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTRASTATE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getIntrastateNumber() {
    return intrastateNumber;
  }


  @JsonProperty(JSON_PROPERTY_INTRASTATE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIntrastateNumber(String intrastateNumber) {
    this.intrastateNumber = intrastateNumber;
  }


  public CustomerBlockedCarrierDto createdDate(LocalDateTime createdDate) {

    this.createdDate = createdDate;
    return this;
  }

  /**
   * Get createdDate
   *
   * @return createdDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getCreatedDate() {
    return createdDate;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedDate(LocalDateTime createdDate) {
    this.createdDate = createdDate;
  }


  public CustomerBlockedCarrierDto createdBy(String createdBy) {

    this.createdBy = createdBy;
    return this;
  }

  /**
   * Get createdBy
   *
   * @return createdBy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCreatedBy() {
    return createdBy;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }


  public CustomerBlockedCarrierDto lastModifiedDate(LocalDateTime lastModifiedDate) {

    this.lastModifiedDate = lastModifiedDate;
    return this;
  }

  /**
   * Get lastModifiedDate
   *
   * @return lastModifiedDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getLastModifiedDate() {
    return lastModifiedDate;
  }


  @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
    this.lastModifiedDate = lastModifiedDate;
  }


  public CustomerBlockedCarrierDto lastModifiedBy(String lastModifiedBy) {

    this.lastModifiedBy = lastModifiedBy;
    return this;
  }

  /**
   * Get lastModifiedBy
   *
   * @return lastModifiedBy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastModifiedBy() {
    return lastModifiedBy;
  }


  @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastModifiedBy(String lastModifiedBy) {
    this.lastModifiedBy = lastModifiedBy;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerBlockedCarrierDto customerBlockedCarrierDto = (CustomerBlockedCarrierDto) o;
    return Objects.equals(this.doTNumber, customerBlockedCarrierDto.doTNumber) && Objects.equals(this.docketNumber, customerBlockedCarrierDto.docketNumber)
           && Objects.equals(this.intrastateNumber, customerBlockedCarrierDto.intrastateNumber) && Objects.equals(this.createdDate,
        customerBlockedCarrierDto.createdDate) && Objects.equals(this.createdBy, customerBlockedCarrierDto.createdBy) && Objects.equals(this.lastModifiedDate,
        customerBlockedCarrierDto.lastModifiedDate) && Objects.equals(this.lastModifiedBy, customerBlockedCarrierDto.lastModifiedBy);
  }

  @Override
  public int hashCode() {
    return Objects.hash(doTNumber, docketNumber, intrastateNumber, createdDate, createdBy, lastModifiedDate, lastModifiedBy);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerBlockedCarrierDto {\n");
    sb.append("    doTNumber: ").append(toIndentedString(doTNumber)).append("\n");
    sb.append("    docketNumber: ").append(toIndentedString(docketNumber)).append("\n");
    sb.append("    intrastateNumber: ").append(toIndentedString(intrastateNumber)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    lastModifiedDate: ").append(toIndentedString(lastModifiedDate)).append("\n");
    sb.append("    lastModifiedBy: ").append(toIndentedString(lastModifiedBy)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

