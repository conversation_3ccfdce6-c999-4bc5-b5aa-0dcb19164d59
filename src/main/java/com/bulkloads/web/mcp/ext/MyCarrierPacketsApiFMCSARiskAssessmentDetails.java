package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSARiskAssessmentDetails
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSARiskAssessmentDetails.JSON_PROPERTY_IS_INTRASTATE_CARRIER,
    MyCarrierPacketsApiFMCSARiskAssessmentDetails.JSON_PROPERTY_TOTAL_POINTS, MyCarrierPacketsApiFMCSARiskAssessmentDetails.JSON_PROPERTY_OVERALL_RATING,
    MyCarrierPacketsApiFMCSARiskAssessmentDetails.JSON_PROPERTY_AUTHORITY, MyCarrierPacketsApiFMCSARiskAssessmentDetails.JSON_PROPERTY_INSURANCE,
    MyCarrierPacketsApiFMCSARiskAssessmentDetails.JSON_PROPERTY_SAFETY, MyCarrierPacketsApiFMCSARiskAssessmentDetails.JSON_PROPERTY_OPERATION,
    MyCarrierPacketsApiFMCSARiskAssessmentDetails.JSON_PROPERTY_OTHER})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.RiskAssessmentDetails")

public class MyCarrierPacketsApiFMCSARiskAssessmentDetails {

  public static final String JSON_PROPERTY_IS_INTRASTATE_CARRIER = "IsIntrastateCarrier";
  public static final String JSON_PROPERTY_TOTAL_POINTS = "TotalPoints";
  public static final String JSON_PROPERTY_OVERALL_RATING = "OverallRating";
  public static final String JSON_PROPERTY_AUTHORITY = "Authority";
  public static final String JSON_PROPERTY_INSURANCE = "Insurance";
  public static final String JSON_PROPERTY_SAFETY = "Safety";
  public static final String JSON_PROPERTY_OPERATION = "Operation";
  public static final String JSON_PROPERTY_OTHER = "Other";
  private Boolean isIntrastateCarrier;
  private Integer totalPoints;
  private String overallRating;
  private MyCarrierPacketsApiFMCSARiskAssessmentDetail authority;
  private MyCarrierPacketsApiFMCSARiskAssessmentDetail insurance;
  private MyCarrierPacketsApiFMCSARiskAssessmentDetail safety;
  private MyCarrierPacketsApiFMCSARiskAssessmentDetail operation;
  private MyCarrierPacketsApiFMCSARiskAssessmentDetail other;

  public MyCarrierPacketsApiFMCSARiskAssessmentDetails() {
  }

  public MyCarrierPacketsApiFMCSARiskAssessmentDetails isIntrastateCarrier(Boolean isIntrastateCarrier) {

    this.isIntrastateCarrier = isIntrastateCarrier;
    return this;
  }

  /**
   * Get isIntrastateCarrier
   *
   * @return isIntrastateCarrier
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_INTRASTATE_CARRIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsIntrastateCarrier() {
    return isIntrastateCarrier;
  }


  @JsonProperty(JSON_PROPERTY_IS_INTRASTATE_CARRIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsIntrastateCarrier(Boolean isIntrastateCarrier) {
    this.isIntrastateCarrier = isIntrastateCarrier;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetails totalPoints(Integer totalPoints) {

    this.totalPoints = totalPoints;
    return this;
  }

  /**
   * Get totalPoints
   *
   * @return totalPoints
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_POINTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalPoints() {
    return totalPoints;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_POINTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalPoints(Integer totalPoints) {
    this.totalPoints = totalPoints;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetails overallRating(String overallRating) {

    this.overallRating = overallRating;
    return this;
  }

  /**
   * Get overallRating
   *
   * @return overallRating
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OVERALL_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOverallRating() {
    return overallRating;
  }


  @JsonProperty(JSON_PROPERTY_OVERALL_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOverallRating(String overallRating) {
    this.overallRating = overallRating;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetails authority(MyCarrierPacketsApiFMCSARiskAssessmentDetail authority) {

    this.authority = authority;
    return this;
  }

  /**
   * Get authority
   *
   * @return authority
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AUTHORITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSARiskAssessmentDetail getAuthority() {
    return authority;
  }


  @JsonProperty(JSON_PROPERTY_AUTHORITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAuthority(MyCarrierPacketsApiFMCSARiskAssessmentDetail authority) {
    this.authority = authority;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetails insurance(MyCarrierPacketsApiFMCSARiskAssessmentDetail insurance) {

    this.insurance = insurance;
    return this;
  }

  /**
   * Get insurance
   *
   * @return insurance
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSURANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSARiskAssessmentDetail getInsurance() {
    return insurance;
  }


  @JsonProperty(JSON_PROPERTY_INSURANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInsurance(MyCarrierPacketsApiFMCSARiskAssessmentDetail insurance) {
    this.insurance = insurance;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetails safety(MyCarrierPacketsApiFMCSARiskAssessmentDetail safety) {

    this.safety = safety;
    return this;
  }

  /**
   * Get safety
   *
   * @return safety
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SAFETY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSARiskAssessmentDetail getSafety() {
    return safety;
  }


  @JsonProperty(JSON_PROPERTY_SAFETY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSafety(MyCarrierPacketsApiFMCSARiskAssessmentDetail safety) {
    this.safety = safety;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetails operation(MyCarrierPacketsApiFMCSARiskAssessmentDetail operation) {

    this.operation = operation;
    return this;
  }

  /**
   * Get operation
   *
   * @return operation
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSARiskAssessmentDetail getOperation() {
    return operation;
  }


  @JsonProperty(JSON_PROPERTY_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperation(MyCarrierPacketsApiFMCSARiskAssessmentDetail operation) {
    this.operation = operation;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetails other(MyCarrierPacketsApiFMCSARiskAssessmentDetail other) {

    this.other = other;
    return this;
  }

  /**
   * Get other
   *
   * @return other
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSARiskAssessmentDetail getOther() {
    return other;
  }


  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOther(MyCarrierPacketsApiFMCSARiskAssessmentDetail other) {
    this.other = other;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSARiskAssessmentDetails myCarrierPacketsApiFMCSARiskAssessmentDetails = (MyCarrierPacketsApiFMCSARiskAssessmentDetails) o;
    return Objects.equals(this.isIntrastateCarrier, myCarrierPacketsApiFMCSARiskAssessmentDetails.isIntrastateCarrier) && Objects.equals(this.totalPoints,
        myCarrierPacketsApiFMCSARiskAssessmentDetails.totalPoints) && Objects.equals(this.overallRating,
        myCarrierPacketsApiFMCSARiskAssessmentDetails.overallRating) && Objects.equals(this.authority, myCarrierPacketsApiFMCSARiskAssessmentDetails.authority)
           && Objects.equals(this.insurance, myCarrierPacketsApiFMCSARiskAssessmentDetails.insurance) && Objects.equals(this.safety,
        myCarrierPacketsApiFMCSARiskAssessmentDetails.safety) && Objects.equals(this.operation, myCarrierPacketsApiFMCSARiskAssessmentDetails.operation)
           && Objects.equals(this.other, myCarrierPacketsApiFMCSARiskAssessmentDetails.other);
  }

  @Override
  public int hashCode() {
    return Objects.hash(isIntrastateCarrier, totalPoints, overallRating, authority, insurance, safety, operation, other);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSARiskAssessmentDetails {\n");
    sb.append("    isIntrastateCarrier: ").append(toIndentedString(isIntrastateCarrier)).append("\n");
    sb.append("    totalPoints: ").append(toIndentedString(totalPoints)).append("\n");
    sb.append("    overallRating: ").append(toIndentedString(overallRating)).append("\n");
    sb.append("    authority: ").append(toIndentedString(authority)).append("\n");
    sb.append("    insurance: ").append(toIndentedString(insurance)).append("\n");
    sb.append("    safety: ").append(toIndentedString(safety)).append("\n");
    sb.append("    operation: ").append(toIndentedString(operation)).append("\n");
    sb.append("    other: ").append(toIndentedString(other)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

