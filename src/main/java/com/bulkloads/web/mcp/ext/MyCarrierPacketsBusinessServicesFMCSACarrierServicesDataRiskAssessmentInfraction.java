package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction
 */
@JsonPropertyOrder({MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction.JSON_PROPERTY_POINTS,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction.JSON_PROPERTY_RISK_LEVEL,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction.JSON_PROPERTY_RULE_TEXT,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction.JSON_PROPERTY_RULE_OUTPUT})
@JsonTypeName("MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentInfraction")

public class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction {

  public static final String JSON_PROPERTY_POINTS = "Points";
  public static final String JSON_PROPERTY_RISK_LEVEL = "RiskLevel";
  public static final String JSON_PROPERTY_RULE_TEXT = "RuleText";
  public static final String JSON_PROPERTY_RULE_OUTPUT = "RuleOutput";
  private Integer points;
  private String riskLevel;
  private String ruleText;
  private String ruleOutput;

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction() {
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction points(Integer points) {

    this.points = points;
    return this;
  }

  /**
   * Get points
   *
   * @return points
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POINTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPoints() {
    return points;
  }


  @JsonProperty(JSON_PROPERTY_POINTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPoints(Integer points) {
    this.points = points;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction riskLevel(String riskLevel) {

    this.riskLevel = riskLevel;
    return this;
  }

  /**
   * Get riskLevel
   *
   * @return riskLevel
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RISK_LEVEL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRiskLevel() {
    return riskLevel;
  }


  @JsonProperty(JSON_PROPERTY_RISK_LEVEL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRiskLevel(String riskLevel) {
    this.riskLevel = riskLevel;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction ruleText(String ruleText) {

    this.ruleText = ruleText;
    return this;
  }

  /**
   * Get ruleText
   *
   * @return ruleText
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RULE_TEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRuleText() {
    return ruleText;
  }


  @JsonProperty(JSON_PROPERTY_RULE_TEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRuleText(String ruleText) {
    this.ruleText = ruleText;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction ruleOutput(String ruleOutput) {

    this.ruleOutput = ruleOutput;
    return this;
  }

  /**
   * Get ruleOutput
   *
   * @return ruleOutput
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RULE_OUTPUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRuleOutput() {
    return ruleOutput;
  }


  @JsonProperty(JSON_PROPERTY_RULE_OUTPUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRuleOutput(String ruleOutput) {
    this.ruleOutput = ruleOutput;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction =
        (MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction) o;
    return Objects.equals(this.points, myCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction.points) && Objects.equals(
        this.riskLevel, myCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction.riskLevel) && Objects.equals(this.ruleText,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction.ruleText) && Objects.equals(this.ruleOutput,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction.ruleOutput);
  }

  @Override
  public int hashCode() {
    return Objects.hash(points, riskLevel, ruleText, ruleOutput);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentInfraction {\n");
    sb.append("    points: ").append(toIndentedString(points)).append("\n");
    sb.append("    riskLevel: ").append(toIndentedString(riskLevel)).append("\n");
    sb.append("    ruleText: ").append(toIndentedString(ruleText)).append("\n");
    sb.append("    ruleOutput: ").append(toIndentedString(ruleOutput)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

