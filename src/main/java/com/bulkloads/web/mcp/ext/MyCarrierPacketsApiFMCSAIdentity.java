package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSAIdentity
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_LEGAL_NAME, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_DBA_NAME,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_BUSINESS_STREET, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_BUSINESS_CITY,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_BUSINESS_STATE, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_BUSINESS_ZIP_CODE,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_BUSINESS_COLONIA, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_BUSINESS_COUNTRY,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_BUSINESS_PHONE, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_BUSINESS_FAX,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_MAILING_STREET, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_MAILING_CITY,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_MAILING_STATE, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_MAILING_ZIP_CODE,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_MAILING_COLONIA, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_MAILING_COUNTRY,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_MAILING_PHONE, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_MAILING_FAX,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_UNDELIVERABLE_MAIL, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_COMPANY_REP1,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_COMPANY_REP2, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_CELL_PHONE,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_EMAIL_ADDRESS, MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_DUN_BRADSTREET_NUM,
    MyCarrierPacketsApiFMCSAIdentity.JSON_PROPERTY_ORGANIZATION})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Identity")

public class MyCarrierPacketsApiFMCSAIdentity {

  public static final String JSON_PROPERTY_LEGAL_NAME = "legalName";
  public static final String JSON_PROPERTY_DBA_NAME = "dbaName";
  public static final String JSON_PROPERTY_BUSINESS_STREET = "businessStreet";
  public static final String JSON_PROPERTY_BUSINESS_CITY = "businessCity";
  public static final String JSON_PROPERTY_BUSINESS_STATE = "businessState";
  public static final String JSON_PROPERTY_BUSINESS_ZIP_CODE = "businessZipCode";
  public static final String JSON_PROPERTY_BUSINESS_COLONIA = "businessColonia";
  public static final String JSON_PROPERTY_BUSINESS_COUNTRY = "businessCountry";
  public static final String JSON_PROPERTY_BUSINESS_PHONE = "businessPhone";
  public static final String JSON_PROPERTY_BUSINESS_FAX = "businessFax";
  public static final String JSON_PROPERTY_MAILING_STREET = "mailingStreet";
  public static final String JSON_PROPERTY_MAILING_CITY = "mailingCity";
  public static final String JSON_PROPERTY_MAILING_STATE = "mailingState";
  public static final String JSON_PROPERTY_MAILING_ZIP_CODE = "mailingZipCode";
  public static final String JSON_PROPERTY_MAILING_COLONIA = "mailingColonia";
  public static final String JSON_PROPERTY_MAILING_COUNTRY = "mailingCountry";
  public static final String JSON_PROPERTY_MAILING_PHONE = "mailingPhone";
  public static final String JSON_PROPERTY_MAILING_FAX = "mailingFax";
  public static final String JSON_PROPERTY_UNDELIVERABLE_MAIL = "undeliverableMail";
  public static final String JSON_PROPERTY_COMPANY_REP1 = "companyRep1";
  public static final String JSON_PROPERTY_COMPANY_REP2 = "companyRep2";
  public static final String JSON_PROPERTY_CELL_PHONE = "cellPhone";
  public static final String JSON_PROPERTY_EMAIL_ADDRESS = "emailAddress";
  public static final String JSON_PROPERTY_DUN_BRADSTREET_NUM = "dunBradstreetNum";
  public static final String JSON_PROPERTY_ORGANIZATION = "organization";
  private String legalName;
  private String dbaName;
  private String businessStreet;
  private String businessCity;
  private String businessState;
  private String businessZipCode;
  private String businessColonia;
  private String businessCountry;
  private String businessPhone;
  private String businessFax;
  private String mailingStreet;
  private String mailingCity;
  private String mailingState;
  private String mailingZipCode;
  private String mailingColonia;
  private String mailingCountry;
  private String mailingPhone;
  private String mailingFax;
  private String undeliverableMail;
  private String companyRep1;
  private String companyRep2;
  private String cellPhone;
  private String emailAddress;
  private String dunBradstreetNum;
  private String organization;

  public MyCarrierPacketsApiFMCSAIdentity() {
  }

  public MyCarrierPacketsApiFMCSAIdentity legalName(String legalName) {

    this.legalName = legalName;
    return this;
  }

  /**
   * Get legalName
   *
   * @return legalName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LEGAL_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLegalName() {
    return legalName;
  }


  @JsonProperty(JSON_PROPERTY_LEGAL_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLegalName(String legalName) {
    this.legalName = legalName;
  }


  public MyCarrierPacketsApiFMCSAIdentity dbaName(String dbaName) {

    this.dbaName = dbaName;
    return this;
  }

  /**
   * Get dbaName
   *
   * @return dbaName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DBA_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDbaName() {
    return dbaName;
  }


  @JsonProperty(JSON_PROPERTY_DBA_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDbaName(String dbaName) {
    this.dbaName = dbaName;
  }


  public MyCarrierPacketsApiFMCSAIdentity businessStreet(String businessStreet) {

    this.businessStreet = businessStreet;
    return this;
  }

  /**
   * Get businessStreet
   *
   * @return businessStreet
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessStreet() {
    return businessStreet;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessStreet(String businessStreet) {
    this.businessStreet = businessStreet;
  }


  public MyCarrierPacketsApiFMCSAIdentity businessCity(String businessCity) {

    this.businessCity = businessCity;
    return this;
  }

  /**
   * Get businessCity
   *
   * @return businessCity
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessCity() {
    return businessCity;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessCity(String businessCity) {
    this.businessCity = businessCity;
  }


  public MyCarrierPacketsApiFMCSAIdentity businessState(String businessState) {

    this.businessState = businessState;
    return this;
  }

  /**
   * Get businessState
   *
   * @return businessState
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessState() {
    return businessState;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessState(String businessState) {
    this.businessState = businessState;
  }


  public MyCarrierPacketsApiFMCSAIdentity businessZipCode(String businessZipCode) {

    this.businessZipCode = businessZipCode;
    return this;
  }

  /**
   * Get businessZipCode
   *
   * @return businessZipCode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessZipCode() {
    return businessZipCode;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessZipCode(String businessZipCode) {
    this.businessZipCode = businessZipCode;
  }


  public MyCarrierPacketsApiFMCSAIdentity businessColonia(String businessColonia) {

    this.businessColonia = businessColonia;
    return this;
  }

  /**
   * Get businessColonia
   *
   * @return businessColonia
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_COLONIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessColonia() {
    return businessColonia;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_COLONIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessColonia(String businessColonia) {
    this.businessColonia = businessColonia;
  }


  public MyCarrierPacketsApiFMCSAIdentity businessCountry(String businessCountry) {

    this.businessCountry = businessCountry;
    return this;
  }

  /**
   * Get businessCountry
   *
   * @return businessCountry
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessCountry() {
    return businessCountry;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessCountry(String businessCountry) {
    this.businessCountry = businessCountry;
  }


  public MyCarrierPacketsApiFMCSAIdentity businessPhone(String businessPhone) {

    this.businessPhone = businessPhone;
    return this;
  }

  /**
   * Get businessPhone
   *
   * @return businessPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessPhone() {
    return businessPhone;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessPhone(String businessPhone) {
    this.businessPhone = businessPhone;
  }


  public MyCarrierPacketsApiFMCSAIdentity businessFax(String businessFax) {

    this.businessFax = businessFax;
    return this;
  }

  /**
   * Get businessFax
   *
   * @return businessFax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessFax() {
    return businessFax;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessFax(String businessFax) {
    this.businessFax = businessFax;
  }


  public MyCarrierPacketsApiFMCSAIdentity mailingStreet(String mailingStreet) {

    this.mailingStreet = mailingStreet;
    return this;
  }

  /**
   * Get mailingStreet
   *
   * @return mailingStreet
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingStreet() {
    return mailingStreet;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingStreet(String mailingStreet) {
    this.mailingStreet = mailingStreet;
  }


  public MyCarrierPacketsApiFMCSAIdentity mailingCity(String mailingCity) {

    this.mailingCity = mailingCity;
    return this;
  }

  /**
   * Get mailingCity
   *
   * @return mailingCity
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingCity() {
    return mailingCity;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingCity(String mailingCity) {
    this.mailingCity = mailingCity;
  }


  public MyCarrierPacketsApiFMCSAIdentity mailingState(String mailingState) {

    this.mailingState = mailingState;
    return this;
  }

  /**
   * Get mailingState
   *
   * @return mailingState
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingState() {
    return mailingState;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingState(String mailingState) {
    this.mailingState = mailingState;
  }


  public MyCarrierPacketsApiFMCSAIdentity mailingZipCode(String mailingZipCode) {

    this.mailingZipCode = mailingZipCode;
    return this;
  }

  /**
   * Get mailingZipCode
   *
   * @return mailingZipCode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingZipCode() {
    return mailingZipCode;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingZipCode(String mailingZipCode) {
    this.mailingZipCode = mailingZipCode;
  }


  public MyCarrierPacketsApiFMCSAIdentity mailingColonia(String mailingColonia) {

    this.mailingColonia = mailingColonia;
    return this;
  }

  /**
   * Get mailingColonia
   *
   * @return mailingColonia
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_COLONIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingColonia() {
    return mailingColonia;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_COLONIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingColonia(String mailingColonia) {
    this.mailingColonia = mailingColonia;
  }


  public MyCarrierPacketsApiFMCSAIdentity mailingCountry(String mailingCountry) {

    this.mailingCountry = mailingCountry;
    return this;
  }

  /**
   * Get mailingCountry
   *
   * @return mailingCountry
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingCountry() {
    return mailingCountry;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingCountry(String mailingCountry) {
    this.mailingCountry = mailingCountry;
  }


  public MyCarrierPacketsApiFMCSAIdentity mailingPhone(String mailingPhone) {

    this.mailingPhone = mailingPhone;
    return this;
  }

  /**
   * Get mailingPhone
   *
   * @return mailingPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingPhone() {
    return mailingPhone;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingPhone(String mailingPhone) {
    this.mailingPhone = mailingPhone;
  }


  public MyCarrierPacketsApiFMCSAIdentity mailingFax(String mailingFax) {

    this.mailingFax = mailingFax;
    return this;
  }

  /**
   * Get mailingFax
   *
   * @return mailingFax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingFax() {
    return mailingFax;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingFax(String mailingFax) {
    this.mailingFax = mailingFax;
  }


  public MyCarrierPacketsApiFMCSAIdentity undeliverableMail(String undeliverableMail) {

    this.undeliverableMail = undeliverableMail;
    return this;
  }

  /**
   * Get undeliverableMail
   *
   * @return undeliverableMail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNDELIVERABLE_MAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUndeliverableMail() {
    return undeliverableMail;
  }


  @JsonProperty(JSON_PROPERTY_UNDELIVERABLE_MAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUndeliverableMail(String undeliverableMail) {
    this.undeliverableMail = undeliverableMail;
  }


  public MyCarrierPacketsApiFMCSAIdentity companyRep1(String companyRep1) {

    this.companyRep1 = companyRep1;
    return this;
  }

  /**
   * Get companyRep1
   *
   * @return companyRep1
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPANY_REP1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCompanyRep1() {
    return companyRep1;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_REP1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompanyRep1(String companyRep1) {
    this.companyRep1 = companyRep1;
  }


  public MyCarrierPacketsApiFMCSAIdentity companyRep2(String companyRep2) {

    this.companyRep2 = companyRep2;
    return this;
  }

  /**
   * Get companyRep2
   *
   * @return companyRep2
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPANY_REP2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCompanyRep2() {
    return companyRep2;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_REP2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompanyRep2(String companyRep2) {
    this.companyRep2 = companyRep2;
  }


  public MyCarrierPacketsApiFMCSAIdentity cellPhone(String cellPhone) {

    this.cellPhone = cellPhone;
    return this;
  }

  /**
   * Get cellPhone
   *
   * @return cellPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CELL_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCellPhone() {
    return cellPhone;
  }


  @JsonProperty(JSON_PROPERTY_CELL_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCellPhone(String cellPhone) {
    this.cellPhone = cellPhone;
  }


  public MyCarrierPacketsApiFMCSAIdentity emailAddress(String emailAddress) {

    this.emailAddress = emailAddress;
    return this;
  }

  /**
   * Get emailAddress
   *
   * @return emailAddress
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmailAddress() {
    return emailAddress;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmailAddress(String emailAddress) {
    this.emailAddress = emailAddress;
  }


  public MyCarrierPacketsApiFMCSAIdentity dunBradstreetNum(String dunBradstreetNum) {

    this.dunBradstreetNum = dunBradstreetNum;
    return this;
  }

  /**
   * Get dunBradstreetNum
   *
   * @return dunBradstreetNum
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DUN_BRADSTREET_NUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDunBradstreetNum() {
    return dunBradstreetNum;
  }


  @JsonProperty(JSON_PROPERTY_DUN_BRADSTREET_NUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDunBradstreetNum(String dunBradstreetNum) {
    this.dunBradstreetNum = dunBradstreetNum;
  }


  public MyCarrierPacketsApiFMCSAIdentity organization(String organization) {

    this.organization = organization;
    return this;
  }

  /**
   * Get organization
   *
   * @return organization
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ORGANIZATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOrganization() {
    return organization;
  }


  @JsonProperty(JSON_PROPERTY_ORGANIZATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOrganization(String organization) {
    this.organization = organization;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSAIdentity myCarrierPacketsApiFMCSAIdentity = (MyCarrierPacketsApiFMCSAIdentity) o;
    return Objects.equals(this.legalName, myCarrierPacketsApiFMCSAIdentity.legalName) && Objects.equals(this.dbaName, myCarrierPacketsApiFMCSAIdentity.dbaName)
           && Objects.equals(this.businessStreet, myCarrierPacketsApiFMCSAIdentity.businessStreet) && Objects.equals(this.businessCity,
        myCarrierPacketsApiFMCSAIdentity.businessCity) && Objects.equals(this.businessState, myCarrierPacketsApiFMCSAIdentity.businessState) && Objects.equals(
        this.businessZipCode, myCarrierPacketsApiFMCSAIdentity.businessZipCode) && Objects.equals(this.businessColonia,
        myCarrierPacketsApiFMCSAIdentity.businessColonia) && Objects.equals(this.businessCountry, myCarrierPacketsApiFMCSAIdentity.businessCountry)
           && Objects.equals(this.businessPhone, myCarrierPacketsApiFMCSAIdentity.businessPhone) && Objects.equals(this.businessFax,
        myCarrierPacketsApiFMCSAIdentity.businessFax) && Objects.equals(this.mailingStreet, myCarrierPacketsApiFMCSAIdentity.mailingStreet) && Objects.equals(
        this.mailingCity, myCarrierPacketsApiFMCSAIdentity.mailingCity) && Objects.equals(this.mailingState, myCarrierPacketsApiFMCSAIdentity.mailingState)
           && Objects.equals(this.mailingZipCode, myCarrierPacketsApiFMCSAIdentity.mailingZipCode) && Objects.equals(this.mailingColonia,
        myCarrierPacketsApiFMCSAIdentity.mailingColonia) && Objects.equals(this.mailingCountry, myCarrierPacketsApiFMCSAIdentity.mailingCountry)
           && Objects.equals(this.mailingPhone, myCarrierPacketsApiFMCSAIdentity.mailingPhone) && Objects.equals(this.mailingFax,
        myCarrierPacketsApiFMCSAIdentity.mailingFax) && Objects.equals(this.undeliverableMail, myCarrierPacketsApiFMCSAIdentity.undeliverableMail)
           && Objects.equals(this.companyRep1, myCarrierPacketsApiFMCSAIdentity.companyRep1) && Objects.equals(this.companyRep2,
        myCarrierPacketsApiFMCSAIdentity.companyRep2) && Objects.equals(this.cellPhone, myCarrierPacketsApiFMCSAIdentity.cellPhone) && Objects.equals(
        this.emailAddress, myCarrierPacketsApiFMCSAIdentity.emailAddress) && Objects.equals(this.dunBradstreetNum,
        myCarrierPacketsApiFMCSAIdentity.dunBradstreetNum) && Objects.equals(this.organization, myCarrierPacketsApiFMCSAIdentity.organization);
  }

  @Override
  public int hashCode() {
    return Objects.hash(legalName, dbaName, businessStreet, businessCity, businessState, businessZipCode, businessColonia, businessCountry, businessPhone,
        businessFax, mailingStreet, mailingCity, mailingState, mailingZipCode, mailingColonia, mailingCountry, mailingPhone, mailingFax, undeliverableMail,
        companyRep1, companyRep2, cellPhone, emailAddress, dunBradstreetNum, organization);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSAIdentity {\n");
    sb.append("    legalName: ").append(toIndentedString(legalName)).append("\n");
    sb.append("    dbaName: ").append(toIndentedString(dbaName)).append("\n");
    sb.append("    businessStreet: ").append(toIndentedString(businessStreet)).append("\n");
    sb.append("    businessCity: ").append(toIndentedString(businessCity)).append("\n");
    sb.append("    businessState: ").append(toIndentedString(businessState)).append("\n");
    sb.append("    businessZipCode: ").append(toIndentedString(businessZipCode)).append("\n");
    sb.append("    businessColonia: ").append(toIndentedString(businessColonia)).append("\n");
    sb.append("    businessCountry: ").append(toIndentedString(businessCountry)).append("\n");
    sb.append("    businessPhone: ").append(toIndentedString(businessPhone)).append("\n");
    sb.append("    businessFax: ").append(toIndentedString(businessFax)).append("\n");
    sb.append("    mailingStreet: ").append(toIndentedString(mailingStreet)).append("\n");
    sb.append("    mailingCity: ").append(toIndentedString(mailingCity)).append("\n");
    sb.append("    mailingState: ").append(toIndentedString(mailingState)).append("\n");
    sb.append("    mailingZipCode: ").append(toIndentedString(mailingZipCode)).append("\n");
    sb.append("    mailingColonia: ").append(toIndentedString(mailingColonia)).append("\n");
    sb.append("    mailingCountry: ").append(toIndentedString(mailingCountry)).append("\n");
    sb.append("    mailingPhone: ").append(toIndentedString(mailingPhone)).append("\n");
    sb.append("    mailingFax: ").append(toIndentedString(mailingFax)).append("\n");
    sb.append("    undeliverableMail: ").append(toIndentedString(undeliverableMail)).append("\n");
    sb.append("    companyRep1: ").append(toIndentedString(companyRep1)).append("\n");
    sb.append("    companyRep2: ").append(toIndentedString(companyRep2)).append("\n");
    sb.append("    cellPhone: ").append(toIndentedString(cellPhone)).append("\n");
    sb.append("    emailAddress: ").append(toIndentedString(emailAddress)).append("\n");
    sb.append("    dunBradstreetNum: ").append(toIndentedString(dunBradstreetNum)).append("\n");
    sb.append("    organization: ").append(toIndentedString(organization)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

