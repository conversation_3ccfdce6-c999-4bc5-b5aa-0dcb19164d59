package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * PolicyOutput
 */
@JsonPropertyOrder({PolicyOutput.JSON_PROPERTY_COMPANY_NAME, PolicyOutput.JSON_PROPERTY_ATTN_TO_NAME, PolicyOutput.JSON_PROPERTY_ADDRESS,
    PolicyOutput.JSON_PROPERTY_CITY, PolicyOutput.JSON_PROPERTY_STATE_CODE, PolicyOutput.JSON_PROPERTY_POSTAL_CODE, PolicyOutput.JSON_PROPERTY_COUNTRY_CODE,
    PolicyOutput.JSON_PROPERTY_PHONE, PolicyOutput.JSON_PROPERTY_FAX, PolicyOutput.JSON_PROPERTY_INSURANCE_TYPE, PolicyOutput.JSON_PROPERTY_POLICY_NUMBER,
    PolicyOutput.JSON_PROPERTY_POSTED_DATE, PolicyOutput.JSON_PROPERTY_EFFECTIVE_DATE, PolicyOutput.JSON_PROPERTY_CANCELATION_DATE,
    PolicyOutput.JSON_PROPERTY_COVERAGE_FROM, PolicyOutput.JSON_PROPERTY_COVERAGE_TO, PolicyOutput.JSON_PROPERTY_AM_BEST_RATING})

public class PolicyOutput {

  public static final String JSON_PROPERTY_COMPANY_NAME = "companyName";
  public static final String JSON_PROPERTY_ATTN_TO_NAME = "attnToName";
  public static final String JSON_PROPERTY_ADDRESS = "address";
  public static final String JSON_PROPERTY_CITY = "city";
  public static final String JSON_PROPERTY_STATE_CODE = "stateCode";
  public static final String JSON_PROPERTY_POSTAL_CODE = "postalCode";
  public static final String JSON_PROPERTY_COUNTRY_CODE = "countryCode";
  public static final String JSON_PROPERTY_PHONE = "phone";
  public static final String JSON_PROPERTY_FAX = "fax";
  public static final String JSON_PROPERTY_INSURANCE_TYPE = "insuranceType";
  public static final String JSON_PROPERTY_POLICY_NUMBER = "policyNumber";
  public static final String JSON_PROPERTY_POSTED_DATE = "postedDate";
  public static final String JSON_PROPERTY_EFFECTIVE_DATE = "effectiveDate";
  public static final String JSON_PROPERTY_CANCELATION_DATE = "cancelationDate";
  public static final String JSON_PROPERTY_COVERAGE_FROM = "coverageFrom";
  public static final String JSON_PROPERTY_COVERAGE_TO = "coverageTo";
  public static final String JSON_PROPERTY_AM_BEST_RATING = "amBestRating";
  private String companyName;
  private String attnToName;
  private String address;
  private String city;
  private String stateCode;
  private String postalCode;
  private String countryCode;
  private String phone;
  private String fax;
  private String insuranceType;
  private String policyNumber;
  private String postedDate;
  private String effectiveDate;
  private String cancelationDate;
  private String coverageFrom;
  private String coverageTo;
  private String amBestRating;

  public PolicyOutput() {
  }

  public PolicyOutput companyName(String companyName) {

    this.companyName = companyName;
    return this;
  }

  /**
   * Get companyName
   *
   * @return companyName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPANY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCompanyName() {
    return companyName;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompanyName(String companyName) {
    this.companyName = companyName;
  }


  public PolicyOutput attnToName(String attnToName) {

    this.attnToName = attnToName;
    return this;
  }

  /**
   * Get attnToName
   *
   * @return attnToName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ATTN_TO_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAttnToName() {
    return attnToName;
  }


  @JsonProperty(JSON_PROPERTY_ATTN_TO_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAttnToName(String attnToName) {
    this.attnToName = attnToName;
  }


  public PolicyOutput address(String address) {

    this.address = address;
    return this;
  }

  /**
   * Get address
   *
   * @return address
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress(String address) {
    this.address = address;
  }


  public PolicyOutput city(String city) {

    this.city = city;
    return this;
  }

  /**
   * Get city
   *
   * @return city
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCity() {
    return city;
  }


  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCity(String city) {
    this.city = city;
  }


  public PolicyOutput stateCode(String stateCode) {

    this.stateCode = stateCode;
    return this;
  }

  /**
   * Get stateCode
   *
   * @return stateCode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStateCode() {
    return stateCode;
  }


  @JsonProperty(JSON_PROPERTY_STATE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStateCode(String stateCode) {
    this.stateCode = stateCode;
  }


  public PolicyOutput postalCode(String postalCode) {

    this.postalCode = postalCode;
    return this;
  }

  /**
   * Get postalCode
   *
   * @return postalCode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POSTAL_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPostalCode() {
    return postalCode;
  }


  @JsonProperty(JSON_PROPERTY_POSTAL_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPostalCode(String postalCode) {
    this.postalCode = postalCode;
  }


  public PolicyOutput countryCode(String countryCode) {

    this.countryCode = countryCode;
    return this;
  }

  /**
   * Get countryCode
   *
   * @return countryCode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCountryCode() {
    return countryCode;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountryCode(String countryCode) {
    this.countryCode = countryCode;
  }


  public PolicyOutput phone(String phone) {

    this.phone = phone;
    return this;
  }

  /**
   * Get phone
   *
   * @return phone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPhone() {
    return phone;
  }


  @JsonProperty(JSON_PROPERTY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPhone(String phone) {
    this.phone = phone;
  }


  public PolicyOutput fax(String fax) {

    this.fax = fax;
    return this;
  }

  /**
   * Get fax
   *
   * @return fax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFax() {
    return fax;
  }


  @JsonProperty(JSON_PROPERTY_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFax(String fax) {
    this.fax = fax;
  }


  public PolicyOutput insuranceType(String insuranceType) {

    this.insuranceType = insuranceType;
    return this;
  }

  /**
   * Get insuranceType
   *
   * @return insuranceType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSURANCE_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInsuranceType() {
    return insuranceType;
  }


  @JsonProperty(JSON_PROPERTY_INSURANCE_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInsuranceType(String insuranceType) {
    this.insuranceType = insuranceType;
  }


  public PolicyOutput policyNumber(String policyNumber) {

    this.policyNumber = policyNumber;
    return this;
  }

  /**
   * Get policyNumber
   *
   * @return policyNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POLICY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPolicyNumber() {
    return policyNumber;
  }


  @JsonProperty(JSON_PROPERTY_POLICY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPolicyNumber(String policyNumber) {
    this.policyNumber = policyNumber;
  }


  public PolicyOutput postedDate(String postedDate) {

    this.postedDate = postedDate;
    return this;
  }

  /**
   * Get postedDate
   *
   * @return postedDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POSTED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPostedDate() {
    return postedDate;
  }


  @JsonProperty(JSON_PROPERTY_POSTED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPostedDate(String postedDate) {
    this.postedDate = postedDate;
  }


  public PolicyOutput effectiveDate(String effectiveDate) {

    this.effectiveDate = effectiveDate;
    return this;
  }

  /**
   * Get effectiveDate
   *
   * @return effectiveDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EFFECTIVE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEffectiveDate() {
    return effectiveDate;
  }


  @JsonProperty(JSON_PROPERTY_EFFECTIVE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEffectiveDate(String effectiveDate) {
    this.effectiveDate = effectiveDate;
  }


  public PolicyOutput cancelationDate(String cancelationDate) {

    this.cancelationDate = cancelationDate;
    return this;
  }

  /**
   * Get cancelationDate
   *
   * @return cancelationDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CANCELATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCancelationDate() {
    return cancelationDate;
  }


  @JsonProperty(JSON_PROPERTY_CANCELATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCancelationDate(String cancelationDate) {
    this.cancelationDate = cancelationDate;
  }


  public PolicyOutput coverageFrom(String coverageFrom) {

    this.coverageFrom = coverageFrom;
    return this;
  }

  /**
   * Get coverageFrom
   *
   * @return coverageFrom
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COVERAGE_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCoverageFrom() {
    return coverageFrom;
  }


  @JsonProperty(JSON_PROPERTY_COVERAGE_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCoverageFrom(String coverageFrom) {
    this.coverageFrom = coverageFrom;
  }


  public PolicyOutput coverageTo(String coverageTo) {

    this.coverageTo = coverageTo;
    return this;
  }

  /**
   * Get coverageTo
   *
   * @return coverageTo
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COVERAGE_TO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCoverageTo() {
    return coverageTo;
  }


  @JsonProperty(JSON_PROPERTY_COVERAGE_TO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCoverageTo(String coverageTo) {
    this.coverageTo = coverageTo;
  }


  public PolicyOutput amBestRating(String amBestRating) {

    this.amBestRating = amBestRating;
    return this;
  }

  /**
   * Get amBestRating
   *
   * @return amBestRating
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AM_BEST_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAmBestRating() {
    return amBestRating;
  }


  @JsonProperty(JSON_PROPERTY_AM_BEST_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAmBestRating(String amBestRating) {
    this.amBestRating = amBestRating;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PolicyOutput policyOutput = (PolicyOutput) o;
    return Objects.equals(this.companyName, policyOutput.companyName) && Objects.equals(this.attnToName, policyOutput.attnToName) && Objects.equals(
        this.address, policyOutput.address) && Objects.equals(this.city, policyOutput.city) && Objects.equals(this.stateCode, policyOutput.stateCode)
           && Objects.equals(this.postalCode, policyOutput.postalCode) && Objects.equals(this.countryCode, policyOutput.countryCode) && Objects.equals(
        this.phone, policyOutput.phone) && Objects.equals(this.fax, policyOutput.fax) && Objects.equals(this.insuranceType, policyOutput.insuranceType)
           && Objects.equals(this.policyNumber, policyOutput.policyNumber) && Objects.equals(this.postedDate, policyOutput.postedDate) && Objects.equals(
        this.effectiveDate, policyOutput.effectiveDate) && Objects.equals(this.cancelationDate, policyOutput.cancelationDate) && Objects.equals(
        this.coverageFrom, policyOutput.coverageFrom) && Objects.equals(this.coverageTo, policyOutput.coverageTo) && Objects.equals(this.amBestRating,
        policyOutput.amBestRating);
  }

  @Override
  public int hashCode() {
    return Objects.hash(companyName, attnToName, address, city, stateCode, postalCode, countryCode, phone, fax, insuranceType, policyNumber, postedDate,
        effectiveDate, cancelationDate, coverageFrom, coverageTo, amBestRating);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PolicyOutput {\n");
    sb.append("    companyName: ").append(toIndentedString(companyName)).append("\n");
    sb.append("    attnToName: ").append(toIndentedString(attnToName)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("    stateCode: ").append(toIndentedString(stateCode)).append("\n");
    sb.append("    postalCode: ").append(toIndentedString(postalCode)).append("\n");
    sb.append("    countryCode: ").append(toIndentedString(countryCode)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("    fax: ").append(toIndentedString(fax)).append("\n");
    sb.append("    insuranceType: ").append(toIndentedString(insuranceType)).append("\n");
    sb.append("    policyNumber: ").append(toIndentedString(policyNumber)).append("\n");
    sb.append("    postedDate: ").append(toIndentedString(postedDate)).append("\n");
    sb.append("    effectiveDate: ").append(toIndentedString(effectiveDate)).append("\n");
    sb.append("    cancelationDate: ").append(toIndentedString(cancelationDate)).append("\n");
    sb.append("    coverageFrom: ").append(toIndentedString(coverageFrom)).append("\n");
    sb.append("    coverageTo: ").append(toIndentedString(coverageTo)).append("\n");
    sb.append("    amBestRating: ").append(toIndentedString(amBestRating)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

