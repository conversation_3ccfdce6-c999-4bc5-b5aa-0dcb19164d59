package com.bulkloads.web.mcp.ext;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierChangesResultDto
 */
@JsonPropertyOrder({CarrierChangesResultDto.JSON_PROPERTY_FROM_DATE, CarrierChangesResultDto.JSON_PROPERTY_TO_DATE,
    CarrierChangesResultDto.JSON_PROPERTY_REQUEST_DATE_TIME_UTC, CarrierChangesResultDto.JSON_PROPERTY_INSURANCE_CHANGE_COUNT,
    CarrierChangesResultDto.JSON_PROPERTY_FM_C_S_A_CHANGE_COUNT, CarrierChangesResultDto.JSON_PROPERTY_RISK_ASSESSMENT_CHANGE_COUNT,
    CarrierChangesResultDto.JSON_PROPERTY_CARRIER_COUNT, CarrierChangesResultDto.JSON_PROPERTY_CARRIER_LIST})

public class CarrierChangesResultDto {

  public static final String JSON_PROPERTY_FROM_DATE = "FromDate";
  public static final String JSON_PROPERTY_TO_DATE = "ToDate";
  public static final String JSON_PROPERTY_REQUEST_DATE_TIME_UTC = "RequestDateTimeUtc";
  public static final String JSON_PROPERTY_INSURANCE_CHANGE_COUNT = "InsuranceChangeCount";
  public static final String JSON_PROPERTY_FM_C_S_A_CHANGE_COUNT = "FMCSAChangeCount";
  public static final String JSON_PROPERTY_RISK_ASSESSMENT_CHANGE_COUNT = "RiskAssessmentChangeCount";
  public static final String JSON_PROPERTY_CARRIER_COUNT = "CarrierCount";
  public static final String JSON_PROPERTY_CARRIER_LIST = "CarrierList";
  private Instant fromDate;
  private OffsetDateTime toDate;
  private LocalDateTime requestDateTimeUtc;
  private Integer insuranceChangeCount;
  private Integer fmCSAChangeCount;
  private Integer riskAssessmentChangeCount;
  private Integer carrierCount;
  private List<MyCarrierPacketsApiFMCSAFMCSACarrierChanged> carrierList;

  public CarrierChangesResultDto() {
  }

  public CarrierChangesResultDto fromDate(Instant fromDate) {

    this.fromDate = fromDate;
    return this;
  }

  /**
   * Get fromDate
   *
   * @return fromDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Instant getFromDate() {
    return fromDate;
  }


  @JsonProperty(JSON_PROPERTY_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFromDate(Instant fromDate) {
    this.fromDate = fromDate;
  }


  public CarrierChangesResultDto toDate(OffsetDateTime toDate) {

    this.toDate = toDate;
    return this;
  }

  /**
   * Get toDate
   *
   * @return toDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TO_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OffsetDateTime getToDate() {
    return toDate;
  }


  @JsonProperty(JSON_PROPERTY_TO_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setToDate(OffsetDateTime toDate) {
    this.toDate = toDate;
  }


  public CarrierChangesResultDto requestDateTimeUtc(LocalDateTime requestDateTimeUtc) {

    this.requestDateTimeUtc = requestDateTimeUtc;
    return this;
  }

  /**
   * Get requestDateTimeUtc
   *
   * @return requestDateTimeUtc
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REQUEST_DATE_TIME_UTC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getRequestDateTimeUtc() {
    return requestDateTimeUtc;
  }


  @JsonProperty(JSON_PROPERTY_REQUEST_DATE_TIME_UTC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRequestDateTimeUtc(LocalDateTime requestDateTimeUtc) {
    this.requestDateTimeUtc = requestDateTimeUtc;
  }


  public CarrierChangesResultDto insuranceChangeCount(Integer insuranceChangeCount) {

    this.insuranceChangeCount = insuranceChangeCount;
    return this;
  }

  /**
   * Get insuranceChangeCount
   *
   * @return insuranceChangeCount
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSURANCE_CHANGE_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getInsuranceChangeCount() {
    return insuranceChangeCount;
  }


  @JsonProperty(JSON_PROPERTY_INSURANCE_CHANGE_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInsuranceChangeCount(Integer insuranceChangeCount) {
    this.insuranceChangeCount = insuranceChangeCount;
  }


  public CarrierChangesResultDto fmCSAChangeCount(Integer fmCSAChangeCount) {

    this.fmCSAChangeCount = fmCSAChangeCount;
    return this;
  }

  /**
   * Get fmCSAChangeCount
   *
   * @return fmCSAChangeCount
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FM_C_S_A_CHANGE_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getFmCSAChangeCount() {
    return fmCSAChangeCount;
  }


  @JsonProperty(JSON_PROPERTY_FM_C_S_A_CHANGE_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFmCSAChangeCount(Integer fmCSAChangeCount) {
    this.fmCSAChangeCount = fmCSAChangeCount;
  }


  public CarrierChangesResultDto riskAssessmentChangeCount(Integer riskAssessmentChangeCount) {

    this.riskAssessmentChangeCount = riskAssessmentChangeCount;
    return this;
  }

  /**
   * Get riskAssessmentChangeCount
   *
   * @return riskAssessmentChangeCount
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT_CHANGE_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getRiskAssessmentChangeCount() {
    return riskAssessmentChangeCount;
  }


  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT_CHANGE_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRiskAssessmentChangeCount(Integer riskAssessmentChangeCount) {
    this.riskAssessmentChangeCount = riskAssessmentChangeCount;
  }


  public CarrierChangesResultDto carrierCount(Integer carrierCount) {

    this.carrierCount = carrierCount;
    return this;
  }

  /**
   * Get carrierCount
   *
   * @return carrierCount
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCarrierCount() {
    return carrierCount;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierCount(Integer carrierCount) {
    this.carrierCount = carrierCount;
  }


  public CarrierChangesResultDto carrierList(List<MyCarrierPacketsApiFMCSAFMCSACarrierChanged> carrierList) {

    this.carrierList = carrierList;
    return this;
  }

  public CarrierChangesResultDto addCarrierListItem(MyCarrierPacketsApiFMCSAFMCSACarrierChanged carrierListItem) {
    if (this.carrierList == null) {
      this.carrierList = new ArrayList<>();
    }
    this.carrierList.add(carrierListItem);
    return this;
  }

  /**
   * Get carrierList
   *
   * @return carrierList
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_LIST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MyCarrierPacketsApiFMCSAFMCSACarrierChanged> getCarrierList() {
    return carrierList;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_LIST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierList(List<MyCarrierPacketsApiFMCSAFMCSACarrierChanged> carrierList) {
    this.carrierList = carrierList;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierChangesResultDto carrierChangesResultDto = (CarrierChangesResultDto) o;
    return Objects.equals(this.fromDate, carrierChangesResultDto.fromDate) && Objects.equals(this.toDate, carrierChangesResultDto.toDate) && Objects.equals(
        this.requestDateTimeUtc, carrierChangesResultDto.requestDateTimeUtc) && Objects.equals(this.insuranceChangeCount,
        carrierChangesResultDto.insuranceChangeCount) && Objects.equals(this.fmCSAChangeCount, carrierChangesResultDto.fmCSAChangeCount) && Objects.equals(
        this.riskAssessmentChangeCount, carrierChangesResultDto.riskAssessmentChangeCount) && Objects.equals(this.carrierCount,
        carrierChangesResultDto.carrierCount) && Objects.equals(this.carrierList, carrierChangesResultDto.carrierList);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fromDate, toDate, requestDateTimeUtc, insuranceChangeCount, fmCSAChangeCount, riskAssessmentChangeCount, carrierCount, carrierList);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierChangesResultDto {\n");
    sb.append("    fromDate: ").append(toIndentedString(fromDate)).append("\n");
    sb.append("    toDate: ").append(toIndentedString(toDate)).append("\n");
    sb.append("    requestDateTimeUtc: ").append(toIndentedString(requestDateTimeUtc)).append("\n");
    sb.append("    insuranceChangeCount: ").append(toIndentedString(insuranceChangeCount)).append("\n");
    sb.append("    fmCSAChangeCount: ").append(toIndentedString(fmCSAChangeCount)).append("\n");
    sb.append("    riskAssessmentChangeCount: ").append(toIndentedString(riskAssessmentChangeCount)).append("\n");
    sb.append("    carrierCount: ").append(toIndentedString(carrierCount)).append("\n");
    sb.append("    carrierList: ").append(toIndentedString(carrierList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

