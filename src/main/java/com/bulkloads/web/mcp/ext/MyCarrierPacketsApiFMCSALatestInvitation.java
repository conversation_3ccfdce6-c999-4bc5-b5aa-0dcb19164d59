package com.bulkloads.web.mcp.ext;

import java.time.LocalDateTime;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSALatestInvitation
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSALatestInvitation.JSON_PROPERTY_INVITED_BY_USER_NAME,
    MyCarrierPacketsApiFMCSALatestInvitation.JSON_PROPERTY_INVITED_BY_EMAIL, MyCarrierPacketsApiFMCSALatestInvitation.JSON_PROPERTY_INVITED_BY_FIRST_NAME,
    MyCarrierPacketsApiFMCSALatestInvitation.JSON_PROPERTY_INVITED_BY_LAST_NAME, MyCarrierPacketsApiFMCSALatestInvitation.JSON_PROPERTY_INVITATION_SENT_DATE,
    MyCarrierPacketsApiFMCSALatestInvitation.JSON_PROPERTY_INVITATION_RECIPIENT})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.LatestInvitation")

public class MyCarrierPacketsApiFMCSALatestInvitation {

  public static final String JSON_PROPERTY_INVITED_BY_USER_NAME = "InvitedByUserName";
  public static final String JSON_PROPERTY_INVITED_BY_EMAIL = "InvitedByEmail";
  public static final String JSON_PROPERTY_INVITED_BY_FIRST_NAME = "InvitedByFirstName";
  public static final String JSON_PROPERTY_INVITED_BY_LAST_NAME = "InvitedByLastName";
  public static final String JSON_PROPERTY_INVITATION_SENT_DATE = "InvitationSentDate";
  public static final String JSON_PROPERTY_INVITATION_RECIPIENT = "InvitationRecipient";
  private String invitedByUserName;
  private String invitedByEmail;
  private String invitedByFirstName;
  private String invitedByLastName;
  private LocalDateTime invitationSentDate;
  private String invitationRecipient;

  public MyCarrierPacketsApiFMCSALatestInvitation() {
  }

  public MyCarrierPacketsApiFMCSALatestInvitation invitedByUserName(String invitedByUserName) {

    this.invitedByUserName = invitedByUserName;
    return this;
  }

  /**
   * Get invitedByUserName
   *
   * @return invitedByUserName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INVITED_BY_USER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInvitedByUserName() {
    return invitedByUserName;
  }


  @JsonProperty(JSON_PROPERTY_INVITED_BY_USER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInvitedByUserName(String invitedByUserName) {
    this.invitedByUserName = invitedByUserName;
  }


  public MyCarrierPacketsApiFMCSALatestInvitation invitedByEmail(String invitedByEmail) {

    this.invitedByEmail = invitedByEmail;
    return this;
  }

  /**
   * Get invitedByEmail
   *
   * @return invitedByEmail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INVITED_BY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInvitedByEmail() {
    return invitedByEmail;
  }


  @JsonProperty(JSON_PROPERTY_INVITED_BY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInvitedByEmail(String invitedByEmail) {
    this.invitedByEmail = invitedByEmail;
  }


  public MyCarrierPacketsApiFMCSALatestInvitation invitedByFirstName(String invitedByFirstName) {

    this.invitedByFirstName = invitedByFirstName;
    return this;
  }

  /**
   * Get invitedByFirstName
   *
   * @return invitedByFirstName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INVITED_BY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInvitedByFirstName() {
    return invitedByFirstName;
  }


  @JsonProperty(JSON_PROPERTY_INVITED_BY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInvitedByFirstName(String invitedByFirstName) {
    this.invitedByFirstName = invitedByFirstName;
  }


  public MyCarrierPacketsApiFMCSALatestInvitation invitedByLastName(String invitedByLastName) {

    this.invitedByLastName = invitedByLastName;
    return this;
  }

  /**
   * Get invitedByLastName
   *
   * @return invitedByLastName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INVITED_BY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInvitedByLastName() {
    return invitedByLastName;
  }


  @JsonProperty(JSON_PROPERTY_INVITED_BY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInvitedByLastName(String invitedByLastName) {
    this.invitedByLastName = invitedByLastName;
  }


  public MyCarrierPacketsApiFMCSALatestInvitation invitationSentDate(LocalDateTime invitationSentDate) {

    this.invitationSentDate = invitationSentDate;
    return this;
  }

  /**
   * Get invitationSentDate
   *
   * @return invitationSentDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INVITATION_SENT_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getInvitationSentDate() {
    return invitationSentDate;
  }


  @JsonProperty(JSON_PROPERTY_INVITATION_SENT_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInvitationSentDate(LocalDateTime invitationSentDate) {
    this.invitationSentDate = invitationSentDate;
  }


  public MyCarrierPacketsApiFMCSALatestInvitation invitationRecipient(String invitationRecipient) {

    this.invitationRecipient = invitationRecipient;
    return this;
  }

  /**
   * Get invitationRecipient
   *
   * @return invitationRecipient
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INVITATION_RECIPIENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInvitationRecipient() {
    return invitationRecipient;
  }


  @JsonProperty(JSON_PROPERTY_INVITATION_RECIPIENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInvitationRecipient(String invitationRecipient) {
    this.invitationRecipient = invitationRecipient;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSALatestInvitation myCarrierPacketsApiFMCSALatestInvitation = (MyCarrierPacketsApiFMCSALatestInvitation) o;
    return Objects.equals(this.invitedByUserName, myCarrierPacketsApiFMCSALatestInvitation.invitedByUserName) && Objects.equals(this.invitedByEmail,
        myCarrierPacketsApiFMCSALatestInvitation.invitedByEmail) && Objects.equals(this.invitedByFirstName,
        myCarrierPacketsApiFMCSALatestInvitation.invitedByFirstName) && Objects.equals(this.invitedByLastName,
        myCarrierPacketsApiFMCSALatestInvitation.invitedByLastName) && Objects.equals(this.invitationSentDate,
        myCarrierPacketsApiFMCSALatestInvitation.invitationSentDate) && Objects.equals(this.invitationRecipient,
        myCarrierPacketsApiFMCSALatestInvitation.invitationRecipient);
  }

  @Override
  public int hashCode() {
    return Objects.hash(invitedByUserName, invitedByEmail, invitedByFirstName, invitedByLastName, invitationSentDate, invitationRecipient);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSALatestInvitation {\n");
    sb.append("    invitedByUserName: ").append(toIndentedString(invitedByUserName)).append("\n");
    sb.append("    invitedByEmail: ").append(toIndentedString(invitedByEmail)).append("\n");
    sb.append("    invitedByFirstName: ").append(toIndentedString(invitedByFirstName)).append("\n");
    sb.append("    invitedByLastName: ").append(toIndentedString(invitedByLastName)).append("\n");
    sb.append("    invitationSentDate: ").append(toIndentedString(invitationSentDate)).append("\n");
    sb.append("    invitationRecipient: ").append(toIndentedString(invitationRecipient)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

