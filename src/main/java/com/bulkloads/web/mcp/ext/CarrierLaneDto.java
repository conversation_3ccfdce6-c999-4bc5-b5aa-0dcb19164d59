package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierLaneDto
 */
@JsonPropertyOrder({CarrierLaneDto.JSON_PROPERTY_UNITED_STATES, CarrierLaneDto.JSON_PROPERTY_MEXICO, CarrierLaneDto.JSON_PROPERTY_CANADA,
    CarrierLaneDto.JSON_PROPERTY_NORTHEAST_REGION, CarrierLaneDto.JSON_PROPERTY_MIDWEST_REGION, CarrierLaneDto.JSON_PROPERTY_SOUTH_REGION,
    CarrierLaneDto.JSON_PROPERTY_WEST_REGION, CarrierLaneDto.JSON_PROPERTY_ALABAMA, CarrierLaneDto.JSON_PROPERTY_ALASKA, CarrierLaneDto.JSON_PROPERTY_ARIZONA,
    CarrierLaneDto.JSON_PROPERTY_ARKANSAS, CarrierLaneDto.JSON_PROPERTY_CALIFORNIA, CarrierLaneDto.JSON_PROPERTY_COLORADO,
    CarrierLaneDto.JSON_PROPERTY_DELAWARE, CarrierLaneDto.JSON_PROPERTY_FLORIDA, CarrierLaneDto.JSON_PROPERTY_GEORGIA, CarrierLaneDto.JSON_PROPERTY_HAWAII,
    CarrierLaneDto.JSON_PROPERTY_IDAHO, CarrierLaneDto.JSON_PROPERTY_ILLINOIS, CarrierLaneDto.JSON_PROPERTY_INDIANA, CarrierLaneDto.JSON_PROPERTY_IOWA,
    CarrierLaneDto.JSON_PROPERTY_KANSAS, CarrierLaneDto.JSON_PROPERTY_KENTUCKY, CarrierLaneDto.JSON_PROPERTY_LOUISIANA, CarrierLaneDto.JSON_PROPERTY_MAINE,
    CarrierLaneDto.JSON_PROPERTY_MARYLAND, CarrierLaneDto.JSON_PROPERTY_MASSACHUSETTS, CarrierLaneDto.JSON_PROPERTY_MICHIGAN,
    CarrierLaneDto.JSON_PROPERTY_MINNESOTA, CarrierLaneDto.JSON_PROPERTY_MISSISSIPPI, CarrierLaneDto.JSON_PROPERTY_MISSOURI,
    CarrierLaneDto.JSON_PROPERTY_MONTANA, CarrierLaneDto.JSON_PROPERTY_NEBRASKA, CarrierLaneDto.JSON_PROPERTY_NEVADA,
    CarrierLaneDto.JSON_PROPERTY_NEW_HAMPSHIRE, CarrierLaneDto.JSON_PROPERTY_NEW_JERSEY, CarrierLaneDto.JSON_PROPERTY_NEW_MEXICO,
    CarrierLaneDto.JSON_PROPERTY_NEW_YORK, CarrierLaneDto.JSON_PROPERTY_NORTH_CAROLINA, CarrierLaneDto.JSON_PROPERTY_NORTH_DAKOTA,
    CarrierLaneDto.JSON_PROPERTY_OHIO, CarrierLaneDto.JSON_PROPERTY_OKLAHOMA, CarrierLaneDto.JSON_PROPERTY_OREGON, CarrierLaneDto.JSON_PROPERTY_PENNSYLVANIA,
    CarrierLaneDto.JSON_PROPERTY_RHODE_ISLAND, CarrierLaneDto.JSON_PROPERTY_SOUTH_CAROLINA, CarrierLaneDto.JSON_PROPERTY_SOUTH_DAKOTA,
    CarrierLaneDto.JSON_PROPERTY_TENNESSEE, CarrierLaneDto.JSON_PROPERTY_UTAH, CarrierLaneDto.JSON_PROPERTY_VERMONT, CarrierLaneDto.JSON_PROPERTY_VIRGINIA,
    CarrierLaneDto.JSON_PROPERTY_WASHINGTON, CarrierLaneDto.JSON_PROPERTY_WASHINGTON_D_C, CarrierLaneDto.JSON_PROPERTY_WEST_VIRGINIA,
    CarrierLaneDto.JSON_PROPERTY_WISCONSIN, CarrierLaneDto.JSON_PROPERTY_WYOMING, CarrierLaneDto.JSON_PROPERTY_CONNECTICUT, CarrierLaneDto.JSON_PROPERTY_TEXAS,
    CarrierLaneDto.JSON_PROPERTY_ALBERTA, CarrierLaneDto.JSON_PROPERTY_BRITISH_COLUMBIA, CarrierLaneDto.JSON_PROPERTY_MANITOBA,
    CarrierLaneDto.JSON_PROPERTY_NEW_BRUNSWICK, CarrierLaneDto.JSON_PROPERTY_NEWFOUNDLAND_AND_LABRADOR, CarrierLaneDto.JSON_PROPERTY_NORTHWEST_TERRITORIES,
    CarrierLaneDto.JSON_PROPERTY_NOVA_SCOTIA, CarrierLaneDto.JSON_PROPERTY_NUNAVUT, CarrierLaneDto.JSON_PROPERTY_ONTARIO,
    CarrierLaneDto.JSON_PROPERTY_PRINCE_EDWARD_ISLAND, CarrierLaneDto.JSON_PROPERTY_QUEBEC, CarrierLaneDto.JSON_PROPERTY_SASKATCHEWAN,
    CarrierLaneDto.JSON_PROPERTY_YUKON_TERRITORY})

public class CarrierLaneDto {

  public static final String JSON_PROPERTY_UNITED_STATES = "UnitedStates";
  public static final String JSON_PROPERTY_MEXICO = "Mexico";
  public static final String JSON_PROPERTY_CANADA = "Canada";
  public static final String JSON_PROPERTY_NORTHEAST_REGION = "NortheastRegion";
  public static final String JSON_PROPERTY_MIDWEST_REGION = "MidwestRegion";
  public static final String JSON_PROPERTY_SOUTH_REGION = "SouthRegion";
  public static final String JSON_PROPERTY_WEST_REGION = "WestRegion";
  public static final String JSON_PROPERTY_ALABAMA = "Alabama";
  public static final String JSON_PROPERTY_ALASKA = "Alaska";
  public static final String JSON_PROPERTY_ARIZONA = "Arizona";
  public static final String JSON_PROPERTY_ARKANSAS = "Arkansas";
  public static final String JSON_PROPERTY_CALIFORNIA = "California";
  public static final String JSON_PROPERTY_COLORADO = "Colorado";
  public static final String JSON_PROPERTY_DELAWARE = "Delaware";
  public static final String JSON_PROPERTY_FLORIDA = "Florida";
  public static final String JSON_PROPERTY_GEORGIA = "Georgia";
  public static final String JSON_PROPERTY_HAWAII = "Hawaii";
  public static final String JSON_PROPERTY_IDAHO = "Idaho";
  public static final String JSON_PROPERTY_ILLINOIS = "Illinois";
  public static final String JSON_PROPERTY_INDIANA = "Indiana";
  public static final String JSON_PROPERTY_IOWA = "Iowa";
  public static final String JSON_PROPERTY_KANSAS = "Kansas";
  public static final String JSON_PROPERTY_KENTUCKY = "Kentucky";
  public static final String JSON_PROPERTY_LOUISIANA = "Louisiana";
  public static final String JSON_PROPERTY_MAINE = "Maine";
  public static final String JSON_PROPERTY_MARYLAND = "Maryland";
  public static final String JSON_PROPERTY_MASSACHUSETTS = "Massachusetts";
  public static final String JSON_PROPERTY_MICHIGAN = "Michigan";
  public static final String JSON_PROPERTY_MINNESOTA = "Minnesota";
  public static final String JSON_PROPERTY_MISSISSIPPI = "Mississippi";
  public static final String JSON_PROPERTY_MISSOURI = "Missouri";
  public static final String JSON_PROPERTY_MONTANA = "Montana";
  public static final String JSON_PROPERTY_NEBRASKA = "Nebraska";
  public static final String JSON_PROPERTY_NEVADA = "Nevada";
  public static final String JSON_PROPERTY_NEW_HAMPSHIRE = "NewHampshire";
  public static final String JSON_PROPERTY_NEW_JERSEY = "NewJersey";
  public static final String JSON_PROPERTY_NEW_MEXICO = "NewMexico";
  public static final String JSON_PROPERTY_NEW_YORK = "NewYork";
  public static final String JSON_PROPERTY_NORTH_CAROLINA = "NorthCarolina";
  public static final String JSON_PROPERTY_NORTH_DAKOTA = "NorthDakota";
  public static final String JSON_PROPERTY_OHIO = "Ohio";
  public static final String JSON_PROPERTY_OKLAHOMA = "Oklahoma";
  public static final String JSON_PROPERTY_OREGON = "Oregon";
  public static final String JSON_PROPERTY_PENNSYLVANIA = "Pennsylvania";
  public static final String JSON_PROPERTY_RHODE_ISLAND = "RhodeIsland";
  public static final String JSON_PROPERTY_SOUTH_CAROLINA = "SouthCarolina";
  public static final String JSON_PROPERTY_SOUTH_DAKOTA = "SouthDakota";
  public static final String JSON_PROPERTY_TENNESSEE = "Tennessee";
  public static final String JSON_PROPERTY_UTAH = "Utah";
  public static final String JSON_PROPERTY_VERMONT = "Vermont";
  public static final String JSON_PROPERTY_VIRGINIA = "Virginia";
  public static final String JSON_PROPERTY_WASHINGTON = "Washington";
  public static final String JSON_PROPERTY_WASHINGTON_D_C = "WashingtonDC";
  public static final String JSON_PROPERTY_WEST_VIRGINIA = "WestVirginia";
  public static final String JSON_PROPERTY_WISCONSIN = "Wisconsin";
  public static final String JSON_PROPERTY_WYOMING = "Wyoming";
  public static final String JSON_PROPERTY_CONNECTICUT = "Connecticut";
  public static final String JSON_PROPERTY_TEXAS = "Texas";
  public static final String JSON_PROPERTY_ALBERTA = "Alberta";
  public static final String JSON_PROPERTY_BRITISH_COLUMBIA = "BritishColumbia";
  public static final String JSON_PROPERTY_MANITOBA = "Manitoba";
  public static final String JSON_PROPERTY_NEW_BRUNSWICK = "NewBrunswick";
  public static final String JSON_PROPERTY_NEWFOUNDLAND_AND_LABRADOR = "NewfoundlandAndLabrador";
  public static final String JSON_PROPERTY_NORTHWEST_TERRITORIES = "NorthwestTerritories";
  public static final String JSON_PROPERTY_NOVA_SCOTIA = "NovaScotia";
  public static final String JSON_PROPERTY_NUNAVUT = "Nunavut";
  public static final String JSON_PROPERTY_ONTARIO = "Ontario";
  public static final String JSON_PROPERTY_PRINCE_EDWARD_ISLAND = "PrinceEdwardIsland";
  public static final String JSON_PROPERTY_QUEBEC = "Quebec";
  public static final String JSON_PROPERTY_SASKATCHEWAN = "Saskatchewan";
  public static final String JSON_PROPERTY_YUKON_TERRITORY = "YukonTerritory";
  private Boolean unitedStates;
  private Boolean mexico;
  private Boolean canada;
  private Boolean northeastRegion;
  private Boolean midwestRegion;
  private Boolean southRegion;
  private Boolean westRegion;
  private Boolean alabama;
  private Boolean alaska;
  private Boolean arizona;
  private Boolean arkansas;
  private Boolean california;
  private Boolean colorado;
  private Boolean delaware;
  private Boolean florida;
  private Boolean georgia;
  private Boolean hawaii;
  private Boolean idaho;
  private Boolean illinois;
  private Boolean indiana;
  private Boolean iowa;
  private Boolean kansas;
  private Boolean kentucky;
  private Boolean louisiana;
  private Boolean maine;
  private Boolean maryland;
  private Boolean massachusetts;
  private Boolean michigan;
  private Boolean minnesota;
  private Boolean mississippi;
  private Boolean missouri;
  private Boolean montana;
  private Boolean nebraska;
  private Boolean nevada;
  private Boolean newHampshire;
  private Boolean newJersey;
  private Boolean newMexico;
  private Boolean newYork;
  private Boolean northCarolina;
  private Boolean northDakota;
  private Boolean ohio;
  private Boolean oklahoma;
  private Boolean oregon;
  private Boolean pennsylvania;
  private Boolean rhodeIsland;
  private Boolean southCarolina;
  private Boolean southDakota;
  private Boolean tennessee;
  private Boolean utah;
  private Boolean vermont;
  private Boolean virginia;
  private Boolean washington;
  private Boolean washingtonDC;
  private Boolean westVirginia;
  private Boolean wisconsin;
  private Boolean wyoming;
  private Boolean connecticut;
  private Boolean texas;
  private Boolean alberta;
  private Boolean britishColumbia;
  private Boolean manitoba;
  private Boolean newBrunswick;
  private Boolean newfoundlandAndLabrador;
  private Boolean northwestTerritories;
  private Boolean novaScotia;
  private Boolean nunavut;
  private Boolean ontario;
  private Boolean princeEdwardIsland;
  private Boolean quebec;
  private Boolean saskatchewan;
  private Boolean yukonTerritory;

  public CarrierLaneDto() {
  }

  public CarrierLaneDto unitedStates(Boolean unitedStates) {

    this.unitedStates = unitedStates;
    return this;
  }

  /**
   * Get unitedStates
   *
   * @return unitedStates
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNITED_STATES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getUnitedStates() {
    return unitedStates;
  }


  @JsonProperty(JSON_PROPERTY_UNITED_STATES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnitedStates(Boolean unitedStates) {
    this.unitedStates = unitedStates;
  }


  public CarrierLaneDto mexico(Boolean mexico) {

    this.mexico = mexico;
    return this;
  }

  /**
   * Get mexico
   *
   * @return mexico
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MEXICO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMexico() {
    return mexico;
  }


  @JsonProperty(JSON_PROPERTY_MEXICO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMexico(Boolean mexico) {
    this.mexico = mexico;
  }


  public CarrierLaneDto canada(Boolean canada) {

    this.canada = canada;
    return this;
  }

  /**
   * Get canada
   *
   * @return canada
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CANADA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCanada() {
    return canada;
  }


  @JsonProperty(JSON_PROPERTY_CANADA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCanada(Boolean canada) {
    this.canada = canada;
  }


  public CarrierLaneDto northeastRegion(Boolean northeastRegion) {

    this.northeastRegion = northeastRegion;
    return this;
  }

  /**
   * Get northeastRegion
   *
   * @return northeastRegion
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NORTHEAST_REGION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNortheastRegion() {
    return northeastRegion;
  }


  @JsonProperty(JSON_PROPERTY_NORTHEAST_REGION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNortheastRegion(Boolean northeastRegion) {
    this.northeastRegion = northeastRegion;
  }


  public CarrierLaneDto midwestRegion(Boolean midwestRegion) {

    this.midwestRegion = midwestRegion;
    return this;
  }

  /**
   * Get midwestRegion
   *
   * @return midwestRegion
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MIDWEST_REGION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMidwestRegion() {
    return midwestRegion;
  }


  @JsonProperty(JSON_PROPERTY_MIDWEST_REGION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMidwestRegion(Boolean midwestRegion) {
    this.midwestRegion = midwestRegion;
  }


  public CarrierLaneDto southRegion(Boolean southRegion) {

    this.southRegion = southRegion;
    return this;
  }

  /**
   * Get southRegion
   *
   * @return southRegion
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOUTH_REGION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSouthRegion() {
    return southRegion;
  }


  @JsonProperty(JSON_PROPERTY_SOUTH_REGION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSouthRegion(Boolean southRegion) {
    this.southRegion = southRegion;
  }


  public CarrierLaneDto westRegion(Boolean westRegion) {

    this.westRegion = westRegion;
    return this;
  }

  /**
   * Get westRegion
   *
   * @return westRegion
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WEST_REGION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getWestRegion() {
    return westRegion;
  }


  @JsonProperty(JSON_PROPERTY_WEST_REGION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWestRegion(Boolean westRegion) {
    this.westRegion = westRegion;
  }


  public CarrierLaneDto alabama(Boolean alabama) {

    this.alabama = alabama;
    return this;
  }

  /**
   * Get alabama
   *
   * @return alabama
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ALABAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAlabama() {
    return alabama;
  }


  @JsonProperty(JSON_PROPERTY_ALABAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAlabama(Boolean alabama) {
    this.alabama = alabama;
  }


  public CarrierLaneDto alaska(Boolean alaska) {

    this.alaska = alaska;
    return this;
  }

  /**
   * Get alaska
   *
   * @return alaska
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ALASKA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAlaska() {
    return alaska;
  }


  @JsonProperty(JSON_PROPERTY_ALASKA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAlaska(Boolean alaska) {
    this.alaska = alaska;
  }


  public CarrierLaneDto arizona(Boolean arizona) {

    this.arizona = arizona;
    return this;
  }

  /**
   * Get arizona
   *
   * @return arizona
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ARIZONA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getArizona() {
    return arizona;
  }


  @JsonProperty(JSON_PROPERTY_ARIZONA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setArizona(Boolean arizona) {
    this.arizona = arizona;
  }


  public CarrierLaneDto arkansas(Boolean arkansas) {

    this.arkansas = arkansas;
    return this;
  }

  /**
   * Get arkansas
   *
   * @return arkansas
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ARKANSAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getArkansas() {
    return arkansas;
  }


  @JsonProperty(JSON_PROPERTY_ARKANSAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setArkansas(Boolean arkansas) {
    this.arkansas = arkansas;
  }


  public CarrierLaneDto california(Boolean california) {

    this.california = california;
    return this;
  }

  /**
   * Get california
   *
   * @return california
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CALIFORNIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCalifornia() {
    return california;
  }


  @JsonProperty(JSON_PROPERTY_CALIFORNIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCalifornia(Boolean california) {
    this.california = california;
  }


  public CarrierLaneDto colorado(Boolean colorado) {

    this.colorado = colorado;
    return this;
  }

  /**
   * Get colorado
   *
   * @return colorado
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COLORADO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getColorado() {
    return colorado;
  }


  @JsonProperty(JSON_PROPERTY_COLORADO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setColorado(Boolean colorado) {
    this.colorado = colorado;
  }


  public CarrierLaneDto delaware(Boolean delaware) {

    this.delaware = delaware;
    return this;
  }

  /**
   * Get delaware
   *
   * @return delaware
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DELAWARE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDelaware() {
    return delaware;
  }


  @JsonProperty(JSON_PROPERTY_DELAWARE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDelaware(Boolean delaware) {
    this.delaware = delaware;
  }


  public CarrierLaneDto florida(Boolean florida) {

    this.florida = florida;
    return this;
  }

  /**
   * Get florida
   *
   * @return florida
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLORIDA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlorida() {
    return florida;
  }


  @JsonProperty(JSON_PROPERTY_FLORIDA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlorida(Boolean florida) {
    this.florida = florida;
  }


  public CarrierLaneDto georgia(Boolean georgia) {

    this.georgia = georgia;
    return this;
  }

  /**
   * Get georgia
   *
   * @return georgia
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GEORGIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getGeorgia() {
    return georgia;
  }


  @JsonProperty(JSON_PROPERTY_GEORGIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGeorgia(Boolean georgia) {
    this.georgia = georgia;
  }


  public CarrierLaneDto hawaii(Boolean hawaii) {

    this.hawaii = hawaii;
    return this;
  }

  /**
   * Get hawaii
   *
   * @return hawaii
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAWAII)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHawaii() {
    return hawaii;
  }


  @JsonProperty(JSON_PROPERTY_HAWAII)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHawaii(Boolean hawaii) {
    this.hawaii = hawaii;
  }


  public CarrierLaneDto idaho(Boolean idaho) {

    this.idaho = idaho;
    return this;
  }

  /**
   * Get idaho
   *
   * @return idaho
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IDAHO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIdaho() {
    return idaho;
  }


  @JsonProperty(JSON_PROPERTY_IDAHO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIdaho(Boolean idaho) {
    this.idaho = idaho;
  }


  public CarrierLaneDto illinois(Boolean illinois) {

    this.illinois = illinois;
    return this;
  }

  /**
   * Get illinois
   *
   * @return illinois
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ILLINOIS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIllinois() {
    return illinois;
  }


  @JsonProperty(JSON_PROPERTY_ILLINOIS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIllinois(Boolean illinois) {
    this.illinois = illinois;
  }


  public CarrierLaneDto indiana(Boolean indiana) {

    this.indiana = indiana;
    return this;
  }

  /**
   * Get indiana
   *
   * @return indiana
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INDIANA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIndiana() {
    return indiana;
  }


  @JsonProperty(JSON_PROPERTY_INDIANA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIndiana(Boolean indiana) {
    this.indiana = indiana;
  }


  public CarrierLaneDto iowa(Boolean iowa) {

    this.iowa = iowa;
    return this;
  }

  /**
   * Get iowa
   *
   * @return iowa
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IOWA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIowa() {
    return iowa;
  }


  @JsonProperty(JSON_PROPERTY_IOWA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIowa(Boolean iowa) {
    this.iowa = iowa;
  }


  public CarrierLaneDto kansas(Boolean kansas) {

    this.kansas = kansas;
    return this;
  }

  /**
   * Get kansas
   *
   * @return kansas
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KANSAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getKansas() {
    return kansas;
  }


  @JsonProperty(JSON_PROPERTY_KANSAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKansas(Boolean kansas) {
    this.kansas = kansas;
  }


  public CarrierLaneDto kentucky(Boolean kentucky) {

    this.kentucky = kentucky;
    return this;
  }

  /**
   * Get kentucky
   *
   * @return kentucky
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KENTUCKY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getKentucky() {
    return kentucky;
  }


  @JsonProperty(JSON_PROPERTY_KENTUCKY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKentucky(Boolean kentucky) {
    this.kentucky = kentucky;
  }


  public CarrierLaneDto louisiana(Boolean louisiana) {

    this.louisiana = louisiana;
    return this;
  }

  /**
   * Get louisiana
   *
   * @return louisiana
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOUISIANA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getLouisiana() {
    return louisiana;
  }


  @JsonProperty(JSON_PROPERTY_LOUISIANA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLouisiana(Boolean louisiana) {
    this.louisiana = louisiana;
  }


  public CarrierLaneDto maine(Boolean maine) {

    this.maine = maine;
    return this;
  }

  /**
   * Get maine
   *
   * @return maine
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAINE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMaine() {
    return maine;
  }


  @JsonProperty(JSON_PROPERTY_MAINE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMaine(Boolean maine) {
    this.maine = maine;
  }


  public CarrierLaneDto maryland(Boolean maryland) {

    this.maryland = maryland;
    return this;
  }

  /**
   * Get maryland
   *
   * @return maryland
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MARYLAND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMaryland() {
    return maryland;
  }


  @JsonProperty(JSON_PROPERTY_MARYLAND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMaryland(Boolean maryland) {
    this.maryland = maryland;
  }


  public CarrierLaneDto massachusetts(Boolean massachusetts) {

    this.massachusetts = massachusetts;
    return this;
  }

  /**
   * Get massachusetts
   *
   * @return massachusetts
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MASSACHUSETTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMassachusetts() {
    return massachusetts;
  }


  @JsonProperty(JSON_PROPERTY_MASSACHUSETTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMassachusetts(Boolean massachusetts) {
    this.massachusetts = massachusetts;
  }


  public CarrierLaneDto michigan(Boolean michigan) {

    this.michigan = michigan;
    return this;
  }

  /**
   * Get michigan
   *
   * @return michigan
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MICHIGAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMichigan() {
    return michigan;
  }


  @JsonProperty(JSON_PROPERTY_MICHIGAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMichigan(Boolean michigan) {
    this.michigan = michigan;
  }


  public CarrierLaneDto minnesota(Boolean minnesota) {

    this.minnesota = minnesota;
    return this;
  }

  /**
   * Get minnesota
   *
   * @return minnesota
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MINNESOTA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMinnesota() {
    return minnesota;
  }


  @JsonProperty(JSON_PROPERTY_MINNESOTA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMinnesota(Boolean minnesota) {
    this.minnesota = minnesota;
  }


  public CarrierLaneDto mississippi(Boolean mississippi) {

    this.mississippi = mississippi;
    return this;
  }

  /**
   * Get mississippi
   *
   * @return mississippi
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MISSISSIPPI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMississippi() {
    return mississippi;
  }


  @JsonProperty(JSON_PROPERTY_MISSISSIPPI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMississippi(Boolean mississippi) {
    this.mississippi = mississippi;
  }


  public CarrierLaneDto missouri(Boolean missouri) {

    this.missouri = missouri;
    return this;
  }

  /**
   * Get missouri
   *
   * @return missouri
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MISSOURI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMissouri() {
    return missouri;
  }


  @JsonProperty(JSON_PROPERTY_MISSOURI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMissouri(Boolean missouri) {
    this.missouri = missouri;
  }


  public CarrierLaneDto montana(Boolean montana) {

    this.montana = montana;
    return this;
  }

  /**
   * Get montana
   *
   * @return montana
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MONTANA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMontana() {
    return montana;
  }


  @JsonProperty(JSON_PROPERTY_MONTANA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMontana(Boolean montana) {
    this.montana = montana;
  }


  public CarrierLaneDto nebraska(Boolean nebraska) {

    this.nebraska = nebraska;
    return this;
  }

  /**
   * Get nebraska
   *
   * @return nebraska
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEBRASKA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNebraska() {
    return nebraska;
  }


  @JsonProperty(JSON_PROPERTY_NEBRASKA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNebraska(Boolean nebraska) {
    this.nebraska = nebraska;
  }


  public CarrierLaneDto nevada(Boolean nevada) {

    this.nevada = nevada;
    return this;
  }

  /**
   * Get nevada
   *
   * @return nevada
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEVADA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNevada() {
    return nevada;
  }


  @JsonProperty(JSON_PROPERTY_NEVADA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNevada(Boolean nevada) {
    this.nevada = nevada;
  }


  public CarrierLaneDto newHampshire(Boolean newHampshire) {

    this.newHampshire = newHampshire;
    return this;
  }

  /**
   * Get newHampshire
   *
   * @return newHampshire
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEW_HAMPSHIRE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNewHampshire() {
    return newHampshire;
  }


  @JsonProperty(JSON_PROPERTY_NEW_HAMPSHIRE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNewHampshire(Boolean newHampshire) {
    this.newHampshire = newHampshire;
  }


  public CarrierLaneDto newJersey(Boolean newJersey) {

    this.newJersey = newJersey;
    return this;
  }

  /**
   * Get newJersey
   *
   * @return newJersey
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEW_JERSEY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNewJersey() {
    return newJersey;
  }


  @JsonProperty(JSON_PROPERTY_NEW_JERSEY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNewJersey(Boolean newJersey) {
    this.newJersey = newJersey;
  }


  public CarrierLaneDto newMexico(Boolean newMexico) {

    this.newMexico = newMexico;
    return this;
  }

  /**
   * Get newMexico
   *
   * @return newMexico
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEW_MEXICO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNewMexico() {
    return newMexico;
  }


  @JsonProperty(JSON_PROPERTY_NEW_MEXICO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNewMexico(Boolean newMexico) {
    this.newMexico = newMexico;
  }


  public CarrierLaneDto newYork(Boolean newYork) {

    this.newYork = newYork;
    return this;
  }

  /**
   * Get newYork
   *
   * @return newYork
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEW_YORK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNewYork() {
    return newYork;
  }


  @JsonProperty(JSON_PROPERTY_NEW_YORK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNewYork(Boolean newYork) {
    this.newYork = newYork;
  }


  public CarrierLaneDto northCarolina(Boolean northCarolina) {

    this.northCarolina = northCarolina;
    return this;
  }

  /**
   * Get northCarolina
   *
   * @return northCarolina
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NORTH_CAROLINA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNorthCarolina() {
    return northCarolina;
  }


  @JsonProperty(JSON_PROPERTY_NORTH_CAROLINA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNorthCarolina(Boolean northCarolina) {
    this.northCarolina = northCarolina;
  }


  public CarrierLaneDto northDakota(Boolean northDakota) {

    this.northDakota = northDakota;
    return this;
  }

  /**
   * Get northDakota
   *
   * @return northDakota
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NORTH_DAKOTA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNorthDakota() {
    return northDakota;
  }


  @JsonProperty(JSON_PROPERTY_NORTH_DAKOTA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNorthDakota(Boolean northDakota) {
    this.northDakota = northDakota;
  }


  public CarrierLaneDto ohio(Boolean ohio) {

    this.ohio = ohio;
    return this;
  }

  /**
   * Get ohio
   *
   * @return ohio
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OHIO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOhio() {
    return ohio;
  }


  @JsonProperty(JSON_PROPERTY_OHIO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOhio(Boolean ohio) {
    this.ohio = ohio;
  }


  public CarrierLaneDto oklahoma(Boolean oklahoma) {

    this.oklahoma = oklahoma;
    return this;
  }

  /**
   * Get oklahoma
   *
   * @return oklahoma
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OKLAHOMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOklahoma() {
    return oklahoma;
  }


  @JsonProperty(JSON_PROPERTY_OKLAHOMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOklahoma(Boolean oklahoma) {
    this.oklahoma = oklahoma;
  }


  public CarrierLaneDto oregon(Boolean oregon) {

    this.oregon = oregon;
    return this;
  }

  /**
   * Get oregon
   *
   * @return oregon
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OREGON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOregon() {
    return oregon;
  }


  @JsonProperty(JSON_PROPERTY_OREGON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOregon(Boolean oregon) {
    this.oregon = oregon;
  }


  public CarrierLaneDto pennsylvania(Boolean pennsylvania) {

    this.pennsylvania = pennsylvania;
    return this;
  }

  /**
   * Get pennsylvania
   *
   * @return pennsylvania
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PENNSYLVANIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPennsylvania() {
    return pennsylvania;
  }


  @JsonProperty(JSON_PROPERTY_PENNSYLVANIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPennsylvania(Boolean pennsylvania) {
    this.pennsylvania = pennsylvania;
  }


  public CarrierLaneDto rhodeIsland(Boolean rhodeIsland) {

    this.rhodeIsland = rhodeIsland;
    return this;
  }

  /**
   * Get rhodeIsland
   *
   * @return rhodeIsland
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RHODE_ISLAND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRhodeIsland() {
    return rhodeIsland;
  }


  @JsonProperty(JSON_PROPERTY_RHODE_ISLAND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRhodeIsland(Boolean rhodeIsland) {
    this.rhodeIsland = rhodeIsland;
  }


  public CarrierLaneDto southCarolina(Boolean southCarolina) {

    this.southCarolina = southCarolina;
    return this;
  }

  /**
   * Get southCarolina
   *
   * @return southCarolina
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOUTH_CAROLINA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSouthCarolina() {
    return southCarolina;
  }


  @JsonProperty(JSON_PROPERTY_SOUTH_CAROLINA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSouthCarolina(Boolean southCarolina) {
    this.southCarolina = southCarolina;
  }


  public CarrierLaneDto southDakota(Boolean southDakota) {

    this.southDakota = southDakota;
    return this;
  }

  /**
   * Get southDakota
   *
   * @return southDakota
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOUTH_DAKOTA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSouthDakota() {
    return southDakota;
  }


  @JsonProperty(JSON_PROPERTY_SOUTH_DAKOTA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSouthDakota(Boolean southDakota) {
    this.southDakota = southDakota;
  }


  public CarrierLaneDto tennessee(Boolean tennessee) {

    this.tennessee = tennessee;
    return this;
  }

  /**
   * Get tennessee
   *
   * @return tennessee
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TENNESSEE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTennessee() {
    return tennessee;
  }


  @JsonProperty(JSON_PROPERTY_TENNESSEE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTennessee(Boolean tennessee) {
    this.tennessee = tennessee;
  }


  public CarrierLaneDto utah(Boolean utah) {

    this.utah = utah;
    return this;
  }

  /**
   * Get utah
   *
   * @return utah
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UTAH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getUtah() {
    return utah;
  }


  @JsonProperty(JSON_PROPERTY_UTAH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUtah(Boolean utah) {
    this.utah = utah;
  }


  public CarrierLaneDto vermont(Boolean vermont) {

    this.vermont = vermont;
    return this;
  }

  /**
   * Get vermont
   *
   * @return vermont
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VERMONT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVermont() {
    return vermont;
  }


  @JsonProperty(JSON_PROPERTY_VERMONT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVermont(Boolean vermont) {
    this.vermont = vermont;
  }


  public CarrierLaneDto virginia(Boolean virginia) {

    this.virginia = virginia;
    return this;
  }

  /**
   * Get virginia
   *
   * @return virginia
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VIRGINIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVirginia() {
    return virginia;
  }


  @JsonProperty(JSON_PROPERTY_VIRGINIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVirginia(Boolean virginia) {
    this.virginia = virginia;
  }


  public CarrierLaneDto washington(Boolean washington) {

    this.washington = washington;
    return this;
  }

  /**
   * Get washington
   *
   * @return washington
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WASHINGTON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getWashington() {
    return washington;
  }


  @JsonProperty(JSON_PROPERTY_WASHINGTON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWashington(Boolean washington) {
    this.washington = washington;
  }


  public CarrierLaneDto washingtonDC(Boolean washingtonDC) {

    this.washingtonDC = washingtonDC;
    return this;
  }

  /**
   * Get washingtonDC
   *
   * @return washingtonDC
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WASHINGTON_D_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getWashingtonDC() {
    return washingtonDC;
  }


  @JsonProperty(JSON_PROPERTY_WASHINGTON_D_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWashingtonDC(Boolean washingtonDC) {
    this.washingtonDC = washingtonDC;
  }


  public CarrierLaneDto westVirginia(Boolean westVirginia) {

    this.westVirginia = westVirginia;
    return this;
  }

  /**
   * Get westVirginia
   *
   * @return westVirginia
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WEST_VIRGINIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getWestVirginia() {
    return westVirginia;
  }


  @JsonProperty(JSON_PROPERTY_WEST_VIRGINIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWestVirginia(Boolean westVirginia) {
    this.westVirginia = westVirginia;
  }


  public CarrierLaneDto wisconsin(Boolean wisconsin) {

    this.wisconsin = wisconsin;
    return this;
  }

  /**
   * Get wisconsin
   *
   * @return wisconsin
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WISCONSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getWisconsin() {
    return wisconsin;
  }


  @JsonProperty(JSON_PROPERTY_WISCONSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWisconsin(Boolean wisconsin) {
    this.wisconsin = wisconsin;
  }


  public CarrierLaneDto wyoming(Boolean wyoming) {

    this.wyoming = wyoming;
    return this;
  }

  /**
   * Get wyoming
   *
   * @return wyoming
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WYOMING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getWyoming() {
    return wyoming;
  }


  @JsonProperty(JSON_PROPERTY_WYOMING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWyoming(Boolean wyoming) {
    this.wyoming = wyoming;
  }


  public CarrierLaneDto connecticut(Boolean connecticut) {

    this.connecticut = connecticut;
    return this;
  }

  /**
   * Get connecticut
   *
   * @return connecticut
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONNECTICUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getConnecticut() {
    return connecticut;
  }


  @JsonProperty(JSON_PROPERTY_CONNECTICUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setConnecticut(Boolean connecticut) {
    this.connecticut = connecticut;
  }


  public CarrierLaneDto texas(Boolean texas) {

    this.texas = texas;
    return this;
  }

  /**
   * Get texas
   *
   * @return texas
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TEXAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTexas() {
    return texas;
  }


  @JsonProperty(JSON_PROPERTY_TEXAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTexas(Boolean texas) {
    this.texas = texas;
  }


  public CarrierLaneDto alberta(Boolean alberta) {

    this.alberta = alberta;
    return this;
  }

  /**
   * Get alberta
   *
   * @return alberta
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ALBERTA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAlberta() {
    return alberta;
  }


  @JsonProperty(JSON_PROPERTY_ALBERTA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAlberta(Boolean alberta) {
    this.alberta = alberta;
  }


  public CarrierLaneDto britishColumbia(Boolean britishColumbia) {

    this.britishColumbia = britishColumbia;
    return this;
  }

  /**
   * Get britishColumbia
   *
   * @return britishColumbia
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BRITISH_COLUMBIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getBritishColumbia() {
    return britishColumbia;
  }


  @JsonProperty(JSON_PROPERTY_BRITISH_COLUMBIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBritishColumbia(Boolean britishColumbia) {
    this.britishColumbia = britishColumbia;
  }


  public CarrierLaneDto manitoba(Boolean manitoba) {

    this.manitoba = manitoba;
    return this;
  }

  /**
   * Get manitoba
   *
   * @return manitoba
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MANITOBA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getManitoba() {
    return manitoba;
  }


  @JsonProperty(JSON_PROPERTY_MANITOBA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setManitoba(Boolean manitoba) {
    this.manitoba = manitoba;
  }


  public CarrierLaneDto newBrunswick(Boolean newBrunswick) {

    this.newBrunswick = newBrunswick;
    return this;
  }

  /**
   * Get newBrunswick
   *
   * @return newBrunswick
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEW_BRUNSWICK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNewBrunswick() {
    return newBrunswick;
  }


  @JsonProperty(JSON_PROPERTY_NEW_BRUNSWICK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNewBrunswick(Boolean newBrunswick) {
    this.newBrunswick = newBrunswick;
  }


  public CarrierLaneDto newfoundlandAndLabrador(Boolean newfoundlandAndLabrador) {

    this.newfoundlandAndLabrador = newfoundlandAndLabrador;
    return this;
  }

  /**
   * Get newfoundlandAndLabrador
   *
   * @return newfoundlandAndLabrador
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEWFOUNDLAND_AND_LABRADOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNewfoundlandAndLabrador() {
    return newfoundlandAndLabrador;
  }


  @JsonProperty(JSON_PROPERTY_NEWFOUNDLAND_AND_LABRADOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNewfoundlandAndLabrador(Boolean newfoundlandAndLabrador) {
    this.newfoundlandAndLabrador = newfoundlandAndLabrador;
  }


  public CarrierLaneDto northwestTerritories(Boolean northwestTerritories) {

    this.northwestTerritories = northwestTerritories;
    return this;
  }

  /**
   * Get northwestTerritories
   *
   * @return northwestTerritories
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NORTHWEST_TERRITORIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNorthwestTerritories() {
    return northwestTerritories;
  }


  @JsonProperty(JSON_PROPERTY_NORTHWEST_TERRITORIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNorthwestTerritories(Boolean northwestTerritories) {
    this.northwestTerritories = northwestTerritories;
  }


  public CarrierLaneDto novaScotia(Boolean novaScotia) {

    this.novaScotia = novaScotia;
    return this;
  }

  /**
   * Get novaScotia
   *
   * @return novaScotia
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NOVA_SCOTIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNovaScotia() {
    return novaScotia;
  }


  @JsonProperty(JSON_PROPERTY_NOVA_SCOTIA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNovaScotia(Boolean novaScotia) {
    this.novaScotia = novaScotia;
  }


  public CarrierLaneDto nunavut(Boolean nunavut) {

    this.nunavut = nunavut;
    return this;
  }

  /**
   * Get nunavut
   *
   * @return nunavut
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUNAVUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNunavut() {
    return nunavut;
  }


  @JsonProperty(JSON_PROPERTY_NUNAVUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNunavut(Boolean nunavut) {
    this.nunavut = nunavut;
  }


  public CarrierLaneDto ontario(Boolean ontario) {

    this.ontario = ontario;
    return this;
  }

  /**
   * Get ontario
   *
   * @return ontario
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ONTARIO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOntario() {
    return ontario;
  }


  @JsonProperty(JSON_PROPERTY_ONTARIO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOntario(Boolean ontario) {
    this.ontario = ontario;
  }


  public CarrierLaneDto princeEdwardIsland(Boolean princeEdwardIsland) {

    this.princeEdwardIsland = princeEdwardIsland;
    return this;
  }

  /**
   * Get princeEdwardIsland
   *
   * @return princeEdwardIsland
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRINCE_EDWARD_ISLAND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPrinceEdwardIsland() {
    return princeEdwardIsland;
  }


  @JsonProperty(JSON_PROPERTY_PRINCE_EDWARD_ISLAND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPrinceEdwardIsland(Boolean princeEdwardIsland) {
    this.princeEdwardIsland = princeEdwardIsland;
  }


  public CarrierLaneDto quebec(Boolean quebec) {

    this.quebec = quebec;
    return this;
  }

  /**
   * Get quebec
   *
   * @return quebec
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_QUEBEC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getQuebec() {
    return quebec;
  }


  @JsonProperty(JSON_PROPERTY_QUEBEC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setQuebec(Boolean quebec) {
    this.quebec = quebec;
  }


  public CarrierLaneDto saskatchewan(Boolean saskatchewan) {

    this.saskatchewan = saskatchewan;
    return this;
  }

  /**
   * Get saskatchewan
   *
   * @return saskatchewan
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SASKATCHEWAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSaskatchewan() {
    return saskatchewan;
  }


  @JsonProperty(JSON_PROPERTY_SASKATCHEWAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSaskatchewan(Boolean saskatchewan) {
    this.saskatchewan = saskatchewan;
  }


  public CarrierLaneDto yukonTerritory(Boolean yukonTerritory) {

    this.yukonTerritory = yukonTerritory;
    return this;
  }

  /**
   * Get yukonTerritory
   *
   * @return yukonTerritory
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_YUKON_TERRITORY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getYukonTerritory() {
    return yukonTerritory;
  }


  @JsonProperty(JSON_PROPERTY_YUKON_TERRITORY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setYukonTerritory(Boolean yukonTerritory) {
    this.yukonTerritory = yukonTerritory;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierLaneDto carrierLaneDto = (CarrierLaneDto) o;
    return Objects.equals(this.unitedStates, carrierLaneDto.unitedStates) && Objects.equals(this.mexico, carrierLaneDto.mexico) && Objects.equals(this.canada,
        carrierLaneDto.canada) && Objects.equals(this.northeastRegion, carrierLaneDto.northeastRegion) && Objects.equals(this.midwestRegion,
        carrierLaneDto.midwestRegion) && Objects.equals(this.southRegion, carrierLaneDto.southRegion) && Objects.equals(this.westRegion,
        carrierLaneDto.westRegion) && Objects.equals(this.alabama, carrierLaneDto.alabama) && Objects.equals(this.alaska, carrierLaneDto.alaska)
           && Objects.equals(this.arizona, carrierLaneDto.arizona) && Objects.equals(this.arkansas, carrierLaneDto.arkansas) && Objects.equals(this.california,
        carrierLaneDto.california) && Objects.equals(this.colorado, carrierLaneDto.colorado) && Objects.equals(this.delaware, carrierLaneDto.delaware)
           && Objects.equals(this.florida, carrierLaneDto.florida) && Objects.equals(this.georgia, carrierLaneDto.georgia) && Objects.equals(this.hawaii,
        carrierLaneDto.hawaii) && Objects.equals(this.idaho, carrierLaneDto.idaho) && Objects.equals(this.illinois, carrierLaneDto.illinois) && Objects.equals(
        this.indiana, carrierLaneDto.indiana) && Objects.equals(this.iowa, carrierLaneDto.iowa) && Objects.equals(this.kansas, carrierLaneDto.kansas)
           && Objects.equals(this.kentucky, carrierLaneDto.kentucky) && Objects.equals(this.louisiana, carrierLaneDto.louisiana) && Objects.equals(this.maine,
        carrierLaneDto.maine) && Objects.equals(this.maryland, carrierLaneDto.maryland) && Objects.equals(this.massachusetts, carrierLaneDto.massachusetts)
           && Objects.equals(this.michigan, carrierLaneDto.michigan) && Objects.equals(this.minnesota, carrierLaneDto.minnesota) && Objects.equals(
        this.mississippi, carrierLaneDto.mississippi) && Objects.equals(this.missouri, carrierLaneDto.missouri) && Objects.equals(this.montana,
        carrierLaneDto.montana) && Objects.equals(this.nebraska, carrierLaneDto.nebraska) && Objects.equals(this.nevada, carrierLaneDto.nevada)
           && Objects.equals(this.newHampshire, carrierLaneDto.newHampshire) && Objects.equals(this.newJersey, carrierLaneDto.newJersey) && Objects.equals(
        this.newMexico, carrierLaneDto.newMexico) && Objects.equals(this.newYork, carrierLaneDto.newYork) && Objects.equals(this.northCarolina,
        carrierLaneDto.northCarolina) && Objects.equals(this.northDakota, carrierLaneDto.northDakota) && Objects.equals(this.ohio, carrierLaneDto.ohio)
           && Objects.equals(this.oklahoma, carrierLaneDto.oklahoma) && Objects.equals(this.oregon, carrierLaneDto.oregon) && Objects.equals(this.pennsylvania,
        carrierLaneDto.pennsylvania) && Objects.equals(this.rhodeIsland, carrierLaneDto.rhodeIsland) && Objects.equals(this.southCarolina,
        carrierLaneDto.southCarolina) && Objects.equals(this.southDakota, carrierLaneDto.southDakota) && Objects.equals(this.tennessee,
        carrierLaneDto.tennessee) && Objects.equals(this.utah, carrierLaneDto.utah) && Objects.equals(this.vermont, carrierLaneDto.vermont) && Objects.equals(
        this.virginia, carrierLaneDto.virginia) && Objects.equals(this.washington, carrierLaneDto.washington) && Objects.equals(this.washingtonDC,
        carrierLaneDto.washingtonDC) && Objects.equals(this.westVirginia, carrierLaneDto.westVirginia) && Objects.equals(this.wisconsin,
        carrierLaneDto.wisconsin) && Objects.equals(this.wyoming, carrierLaneDto.wyoming) && Objects.equals(this.connecticut, carrierLaneDto.connecticut)
           && Objects.equals(this.texas, carrierLaneDto.texas) && Objects.equals(this.alberta, carrierLaneDto.alberta) && Objects.equals(this.britishColumbia,
        carrierLaneDto.britishColumbia) && Objects.equals(this.manitoba, carrierLaneDto.manitoba) && Objects.equals(this.newBrunswick,
        carrierLaneDto.newBrunswick) && Objects.equals(this.newfoundlandAndLabrador, carrierLaneDto.newfoundlandAndLabrador) && Objects.equals(
        this.northwestTerritories, carrierLaneDto.northwestTerritories) && Objects.equals(this.novaScotia, carrierLaneDto.novaScotia) && Objects.equals(
        this.nunavut, carrierLaneDto.nunavut) && Objects.equals(this.ontario, carrierLaneDto.ontario) && Objects.equals(this.princeEdwardIsland,
        carrierLaneDto.princeEdwardIsland) && Objects.equals(this.quebec, carrierLaneDto.quebec) && Objects.equals(this.saskatchewan,
        carrierLaneDto.saskatchewan) && Objects.equals(this.yukonTerritory, carrierLaneDto.yukonTerritory);
  }

  @Override
  public int hashCode() {
    return Objects.hash(unitedStates, mexico, canada, northeastRegion, midwestRegion, southRegion, westRegion, alabama, alaska, arizona, arkansas, california,
        colorado, delaware, florida, georgia, hawaii, idaho, illinois, indiana, iowa, kansas, kentucky, louisiana, maine, maryland, massachusetts, michigan,
        minnesota, mississippi, missouri, montana, nebraska, nevada, newHampshire, newJersey, newMexico, newYork, northCarolina, northDakota, ohio, oklahoma,
        oregon, pennsylvania, rhodeIsland, southCarolina, southDakota, tennessee, utah, vermont, virginia, washington, washingtonDC, westVirginia, wisconsin,
        wyoming, connecticut, texas, alberta, britishColumbia, manitoba, newBrunswick, newfoundlandAndLabrador, northwestTerritories, novaScotia, nunavut,
        ontario, princeEdwardIsland, quebec, saskatchewan, yukonTerritory);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierLaneDto {\n");
    sb.append("    unitedStates: ").append(toIndentedString(unitedStates)).append("\n");
    sb.append("    mexico: ").append(toIndentedString(mexico)).append("\n");
    sb.append("    canada: ").append(toIndentedString(canada)).append("\n");
    sb.append("    northeastRegion: ").append(toIndentedString(northeastRegion)).append("\n");
    sb.append("    midwestRegion: ").append(toIndentedString(midwestRegion)).append("\n");
    sb.append("    southRegion: ").append(toIndentedString(southRegion)).append("\n");
    sb.append("    westRegion: ").append(toIndentedString(westRegion)).append("\n");
    sb.append("    alabama: ").append(toIndentedString(alabama)).append("\n");
    sb.append("    alaska: ").append(toIndentedString(alaska)).append("\n");
    sb.append("    arizona: ").append(toIndentedString(arizona)).append("\n");
    sb.append("    arkansas: ").append(toIndentedString(arkansas)).append("\n");
    sb.append("    california: ").append(toIndentedString(california)).append("\n");
    sb.append("    colorado: ").append(toIndentedString(colorado)).append("\n");
    sb.append("    delaware: ").append(toIndentedString(delaware)).append("\n");
    sb.append("    florida: ").append(toIndentedString(florida)).append("\n");
    sb.append("    georgia: ").append(toIndentedString(georgia)).append("\n");
    sb.append("    hawaii: ").append(toIndentedString(hawaii)).append("\n");
    sb.append("    idaho: ").append(toIndentedString(idaho)).append("\n");
    sb.append("    illinois: ").append(toIndentedString(illinois)).append("\n");
    sb.append("    indiana: ").append(toIndentedString(indiana)).append("\n");
    sb.append("    iowa: ").append(toIndentedString(iowa)).append("\n");
    sb.append("    kansas: ").append(toIndentedString(kansas)).append("\n");
    sb.append("    kentucky: ").append(toIndentedString(kentucky)).append("\n");
    sb.append("    louisiana: ").append(toIndentedString(louisiana)).append("\n");
    sb.append("    maine: ").append(toIndentedString(maine)).append("\n");
    sb.append("    maryland: ").append(toIndentedString(maryland)).append("\n");
    sb.append("    massachusetts: ").append(toIndentedString(massachusetts)).append("\n");
    sb.append("    michigan: ").append(toIndentedString(michigan)).append("\n");
    sb.append("    minnesota: ").append(toIndentedString(minnesota)).append("\n");
    sb.append("    mississippi: ").append(toIndentedString(mississippi)).append("\n");
    sb.append("    missouri: ").append(toIndentedString(missouri)).append("\n");
    sb.append("    montana: ").append(toIndentedString(montana)).append("\n");
    sb.append("    nebraska: ").append(toIndentedString(nebraska)).append("\n");
    sb.append("    nevada: ").append(toIndentedString(nevada)).append("\n");
    sb.append("    newHampshire: ").append(toIndentedString(newHampshire)).append("\n");
    sb.append("    newJersey: ").append(toIndentedString(newJersey)).append("\n");
    sb.append("    newMexico: ").append(toIndentedString(newMexico)).append("\n");
    sb.append("    newYork: ").append(toIndentedString(newYork)).append("\n");
    sb.append("    northCarolina: ").append(toIndentedString(northCarolina)).append("\n");
    sb.append("    northDakota: ").append(toIndentedString(northDakota)).append("\n");
    sb.append("    ohio: ").append(toIndentedString(ohio)).append("\n");
    sb.append("    oklahoma: ").append(toIndentedString(oklahoma)).append("\n");
    sb.append("    oregon: ").append(toIndentedString(oregon)).append("\n");
    sb.append("    pennsylvania: ").append(toIndentedString(pennsylvania)).append("\n");
    sb.append("    rhodeIsland: ").append(toIndentedString(rhodeIsland)).append("\n");
    sb.append("    southCarolina: ").append(toIndentedString(southCarolina)).append("\n");
    sb.append("    southDakota: ").append(toIndentedString(southDakota)).append("\n");
    sb.append("    tennessee: ").append(toIndentedString(tennessee)).append("\n");
    sb.append("    utah: ").append(toIndentedString(utah)).append("\n");
    sb.append("    vermont: ").append(toIndentedString(vermont)).append("\n");
    sb.append("    virginia: ").append(toIndentedString(virginia)).append("\n");
    sb.append("    washington: ").append(toIndentedString(washington)).append("\n");
    sb.append("    washingtonDC: ").append(toIndentedString(washingtonDC)).append("\n");
    sb.append("    westVirginia: ").append(toIndentedString(westVirginia)).append("\n");
    sb.append("    wisconsin: ").append(toIndentedString(wisconsin)).append("\n");
    sb.append("    wyoming: ").append(toIndentedString(wyoming)).append("\n");
    sb.append("    connecticut: ").append(toIndentedString(connecticut)).append("\n");
    sb.append("    texas: ").append(toIndentedString(texas)).append("\n");
    sb.append("    alberta: ").append(toIndentedString(alberta)).append("\n");
    sb.append("    britishColumbia: ").append(toIndentedString(britishColumbia)).append("\n");
    sb.append("    manitoba: ").append(toIndentedString(manitoba)).append("\n");
    sb.append("    newBrunswick: ").append(toIndentedString(newBrunswick)).append("\n");
    sb.append("    newfoundlandAndLabrador: ").append(toIndentedString(newfoundlandAndLabrador)).append("\n");
    sb.append("    northwestTerritories: ").append(toIndentedString(northwestTerritories)).append("\n");
    sb.append("    novaScotia: ").append(toIndentedString(novaScotia)).append("\n");
    sb.append("    nunavut: ").append(toIndentedString(nunavut)).append("\n");
    sb.append("    ontario: ").append(toIndentedString(ontario)).append("\n");
    sb.append("    princeEdwardIsland: ").append(toIndentedString(princeEdwardIsland)).append("\n");
    sb.append("    quebec: ").append(toIndentedString(quebec)).append("\n");
    sb.append("    saskatchewan: ").append(toIndentedString(saskatchewan)).append("\n");
    sb.append("    yukonTerritory: ").append(toIndentedString(yukonTerritory)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

