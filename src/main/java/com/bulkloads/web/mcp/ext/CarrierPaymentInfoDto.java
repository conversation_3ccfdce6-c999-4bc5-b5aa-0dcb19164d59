package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierPaymentInfoDto
 */
@JsonPropertyOrder({CarrierPaymentInfoDto.JSON_PROPERTY_BANK_ROUTING_NUMBER, CarrierPaymentInfoDto.JSON_PROPERTY_BANK_ACCOUNT_NUMBER,
    CarrierPaymentInfoDto.JSON_PROPERTY_BANK_ACCOUNT_NAME, CarrierPaymentInfoDto.JSON_PROPERTY_BANK_NAME, CarrierPaymentInfoDto.JSON_PROPERTY_BANK_ADDRESS,
    CarrierPaymentInfoDto.JSON_PROPERTY_BANK_PHONE, CarrierPaymentInfoDto.JSON_PROPERTY_BANK_FAX, CarrierPaymentInfoDto.JSON_PROPERTY_FACTORING_COMPANY_NAME,
    CarrierPaymentInfoDto.JSON_PROPERTY_REMIT_ADDRESS1, CarrierPaymentInfoDto.JSON_PROPERTY_REMIT_ADDRESS2, CarrierPaymentInfoDto.JSON_PROPERTY_REMIT_CITY,
    CarrierPaymentInfoDto.JSON_PROPERTY_REMIT_ZIP_CODE, CarrierPaymentInfoDto.JSON_PROPERTY_BANK_ACCOUNT_TYPE, CarrierPaymentInfoDto.JSON_PROPERTY_REMIT_STATE,
    CarrierPaymentInfoDto.JSON_PROPERTY_REMIT_COUNTRY, CarrierPaymentInfoDto.JSON_PROPERTY_REMIT_EMAIL, CarrierPaymentInfoDto.JSON_PROPERTY_REQUIRE1099,
    CarrierPaymentInfoDto.JSON_PROPERTY_EPAY_MANAGER_I_D, CarrierPaymentInfoDto.JSON_PROPERTY_REMIT_CURRENCY,
    CarrierPaymentInfoDto.JSON_PROPERTY_PAY_ADVANCE_OPTION_I_D, CarrierPaymentInfoDto.JSON_PROPERTY_PAY_ADVANCE_OPTION_TYPE})

public class CarrierPaymentInfoDto {

  public static final String JSON_PROPERTY_BANK_ROUTING_NUMBER = "BankRoutingNumber";
  public static final String JSON_PROPERTY_BANK_ACCOUNT_NUMBER = "BankAccountNumber";
  public static final String JSON_PROPERTY_BANK_ACCOUNT_NAME = "BankAccountName";
  public static final String JSON_PROPERTY_BANK_NAME = "BankName";
  public static final String JSON_PROPERTY_BANK_ADDRESS = "BankAddress";
  public static final String JSON_PROPERTY_BANK_PHONE = "BankPhone";
  public static final String JSON_PROPERTY_BANK_FAX = "BankFax";
  public static final String JSON_PROPERTY_FACTORING_COMPANY_NAME = "FactoringCompanyName";
  public static final String JSON_PROPERTY_REMIT_ADDRESS1 = "RemitAddress1";
  public static final String JSON_PROPERTY_REMIT_ADDRESS2 = "RemitAddress2";
  public static final String JSON_PROPERTY_REMIT_CITY = "RemitCity";
  public static final String JSON_PROPERTY_REMIT_ZIP_CODE = "RemitZipCode";
  public static final String JSON_PROPERTY_BANK_ACCOUNT_TYPE = "BankAccountType";
  public static final String JSON_PROPERTY_REMIT_STATE = "RemitState";
  public static final String JSON_PROPERTY_REMIT_COUNTRY = "RemitCountry";
  public static final String JSON_PROPERTY_REMIT_EMAIL = "RemitEmail";
  public static final String JSON_PROPERTY_REQUIRE1099 = "Require1099";
  public static final String JSON_PROPERTY_EPAY_MANAGER_I_D = "EpayManagerID";
  public static final String JSON_PROPERTY_REMIT_CURRENCY = "RemitCurrency";
  public static final String JSON_PROPERTY_PAY_ADVANCE_OPTION_I_D = "PayAdvanceOptionID";
  public static final String JSON_PROPERTY_PAY_ADVANCE_OPTION_TYPE = "PayAdvanceOptionType";
  private String bankRoutingNumber;
  private String bankAccountNumber;
  private String bankAccountName;
  private String bankName;
  private String bankAddress;
  private String bankPhone;
  private String bankFax;
  private String factoringCompanyName;
  private String remitAddress1;
  private String remitAddress2;
  private String remitCity;
  private String remitZipCode;
  private String bankAccountType;
  private String remitState;
  private String remitCountry;
  private String remitEmail;
  private Boolean require1099;
  private Integer epayManagerID;
  private String remitCurrency;
  private Integer payAdvanceOptionID;
  private String payAdvanceOptionType;

  public CarrierPaymentInfoDto() {
  }

  public CarrierPaymentInfoDto bankRoutingNumber(String bankRoutingNumber) {

    this.bankRoutingNumber = bankRoutingNumber;
    return this;
  }

  /**
   * Get bankRoutingNumber
   *
   * @return bankRoutingNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_ROUTING_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankRoutingNumber() {
    return bankRoutingNumber;
  }


  @JsonProperty(JSON_PROPERTY_BANK_ROUTING_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankRoutingNumber(String bankRoutingNumber) {
    this.bankRoutingNumber = bankRoutingNumber;
  }


  public CarrierPaymentInfoDto bankAccountNumber(String bankAccountNumber) {

    this.bankAccountNumber = bankAccountNumber;
    return this;
  }

  /**
   * Get bankAccountNumber
   *
   * @return bankAccountNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankAccountNumber() {
    return bankAccountNumber;
  }


  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankAccountNumber(String bankAccountNumber) {
    this.bankAccountNumber = bankAccountNumber;
  }


  public CarrierPaymentInfoDto bankAccountName(String bankAccountName) {

    this.bankAccountName = bankAccountName;
    return this;
  }

  /**
   * Get bankAccountName
   *
   * @return bankAccountName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankAccountName() {
    return bankAccountName;
  }


  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankAccountName(String bankAccountName) {
    this.bankAccountName = bankAccountName;
  }


  public CarrierPaymentInfoDto bankName(String bankName) {

    this.bankName = bankName;
    return this;
  }

  /**
   * Get bankName
   *
   * @return bankName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankName() {
    return bankName;
  }


  @JsonProperty(JSON_PROPERTY_BANK_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankName(String bankName) {
    this.bankName = bankName;
  }


  public CarrierPaymentInfoDto bankAddress(String bankAddress) {

    this.bankAddress = bankAddress;
    return this;
  }

  /**
   * Get bankAddress
   *
   * @return bankAddress
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankAddress() {
    return bankAddress;
  }


  @JsonProperty(JSON_PROPERTY_BANK_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankAddress(String bankAddress) {
    this.bankAddress = bankAddress;
  }


  public CarrierPaymentInfoDto bankPhone(String bankPhone) {

    this.bankPhone = bankPhone;
    return this;
  }

  /**
   * Get bankPhone
   *
   * @return bankPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankPhone() {
    return bankPhone;
  }


  @JsonProperty(JSON_PROPERTY_BANK_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankPhone(String bankPhone) {
    this.bankPhone = bankPhone;
  }


  public CarrierPaymentInfoDto bankFax(String bankFax) {

    this.bankFax = bankFax;
    return this;
  }

  /**
   * Get bankFax
   *
   * @return bankFax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankFax() {
    return bankFax;
  }


  @JsonProperty(JSON_PROPERTY_BANK_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankFax(String bankFax) {
    this.bankFax = bankFax;
  }


  public CarrierPaymentInfoDto factoringCompanyName(String factoringCompanyName) {

    this.factoringCompanyName = factoringCompanyName;
    return this;
  }

  /**
   * Get factoringCompanyName
   *
   * @return factoringCompanyName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FACTORING_COMPANY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFactoringCompanyName() {
    return factoringCompanyName;
  }


  @JsonProperty(JSON_PROPERTY_FACTORING_COMPANY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFactoringCompanyName(String factoringCompanyName) {
    this.factoringCompanyName = factoringCompanyName;
  }


  public CarrierPaymentInfoDto remitAddress1(String remitAddress1) {

    this.remitAddress1 = remitAddress1;
    return this;
  }

  /**
   * Get remitAddress1
   *
   * @return remitAddress1
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMIT_ADDRESS1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemitAddress1() {
    return remitAddress1;
  }


  @JsonProperty(JSON_PROPERTY_REMIT_ADDRESS1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemitAddress1(String remitAddress1) {
    this.remitAddress1 = remitAddress1;
  }


  public CarrierPaymentInfoDto remitAddress2(String remitAddress2) {

    this.remitAddress2 = remitAddress2;
    return this;
  }

  /**
   * Get remitAddress2
   *
   * @return remitAddress2
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMIT_ADDRESS2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemitAddress2() {
    return remitAddress2;
  }


  @JsonProperty(JSON_PROPERTY_REMIT_ADDRESS2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemitAddress2(String remitAddress2) {
    this.remitAddress2 = remitAddress2;
  }


  public CarrierPaymentInfoDto remitCity(String remitCity) {

    this.remitCity = remitCity;
    return this;
  }

  /**
   * Get remitCity
   *
   * @return remitCity
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMIT_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemitCity() {
    return remitCity;
  }


  @JsonProperty(JSON_PROPERTY_REMIT_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemitCity(String remitCity) {
    this.remitCity = remitCity;
  }


  public CarrierPaymentInfoDto remitZipCode(String remitZipCode) {

    this.remitZipCode = remitZipCode;
    return this;
  }

  /**
   * Get remitZipCode
   *
   * @return remitZipCode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMIT_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemitZipCode() {
    return remitZipCode;
  }


  @JsonProperty(JSON_PROPERTY_REMIT_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemitZipCode(String remitZipCode) {
    this.remitZipCode = remitZipCode;
  }


  public CarrierPaymentInfoDto bankAccountType(String bankAccountType) {

    this.bankAccountType = bankAccountType;
    return this;
  }

  /**
   * Get bankAccountType
   *
   * @return bankAccountType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankAccountType() {
    return bankAccountType;
  }


  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankAccountType(String bankAccountType) {
    this.bankAccountType = bankAccountType;
  }


  public CarrierPaymentInfoDto remitState(String remitState) {

    this.remitState = remitState;
    return this;
  }

  /**
   * Get remitState
   *
   * @return remitState
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMIT_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemitState() {
    return remitState;
  }


  @JsonProperty(JSON_PROPERTY_REMIT_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemitState(String remitState) {
    this.remitState = remitState;
  }


  public CarrierPaymentInfoDto remitCountry(String remitCountry) {

    this.remitCountry = remitCountry;
    return this;
  }

  /**
   * Get remitCountry
   *
   * @return remitCountry
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMIT_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemitCountry() {
    return remitCountry;
  }


  @JsonProperty(JSON_PROPERTY_REMIT_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemitCountry(String remitCountry) {
    this.remitCountry = remitCountry;
  }


  public CarrierPaymentInfoDto remitEmail(String remitEmail) {

    this.remitEmail = remitEmail;
    return this;
  }

  /**
   * Get remitEmail
   *
   * @return remitEmail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMIT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemitEmail() {
    return remitEmail;
  }


  @JsonProperty(JSON_PROPERTY_REMIT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemitEmail(String remitEmail) {
    this.remitEmail = remitEmail;
  }


  public CarrierPaymentInfoDto require1099(Boolean require1099) {

    this.require1099 = require1099;
    return this;
  }

  /**
   * Get require1099
   *
   * @return require1099
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REQUIRE1099)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRequire1099() {
    return require1099;
  }


  @JsonProperty(JSON_PROPERTY_REQUIRE1099)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRequire1099(Boolean require1099) {
    this.require1099 = require1099;
  }


  public CarrierPaymentInfoDto epayManagerID(Integer epayManagerID) {

    this.epayManagerID = epayManagerID;
    return this;
  }

  /**
   * Get epayManagerID
   *
   * @return epayManagerID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EPAY_MANAGER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getEpayManagerID() {
    return epayManagerID;
  }


  @JsonProperty(JSON_PROPERTY_EPAY_MANAGER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEpayManagerID(Integer epayManagerID) {
    this.epayManagerID = epayManagerID;
  }


  public CarrierPaymentInfoDto remitCurrency(String remitCurrency) {

    this.remitCurrency = remitCurrency;
    return this;
  }

  /**
   * Get remitCurrency
   *
   * @return remitCurrency
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMIT_CURRENCY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemitCurrency() {
    return remitCurrency;
  }


  @JsonProperty(JSON_PROPERTY_REMIT_CURRENCY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemitCurrency(String remitCurrency) {
    this.remitCurrency = remitCurrency;
  }


  public CarrierPaymentInfoDto payAdvanceOptionID(Integer payAdvanceOptionID) {

    this.payAdvanceOptionID = payAdvanceOptionID;
    return this;
  }

  /**
   * Get payAdvanceOptionID
   *
   * @return payAdvanceOptionID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAY_ADVANCE_OPTION_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPayAdvanceOptionID() {
    return payAdvanceOptionID;
  }


  @JsonProperty(JSON_PROPERTY_PAY_ADVANCE_OPTION_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPayAdvanceOptionID(Integer payAdvanceOptionID) {
    this.payAdvanceOptionID = payAdvanceOptionID;
  }


  public CarrierPaymentInfoDto payAdvanceOptionType(String payAdvanceOptionType) {

    this.payAdvanceOptionType = payAdvanceOptionType;
    return this;
  }

  /**
   * Get payAdvanceOptionType
   *
   * @return payAdvanceOptionType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAY_ADVANCE_OPTION_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPayAdvanceOptionType() {
    return payAdvanceOptionType;
  }


  @JsonProperty(JSON_PROPERTY_PAY_ADVANCE_OPTION_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPayAdvanceOptionType(String payAdvanceOptionType) {
    this.payAdvanceOptionType = payAdvanceOptionType;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierPaymentInfoDto carrierPaymentInfoDto = (CarrierPaymentInfoDto) o;
    return Objects.equals(this.bankRoutingNumber, carrierPaymentInfoDto.bankRoutingNumber) && Objects.equals(this.bankAccountNumber,
        carrierPaymentInfoDto.bankAccountNumber) && Objects.equals(this.bankAccountName, carrierPaymentInfoDto.bankAccountName) && Objects.equals(this.bankName,
        carrierPaymentInfoDto.bankName) && Objects.equals(this.bankAddress, carrierPaymentInfoDto.bankAddress) && Objects.equals(this.bankPhone,
        carrierPaymentInfoDto.bankPhone) && Objects.equals(this.bankFax, carrierPaymentInfoDto.bankFax) && Objects.equals(this.factoringCompanyName,
        carrierPaymentInfoDto.factoringCompanyName) && Objects.equals(this.remitAddress1, carrierPaymentInfoDto.remitAddress1) && Objects.equals(
        this.remitAddress2, carrierPaymentInfoDto.remitAddress2) && Objects.equals(this.remitCity, carrierPaymentInfoDto.remitCity) && Objects.equals(
        this.remitZipCode, carrierPaymentInfoDto.remitZipCode) && Objects.equals(this.bankAccountType, carrierPaymentInfoDto.bankAccountType) && Objects.equals(
        this.remitState, carrierPaymentInfoDto.remitState) && Objects.equals(this.remitCountry, carrierPaymentInfoDto.remitCountry) && Objects.equals(
        this.remitEmail, carrierPaymentInfoDto.remitEmail) && Objects.equals(this.require1099, carrierPaymentInfoDto.require1099) && Objects.equals(
        this.epayManagerID, carrierPaymentInfoDto.epayManagerID) && Objects.equals(this.remitCurrency, carrierPaymentInfoDto.remitCurrency) && Objects.equals(
        this.payAdvanceOptionID, carrierPaymentInfoDto.payAdvanceOptionID) && Objects.equals(this.payAdvanceOptionType,
        carrierPaymentInfoDto.payAdvanceOptionType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(bankRoutingNumber, bankAccountNumber, bankAccountName, bankName, bankAddress, bankPhone, bankFax, factoringCompanyName, remitAddress1,
        remitAddress2, remitCity, remitZipCode, bankAccountType, remitState, remitCountry, remitEmail, require1099, epayManagerID, remitCurrency,
        payAdvanceOptionID, payAdvanceOptionType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierPaymentInfoDto {\n");
    sb.append("    bankRoutingNumber: ").append(toIndentedString(bankRoutingNumber)).append("\n");
    sb.append("    bankAccountNumber: ").append(toIndentedString(bankAccountNumber)).append("\n");
    sb.append("    bankAccountName: ").append(toIndentedString(bankAccountName)).append("\n");
    sb.append("    bankName: ").append(toIndentedString(bankName)).append("\n");
    sb.append("    bankAddress: ").append(toIndentedString(bankAddress)).append("\n");
    sb.append("    bankPhone: ").append(toIndentedString(bankPhone)).append("\n");
    sb.append("    bankFax: ").append(toIndentedString(bankFax)).append("\n");
    sb.append("    factoringCompanyName: ").append(toIndentedString(factoringCompanyName)).append("\n");
    sb.append("    remitAddress1: ").append(toIndentedString(remitAddress1)).append("\n");
    sb.append("    remitAddress2: ").append(toIndentedString(remitAddress2)).append("\n");
    sb.append("    remitCity: ").append(toIndentedString(remitCity)).append("\n");
    sb.append("    remitZipCode: ").append(toIndentedString(remitZipCode)).append("\n");
    sb.append("    bankAccountType: ").append(toIndentedString(bankAccountType)).append("\n");
    sb.append("    remitState: ").append(toIndentedString(remitState)).append("\n");
    sb.append("    remitCountry: ").append(toIndentedString(remitCountry)).append("\n");
    sb.append("    remitEmail: ").append(toIndentedString(remitEmail)).append("\n");
    sb.append("    require1099: ").append(toIndentedString(require1099)).append("\n");
    sb.append("    epayManagerID: ").append(toIndentedString(epayManagerID)).append("\n");
    sb.append("    remitCurrency: ").append(toIndentedString(remitCurrency)).append("\n");
    sb.append("    payAdvanceOptionID: ").append(toIndentedString(payAdvanceOptionID)).append("\n");
    sb.append("    payAdvanceOptionType: ").append(toIndentedString(payAdvanceOptionType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

