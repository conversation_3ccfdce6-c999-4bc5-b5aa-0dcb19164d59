package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSADrivers
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSADrivers.JSON_PROPERTY_DRIVERS_TOTAL, MyCarrierPacketsApiFMCSADrivers.JSON_PROPERTY_DRIVERS_AVG_LEASED,
    MyCarrierPacketsApiFMCSADrivers.JSON_PROPERTY_DRIVERS_C_D_L, MyCarrierPacketsApiFMCSADrivers.JSON_PROPERTY_DRIVERS_INTER,
    MyCarrierPacketsApiFMCSADrivers.JSON_PROPERTY_DRIVERS_INTER_L_T100, MyCarrierPacketsApiFMCSADrivers.JSON_PROPERTY_DRIVERS_INTER_G_T100,
    MyCarrierPacketsApiFMCSADrivers.JSO<PERSON>_PROPERTY_DRIVERS_INTRA, MyCarrierPacketsApiFMCSADrivers.JSON_PROPERTY_DRIVERS_INTRA_L_T100,
    MyCarrierPacketsApiFMCSADrivers.JSON_PROPERTY_DRIVERS_INTRA_G_T100})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Drivers")

public class MyCarrierPacketsApiFMCSADrivers {

  public static final String JSON_PROPERTY_DRIVERS_TOTAL = "driversTotal";
  public static final String JSON_PROPERTY_DRIVERS_AVG_LEASED = "driversAvgLeased";
  public static final String JSON_PROPERTY_DRIVERS_C_D_L = "driversCDL";
  public static final String JSON_PROPERTY_DRIVERS_INTER = "driversInter";
  public static final String JSON_PROPERTY_DRIVERS_INTER_L_T100 = "driversInterLT100";
  public static final String JSON_PROPERTY_DRIVERS_INTER_G_T100 = "driversInterGT100";
  public static final String JSON_PROPERTY_DRIVERS_INTRA = "driversIntra";
  public static final String JSON_PROPERTY_DRIVERS_INTRA_L_T100 = "driversIntraLT100";
  public static final String JSON_PROPERTY_DRIVERS_INTRA_G_T100 = "driversIntraGT100";
  private String driversTotal;
  private String driversAvgLeased;
  private String driversCDL;
  private String driversInter;
  private String driversInterLT100;
  private String driversInterGT100;
  private String driversIntra;
  private String driversIntraLT100;
  private String driversIntraGT100;

  public MyCarrierPacketsApiFMCSADrivers() {
  }

  public MyCarrierPacketsApiFMCSADrivers driversTotal(String driversTotal) {

    this.driversTotal = driversTotal;
    return this;
  }

  /**
   * Get driversTotal
   *
   * @return driversTotal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversTotal() {
    return driversTotal;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversTotal(String driversTotal) {
    this.driversTotal = driversTotal;
  }


  public MyCarrierPacketsApiFMCSADrivers driversAvgLeased(String driversAvgLeased) {

    this.driversAvgLeased = driversAvgLeased;
    return this;
  }

  /**
   * Get driversAvgLeased
   *
   * @return driversAvgLeased
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_AVG_LEASED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversAvgLeased() {
    return driversAvgLeased;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_AVG_LEASED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversAvgLeased(String driversAvgLeased) {
    this.driversAvgLeased = driversAvgLeased;
  }


  public MyCarrierPacketsApiFMCSADrivers driversCDL(String driversCDL) {

    this.driversCDL = driversCDL;
    return this;
  }

  /**
   * Get driversCDL
   *
   * @return driversCDL
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_C_D_L)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversCDL() {
    return driversCDL;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_C_D_L)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversCDL(String driversCDL) {
    this.driversCDL = driversCDL;
  }


  public MyCarrierPacketsApiFMCSADrivers driversInter(String driversInter) {

    this.driversInter = driversInter;
    return this;
  }

  /**
   * Get driversInter
   *
   * @return driversInter
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_INTER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversInter() {
    return driversInter;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_INTER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversInter(String driversInter) {
    this.driversInter = driversInter;
  }


  public MyCarrierPacketsApiFMCSADrivers driversInterLT100(String driversInterLT100) {

    this.driversInterLT100 = driversInterLT100;
    return this;
  }

  /**
   * Get driversInterLT100
   *
   * @return driversInterLT100
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_INTER_L_T100)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversInterLT100() {
    return driversInterLT100;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_INTER_L_T100)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversInterLT100(String driversInterLT100) {
    this.driversInterLT100 = driversInterLT100;
  }


  public MyCarrierPacketsApiFMCSADrivers driversInterGT100(String driversInterGT100) {

    this.driversInterGT100 = driversInterGT100;
    return this;
  }

  /**
   * Get driversInterGT100
   *
   * @return driversInterGT100
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_INTER_G_T100)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversInterGT100() {
    return driversInterGT100;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_INTER_G_T100)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversInterGT100(String driversInterGT100) {
    this.driversInterGT100 = driversInterGT100;
  }


  public MyCarrierPacketsApiFMCSADrivers driversIntra(String driversIntra) {

    this.driversIntra = driversIntra;
    return this;
  }

  /**
   * Get driversIntra
   *
   * @return driversIntra
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_INTRA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversIntra() {
    return driversIntra;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_INTRA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversIntra(String driversIntra) {
    this.driversIntra = driversIntra;
  }


  public MyCarrierPacketsApiFMCSADrivers driversIntraLT100(String driversIntraLT100) {

    this.driversIntraLT100 = driversIntraLT100;
    return this;
  }

  /**
   * Get driversIntraLT100
   *
   * @return driversIntraLT100
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_INTRA_L_T100)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversIntraLT100() {
    return driversIntraLT100;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_INTRA_L_T100)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversIntraLT100(String driversIntraLT100) {
    this.driversIntraLT100 = driversIntraLT100;
  }


  public MyCarrierPacketsApiFMCSADrivers driversIntraGT100(String driversIntraGT100) {

    this.driversIntraGT100 = driversIntraGT100;
    return this;
  }

  /**
   * Get driversIntraGT100
   *
   * @return driversIntraGT100
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_INTRA_G_T100)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversIntraGT100() {
    return driversIntraGT100;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_INTRA_G_T100)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversIntraGT100(String driversIntraGT100) {
    this.driversIntraGT100 = driversIntraGT100;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSADrivers myCarrierPacketsApiFMCSADrivers = (MyCarrierPacketsApiFMCSADrivers) o;
    return Objects.equals(this.driversTotal, myCarrierPacketsApiFMCSADrivers.driversTotal) && Objects.equals(this.driversAvgLeased,
        myCarrierPacketsApiFMCSADrivers.driversAvgLeased) && Objects.equals(this.driversCDL, myCarrierPacketsApiFMCSADrivers.driversCDL) && Objects.equals(
        this.driversInter, myCarrierPacketsApiFMCSADrivers.driversInter) && Objects.equals(this.driversInterLT100,
        myCarrierPacketsApiFMCSADrivers.driversInterLT100) && Objects.equals(this.driversInterGT100, myCarrierPacketsApiFMCSADrivers.driversInterGT100)
           && Objects.equals(this.driversIntra, myCarrierPacketsApiFMCSADrivers.driversIntra) && Objects.equals(this.driversIntraLT100,
        myCarrierPacketsApiFMCSADrivers.driversIntraLT100) && Objects.equals(this.driversIntraGT100, myCarrierPacketsApiFMCSADrivers.driversIntraGT100);
  }

  @Override
  public int hashCode() {
    return Objects.hash(driversTotal, driversAvgLeased, driversCDL, driversInter, driversInterLT100, driversInterGT100, driversIntra, driversIntraLT100,
        driversIntraGT100);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSADrivers {\n");
    sb.append("    driversTotal: ").append(toIndentedString(driversTotal)).append("\n");
    sb.append("    driversAvgLeased: ").append(toIndentedString(driversAvgLeased)).append("\n");
    sb.append("    driversCDL: ").append(toIndentedString(driversCDL)).append("\n");
    sb.append("    driversInter: ").append(toIndentedString(driversInter)).append("\n");
    sb.append("    driversInterLT100: ").append(toIndentedString(driversInterLT100)).append("\n");
    sb.append("    driversInterGT100: ").append(toIndentedString(driversInterGT100)).append("\n");
    sb.append("    driversIntra: ").append(toIndentedString(driversIntra)).append("\n");
    sb.append("    driversIntraLT100: ").append(toIndentedString(driversIntraLT100)).append("\n");
    sb.append("    driversIntraGT100: ").append(toIndentedString(driversIntraGT100)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

