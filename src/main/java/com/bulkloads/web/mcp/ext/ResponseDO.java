package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * ResponseDO
 */
@JsonPropertyOrder({ResponseDO.JSON_PROPERTY_STATUS, ResponseDO.JSON_PROPERTY_ACTION, ResponseDO.JSON_PROPERTY_CODE, ResponseDO.JSON_PROPERTY_DISPLAY_MSG,
    ResponseDO.JSON_PROPERTY_TECH_MSG})

public class ResponseDO {

  public static final String JSON_PROPERTY_STATUS = "status";
  public static final String JSON_PROPERTY_ACTION = "action";
  public static final String JSON_PROPERTY_CODE = "code";
  public static final String JSON_PROPERTY_DISPLAY_MSG = "displayMsg";
  public static final String JSON_PROPERTY_TECH_MSG = "techMsg";
  private String status;
  private String action;
  private String code;
  private String displayMsg;
  private String techMsg;

  public ResponseDO() {
  }

  public ResponseDO status(String status) {

    this.status = status;
    return this;
  }

  /**
   * Get status
   *
   * @return status
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(String status) {
    this.status = status;
  }


  public ResponseDO action(String action) {

    this.action = action;
    return this;
  }

  /**
   * Get action
   *
   * @return action
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAction() {
    return action;
  }


  @JsonProperty(JSON_PROPERTY_ACTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAction(String action) {
    this.action = action;
  }


  public ResponseDO code(String code) {

    this.code = code;
    return this;
  }

  /**
   * Get code
   *
   * @return code
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCode() {
    return code;
  }


  @JsonProperty(JSON_PROPERTY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCode(String code) {
    this.code = code;
  }


  public ResponseDO displayMsg(String displayMsg) {

    this.displayMsg = displayMsg;
    return this;
  }

  /**
   * Get displayMsg
   *
   * @return displayMsg
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DISPLAY_MSG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDisplayMsg() {
    return displayMsg;
  }


  @JsonProperty(JSON_PROPERTY_DISPLAY_MSG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDisplayMsg(String displayMsg) {
    this.displayMsg = displayMsg;
  }


  public ResponseDO techMsg(String techMsg) {

    this.techMsg = techMsg;
    return this;
  }

  /**
   * Get techMsg
   *
   * @return techMsg
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TECH_MSG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTechMsg() {
    return techMsg;
  }


  @JsonProperty(JSON_PROPERTY_TECH_MSG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTechMsg(String techMsg) {
    this.techMsg = techMsg;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResponseDO responseDO = (ResponseDO) o;
    return Objects.equals(this.status, responseDO.status) && Objects.equals(this.action, responseDO.action) && Objects.equals(this.code, responseDO.code)
           && Objects.equals(this.displayMsg, responseDO.displayMsg) && Objects.equals(this.techMsg, responseDO.techMsg);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, action, code, displayMsg, techMsg);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResponseDO {\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    action: ").append(toIndentedString(action)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    displayMsg: ").append(toIndentedString(displayMsg)).append("\n");
    sb.append("    techMsg: ").append(toIndentedString(techMsg)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

