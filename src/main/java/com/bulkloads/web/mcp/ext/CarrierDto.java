package com.bulkloads.web.mcp.ext;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierDto
 */
@JsonPropertyOrder({CarrierDto.JSON_PROPERTY_DO_T_NUMBER, CarrierDto.JSON_PROPERTY_LEGAL_NAME, CarrierDto.JSON_PROPERTY_DB_A_NAME,
    CarrierDto.JSON_PROPERTY_ADDRESS1, CarrierDto.JSON_PROPERTY_ADDRESS2, CarrierDto.JSON_PROPERTY_CITY, CarrierDto.JSON_PROPERTY_ZIPCODE,
    CarrierDto.JSON_PROPERTY_STATE, CarrierDto.JSON_PROPERTY_COUNTRY, CarrierDto.JSON_PROPERTY_CELL_PHONE, CarrierDto.JSON_PROPERTY_PHONE,
    CarrierDto.JSON_PROPERTY_FAX, CarrierDto.JSON_PROPERTY_FREE_PHONE, CarrierDto.JSON_PROPERTY_EMERGENCY_PHONE, CarrierDto.JSON_PROPERTY_EMAIL,
    CarrierDto.JSON_PROPERTY_FRAUD_IDENTITY_THEFT_STATUS, CarrierDto.JSON_PROPERTY_MC_NUMBER, CarrierDto.JSON_PROPERTY_S_C_A_C,
    CarrierDto.JSON_PROPERTY_MAILING_ADDRESS1, CarrierDto.JSON_PROPERTY_MAILING_ADDRESS2, CarrierDto.JSON_PROPERTY_MAILING_CITY,
    CarrierDto.JSON_PROPERTY_MAILING_STATE, CarrierDto.JSON_PROPERTY_MAILING_ZIPCODE, CarrierDto.JSON_PROPERTY_MAILING_COUNTRY,
    CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_NAME, CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_PHONE,
    CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_FAX, CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_FROM,
    CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_TO, CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_NAME,
    CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_PHONE, CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_FAX,
    CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_FROM, CarrierDto.JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_TO, CarrierDto.JSON_PROPERTY_WEBSITE,
    CarrierDto.JSON_PROPERTY_OPERATION_MANAGER_NAME, CarrierDto.JSON_PROPERTY_ONLINE_ACCESS_TO_AVAILABLE_LOADS, CarrierDto.JSON_PROPERTY_AVAILABLE_LOADS_EMAIL,
    CarrierDto.JSON_PROPERTY_DRIVER_LOGS_SAFEY_DEPT_MANAGER_NAME, CarrierDto.JSON_PROPERTY_DRIVER_LOGS_SAFEY_DEPT_MANAGER_PHONE,
    CarrierDto.JSON_PROPERTY_DISPATCHERS, CarrierDto.JSON_PROPERTY_CLAIMS_CONTACT_NAME, CarrierDto.JSON_PROPERTY_CLAIMS_CONTACT_PHONE,
    CarrierDto.JSON_PROPERTY_CLAIMS_CONTACT_EMAIL, CarrierDto.JSON_PROPERTY_DISPATCH_SERVICE_USED, CarrierDto.JSON_PROPERTY_DISPATCH_SERVICE_NAME,
    CarrierDto.JSON_PROPERTY_DISPATCH_SERVICE_PHONE, CarrierDto.JSON_PROPERTY_BROKER_OUT_EXTRA_FREIGHT, CarrierDto.JSON_PROPERTY_REFERENCES1,
    CarrierDto.JSON_PROPERTY_REFERENCES2, CarrierDto.JSON_PROPERTY_REFERENCES3, CarrierDto.JSON_PROPERTY_DRIVERS_TRACKED_BY,
    CarrierDto.JSON_PROPERTY_ACCESS_ONLINE_G_P_S_TRACKING, CarrierDto.JSON_PROPERTY_DRIVERS_TRACKED_BY_OTHER_METHOD, CarrierDto.JSON_PROPERTY_CREATED_DATE_TIME,
    CarrierDto.JSON_PROPERTY_MODIFIED_DATE_TIME, CarrierDto.JSON_PROPERTY_CARRIER_CUSTOMER_AGREEMENTS,
    CarrierDto.JSON_PROPERTY_CARRIER_CUSTOMER_PACKET_STATUSES, CarrierDto.JSON_PROPERTY_CARRIER_CARGO_HAULED,
    CarrierDto.JSON_PROPERTY_CARRIER_COMPANY_CLASSIFICATION, CarrierDto.JSON_PROPERTY_CARRIER_DRIVERS, CarrierDto.JSON_PROPERTY_CARRIER_DISPATCHERS,
    CarrierDto.JSON_PROPERTY_CARRIER_LANE, CarrierDto.JSON_PROPERTY_CARRIER_OPERATIONAL_DETAIL, CarrierDto.JSON_PROPERTY_CARRIER_PAYMENT_INFO,
    CarrierDto.JSON_PROPERTY_CARRIER_REMIT, CarrierDto.JSON_PROPERTY_FACTORING_REMIT, CarrierDto.JSON_PROPERTY_CARRIER_BANK,
    CarrierDto.JSON_PROPERTY_CARRIER_PAYMENT_TERMS, CarrierDto.JSON_PROPERTY_CARRIER_PAYMENT_TYPES, CarrierDto.JSON_PROPERTY_CARRIER_PAYER_TYPE,
    CarrierDto.JSON_PROPERTY_CARRIER_TRUCK_CLASS, CarrierDto.JSON_PROPERTY_CARRIER_TRUCK_TYPE, CarrierDto.JSON_PROPERTY_CARRIER_W9_FORMS,
    CarrierDto.JSON_PROPERTY_CARRIER_CERTIFICATION, CarrierDto.JSON_PROPERTY_ASSURE_ADVANTAGE, CarrierDto.JSON_PROPERTY_CARRIER_MODE,
    CarrierDto.JSON_PROPERTY_CARRIER_E_L_D_PROVIDER, CarrierDto.JSON_PROPERTY_OWNER_CONTACT_NAME, CarrierDto.JSON_PROPERTY_OWNER_CONTACT_PHONE,
    CarrierDto.JSON_PROPERTY_OWNER_CONTACT_EMAIL, CarrierDto.JSON_PROPERTY_CARRIER_T_I_N_MATCHINGS, CarrierDto.JSON_PROPERTY_MESSAGE})

public class CarrierDto {

  public static final String JSON_PROPERTY_DO_T_NUMBER = "DOTNumber";
  public static final String JSON_PROPERTY_LEGAL_NAME = "LegalName";
  public static final String JSON_PROPERTY_DB_A_NAME = "DBAName";
  public static final String JSON_PROPERTY_ADDRESS1 = "Address1";
  public static final String JSON_PROPERTY_ADDRESS2 = "Address2";
  public static final String JSON_PROPERTY_CITY = "City";
  public static final String JSON_PROPERTY_ZIPCODE = "Zipcode";
  public static final String JSON_PROPERTY_STATE = "State";
  public static final String JSON_PROPERTY_COUNTRY = "Country";
  public static final String JSON_PROPERTY_CELL_PHONE = "CellPhone";
  public static final String JSON_PROPERTY_PHONE = "Phone";
  public static final String JSON_PROPERTY_FAX = "Fax";
  public static final String JSON_PROPERTY_FREE_PHONE = "FreePhone";
  public static final String JSON_PROPERTY_EMERGENCY_PHONE = "EmergencyPhone";
  public static final String JSON_PROPERTY_EMAIL = "Email";
  public static final String JSON_PROPERTY_FRAUD_IDENTITY_THEFT_STATUS = "FraudIdentityTheftStatus";
  public static final String JSON_PROPERTY_MC_NUMBER = "MCNumber";
  public static final String JSON_PROPERTY_S_C_A_C = "SCAC";
  public static final String JSON_PROPERTY_MAILING_ADDRESS1 = "MailingAddress1";
  public static final String JSON_PROPERTY_MAILING_ADDRESS2 = "MailingAddress2";
  public static final String JSON_PROPERTY_MAILING_CITY = "MailingCity";
  public static final String JSON_PROPERTY_MAILING_STATE = "MailingState";
  public static final String JSON_PROPERTY_MAILING_ZIPCODE = "MailingZipcode";
  public static final String JSON_PROPERTY_MAILING_COUNTRY = "MailingCountry";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_NAME = "AfterHrsWkDaySupportName";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_PHONE = "AfterHrsWkDaySupportPhone";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_FAX = "AfterHrsWkDaySupportFax";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_FROM = "AfterHrsWkDaySupportFrom";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_TO = "AfterHrsWkDaySupportTo";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_NAME = "AfterHrsWkEndSupportName";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_PHONE = "AfterHrsWkEndSupportPhone";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_FAX = "AfterHrsWkEndSupportFax";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_FROM = "AfterHrsWkEndSupportFrom";
  public static final String JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_TO = "AfterHrsWkEndSupportTo";
  public static final String JSON_PROPERTY_WEBSITE = "Website";
  public static final String JSON_PROPERTY_OPERATION_MANAGER_NAME = "OperationManagerName";
  public static final String JSON_PROPERTY_ONLINE_ACCESS_TO_AVAILABLE_LOADS = "OnlineAccessToAvailableLoads";
  public static final String JSON_PROPERTY_AVAILABLE_LOADS_EMAIL = "AvailableLoadsEmail";
  public static final String JSON_PROPERTY_DRIVER_LOGS_SAFEY_DEPT_MANAGER_NAME = "DriverLogsSafeyDeptManagerName";
  public static final String JSON_PROPERTY_DRIVER_LOGS_SAFEY_DEPT_MANAGER_PHONE = "DriverLogsSafeyDeptManagerPhone";
  public static final String JSON_PROPERTY_DISPATCHERS = "Dispatchers";
  public static final String JSON_PROPERTY_CLAIMS_CONTACT_NAME = "ClaimsContactName";
  public static final String JSON_PROPERTY_CLAIMS_CONTACT_PHONE = "ClaimsContactPhone";
  public static final String JSON_PROPERTY_CLAIMS_CONTACT_EMAIL = "ClaimsContactEmail";
  public static final String JSON_PROPERTY_DISPATCH_SERVICE_USED = "DispatchServiceUsed";
  public static final String JSON_PROPERTY_DISPATCH_SERVICE_NAME = "DispatchServiceName";
  public static final String JSON_PROPERTY_DISPATCH_SERVICE_PHONE = "DispatchServicePhone";
  public static final String JSON_PROPERTY_BROKER_OUT_EXTRA_FREIGHT = "BrokerOutExtraFreight";
  public static final String JSON_PROPERTY_REFERENCES1 = "References1";
  public static final String JSON_PROPERTY_REFERENCES2 = "References2";
  public static final String JSON_PROPERTY_REFERENCES3 = "References3";
  public static final String JSON_PROPERTY_DRIVERS_TRACKED_BY = "DriversTrackedBy";
  public static final String JSON_PROPERTY_ACCESS_ONLINE_G_P_S_TRACKING = "AccessOnlineGPSTracking";
  public static final String JSON_PROPERTY_DRIVERS_TRACKED_BY_OTHER_METHOD = "DriversTrackedByOtherMethod";
  public static final String JSON_PROPERTY_CREATED_DATE_TIME = "CreatedDateTime";
  public static final String JSON_PROPERTY_MODIFIED_DATE_TIME = "ModifiedDateTime";
  public static final String JSON_PROPERTY_CARRIER_CUSTOMER_AGREEMENTS = "CarrierCustomerAgreements";
  public static final String JSON_PROPERTY_CARRIER_CUSTOMER_PACKET_STATUSES = "CarrierCustomerPacketStatuses";
  public static final String JSON_PROPERTY_CARRIER_CARGO_HAULED = "CarrierCargoHauled";
  public static final String JSON_PROPERTY_CARRIER_COMPANY_CLASSIFICATION = "CarrierCompanyClassification";
  public static final String JSON_PROPERTY_CARRIER_DRIVERS = "CarrierDrivers";
  public static final String JSON_PROPERTY_CARRIER_DISPATCHERS = "CarrierDispatchers";
  public static final String JSON_PROPERTY_CARRIER_LANE = "CarrierLane";
  public static final String JSON_PROPERTY_CARRIER_OPERATIONAL_DETAIL = "CarrierOperationalDetail";
  public static final String JSON_PROPERTY_CARRIER_PAYMENT_INFO = "CarrierPaymentInfo";
  public static final String JSON_PROPERTY_CARRIER_REMIT = "CarrierRemit";
  public static final String JSON_PROPERTY_FACTORING_REMIT = "FactoringRemit";
  public static final String JSON_PROPERTY_CARRIER_BANK = "CarrierBank";
  public static final String JSON_PROPERTY_CARRIER_PAYMENT_TERMS = "CarrierPaymentTerms";
  public static final String JSON_PROPERTY_CARRIER_PAYMENT_TYPES = "CarrierPaymentTypes";
  public static final String JSON_PROPERTY_CARRIER_PAYER_TYPE = "CarrierPayerType";
  public static final String JSON_PROPERTY_CARRIER_TRUCK_CLASS = "CarrierTruckClass";
  public static final String JSON_PROPERTY_CARRIER_TRUCK_TYPE = "CarrierTruckType";
  public static final String JSON_PROPERTY_CARRIER_W9_FORMS = "CarrierW9Forms";
  public static final String JSON_PROPERTY_CARRIER_CERTIFICATION = "CarrierCertification";
  public static final String JSON_PROPERTY_ASSURE_ADVANTAGE = "AssureAdvantage";
  public static final String JSON_PROPERTY_CARRIER_MODE = "CarrierMode";
  public static final String JSON_PROPERTY_CARRIER_E_L_D_PROVIDER = "CarrierELDProvider";
  public static final String JSON_PROPERTY_OWNER_CONTACT_NAME = "OwnerContactName";
  public static final String JSON_PROPERTY_OWNER_CONTACT_PHONE = "OwnerContactPhone";
  public static final String JSON_PROPERTY_OWNER_CONTACT_EMAIL = "OwnerContactEmail";
  public static final String JSON_PROPERTY_CARRIER_T_I_N_MATCHINGS = "CarrierTINMatchings";
  public static final String JSON_PROPERTY_MESSAGE = "Message";
  private Integer doTNumber;
  private String legalName;
  private String dbAName;
  private String address1;
  private String address2;
  private String city;
  private String zipcode;
  private String state;
  private String country;
  private String cellPhone;
  private String phone;
  private String fax;
  private String freePhone;
  private String emergencyPhone;
  private String email;
  private String fraudIdentityTheftStatus;
  private String mcNumber;
  private String SCAC;
  private String mailingAddress1;
  private String mailingAddress2;
  private String mailingCity;
  private String mailingState;
  private String mailingZipcode;
  private String mailingCountry;
  private String afterHrsWkDaySupportName;
  private String afterHrsWkDaySupportPhone;
  private String afterHrsWkDaySupportFax;
  private String afterHrsWkDaySupportFrom;
  private String afterHrsWkDaySupportTo;
  private String afterHrsWkEndSupportName;
  private String afterHrsWkEndSupportPhone;
  private String afterHrsWkEndSupportFax;
  private String afterHrsWkEndSupportFrom;
  private String afterHrsWkEndSupportTo;
  private String website;
  private String operationManagerName;
  private Boolean onlineAccessToAvailableLoads;
  private String availableLoadsEmail;
  private String driverLogsSafeyDeptManagerName;
  private String driverLogsSafeyDeptManagerPhone;
  private String dispatchers;
  private String claimsContactName;
  private String claimsContactPhone;
  private String claimsContactEmail;
  private Boolean dispatchServiceUsed;
  private String dispatchServiceName;
  private String dispatchServicePhone;
  private Boolean brokerOutExtraFreight;
  private String references1;
  private String references2;
  private String references3;
  private String driversTrackedBy;
  private Boolean accessOnlineGPSTracking;
  private String driversTrackedByOtherMethod;
  private LocalDateTime createdDateTime;
  private LocalDateTime modifiedDateTime;
  private List<CarrierCustomerAgreementDto> carrierCustomerAgreements;
  private List<CarrierCustomerPacketStatusDto> carrierCustomerPacketStatuses;
  private CarrierCargoHauledDto carrierCargoHauled;
  private CarrierCompanyClassificationDto carrierCompanyClassification;
  private List<CarrierDriverDto> carrierDrivers;
  private List<CarrierDispatcherDto> carrierDispatchers;
  private CarrierLaneDto carrierLane;
  private CarrierOperationalDetailDto carrierOperationalDetail;
  private CarrierPaymentInfoDto carrierPaymentInfo;
  private CarrierRemitDto carrierRemit;
  private FactoringRemitDto factoringRemit;
  private CarrierBankDto carrierBank;
  private List<CarrierPaymentTermDto> carrierPaymentTerms;
  private List<CarrierPaymentTypeDto> carrierPaymentTypes;
  private PayerTypeDto carrierPayerType;
  private CarrierTruckClassDto carrierTruckClass;
  private CarrierTruckTypeDto carrierTruckType;
  private List<CarrierW9FormDto> carrierW9Forms;
  private CarrierCertificationDto carrierCertification;
  private List<MyCarrierPacketsApiFMCSAFMCSACarrier> assureAdvantage;
  private CarrierModeDto carrierMode;
  private CarrierELDProviderDto carrierELDProvider;
  private String ownerContactName;
  private String ownerContactPhone;
  private String ownerContactEmail;
  private List<CarrierTINMatchingDto> carrierTINMatchings;
  private String message;

  public CarrierDto() {
  }

  public CarrierDto doTNumber(Integer doTNumber) {

    this.doTNumber = doTNumber;
    return this;
  }

  /**
   * Get doTNumber
   *
   * @return doTNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getDoTNumber() {
    return doTNumber;
  }


  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDoTNumber(Integer doTNumber) {
    this.doTNumber = doTNumber;
  }


  public CarrierDto legalName(String legalName) {

    this.legalName = legalName;
    return this;
  }

  /**
   * Get legalName
   *
   * @return legalName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LEGAL_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLegalName() {
    return legalName;
  }


  @JsonProperty(JSON_PROPERTY_LEGAL_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLegalName(String legalName) {
    this.legalName = legalName;
  }


  public CarrierDto dbAName(String dbAName) {

    this.dbAName = dbAName;
    return this;
  }

  /**
   * Get dbAName
   *
   * @return dbAName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DB_A_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDbAName() {
    return dbAName;
  }


  @JsonProperty(JSON_PROPERTY_DB_A_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDbAName(String dbAName) {
    this.dbAName = dbAName;
  }


  public CarrierDto address1(String address1) {

    this.address1 = address1;
    return this;
  }

  /**
   * Get address1
   *
   * @return address1
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAddress1() {
    return address1;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress1(String address1) {
    this.address1 = address1;
  }


  public CarrierDto address2(String address2) {

    this.address2 = address2;
    return this;
  }

  /**
   * Get address2
   *
   * @return address2
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAddress2() {
    return address2;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress2(String address2) {
    this.address2 = address2;
  }


  public CarrierDto city(String city) {

    this.city = city;
    return this;
  }

  /**
   * Get city
   *
   * @return city
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCity() {
    return city;
  }


  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCity(String city) {
    this.city = city;
  }


  public CarrierDto zipcode(String zipcode) {

    this.zipcode = zipcode;
    return this;
  }

  /**
   * Get zipcode
   *
   * @return zipcode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ZIPCODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getZipcode() {
    return zipcode;
  }


  @JsonProperty(JSON_PROPERTY_ZIPCODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setZipcode(String zipcode) {
    this.zipcode = zipcode;
  }


  public CarrierDto state(String state) {

    this.state = state;
    return this;
  }

  /**
   * Get state
   *
   * @return state
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getState() {
    return state;
  }


  @JsonProperty(JSON_PROPERTY_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setState(String state) {
    this.state = state;
  }


  public CarrierDto country(String country) {

    this.country = country;
    return this;
  }

  /**
   * Get country
   *
   * @return country
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCountry() {
    return country;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountry(String country) {
    this.country = country;
  }


  public CarrierDto cellPhone(String cellPhone) {

    this.cellPhone = cellPhone;
    return this;
  }

  /**
   * Get cellPhone
   *
   * @return cellPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CELL_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCellPhone() {
    return cellPhone;
  }


  @JsonProperty(JSON_PROPERTY_CELL_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCellPhone(String cellPhone) {
    this.cellPhone = cellPhone;
  }


  public CarrierDto phone(String phone) {

    this.phone = phone;
    return this;
  }

  /**
   * Get phone
   *
   * @return phone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPhone() {
    return phone;
  }


  @JsonProperty(JSON_PROPERTY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPhone(String phone) {
    this.phone = phone;
  }


  public CarrierDto fax(String fax) {

    this.fax = fax;
    return this;
  }

  /**
   * Get fax
   *
   * @return fax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFax() {
    return fax;
  }


  @JsonProperty(JSON_PROPERTY_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFax(String fax) {
    this.fax = fax;
  }


  public CarrierDto freePhone(String freePhone) {

    this.freePhone = freePhone;
    return this;
  }

  /**
   * Get freePhone
   *
   * @return freePhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FREE_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFreePhone() {
    return freePhone;
  }


  @JsonProperty(JSON_PROPERTY_FREE_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFreePhone(String freePhone) {
    this.freePhone = freePhone;
  }


  public CarrierDto emergencyPhone(String emergencyPhone) {

    this.emergencyPhone = emergencyPhone;
    return this;
  }

  /**
   * Get emergencyPhone
   *
   * @return emergencyPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMERGENCY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmergencyPhone() {
    return emergencyPhone;
  }


  @JsonProperty(JSON_PROPERTY_EMERGENCY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmergencyPhone(String emergencyPhone) {
    this.emergencyPhone = emergencyPhone;
  }


  public CarrierDto email(String email) {

    this.email = email;
    return this;
  }

  /**
   * Get email
   *
   * @return email
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmail() {
    return email;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmail(String email) {
    this.email = email;
  }


  public CarrierDto fraudIdentityTheftStatus(String fraudIdentityTheftStatus) {

    this.fraudIdentityTheftStatus = fraudIdentityTheftStatus;
    return this;
  }

  /**
   * Get fraudIdentityTheftStatus
   *
   * @return fraudIdentityTheftStatus
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FRAUD_IDENTITY_THEFT_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFraudIdentityTheftStatus() {
    return fraudIdentityTheftStatus;
  }


  @JsonProperty(JSON_PROPERTY_FRAUD_IDENTITY_THEFT_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFraudIdentityTheftStatus(String fraudIdentityTheftStatus) {
    this.fraudIdentityTheftStatus = fraudIdentityTheftStatus;
  }


  public CarrierDto mcNumber(String mcNumber) {

    this.mcNumber = mcNumber;
    return this;
  }

  /**
   * Get mcNumber
   *
   * @return mcNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MC_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMcNumber() {
    return mcNumber;
  }


  @JsonProperty(JSON_PROPERTY_MC_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMcNumber(String mcNumber) {
    this.mcNumber = mcNumber;
  }


  public CarrierDto SCAC(String SCAC) {

    this.SCAC = SCAC;
    return this;
  }

  /**
   * Get SCAC
   *
   * @return SCAC
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_S_C_A_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSCAC() {
    return SCAC;
  }


  @JsonProperty(JSON_PROPERTY_S_C_A_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSCAC(String SCAC) {
    this.SCAC = SCAC;
  }


  public CarrierDto mailingAddress1(String mailingAddress1) {

    this.mailingAddress1 = mailingAddress1;
    return this;
  }

  /**
   * Get mailingAddress1
   *
   * @return mailingAddress1
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_ADDRESS1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingAddress1() {
    return mailingAddress1;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_ADDRESS1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingAddress1(String mailingAddress1) {
    this.mailingAddress1 = mailingAddress1;
  }


  public CarrierDto mailingAddress2(String mailingAddress2) {

    this.mailingAddress2 = mailingAddress2;
    return this;
  }

  /**
   * Get mailingAddress2
   *
   * @return mailingAddress2
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_ADDRESS2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingAddress2() {
    return mailingAddress2;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_ADDRESS2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingAddress2(String mailingAddress2) {
    this.mailingAddress2 = mailingAddress2;
  }


  public CarrierDto mailingCity(String mailingCity) {

    this.mailingCity = mailingCity;
    return this;
  }

  /**
   * Get mailingCity
   *
   * @return mailingCity
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingCity() {
    return mailingCity;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingCity(String mailingCity) {
    this.mailingCity = mailingCity;
  }


  public CarrierDto mailingState(String mailingState) {

    this.mailingState = mailingState;
    return this;
  }

  /**
   * Get mailingState
   *
   * @return mailingState
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingState() {
    return mailingState;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingState(String mailingState) {
    this.mailingState = mailingState;
  }


  public CarrierDto mailingZipcode(String mailingZipcode) {

    this.mailingZipcode = mailingZipcode;
    return this;
  }

  /**
   * Get mailingZipcode
   *
   * @return mailingZipcode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_ZIPCODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingZipcode() {
    return mailingZipcode;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_ZIPCODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingZipcode(String mailingZipcode) {
    this.mailingZipcode = mailingZipcode;
  }


  public CarrierDto mailingCountry(String mailingCountry) {

    this.mailingCountry = mailingCountry;
    return this;
  }

  /**
   * Get mailingCountry
   *
   * @return mailingCountry
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAILING_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMailingCountry() {
    return mailingCountry;
  }


  @JsonProperty(JSON_PROPERTY_MAILING_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMailingCountry(String mailingCountry) {
    this.mailingCountry = mailingCountry;
  }


  public CarrierDto afterHrsWkDaySupportName(String afterHrsWkDaySupportName) {

    this.afterHrsWkDaySupportName = afterHrsWkDaySupportName;
    return this;
  }

  /**
   * Get afterHrsWkDaySupportName
   *
   * @return afterHrsWkDaySupportName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkDaySupportName() {
    return afterHrsWkDaySupportName;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkDaySupportName(String afterHrsWkDaySupportName) {
    this.afterHrsWkDaySupportName = afterHrsWkDaySupportName;
  }


  public CarrierDto afterHrsWkDaySupportPhone(String afterHrsWkDaySupportPhone) {

    this.afterHrsWkDaySupportPhone = afterHrsWkDaySupportPhone;
    return this;
  }

  /**
   * Get afterHrsWkDaySupportPhone
   *
   * @return afterHrsWkDaySupportPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkDaySupportPhone() {
    return afterHrsWkDaySupportPhone;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkDaySupportPhone(String afterHrsWkDaySupportPhone) {
    this.afterHrsWkDaySupportPhone = afterHrsWkDaySupportPhone;
  }


  public CarrierDto afterHrsWkDaySupportFax(String afterHrsWkDaySupportFax) {

    this.afterHrsWkDaySupportFax = afterHrsWkDaySupportFax;
    return this;
  }

  /**
   * Get afterHrsWkDaySupportFax
   *
   * @return afterHrsWkDaySupportFax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkDaySupportFax() {
    return afterHrsWkDaySupportFax;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkDaySupportFax(String afterHrsWkDaySupportFax) {
    this.afterHrsWkDaySupportFax = afterHrsWkDaySupportFax;
  }


  public CarrierDto afterHrsWkDaySupportFrom(String afterHrsWkDaySupportFrom) {

    this.afterHrsWkDaySupportFrom = afterHrsWkDaySupportFrom;
    return this;
  }

  /**
   * Get afterHrsWkDaySupportFrom
   *
   * @return afterHrsWkDaySupportFrom
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkDaySupportFrom() {
    return afterHrsWkDaySupportFrom;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkDaySupportFrom(String afterHrsWkDaySupportFrom) {
    this.afterHrsWkDaySupportFrom = afterHrsWkDaySupportFrom;
  }


  public CarrierDto afterHrsWkDaySupportTo(String afterHrsWkDaySupportTo) {

    this.afterHrsWkDaySupportTo = afterHrsWkDaySupportTo;
    return this;
  }

  /**
   * Get afterHrsWkDaySupportTo
   *
   * @return afterHrsWkDaySupportTo
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_TO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkDaySupportTo() {
    return afterHrsWkDaySupportTo;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_DAY_SUPPORT_TO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkDaySupportTo(String afterHrsWkDaySupportTo) {
    this.afterHrsWkDaySupportTo = afterHrsWkDaySupportTo;
  }


  public CarrierDto afterHrsWkEndSupportName(String afterHrsWkEndSupportName) {

    this.afterHrsWkEndSupportName = afterHrsWkEndSupportName;
    return this;
  }

  /**
   * Get afterHrsWkEndSupportName
   *
   * @return afterHrsWkEndSupportName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkEndSupportName() {
    return afterHrsWkEndSupportName;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkEndSupportName(String afterHrsWkEndSupportName) {
    this.afterHrsWkEndSupportName = afterHrsWkEndSupportName;
  }


  public CarrierDto afterHrsWkEndSupportPhone(String afterHrsWkEndSupportPhone) {

    this.afterHrsWkEndSupportPhone = afterHrsWkEndSupportPhone;
    return this;
  }

  /**
   * Get afterHrsWkEndSupportPhone
   *
   * @return afterHrsWkEndSupportPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkEndSupportPhone() {
    return afterHrsWkEndSupportPhone;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkEndSupportPhone(String afterHrsWkEndSupportPhone) {
    this.afterHrsWkEndSupportPhone = afterHrsWkEndSupportPhone;
  }


  public CarrierDto afterHrsWkEndSupportFax(String afterHrsWkEndSupportFax) {

    this.afterHrsWkEndSupportFax = afterHrsWkEndSupportFax;
    return this;
  }

  /**
   * Get afterHrsWkEndSupportFax
   *
   * @return afterHrsWkEndSupportFax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkEndSupportFax() {
    return afterHrsWkEndSupportFax;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkEndSupportFax(String afterHrsWkEndSupportFax) {
    this.afterHrsWkEndSupportFax = afterHrsWkEndSupportFax;
  }


  public CarrierDto afterHrsWkEndSupportFrom(String afterHrsWkEndSupportFrom) {

    this.afterHrsWkEndSupportFrom = afterHrsWkEndSupportFrom;
    return this;
  }

  /**
   * Get afterHrsWkEndSupportFrom
   *
   * @return afterHrsWkEndSupportFrom
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkEndSupportFrom() {
    return afterHrsWkEndSupportFrom;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkEndSupportFrom(String afterHrsWkEndSupportFrom) {
    this.afterHrsWkEndSupportFrom = afterHrsWkEndSupportFrom;
  }


  public CarrierDto afterHrsWkEndSupportTo(String afterHrsWkEndSupportTo) {

    this.afterHrsWkEndSupportTo = afterHrsWkEndSupportTo;
    return this;
  }

  /**
   * Get afterHrsWkEndSupportTo
   *
   * @return afterHrsWkEndSupportTo
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_TO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAfterHrsWkEndSupportTo() {
    return afterHrsWkEndSupportTo;
  }


  @JsonProperty(JSON_PROPERTY_AFTER_HRS_WK_END_SUPPORT_TO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAfterHrsWkEndSupportTo(String afterHrsWkEndSupportTo) {
    this.afterHrsWkEndSupportTo = afterHrsWkEndSupportTo;
  }


  public CarrierDto website(String website) {

    this.website = website;
    return this;
  }

  /**
   * Get website
   *
   * @return website
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WEBSITE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWebsite() {
    return website;
  }


  @JsonProperty(JSON_PROPERTY_WEBSITE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWebsite(String website) {
    this.website = website;
  }


  public CarrierDto operationManagerName(String operationManagerName) {

    this.operationManagerName = operationManagerName;
    return this;
  }

  /**
   * Get operationManagerName
   *
   * @return operationManagerName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATION_MANAGER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOperationManagerName() {
    return operationManagerName;
  }


  @JsonProperty(JSON_PROPERTY_OPERATION_MANAGER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperationManagerName(String operationManagerName) {
    this.operationManagerName = operationManagerName;
  }


  public CarrierDto onlineAccessToAvailableLoads(Boolean onlineAccessToAvailableLoads) {

    this.onlineAccessToAvailableLoads = onlineAccessToAvailableLoads;
    return this;
  }

  /**
   * Get onlineAccessToAvailableLoads
   *
   * @return onlineAccessToAvailableLoads
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ONLINE_ACCESS_TO_AVAILABLE_LOADS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOnlineAccessToAvailableLoads() {
    return onlineAccessToAvailableLoads;
  }


  @JsonProperty(JSON_PROPERTY_ONLINE_ACCESS_TO_AVAILABLE_LOADS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOnlineAccessToAvailableLoads(Boolean onlineAccessToAvailableLoads) {
    this.onlineAccessToAvailableLoads = onlineAccessToAvailableLoads;
  }


  public CarrierDto availableLoadsEmail(String availableLoadsEmail) {

    this.availableLoadsEmail = availableLoadsEmail;
    return this;
  }

  /**
   * Get availableLoadsEmail
   *
   * @return availableLoadsEmail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AVAILABLE_LOADS_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAvailableLoadsEmail() {
    return availableLoadsEmail;
  }


  @JsonProperty(JSON_PROPERTY_AVAILABLE_LOADS_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAvailableLoadsEmail(String availableLoadsEmail) {
    this.availableLoadsEmail = availableLoadsEmail;
  }


  public CarrierDto driverLogsSafeyDeptManagerName(String driverLogsSafeyDeptManagerName) {

    this.driverLogsSafeyDeptManagerName = driverLogsSafeyDeptManagerName;
    return this;
  }

  /**
   * Get driverLogsSafeyDeptManagerName
   *
   * @return driverLogsSafeyDeptManagerName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVER_LOGS_SAFEY_DEPT_MANAGER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriverLogsSafeyDeptManagerName() {
    return driverLogsSafeyDeptManagerName;
  }


  @JsonProperty(JSON_PROPERTY_DRIVER_LOGS_SAFEY_DEPT_MANAGER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriverLogsSafeyDeptManagerName(String driverLogsSafeyDeptManagerName) {
    this.driverLogsSafeyDeptManagerName = driverLogsSafeyDeptManagerName;
  }


  public CarrierDto driverLogsSafeyDeptManagerPhone(String driverLogsSafeyDeptManagerPhone) {

    this.driverLogsSafeyDeptManagerPhone = driverLogsSafeyDeptManagerPhone;
    return this;
  }

  /**
   * Get driverLogsSafeyDeptManagerPhone
   *
   * @return driverLogsSafeyDeptManagerPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVER_LOGS_SAFEY_DEPT_MANAGER_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriverLogsSafeyDeptManagerPhone() {
    return driverLogsSafeyDeptManagerPhone;
  }


  @JsonProperty(JSON_PROPERTY_DRIVER_LOGS_SAFEY_DEPT_MANAGER_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriverLogsSafeyDeptManagerPhone(String driverLogsSafeyDeptManagerPhone) {
    this.driverLogsSafeyDeptManagerPhone = driverLogsSafeyDeptManagerPhone;
  }


  public CarrierDto dispatchers(String dispatchers) {

    this.dispatchers = dispatchers;
    return this;
  }

  /**
   * Get dispatchers
   *
   * @return dispatchers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DISPATCHERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDispatchers() {
    return dispatchers;
  }


  @JsonProperty(JSON_PROPERTY_DISPATCHERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDispatchers(String dispatchers) {
    this.dispatchers = dispatchers;
  }


  public CarrierDto claimsContactName(String claimsContactName) {

    this.claimsContactName = claimsContactName;
    return this;
  }

  /**
   * Get claimsContactName
   *
   * @return claimsContactName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLAIMS_CONTACT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClaimsContactName() {
    return claimsContactName;
  }


  @JsonProperty(JSON_PROPERTY_CLAIMS_CONTACT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClaimsContactName(String claimsContactName) {
    this.claimsContactName = claimsContactName;
  }


  public CarrierDto claimsContactPhone(String claimsContactPhone) {

    this.claimsContactPhone = claimsContactPhone;
    return this;
  }

  /**
   * Get claimsContactPhone
   *
   * @return claimsContactPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLAIMS_CONTACT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClaimsContactPhone() {
    return claimsContactPhone;
  }


  @JsonProperty(JSON_PROPERTY_CLAIMS_CONTACT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClaimsContactPhone(String claimsContactPhone) {
    this.claimsContactPhone = claimsContactPhone;
  }


  public CarrierDto claimsContactEmail(String claimsContactEmail) {

    this.claimsContactEmail = claimsContactEmail;
    return this;
  }

  /**
   * Get claimsContactEmail
   *
   * @return claimsContactEmail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLAIMS_CONTACT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClaimsContactEmail() {
    return claimsContactEmail;
  }


  @JsonProperty(JSON_PROPERTY_CLAIMS_CONTACT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClaimsContactEmail(String claimsContactEmail) {
    this.claimsContactEmail = claimsContactEmail;
  }


  public CarrierDto dispatchServiceUsed(Boolean dispatchServiceUsed) {

    this.dispatchServiceUsed = dispatchServiceUsed;
    return this;
  }

  /**
   * Get dispatchServiceUsed
   *
   * @return dispatchServiceUsed
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DISPATCH_SERVICE_USED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDispatchServiceUsed() {
    return dispatchServiceUsed;
  }


  @JsonProperty(JSON_PROPERTY_DISPATCH_SERVICE_USED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDispatchServiceUsed(Boolean dispatchServiceUsed) {
    this.dispatchServiceUsed = dispatchServiceUsed;
  }


  public CarrierDto dispatchServiceName(String dispatchServiceName) {

    this.dispatchServiceName = dispatchServiceName;
    return this;
  }

  /**
   * Get dispatchServiceName
   *
   * @return dispatchServiceName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DISPATCH_SERVICE_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDispatchServiceName() {
    return dispatchServiceName;
  }


  @JsonProperty(JSON_PROPERTY_DISPATCH_SERVICE_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDispatchServiceName(String dispatchServiceName) {
    this.dispatchServiceName = dispatchServiceName;
  }


  public CarrierDto dispatchServicePhone(String dispatchServicePhone) {

    this.dispatchServicePhone = dispatchServicePhone;
    return this;
  }

  /**
   * Get dispatchServicePhone
   *
   * @return dispatchServicePhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DISPATCH_SERVICE_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDispatchServicePhone() {
    return dispatchServicePhone;
  }


  @JsonProperty(JSON_PROPERTY_DISPATCH_SERVICE_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDispatchServicePhone(String dispatchServicePhone) {
    this.dispatchServicePhone = dispatchServicePhone;
  }


  public CarrierDto brokerOutExtraFreight(Boolean brokerOutExtraFreight) {

    this.brokerOutExtraFreight = brokerOutExtraFreight;
    return this;
  }

  /**
   * Get brokerOutExtraFreight
   *
   * @return brokerOutExtraFreight
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BROKER_OUT_EXTRA_FREIGHT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getBrokerOutExtraFreight() {
    return brokerOutExtraFreight;
  }


  @JsonProperty(JSON_PROPERTY_BROKER_OUT_EXTRA_FREIGHT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBrokerOutExtraFreight(Boolean brokerOutExtraFreight) {
    this.brokerOutExtraFreight = brokerOutExtraFreight;
  }


  public CarrierDto references1(String references1) {

    this.references1 = references1;
    return this;
  }

  /**
   * Get references1
   *
   * @return references1
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFERENCES1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReferences1() {
    return references1;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCES1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReferences1(String references1) {
    this.references1 = references1;
  }


  public CarrierDto references2(String references2) {

    this.references2 = references2;
    return this;
  }

  /**
   * Get references2
   *
   * @return references2
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFERENCES2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReferences2() {
    return references2;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCES2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReferences2(String references2) {
    this.references2 = references2;
  }


  public CarrierDto references3(String references3) {

    this.references3 = references3;
    return this;
  }

  /**
   * Get references3
   *
   * @return references3
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFERENCES3)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReferences3() {
    return references3;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCES3)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReferences3(String references3) {
    this.references3 = references3;
  }


  public CarrierDto driversTrackedBy(String driversTrackedBy) {

    this.driversTrackedBy = driversTrackedBy;
    return this;
  }

  /**
   * Get driversTrackedBy
   *
   * @return driversTrackedBy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_TRACKED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversTrackedBy() {
    return driversTrackedBy;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_TRACKED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversTrackedBy(String driversTrackedBy) {
    this.driversTrackedBy = driversTrackedBy;
  }


  public CarrierDto accessOnlineGPSTracking(Boolean accessOnlineGPSTracking) {

    this.accessOnlineGPSTracking = accessOnlineGPSTracking;
    return this;
  }

  /**
   * Get accessOnlineGPSTracking
   *
   * @return accessOnlineGPSTracking
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACCESS_ONLINE_G_P_S_TRACKING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAccessOnlineGPSTracking() {
    return accessOnlineGPSTracking;
  }


  @JsonProperty(JSON_PROPERTY_ACCESS_ONLINE_G_P_S_TRACKING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAccessOnlineGPSTracking(Boolean accessOnlineGPSTracking) {
    this.accessOnlineGPSTracking = accessOnlineGPSTracking;
  }


  public CarrierDto driversTrackedByOtherMethod(String driversTrackedByOtherMethod) {

    this.driversTrackedByOtherMethod = driversTrackedByOtherMethod;
    return this;
  }

  /**
   * Get driversTrackedByOtherMethod
   *
   * @return driversTrackedByOtherMethod
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS_TRACKED_BY_OTHER_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriversTrackedByOtherMethod() {
    return driversTrackedByOtherMethod;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS_TRACKED_BY_OTHER_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriversTrackedByOtherMethod(String driversTrackedByOtherMethod) {
    this.driversTrackedByOtherMethod = driversTrackedByOtherMethod;
  }


  public CarrierDto createdDateTime(LocalDateTime createdDateTime) {

    this.createdDateTime = createdDateTime;
    return this;
  }

  /**
   * Get createdDateTime
   *
   * @return createdDateTime
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_DATE_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getCreatedDateTime() {
    return createdDateTime;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_DATE_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedDateTime(LocalDateTime createdDateTime) {
    this.createdDateTime = createdDateTime;
  }


  public CarrierDto modifiedDateTime(LocalDateTime modifiedDateTime) {

    this.modifiedDateTime = modifiedDateTime;
    return this;
  }

  /**
   * Get modifiedDateTime
   *
   * @return modifiedDateTime
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MODIFIED_DATE_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getModifiedDateTime() {
    return modifiedDateTime;
  }


  @JsonProperty(JSON_PROPERTY_MODIFIED_DATE_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setModifiedDateTime(LocalDateTime modifiedDateTime) {
    this.modifiedDateTime = modifiedDateTime;
  }


  public CarrierDto carrierCustomerAgreements(List<CarrierCustomerAgreementDto> carrierCustomerAgreements) {

    this.carrierCustomerAgreements = carrierCustomerAgreements;
    return this;
  }

  public CarrierDto addCarrierCustomerAgreementsItem(CarrierCustomerAgreementDto carrierCustomerAgreementsItem) {
    if (this.carrierCustomerAgreements == null) {
      this.carrierCustomerAgreements = new ArrayList<>();
    }
    this.carrierCustomerAgreements.add(carrierCustomerAgreementsItem);
    return this;
  }

  /**
   * Get carrierCustomerAgreements
   *
   * @return carrierCustomerAgreements
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_CUSTOMER_AGREEMENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierCustomerAgreementDto> getCarrierCustomerAgreements() {
    return carrierCustomerAgreements;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_CUSTOMER_AGREEMENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierCustomerAgreements(List<CarrierCustomerAgreementDto> carrierCustomerAgreements) {
    this.carrierCustomerAgreements = carrierCustomerAgreements;
  }


  public CarrierDto carrierCustomerPacketStatuses(List<CarrierCustomerPacketStatusDto> carrierCustomerPacketStatuses) {

    this.carrierCustomerPacketStatuses = carrierCustomerPacketStatuses;
    return this;
  }

  public CarrierDto addCarrierCustomerPacketStatusesItem(CarrierCustomerPacketStatusDto carrierCustomerPacketStatusesItem) {
    if (this.carrierCustomerPacketStatuses == null) {
      this.carrierCustomerPacketStatuses = new ArrayList<>();
    }
    this.carrierCustomerPacketStatuses.add(carrierCustomerPacketStatusesItem);
    return this;
  }

  /**
   * Get carrierCustomerPacketStatuses
   *
   * @return carrierCustomerPacketStatuses
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_CUSTOMER_PACKET_STATUSES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierCustomerPacketStatusDto> getCarrierCustomerPacketStatuses() {
    return carrierCustomerPacketStatuses;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_CUSTOMER_PACKET_STATUSES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierCustomerPacketStatuses(List<CarrierCustomerPacketStatusDto> carrierCustomerPacketStatuses) {
    this.carrierCustomerPacketStatuses = carrierCustomerPacketStatuses;
  }


  public CarrierDto carrierCargoHauled(CarrierCargoHauledDto carrierCargoHauled) {

    this.carrierCargoHauled = carrierCargoHauled;
    return this;
  }

  /**
   * Get carrierCargoHauled
   *
   * @return carrierCargoHauled
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_CARGO_HAULED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierCargoHauledDto getCarrierCargoHauled() {
    return carrierCargoHauled;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_CARGO_HAULED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierCargoHauled(CarrierCargoHauledDto carrierCargoHauled) {
    this.carrierCargoHauled = carrierCargoHauled;
  }


  public CarrierDto carrierCompanyClassification(CarrierCompanyClassificationDto carrierCompanyClassification) {

    this.carrierCompanyClassification = carrierCompanyClassification;
    return this;
  }

  /**
   * Get carrierCompanyClassification
   *
   * @return carrierCompanyClassification
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_COMPANY_CLASSIFICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierCompanyClassificationDto getCarrierCompanyClassification() {
    return carrierCompanyClassification;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_COMPANY_CLASSIFICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierCompanyClassification(CarrierCompanyClassificationDto carrierCompanyClassification) {
    this.carrierCompanyClassification = carrierCompanyClassification;
  }


  public CarrierDto carrierDrivers(List<CarrierDriverDto> carrierDrivers) {

    this.carrierDrivers = carrierDrivers;
    return this;
  }

  public CarrierDto addCarrierDriversItem(CarrierDriverDto carrierDriversItem) {
    if (this.carrierDrivers == null) {
      this.carrierDrivers = new ArrayList<>();
    }
    this.carrierDrivers.add(carrierDriversItem);
    return this;
  }

  /**
   * Get carrierDrivers
   *
   * @return carrierDrivers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierDriverDto> getCarrierDrivers() {
    return carrierDrivers;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierDrivers(List<CarrierDriverDto> carrierDrivers) {
    this.carrierDrivers = carrierDrivers;
  }


  public CarrierDto carrierDispatchers(List<CarrierDispatcherDto> carrierDispatchers) {

    this.carrierDispatchers = carrierDispatchers;
    return this;
  }

  public CarrierDto addCarrierDispatchersItem(CarrierDispatcherDto carrierDispatchersItem) {
    if (this.carrierDispatchers == null) {
      this.carrierDispatchers = new ArrayList<>();
    }
    this.carrierDispatchers.add(carrierDispatchersItem);
    return this;
  }

  /**
   * Get carrierDispatchers
   *
   * @return carrierDispatchers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_DISPATCHERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierDispatcherDto> getCarrierDispatchers() {
    return carrierDispatchers;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_DISPATCHERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierDispatchers(List<CarrierDispatcherDto> carrierDispatchers) {
    this.carrierDispatchers = carrierDispatchers;
  }


  public CarrierDto carrierLane(CarrierLaneDto carrierLane) {

    this.carrierLane = carrierLane;
    return this;
  }

  /**
   * Get carrierLane
   *
   * @return carrierLane
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_LANE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierLaneDto getCarrierLane() {
    return carrierLane;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_LANE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierLane(CarrierLaneDto carrierLane) {
    this.carrierLane = carrierLane;
  }


  public CarrierDto carrierOperationalDetail(CarrierOperationalDetailDto carrierOperationalDetail) {

    this.carrierOperationalDetail = carrierOperationalDetail;
    return this;
  }

  /**
   * Get carrierOperationalDetail
   *
   * @return carrierOperationalDetail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_OPERATIONAL_DETAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierOperationalDetailDto getCarrierOperationalDetail() {
    return carrierOperationalDetail;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_OPERATIONAL_DETAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierOperationalDetail(CarrierOperationalDetailDto carrierOperationalDetail) {
    this.carrierOperationalDetail = carrierOperationalDetail;
  }


  public CarrierDto carrierPaymentInfo(CarrierPaymentInfoDto carrierPaymentInfo) {

    this.carrierPaymentInfo = carrierPaymentInfo;
    return this;
  }

  /**
   * Get carrierPaymentInfo
   *
   * @return carrierPaymentInfo
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_PAYMENT_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierPaymentInfoDto getCarrierPaymentInfo() {
    return carrierPaymentInfo;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_PAYMENT_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierPaymentInfo(CarrierPaymentInfoDto carrierPaymentInfo) {
    this.carrierPaymentInfo = carrierPaymentInfo;
  }


  public CarrierDto carrierRemit(CarrierRemitDto carrierRemit) {

    this.carrierRemit = carrierRemit;
    return this;
  }

  /**
   * Get carrierRemit
   *
   * @return carrierRemit
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_REMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierRemitDto getCarrierRemit() {
    return carrierRemit;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_REMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierRemit(CarrierRemitDto carrierRemit) {
    this.carrierRemit = carrierRemit;
  }


  public CarrierDto factoringRemit(FactoringRemitDto factoringRemit) {

    this.factoringRemit = factoringRemit;
    return this;
  }

  /**
   * Get factoringRemit
   *
   * @return factoringRemit
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FACTORING_REMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public FactoringRemitDto getFactoringRemit() {
    return factoringRemit;
  }


  @JsonProperty(JSON_PROPERTY_FACTORING_REMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFactoringRemit(FactoringRemitDto factoringRemit) {
    this.factoringRemit = factoringRemit;
  }


  public CarrierDto carrierBank(CarrierBankDto carrierBank) {

    this.carrierBank = carrierBank;
    return this;
  }

  /**
   * Get carrierBank
   *
   * @return carrierBank
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierBankDto getCarrierBank() {
    return carrierBank;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBank(CarrierBankDto carrierBank) {
    this.carrierBank = carrierBank;
  }


  public CarrierDto carrierPaymentTerms(List<CarrierPaymentTermDto> carrierPaymentTerms) {

    this.carrierPaymentTerms = carrierPaymentTerms;
    return this;
  }

  public CarrierDto addCarrierPaymentTermsItem(CarrierPaymentTermDto carrierPaymentTermsItem) {
    if (this.carrierPaymentTerms == null) {
      this.carrierPaymentTerms = new ArrayList<>();
    }
    this.carrierPaymentTerms.add(carrierPaymentTermsItem);
    return this;
  }

  /**
   * Get carrierPaymentTerms
   *
   * @return carrierPaymentTerms
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_PAYMENT_TERMS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierPaymentTermDto> getCarrierPaymentTerms() {
    return carrierPaymentTerms;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_PAYMENT_TERMS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierPaymentTerms(List<CarrierPaymentTermDto> carrierPaymentTerms) {
    this.carrierPaymentTerms = carrierPaymentTerms;
  }


  public CarrierDto carrierPaymentTypes(List<CarrierPaymentTypeDto> carrierPaymentTypes) {

    this.carrierPaymentTypes = carrierPaymentTypes;
    return this;
  }

  public CarrierDto addCarrierPaymentTypesItem(CarrierPaymentTypeDto carrierPaymentTypesItem) {
    if (this.carrierPaymentTypes == null) {
      this.carrierPaymentTypes = new ArrayList<>();
    }
    this.carrierPaymentTypes.add(carrierPaymentTypesItem);
    return this;
  }

  /**
   * Get carrierPaymentTypes
   *
   * @return carrierPaymentTypes
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_PAYMENT_TYPES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierPaymentTypeDto> getCarrierPaymentTypes() {
    return carrierPaymentTypes;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_PAYMENT_TYPES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierPaymentTypes(List<CarrierPaymentTypeDto> carrierPaymentTypes) {
    this.carrierPaymentTypes = carrierPaymentTypes;
  }


  public CarrierDto carrierPayerType(PayerTypeDto carrierPayerType) {

    this.carrierPayerType = carrierPayerType;
    return this;
  }

  /**
   * Get carrierPayerType
   *
   * @return carrierPayerType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_PAYER_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public PayerTypeDto getCarrierPayerType() {
    return carrierPayerType;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_PAYER_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierPayerType(PayerTypeDto carrierPayerType) {
    this.carrierPayerType = carrierPayerType;
  }


  public CarrierDto carrierTruckClass(CarrierTruckClassDto carrierTruckClass) {

    this.carrierTruckClass = carrierTruckClass;
    return this;
  }

  /**
   * Get carrierTruckClass
   *
   * @return carrierTruckClass
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_TRUCK_CLASS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierTruckClassDto getCarrierTruckClass() {
    return carrierTruckClass;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_TRUCK_CLASS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierTruckClass(CarrierTruckClassDto carrierTruckClass) {
    this.carrierTruckClass = carrierTruckClass;
  }


  public CarrierDto carrierTruckType(CarrierTruckTypeDto carrierTruckType) {

    this.carrierTruckType = carrierTruckType;
    return this;
  }

  /**
   * Get carrierTruckType
   *
   * @return carrierTruckType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_TRUCK_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierTruckTypeDto getCarrierTruckType() {
    return carrierTruckType;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_TRUCK_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierTruckType(CarrierTruckTypeDto carrierTruckType) {
    this.carrierTruckType = carrierTruckType;
  }


  public CarrierDto carrierW9Forms(List<CarrierW9FormDto> carrierW9Forms) {

    this.carrierW9Forms = carrierW9Forms;
    return this;
  }

  public CarrierDto addCarrierW9FormsItem(CarrierW9FormDto carrierW9FormsItem) {
    if (this.carrierW9Forms == null) {
      this.carrierW9Forms = new ArrayList<>();
    }
    this.carrierW9Forms.add(carrierW9FormsItem);
    return this;
  }

  /**
   * Get carrierW9Forms
   *
   * @return carrierW9Forms
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_W9_FORMS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierW9FormDto> getCarrierW9Forms() {
    return carrierW9Forms;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_W9_FORMS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierW9Forms(List<CarrierW9FormDto> carrierW9Forms) {
    this.carrierW9Forms = carrierW9Forms;
  }


  public CarrierDto carrierCertification(CarrierCertificationDto carrierCertification) {

    this.carrierCertification = carrierCertification;
    return this;
  }

  /**
   * Get carrierCertification
   *
   * @return carrierCertification
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_CERTIFICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierCertificationDto getCarrierCertification() {
    return carrierCertification;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_CERTIFICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierCertification(CarrierCertificationDto carrierCertification) {
    this.carrierCertification = carrierCertification;
  }


  public CarrierDto assureAdvantage(List<MyCarrierPacketsApiFMCSAFMCSACarrier> assureAdvantage) {

    this.assureAdvantage = assureAdvantage;
    return this;
  }

  public CarrierDto addAssureAdvantageItem(MyCarrierPacketsApiFMCSAFMCSACarrier assureAdvantageItem) {
    if (this.assureAdvantage == null) {
      this.assureAdvantage = new ArrayList<>();
    }
    this.assureAdvantage.add(assureAdvantageItem);
    return this;
  }

  /**
   * Get assureAdvantage
   *
   * @return assureAdvantage
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ASSURE_ADVANTAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MyCarrierPacketsApiFMCSAFMCSACarrier> getAssureAdvantage() {
    return assureAdvantage;
  }


  @JsonProperty(JSON_PROPERTY_ASSURE_ADVANTAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAssureAdvantage(List<MyCarrierPacketsApiFMCSAFMCSACarrier> assureAdvantage) {
    this.assureAdvantage = assureAdvantage;
  }


  public CarrierDto carrierMode(CarrierModeDto carrierMode) {

    this.carrierMode = carrierMode;
    return this;
  }

  /**
   * Get carrierMode
   *
   * @return carrierMode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierModeDto getCarrierMode() {
    return carrierMode;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierMode(CarrierModeDto carrierMode) {
    this.carrierMode = carrierMode;
  }


  public CarrierDto carrierELDProvider(CarrierELDProviderDto carrierELDProvider) {

    this.carrierELDProvider = carrierELDProvider;
    return this;
  }

  /**
   * Get carrierELDProvider
   *
   * @return carrierELDProvider
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_E_L_D_PROVIDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CarrierELDProviderDto getCarrierELDProvider() {
    return carrierELDProvider;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_E_L_D_PROVIDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierELDProvider(CarrierELDProviderDto carrierELDProvider) {
    this.carrierELDProvider = carrierELDProvider;
  }


  public CarrierDto ownerContactName(String ownerContactName) {

    this.ownerContactName = ownerContactName;
    return this;
  }

  /**
   * Get ownerContactName
   *
   * @return ownerContactName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OWNER_CONTACT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOwnerContactName() {
    return ownerContactName;
  }


  @JsonProperty(JSON_PROPERTY_OWNER_CONTACT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOwnerContactName(String ownerContactName) {
    this.ownerContactName = ownerContactName;
  }


  public CarrierDto ownerContactPhone(String ownerContactPhone) {

    this.ownerContactPhone = ownerContactPhone;
    return this;
  }

  /**
   * Get ownerContactPhone
   *
   * @return ownerContactPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OWNER_CONTACT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOwnerContactPhone() {
    return ownerContactPhone;
  }


  @JsonProperty(JSON_PROPERTY_OWNER_CONTACT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOwnerContactPhone(String ownerContactPhone) {
    this.ownerContactPhone = ownerContactPhone;
  }


  public CarrierDto ownerContactEmail(String ownerContactEmail) {

    this.ownerContactEmail = ownerContactEmail;
    return this;
  }

  /**
   * Get ownerContactEmail
   *
   * @return ownerContactEmail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OWNER_CONTACT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOwnerContactEmail() {
    return ownerContactEmail;
  }


  @JsonProperty(JSON_PROPERTY_OWNER_CONTACT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOwnerContactEmail(String ownerContactEmail) {
    this.ownerContactEmail = ownerContactEmail;
  }


  public CarrierDto carrierTINMatchings(List<CarrierTINMatchingDto> carrierTINMatchings) {

    this.carrierTINMatchings = carrierTINMatchings;
    return this;
  }

  public CarrierDto addCarrierTINMatchingsItem(CarrierTINMatchingDto carrierTINMatchingsItem) {
    if (this.carrierTINMatchings == null) {
      this.carrierTINMatchings = new ArrayList<>();
    }
    this.carrierTINMatchings.add(carrierTINMatchingsItem);
    return this;
  }

  /**
   * Get carrierTINMatchings
   *
   * @return carrierTINMatchings
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_T_I_N_MATCHINGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierTINMatchingDto> getCarrierTINMatchings() {
    return carrierTINMatchings;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_T_I_N_MATCHINGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierTINMatchings(List<CarrierTINMatchingDto> carrierTINMatchings) {
    this.carrierTINMatchings = carrierTINMatchings;
  }


  public CarrierDto message(String message) {

    this.message = message;
    return this;
  }

  /**
   * Get message
   *
   * @return message
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMessage() {
    return message;
  }


  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMessage(String message) {
    this.message = message;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierDto carrierDto = (CarrierDto) o;
    return Objects.equals(this.doTNumber, carrierDto.doTNumber) && Objects.equals(this.legalName, carrierDto.legalName) && Objects.equals(this.dbAName,
        carrierDto.dbAName) && Objects.equals(this.address1, carrierDto.address1) && Objects.equals(this.address2, carrierDto.address2) && Objects.equals(
        this.city, carrierDto.city) && Objects.equals(this.zipcode, carrierDto.zipcode) && Objects.equals(this.state, carrierDto.state) && Objects.equals(
        this.country, carrierDto.country) && Objects.equals(this.cellPhone, carrierDto.cellPhone) && Objects.equals(this.phone, carrierDto.phone)
           && Objects.equals(this.fax, carrierDto.fax) && Objects.equals(this.freePhone, carrierDto.freePhone) && Objects.equals(this.emergencyPhone,
        carrierDto.emergencyPhone) && Objects.equals(this.email, carrierDto.email) && Objects.equals(this.fraudIdentityTheftStatus,
        carrierDto.fraudIdentityTheftStatus) && Objects.equals(this.mcNumber, carrierDto.mcNumber) && Objects.equals(this.SCAC, carrierDto.SCAC)
           && Objects.equals(this.mailingAddress1, carrierDto.mailingAddress1) && Objects.equals(this.mailingAddress2, carrierDto.mailingAddress2)
           && Objects.equals(this.mailingCity, carrierDto.mailingCity) && Objects.equals(this.mailingState, carrierDto.mailingState) && Objects.equals(
        this.mailingZipcode, carrierDto.mailingZipcode) && Objects.equals(this.mailingCountry, carrierDto.mailingCountry) && Objects.equals(
        this.afterHrsWkDaySupportName, carrierDto.afterHrsWkDaySupportName) && Objects.equals(this.afterHrsWkDaySupportPhone,
        carrierDto.afterHrsWkDaySupportPhone) && Objects.equals(this.afterHrsWkDaySupportFax, carrierDto.afterHrsWkDaySupportFax) && Objects.equals(
        this.afterHrsWkDaySupportFrom, carrierDto.afterHrsWkDaySupportFrom) && Objects.equals(this.afterHrsWkDaySupportTo, carrierDto.afterHrsWkDaySupportTo)
           && Objects.equals(this.afterHrsWkEndSupportName, carrierDto.afterHrsWkEndSupportName) && Objects.equals(this.afterHrsWkEndSupportPhone,
        carrierDto.afterHrsWkEndSupportPhone) && Objects.equals(this.afterHrsWkEndSupportFax, carrierDto.afterHrsWkEndSupportFax) && Objects.equals(
        this.afterHrsWkEndSupportFrom, carrierDto.afterHrsWkEndSupportFrom) && Objects.equals(this.afterHrsWkEndSupportTo, carrierDto.afterHrsWkEndSupportTo)
           && Objects.equals(this.website, carrierDto.website) && Objects.equals(this.operationManagerName, carrierDto.operationManagerName) && Objects.equals(
        this.onlineAccessToAvailableLoads, carrierDto.onlineAccessToAvailableLoads) && Objects.equals(this.availableLoadsEmail, carrierDto.availableLoadsEmail)
           && Objects.equals(this.driverLogsSafeyDeptManagerName, carrierDto.driverLogsSafeyDeptManagerName) && Objects.equals(
        this.driverLogsSafeyDeptManagerPhone, carrierDto.driverLogsSafeyDeptManagerPhone) && Objects.equals(this.dispatchers, carrierDto.dispatchers)
           && Objects.equals(this.claimsContactName, carrierDto.claimsContactName) && Objects.equals(this.claimsContactPhone, carrierDto.claimsContactPhone)
           && Objects.equals(this.claimsContactEmail, carrierDto.claimsContactEmail) && Objects.equals(this.dispatchServiceUsed, carrierDto.dispatchServiceUsed)
           && Objects.equals(this.dispatchServiceName, carrierDto.dispatchServiceName) && Objects.equals(this.dispatchServicePhone,
        carrierDto.dispatchServicePhone) && Objects.equals(this.brokerOutExtraFreight, carrierDto.brokerOutExtraFreight) && Objects.equals(this.references1,
        carrierDto.references1) && Objects.equals(this.references2, carrierDto.references2) && Objects.equals(this.references3, carrierDto.references3)
           && Objects.equals(this.driversTrackedBy, carrierDto.driversTrackedBy) && Objects.equals(this.accessOnlineGPSTracking,
        carrierDto.accessOnlineGPSTracking) && Objects.equals(this.driversTrackedByOtherMethod, carrierDto.driversTrackedByOtherMethod) && Objects.equals(
        this.createdDateTime, carrierDto.createdDateTime) && Objects.equals(this.modifiedDateTime, carrierDto.modifiedDateTime) && Objects.equals(
        this.carrierCustomerAgreements, carrierDto.carrierCustomerAgreements) && Objects.equals(this.carrierCustomerPacketStatuses,
        carrierDto.carrierCustomerPacketStatuses) && Objects.equals(this.carrierCargoHauled, carrierDto.carrierCargoHauled) && Objects.equals(
        this.carrierCompanyClassification, carrierDto.carrierCompanyClassification) && Objects.equals(this.carrierDrivers, carrierDto.carrierDrivers)
           && Objects.equals(this.carrierDispatchers, carrierDto.carrierDispatchers) && Objects.equals(this.carrierLane, carrierDto.carrierLane)
           && Objects.equals(this.carrierOperationalDetail, carrierDto.carrierOperationalDetail) && Objects.equals(this.carrierPaymentInfo,
        carrierDto.carrierPaymentInfo) && Objects.equals(this.carrierRemit, carrierDto.carrierRemit) && Objects.equals(this.factoringRemit,
        carrierDto.factoringRemit) && Objects.equals(this.carrierBank, carrierDto.carrierBank) && Objects.equals(this.carrierPaymentTerms,
        carrierDto.carrierPaymentTerms) && Objects.equals(this.carrierPaymentTypes, carrierDto.carrierPaymentTypes) && Objects.equals(this.carrierPayerType,
        carrierDto.carrierPayerType) && Objects.equals(this.carrierTruckClass, carrierDto.carrierTruckClass) && Objects.equals(this.carrierTruckType,
        carrierDto.carrierTruckType) && Objects.equals(this.carrierW9Forms, carrierDto.carrierW9Forms) && Objects.equals(this.carrierCertification,
        carrierDto.carrierCertification) && Objects.equals(this.assureAdvantage, carrierDto.assureAdvantage) && Objects.equals(this.carrierMode,
        carrierDto.carrierMode) && Objects.equals(this.carrierELDProvider, carrierDto.carrierELDProvider) && Objects.equals(this.ownerContactName,
        carrierDto.ownerContactName) && Objects.equals(this.ownerContactPhone, carrierDto.ownerContactPhone) && Objects.equals(this.ownerContactEmail,
        carrierDto.ownerContactEmail) && Objects.equals(this.carrierTINMatchings, carrierDto.carrierTINMatchings) && Objects.equals(this.message,
        carrierDto.message);
  }

  @Override
  public int hashCode() {
    return Objects.hash(doTNumber, legalName, dbAName, address1, address2, city, zipcode, state, country, cellPhone, phone, fax, freePhone, emergencyPhone,
        email, fraudIdentityTheftStatus, mcNumber, SCAC, mailingAddress1, mailingAddress2, mailingCity, mailingState, mailingZipcode, mailingCountry,
        afterHrsWkDaySupportName, afterHrsWkDaySupportPhone, afterHrsWkDaySupportFax, afterHrsWkDaySupportFrom, afterHrsWkDaySupportTo,
        afterHrsWkEndSupportName, afterHrsWkEndSupportPhone, afterHrsWkEndSupportFax, afterHrsWkEndSupportFrom, afterHrsWkEndSupportTo, website,
        operationManagerName, onlineAccessToAvailableLoads, availableLoadsEmail, driverLogsSafeyDeptManagerName, driverLogsSafeyDeptManagerPhone, dispatchers,
        claimsContactName, claimsContactPhone, claimsContactEmail, dispatchServiceUsed, dispatchServiceName, dispatchServicePhone, brokerOutExtraFreight,
        references1, references2, references3, driversTrackedBy, accessOnlineGPSTracking, driversTrackedByOtherMethod, createdDateTime, modifiedDateTime,
        carrierCustomerAgreements, carrierCustomerPacketStatuses, carrierCargoHauled, carrierCompanyClassification, carrierDrivers, carrierDispatchers,
        carrierLane, carrierOperationalDetail, carrierPaymentInfo, carrierRemit, factoringRemit, carrierBank, carrierPaymentTerms, carrierPaymentTypes,
        carrierPayerType, carrierTruckClass, carrierTruckType, carrierW9Forms, carrierCertification, assureAdvantage, carrierMode, carrierELDProvider,
        ownerContactName, ownerContactPhone, ownerContactEmail, carrierTINMatchings, message);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierDto {\n");
    sb.append("    doTNumber: ").append(toIndentedString(doTNumber)).append("\n");
    sb.append("    legalName: ").append(toIndentedString(legalName)).append("\n");
    sb.append("    dbAName: ").append(toIndentedString(dbAName)).append("\n");
    sb.append("    address1: ").append(toIndentedString(address1)).append("\n");
    sb.append("    address2: ").append(toIndentedString(address2)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("    zipcode: ").append(toIndentedString(zipcode)).append("\n");
    sb.append("    state: ").append(toIndentedString(state)).append("\n");
    sb.append("    country: ").append(toIndentedString(country)).append("\n");
    sb.append("    cellPhone: ").append(toIndentedString(cellPhone)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("    fax: ").append(toIndentedString(fax)).append("\n");
    sb.append("    freePhone: ").append(toIndentedString(freePhone)).append("\n");
    sb.append("    emergencyPhone: ").append(toIndentedString(emergencyPhone)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    fraudIdentityTheftStatus: ").append(toIndentedString(fraudIdentityTheftStatus)).append("\n");
    sb.append("    mcNumber: ").append(toIndentedString(mcNumber)).append("\n");
    sb.append("    SCAC: ").append(toIndentedString(SCAC)).append("\n");
    sb.append("    mailingAddress1: ").append(toIndentedString(mailingAddress1)).append("\n");
    sb.append("    mailingAddress2: ").append(toIndentedString(mailingAddress2)).append("\n");
    sb.append("    mailingCity: ").append(toIndentedString(mailingCity)).append("\n");
    sb.append("    mailingState: ").append(toIndentedString(mailingState)).append("\n");
    sb.append("    mailingZipcode: ").append(toIndentedString(mailingZipcode)).append("\n");
    sb.append("    mailingCountry: ").append(toIndentedString(mailingCountry)).append("\n");
    sb.append("    afterHrsWkDaySupportName: ").append(toIndentedString(afterHrsWkDaySupportName)).append("\n");
    sb.append("    afterHrsWkDaySupportPhone: ").append(toIndentedString(afterHrsWkDaySupportPhone)).append("\n");
    sb.append("    afterHrsWkDaySupportFax: ").append(toIndentedString(afterHrsWkDaySupportFax)).append("\n");
    sb.append("    afterHrsWkDaySupportFrom: ").append(toIndentedString(afterHrsWkDaySupportFrom)).append("\n");
    sb.append("    afterHrsWkDaySupportTo: ").append(toIndentedString(afterHrsWkDaySupportTo)).append("\n");
    sb.append("    afterHrsWkEndSupportName: ").append(toIndentedString(afterHrsWkEndSupportName)).append("\n");
    sb.append("    afterHrsWkEndSupportPhone: ").append(toIndentedString(afterHrsWkEndSupportPhone)).append("\n");
    sb.append("    afterHrsWkEndSupportFax: ").append(toIndentedString(afterHrsWkEndSupportFax)).append("\n");
    sb.append("    afterHrsWkEndSupportFrom: ").append(toIndentedString(afterHrsWkEndSupportFrom)).append("\n");
    sb.append("    afterHrsWkEndSupportTo: ").append(toIndentedString(afterHrsWkEndSupportTo)).append("\n");
    sb.append("    website: ").append(toIndentedString(website)).append("\n");
    sb.append("    operationManagerName: ").append(toIndentedString(operationManagerName)).append("\n");
    sb.append("    onlineAccessToAvailableLoads: ").append(toIndentedString(onlineAccessToAvailableLoads)).append("\n");
    sb.append("    availableLoadsEmail: ").append(toIndentedString(availableLoadsEmail)).append("\n");
    sb.append("    driverLogsSafeyDeptManagerName: ").append(toIndentedString(driverLogsSafeyDeptManagerName)).append("\n");
    sb.append("    driverLogsSafeyDeptManagerPhone: ").append(toIndentedString(driverLogsSafeyDeptManagerPhone)).append("\n");
    sb.append("    dispatchers: ").append(toIndentedString(dispatchers)).append("\n");
    sb.append("    claimsContactName: ").append(toIndentedString(claimsContactName)).append("\n");
    sb.append("    claimsContactPhone: ").append(toIndentedString(claimsContactPhone)).append("\n");
    sb.append("    claimsContactEmail: ").append(toIndentedString(claimsContactEmail)).append("\n");
    sb.append("    dispatchServiceUsed: ").append(toIndentedString(dispatchServiceUsed)).append("\n");
    sb.append("    dispatchServiceName: ").append(toIndentedString(dispatchServiceName)).append("\n");
    sb.append("    dispatchServicePhone: ").append(toIndentedString(dispatchServicePhone)).append("\n");
    sb.append("    brokerOutExtraFreight: ").append(toIndentedString(brokerOutExtraFreight)).append("\n");
    sb.append("    references1: ").append(toIndentedString(references1)).append("\n");
    sb.append("    references2: ").append(toIndentedString(references2)).append("\n");
    sb.append("    references3: ").append(toIndentedString(references3)).append("\n");
    sb.append("    driversTrackedBy: ").append(toIndentedString(driversTrackedBy)).append("\n");
    sb.append("    accessOnlineGPSTracking: ").append(toIndentedString(accessOnlineGPSTracking)).append("\n");
    sb.append("    driversTrackedByOtherMethod: ").append(toIndentedString(driversTrackedByOtherMethod)).append("\n");
    sb.append("    createdDateTime: ").append(toIndentedString(createdDateTime)).append("\n");
    sb.append("    modifiedDateTime: ").append(toIndentedString(modifiedDateTime)).append("\n");
    sb.append("    carrierCustomerAgreements: ").append(toIndentedString(carrierCustomerAgreements)).append("\n");
    sb.append("    carrierCustomerPacketStatuses: ").append(toIndentedString(carrierCustomerPacketStatuses)).append("\n");
    sb.append("    carrierCargoHauled: ").append(toIndentedString(carrierCargoHauled)).append("\n");
    sb.append("    carrierCompanyClassification: ").append(toIndentedString(carrierCompanyClassification)).append("\n");
    sb.append("    carrierDrivers: ").append(toIndentedString(carrierDrivers)).append("\n");
    sb.append("    carrierDispatchers: ").append(toIndentedString(carrierDispatchers)).append("\n");
    sb.append("    carrierLane: ").append(toIndentedString(carrierLane)).append("\n");
    sb.append("    carrierOperationalDetail: ").append(toIndentedString(carrierOperationalDetail)).append("\n");
    sb.append("    carrierPaymentInfo: ").append(toIndentedString(carrierPaymentInfo)).append("\n");
    sb.append("    carrierRemit: ").append(toIndentedString(carrierRemit)).append("\n");
    sb.append("    factoringRemit: ").append(toIndentedString(factoringRemit)).append("\n");
    sb.append("    carrierBank: ").append(toIndentedString(carrierBank)).append("\n");
    sb.append("    carrierPaymentTerms: ").append(toIndentedString(carrierPaymentTerms)).append("\n");
    sb.append("    carrierPaymentTypes: ").append(toIndentedString(carrierPaymentTypes)).append("\n");
    sb.append("    carrierPayerType: ").append(toIndentedString(carrierPayerType)).append("\n");
    sb.append("    carrierTruckClass: ").append(toIndentedString(carrierTruckClass)).append("\n");
    sb.append("    carrierTruckType: ").append(toIndentedString(carrierTruckType)).append("\n");
    sb.append("    carrierW9Forms: ").append(toIndentedString(carrierW9Forms)).append("\n");
    sb.append("    carrierCertification: ").append(toIndentedString(carrierCertification)).append("\n");
    sb.append("    assureAdvantage: ").append(toIndentedString(assureAdvantage)).append("\n");
    sb.append("    carrierMode: ").append(toIndentedString(carrierMode)).append("\n");
    sb.append("    carrierELDProvider: ").append(toIndentedString(carrierELDProvider)).append("\n");
    sb.append("    ownerContactName: ").append(toIndentedString(ownerContactName)).append("\n");
    sb.append("    ownerContactPhone: ").append(toIndentedString(ownerContactPhone)).append("\n");
    sb.append("    ownerContactEmail: ").append(toIndentedString(ownerContactEmail)).append("\n");
    sb.append("    carrierTINMatchings: ").append(toIndentedString(carrierTINMatchings)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

