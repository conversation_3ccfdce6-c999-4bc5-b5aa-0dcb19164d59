package com.bulkloads.web.truck.repository;

import static com.bulkloads.common.Converters.dateToSql;
import static com.bulkloads.common.Converters.instantToSql;
import static com.bulkloads.common.Parsers.parseStringCsvToList;
import static com.bulkloads.web.truck.repository.template.GetTruckQueryTemplate.GET_TRUCK_QUERY_TEMPLATE;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.truck.service.dto.MyTruckResponse;
import com.bulkloads.web.truck.service.dto.TruckResponse;
import com.bulkloads.web.truck.service.dto.transformer.MyTruckResponseTransformer;
import com.bulkloads.web.truck.service.dto.transformer.TruckResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class TruckQueryRepositoryImpl implements TruckQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final TruckResponseTransformer truckResponseTransformer;
  private final MyTruckResponseTransformer myTruckResponseTransformer;

  public List<MyTruckResponse> getMyTrucks(Integer uId,
                                           String orderBy,
                                           Integer skip,
                                           Integer limit) {

    Map<String, Object> params = new HashMap<>();

    params.put("uId", uId);
    params.put("orderBy", orderBy);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(GET_TRUCK_QUERY_TEMPLATE, params, myTruckResponseTransformer);
  }

  public List<TruckResponse> getTrucks(Integer cId,
                                       String originState,
                                       Double latitude,
                                       Double longitude,
                                       Integer distance,
                                       String equipment,
                                       LocalDate shipFrom,
                                       LocalDate shipTo,
                                       Instant truckDate,
                                       String orderBy,
                                       Integer skip,
                                       Integer limit) {

    Map<String, Object> params = new HashMap<>();

    params.put("cId", cId);
    params.put("originState", originState);
    params.put("latitude", latitude);
    params.put("longitude", longitude);
    params.put("distance", distance);
    params.put("equipment", parseStringCsvToList(equipment));
    params.put("shipFrom", dateToSql(shipFrom));
    params.put("shipTo", dateToSql(shipTo));
    params.put("truckDate", instantToSql(truckDate));
    params.put("orderBy", orderBy);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(GET_TRUCK_QUERY_TEMPLATE, params, truckResponseTransformer);
  }

  public TruckResponse getTruckById(Integer truckId) {

    Map<String, Object> params = new HashMap<>();

    params.put("truckId", truckId);

    return jpaNativeQueryService.queryForObject(GET_TRUCK_QUERY_TEMPLATE, params, truckResponseTransformer);
  }

}
