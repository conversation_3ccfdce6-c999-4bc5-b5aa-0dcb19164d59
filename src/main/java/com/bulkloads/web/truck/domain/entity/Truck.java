package com.bulkloads.web.truck.domain.entity;

import java.time.Instant;
import java.time.LocalDate;
import com.bulkloads.web.site.domain.entity.Site;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "trucks")
public class Truck {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "truck_id")
  private Integer truckId;

  @NotNull
  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @NotNull
  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @Size(max = 1000, message = "Up to 1000 chars")
  @NotNull
  @Column(name = "comments")
  private String comments = "";

  @Size(max = 50, message = "Up to 50 chars")
  @NotEmpty(message = "You must enter your Contact Name")
  @Column(name = "contact_name")
  private String contactName;

  @Size(max = 50, message = "Up to 50 chars")
  @NotEmpty(message = "You must enter your Contact Number")
  // @Pattern(regexp = "\\d{3}-\\d{3}-\\d{4}")
  @Column(name = "contact_number")
  private String contactNumber;

  @Size(max = 50, message = "Up to 10 chars")
  @NotNull
  @Column(name = "contact_number_type")
  private String contactNumberType = "";


  @Size(max = 50, message = "Up to 50 chars")
  @NotEmpty(message = "You must select a trailer type")
  @Column(name = "trailer_type")
  private String equipmentName = "";

  @Size(max = 60, message = "Up to 60 chars")
  @NotEmpty(message = "You must select the city you'll be shipping from!")
  @Column(name = "origin_city")
  private String originCity = "";

  @Size(max = 2, message = "Up to 2 chars")
  @NotEmpty(message = "You must select the state you'll be shipping from!")
  @Column(name = "origin_state")
  private String originState = "";

  @Size(max = 15, message = "Up to 15 chars")
  @NotNull
  @Column(name = "origin_zipcode")
  private String originZipcode = "";

  @Size(max = 15, message = "Up to 15 chars")
  @NotNull
  @Column(name = "origin_country")
  private String originCountry = "";

  @DecimalMin(value = "-90.0")
  @DecimalMax(value = "90.0")
  @Column(name = "origin_lat")
  private Double originLat;

  @DecimalMin(value = "-180.0")
  @DecimalMax(value = "180.0")
  @Column(name = "origin_long")
  private Double originLong;

  @Size(max = 40, message = "Up to 40 chars")
  @NotNull
  @Column(name = "destination_city")
  private String destinationCity = "";

  @Size(max = 15, message = "Up to 15 chars")
  @NotNull
  @Column(name = "destination_state")
  private String destinationState = "";

  @Size(max = 15, message = "Up to 15 chars")
  @NotNull
  @Column(name = "destination_zipcode")
  private String destinationZipcode = "";

  @Size(max = 15, message = "Up to 15 chars")
  @NotNull
  @Column(name = "destination_country")
  private String destinationCountry = "";

  @DecimalMin(value = "-90.0")
  @DecimalMax(value = "90.0")
  @Column(name = "dest_lat")
  private Double destinationLat;

  @DecimalMin(value = "-180.0")
  @DecimalMax(value = "180.0")
  @Column(name = "dest_long")
  private Double destinationLong;

  @Size(max = 500, message = "Up to 500 chars")
  @NotNull
  @Column(name = "prefered_destination")
  private String preferedDestination = "";

  @NotNull
  @Column(name = "post_date")
  private Instant postDate;

  //@FutureOrPresent(message = "The available date must not be in the past")
  @NotNull(message = "You must select the date you'll be available!")
  @Column(name = "date_available")
  private LocalDate dateAvailable;

  @Size(max = 15, message = "Up to 15 chars")
  @NotNull
  @Column(name = "time_available")
  private String timeAvailable = "";


  @PositiveOrZero(message = "The repost days must be a positive number")
  @Column(name = "repost_days")
  private Integer repostDays;

  @Size(max = 50, message = "Up to 50 chars")
  @Column(name = "source")
  private String source = "";

  @Size(max = 45, message = "Up to 45 chars")
  @Column(name = "source_id")
  private String sourceId = "";

  //  @Column(name = "site_id")
  //  private short siteId = 1;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "site_id", referencedColumnName = "id")
  private Site site;

  @Column(name = "active")
  private Boolean active = true;

  /*
  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "equipment_id", referencedColumnName = "equipment_id",  nullable = false)
  private Equipment equipment;
  */
  @Transient
  private LocalDate expiresOn;

  @Transient
  private String origin;

  @Transient
  private String destination;

  public LocalDate getExpiresOn() {
    return this.dateAvailable.plusDays(this.repostDays);
  }

  public String getOrigin() {
    return getOriginCity() + ", " + getOriginState();
  }

  public String getDestination() {
    return getPreferedDestination().isEmpty()
      ?
      (getDestinationCity().isEmpty() ? "ANY" : getDestinationCity() + ", " + getDestinationState()) :
      getPreferedDestination();
  }
}