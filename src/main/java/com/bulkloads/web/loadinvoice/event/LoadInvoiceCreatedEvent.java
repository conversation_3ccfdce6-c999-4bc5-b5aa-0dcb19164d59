package com.bulkloads.web.loadinvoice.event;

import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_CREATE;
import java.util.List;
import lombok.Getter;

@Getter
public class LoadInvoiceCreatedEvent extends LoadInvoiceEvent {

  public LoadInvoiceCreatedEvent(final int loadInvoiceId,
                                 final String fileUrl,
                                 final List<Integer> loadAssignmentIds) {
    super(loadInvoiceId, LOAD_INVOICE_CREATE, fileUrl, loadAssignmentIds);
  }
}
