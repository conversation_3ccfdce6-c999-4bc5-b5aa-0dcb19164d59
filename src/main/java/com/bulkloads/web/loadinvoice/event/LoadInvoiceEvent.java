package com.bulkloads.web.loadinvoice.event;

import java.util.List;
import com.bulkloads.web.common.event.DomainEvent;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class LoadInvoiceEvent implements DomainEvent {

  private int loadInvoiceId;
  private String action;
  private final String fileUrl;
  private final List<Integer> loadAssignmentIds;
}
