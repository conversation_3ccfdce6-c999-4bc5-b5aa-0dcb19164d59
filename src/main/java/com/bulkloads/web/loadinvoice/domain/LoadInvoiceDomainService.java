package com.bulkloads.web.loadinvoice.domain;

import static com.bulkloads.common.StringUtil.dollarFormat;
import static com.bulkloads.common.StringUtil.numberFormat;
import static com.bulkloads.common.StringUtil.rateFormat;
import static com.bulkloads.common.StringUtil.restrictMaxSize;
import static com.bulkloads.common.validation.ValidationUtils.equal;
import static com.bulkloads.common.validation.ValidationUtils.exists;
import static com.bulkloads.common.validation.ValidationUtils.isBetweenOrEqual;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isPositive;
import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.atteo.evo.inflector.English.plural;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.domain.entity.AssignmentSurcharge;
import com.bulkloads.web.assignment.domain.entity.SurchargeType;
import com.bulkloads.web.assignment.repository.AssignmentSurchageRepository;
import com.bulkloads.web.loadinvoice.domain.data.LoadInvoiceData;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoice;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoiceItem;
import com.bulkloads.web.loadinvoice.mapper.LoadInvoiceMapper;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoadInvoiceDomainService extends BaseDomainService<LoadInvoice> {

  private static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100.0);

  private final UserService userService;
  private final AssignmentSurchageRepository assignmentSurchageRepository;
  private final LoadInvoiceMapper loadInvoiceMapper;

  public Result<LoadInvoice> create(LoadInvoiceData data) {
    return validate(new LoadInvoice(), null, data, ValidationMethod.CREATE);
  }

  @Override
  public void validateDataAndMapToEntity(
      final Result<LoadInvoice> result,
      final LoadInvoice invoice,
      final LoadInvoice existing,
      final Object dataObject,
      final ValidationMethod method) {

    if (method == ValidationMethod.UPDATE) {
      throw new IllegalStateException("Update is not allowed for invoices");
    }

    final User user = userService.getLoggedInUser();
    invoice.setAddedDate(Instant.now());
    invoice.setModifiedDate(Instant.now());
    invoice.setInvoiceDate(Instant.now());

    invoice.setPaymentDueDate(LocalDateTime.now().plusMonths(1)
        .atZone(ZoneId.systemDefault()).toInstant());

    // Add this error only if billToAbcompany is null in the firstBooking
    // <cfset local.result.errors["bill_to_ab_company_id"] = "The company was not found in your address book">

    // this happens only if an abuser is assigned as the billTo
    // <cfset local.result.errors["bill_to_ab_user_id"] = "The user was not found in your address book">

    final LoadInvoiceData data = (LoadInvoiceData) dataObject;
    final List<Assignment> bookings = data.getLoadAssignments();

    if (bookings.isEmpty()) {
      throw new IllegalStateException("No boookings found");
    }

    final Assignment firstBooking = bookings.get(0);

    invoice.setHiringAbCompany(firstBooking.getHiringAbCompany());
    invoice.setHiringAbUser(firstBooking.getHiringAbUser());
    invoice.setAbCompanyId(firstBooking.getToAbCompany() != null ? firstBooking.getToAbCompany().getAbCompanyId() : null);

    AbCompany billToAbCompany = firstBooking.getBillToAbCompany();
    AbUser billToAbUser = firstBooking.getBillToAbUser();
    UserCompany billToUserCompany = firstBooking.getUserCompany();
    User billToUser = firstBooking.getUser();

    setInvoiceSender(invoice, user);

    if (billToAbCompany != null) {
      setExternalBillTo(invoice, billToAbUser, billToAbCompany);
    } else if (billToUserCompany != null) {
      setInternalBillTo(invoice, billToUser, billToUserCompany);
    }

    billToUser = invoice.getBillToUser();
    if (billToUser != null && !isEmpty(billToUser.getAccountingEmail())) {
      invoice.setBillToEmail(billToUser.getAccountingEmail());
    }

    if (!invoice.isInternalAssignment() && !invoice.isExternalAssignment()) {
      // TODO: is the error key correct?
      result.addError("bill_to_ab_company_id", "There is no billing company set for this load");
    }

    for (Assignment booking : bookings) {

      if (!booking.getAutoInvoice() && !booking.getReadyToInvoice()) {
        result.addError("ready_to_invoice",
            "This load is not ready to be invoiced (load_assignment_id:%s)",
            booking.getLoadAssignmentId()
        );
      }

      if (!booking.hasStatus("Delivered") && !booking.hasStatus("Completed")) {
        result.addError("assignment_status",
            "The assignment_status must be delivered|completed (load_assignment_id:%s)",
            booking.getLoadAssignmentId());
      }

      if (isEmpty(booking.getRate())) {
        result.addError("rate",
            "There is no rate specified in order to invoice this load (load_assignment_id:%s)",
            booking.getLoadAssignmentId());
      }

      result.addErrors(validateRateType(booking));

      if (exists(booking.getLoadInvoiceId())) {
        result.addError("load_invoice_id",
            "This load has already been invoiced (load_assignment_id:%s, load_invoice_id: %s)",
            booking.getLoadAssignmentId(), booking.getLoadInvoiceId()
        );
      }

      if (invoice.isExternalAssignment()) {

        if (booking.getBillToAbCompany() != billToAbCompany) {
          result.addError("bill_to_ab_company_id",
              "The billing company must be the same for all loads in the invoice (load_assignment_id:%s, bill_to_ab_company_id:%d not %d)",
              booking.getLoadAssignmentId(),
              booking.getBillToAbCompany().getAbCompanyId(),
              billToAbCompany.getAbCompanyId()
          );
        }
        if (booking.getBillToAbUser() != billToAbUser) {
          result.addError("bill_to_ab_user_id",
              "The billing user must be the same for all loads in the invoice (load_assignment_id:%s, bill_to_ab_user_id:%s not %s)",
              booking.getLoadAssignmentId(),
              booking.getBillToAbUser().getAbUserId(),
              billToAbUser.getAbUserId()
          );
        }

      } else if (invoice.isInternalAssignment()) {
        if (booking.getUserCompany() != billToUserCompany) {
          result.addError("user_company_id",
              "The billing company must be the same for all loads in the invoice (load_assignment_id:%s, bill_to_company_id:%s not %s)",
              booking.getLoadAssignmentId(),
              booking.getUserCompany().getUserCompanyId(),
              billToUserCompany.getUserCompanyId()
          );

        }
        if (booking.getUser() != billToUser) {
          result.addError("bill_to_user_id",
              "The billing user must be the same for all loads in the invoice (load_assignment_id:%s, bill_to_user_id:%s not %s)",
              booking.getLoadAssignmentId(),
              booking.getUser().getUserId(),
              billToUser.getUserId()
          );
        }
      }
    }

    String billDesc = format("%d %s", bookings.size(), plural("load", bookings.size()));
    invoice.setBillDescription(restrictMaxSize(billDesc, 1000));

    if (data.isMarkInvoiceSent()) {
      invoice.setEmailStatus("Sent");
      invoice.setEmailStatusDate(Instant.now());
    }

    for (Assignment booking : bookings) {

      final LoadInvoiceItem item = bookingToInvoiceItem(invoice, booking);
      invoice.getLoadInvoiceItems().add(item);

      assignmentSurchageRepository
          .findByLoadAssignmentLoadAssignmentId(booking.getLoadAssignmentId())
          .forEach(surcharge -> invoice.getLoadInvoiceItems().add(surchargeToInvoiceItem(invoice, booking, surcharge)));

    }
    recalculateInvoiceTotal(invoice);
  }

  private LoadInvoiceItem bookingToInvoiceItem(LoadInvoice invoice, Assignment booking) {
    LoadInvoiceItem item = new LoadInvoiceItem();
    item.setLoadInvoice(invoice);
    item.setLoadAssignment(booking);

    item.setItemDescription(buildBillDescription(booking));

    item.setBillWeight(booking.getBillWeight());
    item.setBillVolume(booking.getBillVolume());
    item.setBillMiles(booking.getBillMiles());
    item.setBillHours(booking.getBillHours());
    item.setBillRate(booking.getRate());
    item.setBillRateType(booking.getRateType());

    item.setBillRateMessage(rateFormat(booking.getRate(), booking.getRateType().getRateTypeTextMedium()));

    item.setHauledDate(booking.getHauledDate());
    item.setHauledNotes(booking.getHauledNotes());

    item.setPickupCompanyName(booking.getToLoad().getPickupAbCompany().getCompanyName());
    item.setPickupCity(booking.getToLoad().getPickupAbCompany().getCity());
    item.setPickupState(booking.getToLoad().getPickupAbCompany().getState());

    item.setDropCompanyName(booking.getToLoad().getDropAbCompany().getCompanyName());
    item.setDropCity(booking.getToLoad().getDropAbCompany().getCity());
    item.setDropState(booking.getToLoad().getDropAbCompany().getState());
    item.setLoadAssignmentNumber(booking.getLoadAssignmentNumber());

    item.setCommodity(booking.getToLoad().getLoCommodity());
    item.setLoadedWeight(booking.getLoadedWeight());
    item.setLoadedVolume(booking.getLoadedVolume());
    item.setUnloadWeight(booking.getUnloadWeight());
    item.setUnloadVolume(booking.getUnloadVolume());

    item.setBolNumber(booking.getBolNumber());
    item.setWorkOrderNumber(booking.getWorkOrderNumber());
    item.setItemQuantity(booking.getBillQuantity());

    item.setLoadingTicketNumber(booking.getLoadingTicketNumber());
    item.setPickupNumber(booking.getPickupNumber());
    item.setUnloadingTicketNumber(booking.getUnloadingTicketNumber());
    item.setDropNumber(booking.getDropNumber());
    item.setItemAmount(booking.getBillSubtotal());

    if (booking.getIsRerouted()) {
      item.setIsRerouted(true);
      final AbCompany rerouteToAbCompany = nonNull(booking.getRerouteToAbCompany()) ? booking.getRerouteToAbCompany() : booking.getRerouteAbCompany();
      item.setRerouteCompanyName(rerouteToAbCompany.getCompanyName());
      item.setRerouteCity(rerouteToAbCompany.getCity());
      item.setRerouteState(rerouteToAbCompany.getState());
      item.setReroutePickupDrop(booking.getReroutePickupDrop());
      item.setPreviousLoadAssignmentNumber(booking.getPreviousLoadAssignmentNumber());
      item.setPreviousBillRate(booking.getPreviousRate());
      item.setPreviousBillRateType(booking.getPreviousRateType());
      item.setPreviousBillRateMessage(rateFormat(booking.getPreviousRate(), booking.getPreviousRateType().getRateTypeTextMedium()));
      item.setPreviousItemAmount(booking.getPreviousBillSubtotal());
    }

    return item;
  }

  private LoadInvoiceItem surchargeToInvoiceItem(LoadInvoice invoice, Assignment booking, AssignmentSurcharge assignmentSurcharge) {
    final LoadInvoiceItem surchargeLoadInvoiceItem = new LoadInvoiceItem();

    surchargeLoadInvoiceItem.setLoadInvoice(invoice);
    surchargeLoadInvoiceItem.setLoadAssignment(booking);

    surchargeLoadInvoiceItem.setIsSurcharge(true);
    surchargeLoadInvoiceItem.setItemQuantity(BigDecimal.ONE);
    surchargeLoadInvoiceItem.setBillRateType(booking.getRateType());

    BigDecimal surcharge = assignmentSurcharge.getSurcharge();
    SurchargeType surchargeType = assignmentSurcharge.getSurchargeType();

    surchargeLoadInvoiceItem.setLoadAssignmentSurchargeTypeId(surchargeType.getId());

    if (assignmentSurcharge.getSurchargeType().getIsPercentage()) {

      surchargeLoadInvoiceItem.setItemDescription(format("%s%% %s", surcharge, surchargeType.getType()));

      // round(round(arguments.f.bookings.bill_subtotal * 100) * (local.surcharges.surcharge / 100.0))/100
      BigDecimal itemAmount = booking.getBillSubtotal()
          .multiply(surcharge.divide(ONE_HUNDRED, 2, RoundingMode.HALF_UP));

      surchargeLoadInvoiceItem.setItemAmount(itemAmount);
      surchargeLoadInvoiceItem.setBillRate(itemAmount);

    } else if (assignmentSurcharge.getSurchargeType().getIsPerMile()) {

      // "#numberformat(local.surcharges.bill_miles)#mi * #dollarformat(local.surcharges.surcharge)#/mi #local.surcharges.surcharge_type#"
      String desc = format("%smi * %s/mi %s",
          numberFormat(assignmentSurcharge.getLoadAssignment().getBillMiles()),
          dollarFormat(surcharge),
          surchargeType);

      surchargeLoadInvoiceItem.setItemDescription(desc);

      // round(round(arguments.f.bookings.bill_miles) * round(local.surcharges.surcharge * 100))/100
      BigDecimal itemAmount = booking.getBillMiles().multiply(surcharge);

      surchargeLoadInvoiceItem.setItemAmount(itemAmount);
      surchargeLoadInvoiceItem.setBillRate(itemAmount);

    } else {
      surchargeLoadInvoiceItem.setItemDescription(surchargeType.getType());
      surchargeLoadInvoiceItem.setItemAmount(surcharge);
      surchargeLoadInvoiceItem.setBillRate(surcharge);
    }

    return surchargeLoadInvoiceItem;
  }

  private void recalculateInvoiceTotal(LoadInvoice invoice) {

    BigDecimal total = invoice.getLoadInvoiceItems()
        .stream()
        .map(LoadInvoiceItem::getItemAmount)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    invoice.setInvoiceTotal(total);
    invoice.setModifiedDate(Instant.now());
  }

  private static String buildBillDescription(Assignment booking) {

    String billDescription = String.format("%s - %s @%s",
        booking.getToLoad().getPickupAbCompany().getCompanyName(),
        booking.getToLoad().getDropAbCompany().getCompanyName(),
        rateFormat(booking.getRate(), booking.getRateType().getRateTypeTextMedium()));

    if (booking.getRateType() != null) {

      if (booking.getRateType().is("1000")) {
        billDescription += ", " + booking.getBillWeight() + " kgs";

      } else if (booking.getRateType().isWeightType()) {
        billDescription += ", " + booking.getBillWeight() + " lbs";

      } else if (booking.getRateType().isMileType()) {
        billDescription += ", " + booking.getBillMiles() + " mi";

      } else if (booking.getRateType().isHourType()) {
        billDescription += ", " + booking.getBillHours() + " hrs";

      } else if (booking.getRateType().isGallonType()) {
        billDescription += ", " + booking.getBillVolume() + " gal";

      } else if (booking.getRateType().isLiterType()) {
        billDescription += ", " + booking.getBillVolume() + " lt";
      }
    }
    if (booking.getHauledDate() != null) {
      billDescription += ", Hauled on " + booking.getHauledDate().format(DateTimeFormatter.ofPattern("M/d"));
    }

    if (!isEmpty(booking.getHauledNotes())) {
      billDescription += "<br/>" + booking.getHauledNotes();
    }

    return restrictMaxSize(billDescription, 1000);
  }

  @Override
  public void mapToEntityAuto(final Object data, final LoadInvoice entity) {
    loadInvoiceMapper.dataToEntity((LoadInvoiceData) data, entity);
  }

  @Override
  public void validateEntity(final Result<LoadInvoice> result, final LoadInvoice entity) {
  }

  private static Map<String, String> validateRateType(Assignment booking) {

    RateType rateType = booking.getRateType();
    Map<String, String> errors = new HashMap<>();

    if (rateType.isGallonType() || rateType.isLiterType()) {
      if (equal("loaded_weight", booking.getBillWeightUse())) {

        if (!isBetweenOrEqual(booking.getLoadedVolume(), 0, 70000)) {
          errors.put("loaded_volume", "Enter a valid number");
        }

      } else {
        if (!isBetweenOrEqual(booking.getUnloadVolume(), 0, 70000)) {
          errors.put("unloaded_volume", "Enter a valid number");
        }
      }
    } else if (rateType.isMileType()) {

      if (!isPositive(booking.getBillMiles())) {
        errors.put("bill_miles", "Enter a valid number");
      }

    } else if (rateType.isHourType()) {
      if (!isPositive(booking.getBillHours())) {
        errors.put("bill_hours", "Enter a valid number");
      }

    } else if (rateType.isWeightType()) {
      if (equal(booking.getBillWeightUse(), "loaded_weight")) {

        if (isNull(booking.getLoadedWeight())) {
          errors.put("loaded_weight", "Enter a valid number");
        }
      } else {
        if (isNull(booking.getUnloadWeight())) {
          errors.put("unload_weight", "Enter a valid number");
        }
      }
    }

    return errors;
  }

  private void setInvoiceSender(LoadInvoice invoice, User user) {

    invoice.setUser(user);

    invoice.setFirstName(user.getFirstName());
    invoice.setLastName(user.getLastName());

    final UserCompany userCompany = user.getUserCompany();
    invoice.setUserCompany(userCompany);
    invoice.setCompanyName(userCompany.getCompanyName());

  }

  private void setInternalBillTo(LoadInvoice invoice, User user, UserCompany userCompany) {
    Objects.requireNonNull(user);
    Objects.requireNonNull(userCompany);
    invoice.setBillToAbUserId(null);
    invoice.setBillToAbCompanyId(null);

    invoice.setBillToUser(user);
    invoice.setBillToFirstName(user.getFirstName());
    invoice.setBillToLastName(user.getLastName());
    invoice.setBillToEmail(!isEmpty(user.getAccountingEmail()) ? user.getAccountingEmail() : user.getEmail());
    invoice.setBillToPhone(user.getPhone1());

    invoice.setBillToUserCompany(userCompany);
    invoice.setBillToCompanyName(userCompany.getCompanyName());
    invoice.setBillToAddress(user.getAddress());
    invoice.setBillToCity(user.getCity());
    invoice.setBillToState(user.getState());
    invoice.setBillToZip(user.getZip());
    invoice.setBillToCountry(user.getCountry());
    invoice.setBillToLocation(user.getLocation());
  }

  private void setExternalBillTo(LoadInvoice invoice, AbUser abUser, AbCompany abCompany) {
    Objects.requireNonNull(abUser);
    Objects.requireNonNull(abCompany);

    invoice.setBillToAbUserId(abUser.getAbUserId());
    invoice.setBillToFirstName(abUser.getFirstName());
    invoice.setBillToLastName(abUser.getLastName());
    invoice.setBillToEmail(abUser.getEmail());
    invoice.setBillToPhone(abUser.getPhone1());

    invoice.setBillToAbCompanyId(abCompany.getAbCompanyId());
    invoice.setBillToCompanyName(abCompany.getCompanyName());
    invoice.setBillToAddress(abCompany.getAddress());
    invoice.setBillToCity(abCompany.getCity());
    invoice.setBillToState(abCompany.getState());
    invoice.setBillToZip(abCompany.getZip());
    invoice.setBillToCountry(abCompany.getCountry());
    invoice.setBillToLocation(abCompany.getLocation());

    if (abUser.getBlUser() != null) {
      invoice.setBillToUser(abUser.getBlUser());
      invoice.setBillToUserCompany(abUser.getBlUser().getUserCompany());
      if (!isEmpty(abUser.getBlUser().getAccountingEmail())) {
        invoice.setBillToEmail(abUser.getBlUser().getAccountingEmail());
      }
    }
  }

  public void delete(final LoadInvoice loadInvoice, boolean moveToReadyToInvoice) {
    if (Boolean.TRUE.equals(loadInvoice.getDeleted())) {
      return;
    }
    Integer userId = UserUtil.getUserId().orElse(loadInvoice.getUser().getUserId());

    loadInvoice.setDeleted(true);
    final Instant now = Instant.now();
    loadInvoice.setDeletedDate(now);
    loadInvoice.setDeletedByUserId(userId);
    loadInvoice.setModifiedDate(now);
    loadInvoice.getLoadInvoiceItems().forEach(item -> {
      item.setDeleted(true);
      item.setDeletedDate(now);
      item.setDeletedByUserId(userId);
      Assignment assignment = item.getLoadAssignment();
      if (nonNull(assignment)) {
        assignment.setLoadInvoiceId(null);
        assignment.setModifiedDate(now);

        if (moveToReadyToInvoice) {
          assignment.setAutoInvoice(false);
          assignment.setReadyToInvoice(true);
        }

      }
    });

  }
}
