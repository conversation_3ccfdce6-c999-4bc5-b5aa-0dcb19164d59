package com.bulkloads.web.loadinvoice.service;

import static com.bulkloads.common.StringUtil.currentDateTime;
import static com.bulkloads.common.UserUtil.getUserCompanyIdOrThrow;
import static com.bulkloads.config.AppConstants.AssignmentStatus.COMPLETED;
import static com.bulkloads.config.AppConstants.AssignmentStatus.DELIVERED;
import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_CREATE;
import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_REBUNDLE;
import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_REGENERATE;
import static com.bulkloads.config.AppConstants.Paths.TEMP;
import static com.bulkloads.web.aws.service.AmazonS3Service.INVOICES;
import static com.bulkloads.web.loadinvoice.util.InvoiceUtils.buildInvoiceDocumentFileName;
import static com.bulkloads.web.loadinvoice.util.InvoiceUtils.mergePdfAndImageFiles;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.io.IOException;
import java.nio.file.Path;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import com.bulkloads.common.PdfUtils;
import com.bulkloads.common.jpa.JpaUtils;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.common.validation.Result;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.domain.entity.AssignmentFile;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.communication.CommunicationService;
import com.bulkloads.web.communication.domain.CommunicationDetails;
import com.bulkloads.web.communication.domain.CommunicationDetails.CommunicationDetailsBuilder;
import com.bulkloads.web.communication.domain.CommunicationIds;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.service.FileService;
import com.bulkloads.web.file.service.dto.FileResponse;
import com.bulkloads.web.file.service.dto.LocalFileRequest;
import com.bulkloads.web.file.util.FileUtils;
import com.bulkloads.web.infra.email.domain.Attachment;
import com.bulkloads.web.infra.invoice.dto.InvoiceMessageDto;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import com.bulkloads.web.integration.agtrax.AgTraxInternalService;
import com.bulkloads.web.loadinvoice.domain.LoadInvoiceDomainService;
import com.bulkloads.web.loadinvoice.domain.data.LoadInvoiceData;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoice;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoiceItem;
import com.bulkloads.web.loadinvoice.domain.template.LoadInvoiceTemplateModel;
import com.bulkloads.web.loadinvoice.event.LoadInvoiceCreatedEvent;
import com.bulkloads.web.loadinvoice.event.LoadInvoiceDeletedEvent;
import com.bulkloads.web.loadinvoice.event.LoadInvoiceRevisedEvent;
import com.bulkloads.web.loadinvoice.mapper.LoadInvoiceMapper;
import com.bulkloads.web.loadinvoice.repository.LoadInvoiceRepository;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoicePayableByCompanyResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoicePayableResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceReceivableByCompanyResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceReceivableResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceRequest;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceResponse;
import org.apache.logging.log4j.util.Strings;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class LoadInvoiceService {

  public static final boolean PUSH_NOTIFICATIONS_ENABLED = false;

  public static final String GENERAL = "general";
  public static final String LOAD_INVOICE_ID = "load_invoice_id";
  private static final List<String> REGENERATION_INVOICE_DOCUMENT_FIELDS = List.of(
      "billToAbCompany",
      "billToAbUser",
      "rate",
      "rateType",
      "hauledNotes",
      "hauledDate",
      "billWeight",
      "billVolume",
      "billMiles",
      "billHours",
      "billQuantity",
      "billSubtotal",
      "billSurcharges"
  );
  private final LoadInvoiceRepository loadInvoiceRepository;
  private final LoadInvoiceTemplateBuilder loadInvoiceTemplateService;
  private final LoadInvoiceDomainService loadInvoiceDomainService;
  private final LoadInvoiceMapper loadInvoiceMapper;
  private final AssignmentRepository assignmentRepository;
  private final CommunicationService communicationService;
  private final FileService fileService;
  private final FileRepository fileRepository;
  private final AgTraxInternalService agTraxInternalService;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final MessageQueueSender queueSender;
  private final AppProperties appProperties;
  private final JpaUtils jpaUtils;

  @Transactional
  public LoadInvoiceResponse create(final @Valid LoadInvoiceRequest loadInvoiceRequest) {

    final Map<String, String> errors = new HashMap<>();
    LoadInvoiceData data = loadInvoiceMapper.requestToDataLocking(loadInvoiceRequest, errors);

    if (!errors.isEmpty()) {
      throw new ValidationException(errors);
    }

    Result<LoadInvoice> result = loadInvoiceDomainService.create(data);
    LoadInvoice loadInvoice = loadInvoiceRepository.save(result.orElseThrow());

    final LoadInvoiceCreateDocumentResult invoiceCreateDocumentResult;
    try {
      invoiceCreateDocumentResult = createLoadInvoiceDocument(loadInvoice);
    } catch (IOException e) {
      log.error("Error creating invoice document", e);
      throw new BulkloadsException("Error creating invoice document");
    }

    loadInvoice = loadInvoiceRepository.save(result.orElseThrow());

    List<Integer> loadAssignmentIds = loadInvoice.getLoadInvoiceItems().stream()
        .map(e -> e.getLoadAssignment().getLoadAssignmentId())
        .toList();

    final Integer revisedFromLoadInvoiceId = loadInvoiceRequest.getRevisedFromLoadInvoiceId();
    if (isNull(revisedFromLoadInvoiceId)) {
      loadInvoice.registerDomainEvent(new LoadInvoiceCreatedEvent(
          loadInvoice.getLoadInvoiceId(),
          loadInvoice.getFile().getFileUrl(),
          loadAssignmentIds
      ));
    } else {
      loadInvoice.registerDomainEvent(new LoadInvoiceRevisedEvent(
          loadInvoice.getLoadInvoiceId(),
          loadInvoice.getFile().getFileUrl(),
          loadAssignmentIds,
          revisedFromLoadInvoiceId
      ));
    }

    loadInvoice = loadInvoiceRepository.save(loadInvoice);

    if (data.isSendInvoice()) {
      sendLoadInvoice(loadInvoice.getLoadInvoiceId(), "", false);
    }

    agTraxInternalService.repostScaleTicketsByLoadAssignmentIdsAsync(loadAssignmentIds);

    return LoadInvoiceResponse.builder()
        .loadInvoiceId(loadInvoice.getLoadInvoiceId())
        .loadInvoiceFileId(invoiceCreateDocumentResult.getLoadInvoiceFileId())
        .loadInvoiceFileUrl(invoiceCreateDocumentResult.getInvoiceFileUrl())
        .build();
  }

  public void createInvoiceAsync(final int loadAssignmentId) {
    sendInvoiceMessage(LOAD_INVOICE_CREATE, loadAssignmentId, null);
  }

  @Transactional
  public LoadInvoiceResponse revise(@Valid final int loadInvoiceId) {
    // get the invoice using the logged in user's company id
    final int userCompanyId = getUserCompanyIdOrThrow();
    final LoadInvoice loadInvoice = loadInvoiceRepository.findByLoadInvoiceIdAndUserCompanyUserCompanyId(loadInvoiceId, userCompanyId)
        .orElseThrow(() -> new ValidationException(LOAD_INVOICE_ID, "You don't own this load invoice"));

    List<Assignment> bookings = assignmentRepository.findByLoadInvoiceId(loadInvoiceId);

    String loadAssignmentIds = bookings.stream()
        .map(Assignment::getLoadAssignmentId)
        .map(String::valueOf)
        .collect(Collectors.joining(","));

    LoadInvoiceRequest loadInvoiceRequest = LoadInvoiceRequest.builder()
        .loadAssignmentIds(loadAssignmentIds)
        .revisedFromLoadInvoiceId(loadInvoiceId)
        .sendInvoice(bookings.get(0).getAutoInvoice())
        .build();

    loadInvoiceDomainService.delete(loadInvoice, false);

    return create(loadInvoiceRequest);

  }

  public void reviseAsync(final int loadAssignmentId, final int loadInvoiceId) {
    sendInvoiceMessage(LOAD_INVOICE_REGENERATE, loadAssignmentId, loadInvoiceId);
  }

  @Transactional
  public LoadInvoiceResponse rebundle(@Valid final int loadInvoiceId) {

    final LoadInvoice loadInvoice = loadInvoiceRepository.getReferenceById(loadInvoiceId);

    final LoadInvoiceCreateDocumentResult invoiceCreateDocumentResult;
    try {
      invoiceCreateDocumentResult = createLoadInvoiceDocument(loadInvoice);
    } catch (IOException e) {
      log.error("Error creating invoice document", e);
      throw new BulkloadsException("Error creating invoice document");
    }

    loadInvoiceRepository.save(loadInvoice);

    List<Integer> loadAssignmentIds = loadInvoice.getLoadInvoiceItems().stream()
        .map(e -> e.getLoadAssignment().getLoadAssignmentId())
        .toList();

    loadInvoice.registerDomainEvent(new LoadInvoiceCreatedEvent(
        loadInvoice.getLoadInvoiceId(),
        loadInvoice.getFile().getFileUrl(),
        loadAssignmentIds
    ));

    loadInvoiceRepository.save(loadInvoice);

    if (nonNull(loadInvoice.getEmailQueueId())) {
      sendLoadInvoice(loadInvoiceId, "Resending Invoice with updated Supporting Documents", false);
    }

    agTraxInternalService.repostScaleTicketsByLoadAssignmentIdsAsync(loadAssignmentIds);

    return LoadInvoiceResponse.builder()
        .loadInvoiceId(loadInvoice.getLoadInvoiceId())
        .loadInvoiceFileId(invoiceCreateDocumentResult.getLoadInvoiceFileId())
        .loadInvoiceFileUrl(invoiceCreateDocumentResult.getInvoiceFileUrl())
        .build();
  }

  public void rebundleAsync(final int loadAssignmentId, final int loadInvoiceId) {
    sendInvoiceMessage(LOAD_INVOICE_REBUNDLE, loadAssignmentId, loadInvoiceId);
  }

  @Transactional
  public void delete(final int loadInvoiceId, boolean sendEmail) {
    final int userCompanyId = getUserCompanyIdOrThrow();
    final LoadInvoice loadInvoice = loadInvoiceRepository.findByLoadInvoiceIdAndUserCompanyUserCompanyId(loadInvoiceId, userCompanyId)
        .orElseThrow(() -> new ValidationException(LOAD_INVOICE_ID, "Invoice %s not found".formatted(loadInvoiceId)));

    List<Integer> loadAssignmentIds = loadInvoice.getLoadInvoiceItems().stream()
        .map(e -> e.getLoadAssignment().getLoadAssignmentId())
        .distinct()
        .toList();

    loadInvoiceDomainService.delete(loadInvoice, sendEmail);

    applicationEventPublisher.publishEvent(new LoadInvoiceDeletedEvent(
        loadInvoice.getLoadInvoiceId(),
        loadInvoice.getFile().getFileUrl(),
        loadAssignmentIds));

    loadInvoiceRepository.save(loadInvoice);

    if (sendEmail && nonNull(loadInvoice.getEmailQueueId())) {
      sendLoadInvoice(loadInvoiceId, "Invoice Voided", true);
    }

  }

  public String preview(final @Valid LoadInvoiceRequest loadInvoiceRequest) {

    final Map<String, String> errors = new HashMap<>();
    LoadInvoiceData data = loadInvoiceMapper.requestToData(loadInvoiceRequest, errors);

    if (!errors.isEmpty()) {
      throw new ValidationException(errors);
    }

    Result<LoadInvoice> result = loadInvoiceDomainService.create(data);
    LoadInvoice loadInvoice = result.orElseThrow();

    final LoadInvoiceTemplateModel model = loadInvoiceMapper.invoiceToFmModel(loadInvoice);
    String content = loadInvoiceTemplateService.getLoadInvoiceTemplate(model, "", "", false);

    // rollback
    loadInvoiceRepository.delete(loadInvoice);

    return content;
  }

  public String handleBookingUpdated(final Assignment booking) {
    final int firstAssignmentId = booking.getLoadAssignmentId();

    final Integer loadInvoiceId = booking.getLoadInvoiceId();
    final String assignmentStatus = booking.getAssignmentStatus();
    final Map<String, Object> dirtyFields = jpaUtils.findDirtyFields(booking);
    final boolean shouldReGenerateInvoice = shouldReGenerateInvoice(booking, dirtyFields);
    final boolean alteredFiles = dirtyFields.containsKey("totalFilesSize");
    final boolean statusDeliveredAndCompleted = List.of(DELIVERED, COMPLETED).contains(assignmentStatus);
    final boolean shouldReBundleInvoice = !shouldReGenerateInvoice && statusDeliveredAndCompleted
        && nonNull(booking.getHiringAbCompany()) && nonNull(loadInvoiceId) && alteredFiles;

    String message = "";

    if (shouldReGenerateInvoice) {
      log.info("shouldReGenerateInvoice", loadInvoiceId);
      reviseAsync(firstAssignmentId, loadInvoiceId);
      if (Boolean.TRUE.equals(booking.getAutoInvoice())) {
        message = "(revised invoice sent)";
      } else {
        message = "(revised invoice created)";
      }
    } else if (shouldReBundleInvoice) {
      log.info("shouldReBundleInvoice", loadInvoiceId);
      rebundleAsync(firstAssignmentId, loadInvoiceId);
      message = "(revised documents sent)";
    } else if (shouldCreateInvoice(booking, statusDeliveredAndCompleted)) {
      log.info("shouldCreateInvoice", loadInvoiceId);
      createInvoiceAsync(firstAssignmentId);
    }

    return message;
  }

  private boolean shouldCreateInvoice(final Assignment booking, final boolean statusDeliveredAndCompleted) {
    return isNull(booking.getLoadInvoiceId())
        && booking.getAutoInvoice()
        && !booking.getReadyToInvoice()
        && booking.getToUserCompany().getUseTmsForInvoices()
        && nonNull(booking.getBillTotal())
        && statusDeliveredAndCompleted;
  }

  private boolean shouldReGenerateInvoice(final Assignment assignment, final Map<String, Object> dirtyFields) {
    return nonNull(assignment.getLoadInvoiceId()) && REGENERATION_INVOICE_DOCUMENT_FIELDS.stream()
        .anyMatch(dirtyFields::containsKey);
  }

  private void sendInvoiceMessage(final String action,
                                  final int loadAssignmentId,
                                  final Integer loadInvoiceId) {

    final InvoiceMessageDto dto = InvoiceMessageDto.builder()
        .action(action)
        .loadAssignmentId(loadAssignmentId)
        .loadInvoiceId(loadInvoiceId).build();

    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
      @Override
      public void afterCommit() {
        queueSender.send(appProperties.getInvoice().getQueueName(), dto);
      }
    });
  }

  private Path getBaseInvoiceDocument(LoadInvoice loadInvoice) {

    String header = currentDateTime("MMM d, yyyy");
    String footer = "This invoice was generated using the BulkLoads.com Mobile App.  Visit BulkTMS.com for more details.";

    Path invoiceDocumentFilePath = TEMP.resolve(buildInvoiceDocumentFileName(loadInvoice));

    final LoadInvoiceTemplateModel model = loadInvoiceMapper.invoiceToFmModel(loadInvoice);
    String content = loadInvoiceTemplateService.getLoadInvoiceTemplate(model, header, footer, false);

    PdfUtils.htmlToPdf(content, invoiceDocumentFilePath);
    return invoiceDocumentFilePath;
  }

  LoadInvoiceCreateDocumentResult createLoadInvoiceDocument(LoadInvoice loadInvoice) throws IOException {

    Objects.requireNonNull(loadInvoice);
    Objects.requireNonNull(loadInvoice.getLoadInvoiceId());

    Path invoiceDocumentFilePath = getBaseInvoiceDocument(loadInvoice);

    List<InvoicePart> invoiceParts = loadInvoice.getLoadInvoiceItems()
        .stream()
        .filter(item -> !item.getIsSurcharge())
        .map(LoadInvoiceItem::getLoadAssignment)
        .flatMap(assignment -> assignment.getAssignmentFiles().stream())
        .map(assignmentFile -> InvoicePart.of(assignmentFile))
        .toList();

    if (!invoiceParts.isEmpty()) {
      mergePdfAndImageFiles(invoiceDocumentFilePath, invoiceParts);
    }

    LocalFileRequest localFileRequest = LocalFileRequest.builder()
        .localPath(invoiceDocumentFilePath)
        .caption(Strings.EMPTY)
        .build();

    FileResponse fileResponse = fileService.createFromLocalFile(localFileRequest, false, INVOICES);

    File invoiceDocumentFile = fileRepository.findById(fileResponse.getFileId())
        .orElseThrow(() -> new BulkloadsException("File not found"));

    loadInvoice.setFile(invoiceDocumentFile);
    FileUtils.deleteFileIfExists(invoiceDocumentFilePath);

    return LoadInvoiceCreateDocumentResult.builder()
        .loadInvoiceFileId(loadInvoice.getFile().getFileId())
        .invoiceFileUrl(fileResponse.getFileUrl())
        .build();
  }

  public List<LoadInvoicePayableByCompanyResponse> getLoadInvoicePayablesByCompany(
      Boolean archived, QueryParams queryParams, int skip, int limit) {

    int cId = getUserCompanyIdOrThrow();
    return loadInvoiceRepository.getLoadInvoicePayablesByCompany(cId, archived, queryParams, skip, limit);
  }

  public List<LoadInvoicePayableResponse> getLoadInvoicePayables(
      Boolean archived, Integer userCompanyId, Integer userId, int skip, int limit, QueryParams queryParams) {

    int cId = getUserCompanyIdOrThrow();
    return loadInvoiceRepository.getLoadInvoicePayables(cId, archived, userCompanyId, userId, queryParams, skip, limit);
  }

  public List<LoadInvoiceReceivableByCompanyResponse> getLoadInvoiceReceivablesByCompany(
      Boolean archived, QueryParams queryParams, int skip, int limit) {

    int cId = getUserCompanyIdOrThrow();
    return loadInvoiceRepository.getLoadInvoiceReceivablesByCompany(cId, archived, queryParams, skip, limit);
  }

  public List<LoadInvoiceReceivableResponse> getLoadInvoiceReceivables(
      Boolean archived, Integer billToUserCompanyId, Integer billToAbCompanyId,
      QueryParams queryParams, int skip, int limit) {

    int cId = getUserCompanyIdOrThrow();
    return loadInvoiceRepository.getLoadInvoiceReceivables(cId, archived, billToUserCompanyId, billToAbCompanyId,
        queryParams, skip, limit);
  }

  @Transactional
  public void sendLoadInvoice(int loadInvoiceId, String message, boolean isVoid) {

    LoadInvoice invoice = loadInvoiceRepository.findById(loadInvoiceId)
        .orElseThrow(() -> new ValidationException(GENERAL, "You don't own this load invoice"));

    if (invoice.getBillToAbUserId() == null && invoice.getBillToUser() == null) {
      throw new ValidationException(GENERAL, "No bill to user found");
    }

    LoadInvoiceTemplateModel model = loadInvoiceMapper.invoiceToFmModel(invoice);

    CommunicationDetailsBuilder details = CommunicationDetails.builder();
    details
        .fromUserId(invoice.getUser().getUserId())
        .emailCategoryId(20)
        .emailTitle(loadInvoiceTemplateService.getLoadInvoiceEmailNotificationTitle(model, isVoid))
        .emailMessage(loadInvoiceTemplateService.getLoadInvoiceEmailContent(model, message, isVoid))
        .sendEmailCopyToSender(true)
        .isAccounting(true)
        .emailBcc("<EMAIL>");

    if (invoice.getBillToAbUserId() != null) {
      details.toAbUserId(invoice.getBillToAbUserId());
    } else {
      details.toUserId(invoice.getBillToUser().getUserId());
    }

    List<Attachment> attachments = new ArrayList<>();

    File invoiceFile = invoice.getFile();

    if (invoiceFile == null) {
      throw new BulkloadsException("Invoice file not found");
    }

    if (invoiceFile.getSize() < 9_800_000) {
      attachments.add(Attachment.builder()
          .filename(invoiceFile.getFilename())
          .url(invoiceFile.getFileUrl())
          .build());
      attachments.add(new Attachment(invoiceFile.getFilename(), invoiceFile.getFileUrl()));
    }

    invoice.getLoadInvoiceItems()
        .stream()
        .filter(item -> !item.getIsSurcharge()) // only non-surcharge items have files
        .flatMap(item -> item.getLoadAssignment().getAssignmentFiles().stream())
        .map(AssignmentFile::getFile)
        .filter(f -> !f.getIsImage() && !"pdf".equalsIgnoreCase(f.getExtension()))
        .map(f -> new Attachment(f.getFilename(), f.getFileUrl()))
        .forEach(attachments::add);

    details.attachments(attachments);

    String notificationTitle = loadInvoiceTemplateService.getLoadInvoiceEmailNotificationTitle(model, isVoid);
    String notificationContent = loadInvoiceTemplateService.getLoadInvoiceNotificationContent(model, message, isVoid);

    if (PUSH_NOTIFICATIONS_ENABLED) {

      Map<String, Object> pushData = new HashMap<>();
      pushData.put("notification_type", "Load Assignments");
      pushData.put("notification_subtype", "Invoices");
      pushData.put("confirmation_file_id", null);
      pushData.put("load_assignment_ids", null);
      pushData.put(LOAD_INVOICE_ID, loadInvoiceId);
      details.pushData(pushData);

      details.pushTitle(notificationTitle);
      details.pushMessage(notificationContent);
    }

    Optional<CommunicationIds> communicationIds;

    if (invoice.getBillToAbUserId() != null) {
      communicationIds = communicationService.sendToAbUser(details.build());
    } else {
      communicationIds = communicationService.sendToUser(details.build());
    }

    Instant now = Instant.now();
    communicationIds
        .flatMap(CommunicationIds::getEmailQueueId)
        .ifPresent(emailQueueId -> {
          invoice.setEmailQueueId(emailQueueId);
          invoice.setEmailStatus("Sent");
          invoice.setEmailStatusDate(now);
          invoice.setModifiedDate(now);
        });
  }

}
