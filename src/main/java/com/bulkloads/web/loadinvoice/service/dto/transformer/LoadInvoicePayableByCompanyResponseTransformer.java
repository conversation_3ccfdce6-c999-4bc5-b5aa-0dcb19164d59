package com.bulkloads.web.loadinvoice.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoicePayableByCompanyResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadInvoicePayableByCompanyResponseTransformer implements TupleTransformer<LoadInvoicePayableByCompanyResponse> {

  @Override
  public LoadInvoicePayableByCompanyResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return LoadInvoicePayableByCompanyResponse.builder()
        .userCompanyId(parts.asInteger("user_company_id"))
        .userId(parts.asInteger("user_id"))
        .companyName(parts.asString("company_name"))
        .billAmount(parts.asBigDecimal("bill_amount"))
        .loadInvoiceIds(parts.asString("load_invoice_ids"))
        .payment(parts.asBigDecimal("payment"))
        .paid(parts.asBoolean("paid"))
        .build();
  }
}
