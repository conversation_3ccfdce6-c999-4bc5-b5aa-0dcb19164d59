package com.bulkloads.web.commodity.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.time.Instant;
import java.util.List;
import com.bulkloads.web.commodity.service.CommodityService;
import com.bulkloads.web.commodity.service.dto.CommodityResponse;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

// sort the endpoint Tags alphabetically

@Slf4j
@RestController
@RequestMapping(path = "/rest/commodities", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Commodities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class CommodityQueryController {

  private final CommodityService commodityService;

  @Operation(summary = "Get your company's commodities")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping
  public List<CommodityResponse> getCommodities(
      @Parameter(description = "The search term.")
      @RequestParam(value = "term", required = false) String term,
      @RequestParam(value = "commodity_id", required = false) Integer commodityId,
      @Parameter(description = "Records that were edited after this date. UTC datetime in ISO8601 "
                               + "format e.g. 2022-05-03T18:31:38.480Z")
      @RequestParam(value = "last_modified_date", required = false) Instant lastModifiedDate,
      @Parameter(description = "When syncing records with last_modified_date you may want to pass "
                               + "include_deleted=1 to get past records that were deleted after the last sync")
      @RequestParam(value = "include_deleted", required = false) boolean includeDeleted) {
    return commodityService.getCommodities(term, commodityId, lastModifiedDate, includeDeleted);
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/{commodity_id}")
  public CommodityResponse getCommodities(@PathVariable("commodity_id") Integer commodityId) {
    return commodityService.getCommodity(commodityId);
  }

}
