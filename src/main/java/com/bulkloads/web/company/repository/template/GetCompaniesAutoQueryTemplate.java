package com.bulkloads.web.company.repository.template;

import org.intellij.lang.annotations.Language;

public class GetCompaniesAutoQueryTemplate {

  @Language("SQL")
  public static final String GET_COMPANIES_AUTO_QUERY_TEMPLATE = """
      <% params.put("searchTerm", searchTerm) %>
      SELECT
          c.user_company_id,
          c.company_name,
          MATCH(company_name) AGAINST(:searchTerm IN BOOLEAN MODE) as score
      FROM user_company c
          INNER JOIN bl_user_company_settings cs ON c.user_company_id = cs.user_company_id
          INNER JOIN bl_user_settings us ON c.company_owner_id = us.user_id
      WHERE c.merged_to IS NULL
          AND us.show_in_company_finder = 1
          AND us.app_mode != 'driver'
          AND cs.deletion_date IS NULL
          AND MATCH(company_name) AGAINST(:searchTerm IN BOOLEAN MODE)
      ORDER BY score DESC
      LIMIT 15
      """;
}