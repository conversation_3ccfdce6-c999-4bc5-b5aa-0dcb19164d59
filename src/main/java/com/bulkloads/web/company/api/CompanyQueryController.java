package com.bulkloads.web.company.api;

import java.util.List;
import com.bulkloads.web.company.api.dto.CompanyAutoResponse;
import com.bulkloads.web.company.service.CompanyService;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/companies")
@Tag(name = "Companies")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class CompanyQueryController {

  private final CompanyService companyService;

  @Operation(summary = "Get companies for autosuggest")
  @GetMapping("/auto")
  public List<CompanyAutoResponse> getCompaniesAuto(
      @Parameter(description = "The search term")
      @RequestParam(value = "term", required = false) String term) {

    if (term == null || term.isEmpty()) {
      return List.of();
    }

    return companyService.getCompaniesAuto(term);
  }
}