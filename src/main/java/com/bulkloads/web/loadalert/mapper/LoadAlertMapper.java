package com.bulkloads.web.loadalert.mapper;

import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.loadalert.domain.data.LoadAlertData;
import com.bulkloads.web.loadalert.domain.entity.LoadAlert;
import com.bulkloads.web.loadalert.service.dto.LoadAlertRequest;
import com.bulkloads.web.loadalert.service.dto.LoadAlertResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
        uses = CommonMapper.class)
public abstract class LoadAlertMapper {

  public abstract LoadAlertData requestToData(final LoadAlertRequest request);

  // exclude the product
  @Mapping(target = "product", ignore = true)
  public abstract void dataToEntity(final LoadAlertData data, @MappingTarget final LoadAlert loadAlerts);

  public abstract LoadAlertResponse entityToResponse(final LoadAlert entity);

}
