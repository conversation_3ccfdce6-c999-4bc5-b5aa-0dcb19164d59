package com.bulkloads.web.loadalert.repository;

import java.util.Optional;
import com.bulkloads.web.loadalert.domain.entity.LoadAlert;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LoadAlertRepository extends JpaRepository<LoadAlert, Integer>, LoadAlertQueryRepository {

  LoadAlert findByLoadAlertId(final Integer loadAlertId);

  Optional<LoadAlert> findByLoadAlertIdAndUserUserId(final Integer loadAlertId,final Integer userId);

}