package com.bulkloads.web.loadalert.repository;

import java.util.List;
import com.bulkloads.web.loadalert.domain.entity.LoadAlert;
import com.bulkloads.web.loadalert.service.dto.LoadAlertResponse;
import com.bulkloads.web.loadalert.service.dto.LoadAlertSearchRequest;

public interface LoadAlertQueryRepository {

  List<LoadAlertResponse> getLoadAlerts(
      int userId,
      LoadAlertSearchRequest loadAlertSearchRequest
  );

  List<LoadAlertResponse> findDuplicate(
      LoadAlert loadAlert
  );
}
