package com.bulkloads.web.loadalert.api;

import java.util.List;
import com.bulkloads.web.loadalert.service.LoadAlertService;
import com.bulkloads.web.loadalert.service.dto.LoadAlertResponse;
import com.bulkloads.web.loadalert.service.dto.LoadAlertSearchRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@Tag(name = "Loads Alerts")
@RequiredArgsConstructor
@RequestMapping("/rest/loads/alerts")
public class LoadAlertQueryController {

  private final LoadAlertService loadAlertService;


  @GetMapping(value = "")
  public List<LoadAlertResponse> getLoadAlerts(
      @RequestParam(value = "active", required = false, defaultValue = "") Boolean active,
      @RequestParam(value = "origin_country", required = false, defaultValue = "") String originCountry,
      @RequestParam(value = "origin_state", required = false, defaultValue = "") String originState,
      @RequestParam(value = "origin_city", required = false, defaultValue = "") String originCity,
      @RequestParam(value = "origin_zip", required = false, defaultValue = "") String originZip,
      @RequestParam(value = "origin_lat", required = false) Double originLat,
      @RequestParam(value = "origin_long", required = false) Double originLong,
      @RequestParam(value = "origin_radius", required = false) Integer originRadius,
      @RequestParam(value = "destination_country", required = false, defaultValue = "") String destinationCountry,
      @RequestParam(value = "destination_state", required = false, defaultValue = "") String destinationState,
      @RequestParam(value = "destination_city", required = false, defaultValue = "") String destinationCity,
      @RequestParam(value = "destination_zip", required = false, defaultValue = "") String destinationZip,
      @RequestParam(value = "destination_lat", required = false) Double destinationLat,
      @RequestParam(value = "destination_long", required = false) Double destinationLong,
      @RequestParam(value = "destination_radius", required = false) Integer destinationRadius,
      @RequestParam(value = "user_company_ids", required = false, defaultValue = "") String userCompanyIds,
      @RequestParam(value = "equipment_ids", required = false, defaultValue = "") String equipmentIds,
      @RequestParam(value = "product", required = false, defaultValue = "") String product
  ) {

    LoadAlertSearchRequest loadAlertSearchRequest = LoadAlertSearchRequest.builder()
        .active(active)
        .originCountry(originCountry)
        .originState(originState)
        .originCity(originCity)
        .originZip(originZip)
        .originLat(originLat)
        .originLong(originLong)
        .originRadius(originRadius)
        .destinationCountry(destinationCountry)
        .destinationState(destinationState)
        .destinationCity(destinationCity)
        .destinationZip(destinationZip)
        .destinationLat(destinationLat)
        .destinationLong(destinationLong)
        .destinationRadius(destinationRadius)
        .userCompanyIds(userCompanyIds)
        .equipmentIds(equipmentIds)
        .product(product)
        .build();

    return loadAlertService.getLoadAlerts(loadAlertSearchRequest);
  }
}
