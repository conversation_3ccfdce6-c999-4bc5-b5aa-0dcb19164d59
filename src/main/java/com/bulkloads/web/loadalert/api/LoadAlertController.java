package com.bulkloads.web.loadalert.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;

import com.bulkloads.web.loadalert.service.LoadAlertService;
import com.bulkloads.web.loadalert.service.dto.LoadAlertRequest;
import com.bulkloads.web.loadalert.service.dto.LoadAlertResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.Value;


@RestController
@Tag(name = "Load Alerts")
@RequiredArgsConstructor
@RequestMapping("/rest/loads/alerts")
public class LoadAlertController {

  private final LoadAlertService loadAlertService;

  @Operation(summary = "Create a new load alert")
  @PostMapping(value = "")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public LoadAlertController.LoadAlertApiResponse createLoadAlert(@RequestBody LoadAlertRequest loadAlertRequest) {
    LoadAlertResponse loadAlertResponse = loadAlertService.createLoadAlert(loadAlertRequest);

    return LoadAlertController.LoadAlertApiResponse.builder()
            .message("Load alert created")
            .key(loadAlertResponse.getLoadAlertId())
            .loadAlertId(loadAlertResponse.getLoadAlertId())
            .data(loadAlertResponse)
            .build();
  }

  @Operation(summary = "Update load alert details")
  @PutMapping(value = "/{loadAlertId}")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public LoadAlertController.LoadAlertApiResponse updateLoadAlert(
          @PathVariable("loadAlertId")
          Integer loadAlertId,
          @RequestBody LoadAlertRequest loadAlertRequest) {
    LoadAlertResponse loadAlertResponse = loadAlertService.updateLoadAlert(loadAlertId, loadAlertRequest);

    return LoadAlertController.LoadAlertApiResponse.builder()
            .key(loadAlertResponse.getLoadAlertId())
            .data(loadAlertResponse)
            .message("Load alert updated")
            .build();
  }

  @Operation(summary = "Delete Load Alert")
  @DeleteMapping("/{loadAlertId}")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public LoadAlertController.LoadAlertApiResponse deleteLoadAlert(@PathVariable Integer loadAlertId) {
    loadAlertService.deleteLoadAlert(loadAlertId);
    return LoadAlertController.LoadAlertApiResponse.builder()
            .message("Load alert deleted")
            .build();
  }


  @Operation(summary = "Activate Load Alert")
  @PostMapping(value = "/{load_alert_id}/activate")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public LoadAlertController.LoadAlertApiResponse activateLoadAlert(@PathVariable("load_alert_id") Integer loadAlertId) {

    loadAlertService.activateLoadAlert(loadAlertId);

    return LoadAlertController.LoadAlertApiResponse.builder()
            .message("Load alert activated")
            .build();
  }

  @Operation(summary = "deactivate Load Alert")
  @PostMapping(value = "/{load_alert_id}/deactivate")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public LoadAlertController.LoadAlertApiResponse deactivateLoadAlert(@PathVariable("load_alert_id") Integer loadAlertId) {

    loadAlertService.deactivateLoadAlert(loadAlertId);

    return LoadAlertController.LoadAlertApiResponse.builder()
            .message("Load alert deactivated")
            .build();
  }



  @Value
  @Builder
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class LoadAlertApiResponse {

    @Schema(name = "key", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String message;
    @Schema(name = "load_alert_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    Integer loadAlertId;
    @Schema(name = "key", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    Integer key;
    @Schema(name = "data", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    LoadAlertResponse data;
  }
}
