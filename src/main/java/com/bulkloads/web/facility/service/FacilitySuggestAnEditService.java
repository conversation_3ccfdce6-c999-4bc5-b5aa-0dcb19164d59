package com.bulkloads.web.facility.service;

import java.time.Instant;
import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.facility.domain.entity.FacilitySuggestAnEdit;
import com.bulkloads.web.facility.repository.FacilityRepository;
import com.bulkloads.web.facility.repository.FacilitySuggestAnEditRepository;
import com.bulkloads.web.facility.service.dto.FacilitySuggestAnEditRequest;
import com.bulkloads.web.facility.service.dto.FacilitySuggestAnEditResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class FacilitySuggestAnEditService {

  private final FacilitySuggestAnEditRepository facilitySuggestAnEditRepository;
  private final FacilityRepository facilityRepository;

  public List<FacilitySuggestAnEditResponse> findAll() {
    return facilitySuggestAnEditRepository.findByDeletedFalse().stream().map(this::mapToResponse).toList();
  }

  @Transactional
  public int create(final int facilityId, final FacilitySuggestAnEditRequest request) {
    facilityRepository.findById(facilityId)
        .orElseThrow(() -> new ValidationException("facility_id", "Could not find facility with id %s".formatted(facilityId)));

    FacilitySuggestAnEdit entity = new FacilitySuggestAnEdit();
    entity.setFacilityId(facilityId);
    entity.setUserId(UserUtil.getUserIdOrThrow());
    entity.setComment(request.getComment());
    entity.setDateAdded(Instant.now());
    entity.setDeleted(false);

    facilitySuggestAnEditRepository.save(entity);
    return entity.getFacilitySuggestAnEditId();
  }

  @Transactional
  public void delete(final int facilityId, final int suggestEditId) {
    FacilitySuggestAnEdit entity = facilitySuggestAnEditRepository.findByFacilitySuggestAnEditIdAndFacilityIdAndDeletedFalse(suggestEditId, facilityId)
        .orElseThrow(() -> new ValidationException("suggest_edit_id", "Could not find suggestion with id %s".formatted(suggestEditId)));

    entity.setDeleted(true);
    facilitySuggestAnEditRepository.save(entity);
  }

  private FacilitySuggestAnEditResponse mapToResponse(FacilitySuggestAnEdit entity) {
    FacilitySuggestAnEditResponse response = new FacilitySuggestAnEditResponse();
    response.setFacilitySuggestAnEditId(entity.getFacilitySuggestAnEditId());
    response.setUserId(entity.getUserId());
    response.setFacilityId(entity.getFacilityId());
    response.setComment(entity.getComment());
    response.setDateAdded(entity.getDateAdded());
    return response;
  }
}