package com.bulkloads.web.facility.service;

import java.time.Instant;
import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.city.domain.entity.City;
import com.bulkloads.web.city.repository.CityRepository;
import com.bulkloads.web.facility.domain.FacilityDomainService;
import com.bulkloads.web.facility.domain.data.FacilityData;
import com.bulkloads.web.facility.domain.entity.Facility;
import com.bulkloads.web.facility.domain.entity.FacilitySearch;
import com.bulkloads.web.facility.mapper.FacilityMapper;
import com.bulkloads.web.facility.repository.FacilityListProjection;
import com.bulkloads.web.facility.repository.FacilityProjection;
import com.bulkloads.web.facility.repository.FacilityRepository;
import com.bulkloads.web.facility.repository.FacilitySearchRepository;
import com.bulkloads.web.facility.service.dto.FacilityListResponse;
import com.bulkloads.web.facility.service.dto.FacilityRequest;
import com.bulkloads.web.facility.service.dto.FacilityResponse;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.repository.FileRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class FacilityService {

  private final FacilityDomainService facilityDomainService;
  private final FacilityRepository facilityRepository;
  private final FacilityMapper facilityMapper;
  private final FacilitySearchRepository facilitySearchRepository;
  private final CityRepository cityRepository;
  private final FileRepository fileRepository;

  @Transactional
  public int create(final FacilityRequest request) {
    final FacilityData facilityData = facilityMapper.requestToData(request);

    final Result<Facility> facilityResult = facilityDomainService.create(facilityData);
    Facility entity = facilityResult.orElseThrow();

    facilityRepository.save(entity);

    if (request.getFileIds() != null && !request.getFileIds().isEmpty()) {
      for (Integer fileId : request.getFileIds()) {
        File file = fileRepository.findById(fileId)
            .orElseThrow(() -> new ValidationException("file_id", "Could not find file with id %s".formatted(fileId)));
        entity.getFiles().add(file);
      }
      facilityRepository.save(entity);
    }

    return entity.getFacilityId();
  }

  @Transactional
  public void update(final int facilityId, final FacilityRequest request) {
    final Facility existingEntity = facilityRepository.findByFacilityIdAndDeletedFalse(facilityId).orElseThrow();
    final FacilityData facilityData = facilityMapper.requestToData(request);

    final Result<Facility> facilityResult =
        facilityDomainService.update(existingEntity, facilityData);
    Facility entity = facilityResult.orElseThrow();

    facilityRepository.save(entity);
  }

  @Transactional
  public void deleteById(final int facilityId) {
    final Facility facility = facilityRepository.findByFacilityIdAndDeletedFalse(facilityId).orElseThrow();
    facilityDomainService.delete(facility);
    facilityRepository.save(facility);
  }

  @Transactional
  public List<FacilityListResponse> findFacilities(final String location, final Double latitude, final Double longitude, Double radius,
                                                   List<Integer> facilityIds, int skip, int limit) {
    Double searchLat = latitude;
    Double searchLong = longitude;
    if (radius == null) {
      radius = 200.0;
    }

    if (location != null && (latitude == null || longitude == null)) {
      // Get coordinates from city repository if location provided but no coordinates
      City city = cityRepository.getCityZip(location);
      if (city == null) {
        throw new ValidationException("location", "Unknown location");
      }
      searchLat = city.getLatitude();
      searchLong = city.getLongitude();
      addToRecentSearches(location);
    }

    boolean hasFacilityIds = facilityIds != null && !facilityIds.isEmpty();

    List<FacilityListProjection> facilities =
        facilityRepository.findFacilities(searchLat, searchLong, radius, null, UserUtil.getUserIdOrThrow(), hasFacilityIds, facilityIds, skip, limit);
    return facilityMapper.mapToListResponse(facilities);
  }

  public FacilityResponse findByFacilityId(final int facilityId) {
    FacilityProjection facility = facilityRepository.findFacilityById(facilityId)
        .orElseThrow(() -> new ValidationException("facility_id", "Could not find id %s".formatted(facilityId)));

    // Increment view count
    facilityRepository.incrementViewCount(facilityId);

    // Map to response without populating the array fields
    FacilityResponse response = facilityMapper.mapToResponse(facility);

    // We're no longer populating these fields as requested
    // response.setWaitTimes(waitTimeResponses);
    // response.setReviews(reviewResponses);
    // response.setFiles(fileResponse);

    return response;
  }

  public List<String> findFacilitySearches() {
    int userId = UserUtil.getUserIdOrThrow();
    List<FacilitySearch> searches = facilitySearchRepository.findTop10ByUserIdOrderBySearchDateDesc(userId);

    return searches.stream().map(FacilitySearch::getLocation).toList();
  }

  private void addToRecentSearches(String location) {
    // Update search entity for location
    final int userId = UserUtil.getUserIdOrThrow();
    FacilitySearch search = facilitySearchRepository.findByUserIdAndLocation(userId, location);
    final Instant now = Instant.now();
    if (search == null) {
      search = new FacilitySearch();
      search.setLocation(location);
      search.setUserId(userId);
      search.setSearchDate(now);
    } else {
      search.setSearchDate(now);
    }

    facilitySearchRepository.save(search);
  }

  public List<FacilityListResponse> findUnapprovedFacilities() {
    List<FacilityListProjection> facilities = facilityRepository.findFacilities(null, null, null, false,
        UserUtil.getUserIdOrThrow(), false, null, 0, 100);
    return facilityMapper.mapToListResponse(facilities);
  }
}
