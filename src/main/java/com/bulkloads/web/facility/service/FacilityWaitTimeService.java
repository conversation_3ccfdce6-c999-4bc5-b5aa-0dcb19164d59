package com.bulkloads.web.facility.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.facility.domain.FacilityWaitTimeDomainService;
import com.bulkloads.web.facility.domain.data.FacilityWaitTimeData;
import com.bulkloads.web.facility.domain.entity.FacilityWaitTime;
import com.bulkloads.web.facility.mapper.FacilityWaitTimeMapper;
import com.bulkloads.web.facility.repository.FacilityRepository;
import com.bulkloads.web.facility.repository.FacilityWaitTimeRepository;
import com.bulkloads.web.facility.service.dto.FacilityWaitTimeRequest;
import com.bulkloads.web.facility.service.dto.FacilityWaitTimeResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class FacilityWaitTimeService {

  private final FacilityWaitTimeDomainService facilityWaitTimeDomainService;
  private final FacilityWaitTimeRepository facilityWaitTimeRepository;
  private final FacilityWaitTimeMapper facilityWaitTimeMapper;
  private final FacilityRepository facilityRepository;

  @Transactional
  public int create(final int facilityId, @Valid final FacilityWaitTimeRequest request) {
    final FacilityWaitTimeData facilityWaitTimeData = mapToData(facilityId, request);

    final Result<FacilityWaitTime> facilityWaitTimeResult = facilityWaitTimeDomainService.create(facilityWaitTimeData);
    FacilityWaitTime entity = facilityWaitTimeResult.orElseThrow();

    facilityWaitTimeRepository.save(entity);
    
    // Flush to ensure the new wait time is committed
    facilityWaitTimeRepository.flush();
    
    // Update facility wait time count and average
    facilityRepository.updateWaitTimeCountAndAvg(facilityId);

    return entity.getFacilityWaitTimeId();
  }

  private FacilityWaitTimeData mapToData(final int facilityId, final FacilityWaitTimeRequest request) {
    final Map<String, String> errors = new HashMap<>();
    final FacilityWaitTimeData facilityWaitTimeData = facilityWaitTimeMapper.requestToData(facilityId, request, errors);

    if (!errors.isEmpty()) {
      throw new ValidationException(errors);
    }
    return facilityWaitTimeData;
  }

  public List<FacilityWaitTimeResponse> findFacilityWaitTimes(int facilityId, int skip, int limit) {
    return facilityWaitTimeRepository.findFacilityWaitTimes(facilityId, skip, limit);
    
    /*

    List<FacilityWaitTime> facilityWaitTimes = facilityWaitTimeRepository.findTop100ByFacilityFacilityId(facilityId);

    List<FacilityWaitTimeResponse> response = facilityWaitTimeMapper.mapToListResponse(facilityWaitTimes);

    return response;
     */
  }

}
