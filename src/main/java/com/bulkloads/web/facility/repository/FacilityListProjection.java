package com.bulkloads.web.facility.repository;

public interface FacilityListProjection {
  // Base facility fields
  Integer getFacilityId();

  String getName();

  String getLocation();

  String getCity();

  String getState();

  String getAddress();

  String getCountry();

  String getLatitude();

  String getLongitude();

  Integer getWaitTimeCount();

  // Wait time calculations
  Integer getMinWaitTime();

  Integer getMaxWaitTime();

  Integer getMinTodayWaitTime();

  Integer getMaxTodayWaitTime();

  Boolean getApproved();
}