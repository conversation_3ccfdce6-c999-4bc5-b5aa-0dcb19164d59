package com.bulkloads.web.facility.repository.template;

public class GetFacilityWaitTimesQueryTemplate {
  public static final String GET_FACILITY_WAIT_TIMES_QUERY_TEMPLATE = """
      SELECT
          fwt.facility_wait_time_id,
          fwt.wait_duration_minutes,
          fwt.added_date,
          fwt.load_or_unload,
          fwt.comment,
          r.rate_product_category,
          u.user_id,
          u.first_name,
          u.last_name
      
      FROM facility_wait_times fwt
        INNER JOIN user_info u ON u.user_id = fwt.added_by_user_id
        LEFT JOIN rate_product_categories r on r.rate_product_category_id = fwt.rate_product_category_id 
      
      <% params.put("facilityId", facilityId) %>
      WHERE fwt.facility_id = :facilityId
      
      ORDER BY fwt.facility_wait_time_id DESC
      
      <% if (paramExistsAdd("limit")) { %>
          LIMIT
          <% if (paramExistsAdd("skip")) { %>
          :skip,
          <% } %>
          :limit
      <% } %>
      """;
}