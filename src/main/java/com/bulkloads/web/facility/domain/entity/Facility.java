package com.bulkloads.web.facility.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.jpa.CsvListSize;
import com.bulkloads.web.common.jpa.converter.CsvStringListConverter;
import com.bulkloads.web.equipment.domain.entity.Equipment;
import com.bulkloads.web.file.domain.entity.File;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "facilities")
@Getter
@Setter
public class Facility {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "facility_id")
  private Integer facilityId;

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "name")
  private String name = "";

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "location")
  private String location = "";

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "city")
  private String city = "";

  @Size(max = 25, message = "Up to 25 chars")
  @Column(name = "state")
  private String state = "";

  @Size(max = 1000, message = "Up to 1000 chars")
  @Column(name = "address")
  private String address = "";

  @Size(max = 25, message = "Up to 25 chars")
  @Column(name = "zip")
  private String zip = "";

  @Size(max = 100, message = "Up to 100 chars")
  @Column(name = "county")
  private String county = "";

  @Size(max = 100, message = "Up to 100 chars")
  @Column(name = "country")
  private String country = "";

  @Column(name = "longitude")
  private Double longitude;

  @Column(name = "latitude")
  private Double latitude;

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "website")
  private String website = "";

  @Size(max = 50, message = "Up to 50 chars")
  @Column(name = "phone_1")
  private String phone1 = "";

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "email")
  private String email = "";

  @Column(name = "deleted")
  private Boolean deleted = false;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  @Column(name = "deleted_by_user_id")
  private Integer deletedByUserId;

  @Column(name = "total_reviews")
  private Integer totalReviews = 0;

  @Column(name = "avg_rating")
  private Double avgRating;

  @CsvListSize(max = 70)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "equipment_ids")
  private List<String> equipmentIds = new ArrayList<>();

  @CsvListSize(max = 300)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "equipment_names")
  private List<String> equipmentNames = new ArrayList<>();

  @Column(name = "view_count")
  private Integer viewCount = 0;

  @NotNull
  @Column(name = "added_date")
  private Instant addedDate;

  @NotNull
  @Column(name = "added_by_user_id")
  private Integer addedByUserId;

  @Column(name = "updated_date")
  private Instant updatedDate;

  @Column(name = "updated_by_user_id")
  private Integer updatedByUserId;

  @Column(name = "public_restrooms")
  private Boolean publicRestrooms;

  @Column(name = "overnight_parking")
  private Boolean overnightParking;

  @Column(name = "driver_lounge")
  private Boolean driverLounge;

  @Column(name = "onsite_scale")
  private Boolean onsiteScale;

  @Column(name = "appointment_required")
  private Boolean appointmentRequired;

  @Column(name = "washout_required")
  private Boolean washoutRequired;

  @NotNull(message = "Hours of operation cannot be null")
  @Column(name = "hours_of_operation")
  private String hoursOfOperation;

  @ManyToMany
  @JoinTable(
      name = "facility_equipments",
      joinColumns = @JoinColumn(name = "facility_id"),
      inverseJoinColumns = @JoinColumn(name = "equipment_id")
  )
  private List<Equipment> equipments = new ArrayList<>();

  @ManyToMany
  @JoinTable(
      name = "facility_files",
      joinColumns = @JoinColumn(name = "facility_id"),
      inverseJoinColumns = @JoinColumn(name = "file_id")
  )
  private List<File> files = new ArrayList<>();

  @Column(name = "approved", nullable = false)
  private Boolean approved = false;
}
