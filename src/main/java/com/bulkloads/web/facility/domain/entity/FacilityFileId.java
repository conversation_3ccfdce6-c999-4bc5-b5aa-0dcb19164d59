package com.bulkloads.web.facility.domain.entity;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class FacilityFileId implements Serializable {

  @Column(name = "facility_id")
  private Integer facilityId;

  @Column(name = "file_id")
  private Integer fileId;
}
