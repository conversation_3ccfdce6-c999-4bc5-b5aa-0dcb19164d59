package com.bulkloads.web.facility.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.web.facility.service.FacilityReviewService;
import com.bulkloads.web.facility.service.dto.FacilityReviewResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/facilities")
@Tag(name = "Facilities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class FacilityReviewQueryController {

  private final FacilityReviewService facilityReviewService;

  @Operation(summary = "Get my Facility Review")
  @GetMapping("/{facility_id}/my_review")
  public FacilityReviewResponse getMyFacilityReview(
      @PathVariable("facility_id") final int facilityId) {
    return facilityReviewService.getMyFacilityReview(facilityId);
  }

  @Operation(summary = "Get Facility Reviews")
  @GetMapping("/{facility_id}/reviews")
  public List<FacilityReviewResponse> getFacilityReviews(
      @PathVariable("facility_id") final int facilityId,
      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,
      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {
    return facilityReviewService.getFacilityReviews(facilityId, skip, limit);
  }

  @Operation(summary = "Get unapproved Facility Reviews")
  @GetMapping("/reviews/unapproved")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public List<FacilityReviewResponse> getUnapprovedFacilityReviews() {
    return facilityReviewService.getUnapprovedFacilityReviews();
  }
}
