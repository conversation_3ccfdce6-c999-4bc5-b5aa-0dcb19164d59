package com.bulkloads.web.facility.mapper;

import java.util.Map;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.facility.domain.data.FacilityWaitTimeData;
import com.bulkloads.web.facility.domain.entity.Facility;
import com.bulkloads.web.facility.domain.entity.FacilityWaitTime;
import com.bulkloads.web.facility.repository.FacilityRepository;
import com.bulkloads.web.facility.service.dto.FacilityWaitTimeRequest;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import com.bulkloads.web.rate.repository.RateProductCategoryRepository;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class FacilityWaitTimeMapper {

  @Autowired
  private FacilityRepository facilityRepository;
  
  @Autowired
  private RateProductCategoryRepository rateProductCategoryRepository;

  @Mapping(target = "facility", source = "facilityId")
  @Mapping(target = "startTime", source = "request.startTime")
  @Mapping(target = "endTime", source = "request.endTime")
  @Mapping(target = "waitDurationMinutes", ignore = true) // Ignore wait duration from request
  @Mapping(target = "byAppointment", source = "request.byAppointment")
  @Mapping(target = "comment", source = "request.comment")
  @Mapping(target = "rateProductCategory", source = "request.rateProductCategoryId")
  @Mapping(target = "loadOrUnload", source = "request.loadOrUnload")
  public abstract FacilityWaitTimeData requestToData(final int facilityId, final FacilityWaitTimeRequest request, @Context final Map<String, String> errors);

  @Mapping(target = "facilityWaitTimeId", ignore = true)
  @Mapping(target = "addedByUserId", ignore = true)
  @Mapping(target = "addedDate", ignore = true)
  public abstract void dataToEntity(FacilityWaitTimeData data, @MappingTarget FacilityWaitTime entity);

  public Facility mapFacility(final int facilityId) {
    return facilityRepository.findById(facilityId)
        .orElseThrow(() -> new ValidationException("facility_id", 
            "Could not find facility with id %s".formatted(facilityId)));
  }

  public RateProductCategory mapRateProductCategory(final Integer id) {
    if (id == null) {
      return null;
    }
    return rateProductCategoryRepository.findById(id)
        .orElseThrow(() -> new ValidationException("rate_product_category_id", 
            "Could not find product category with id %s".formatted(id)));
  }
}

