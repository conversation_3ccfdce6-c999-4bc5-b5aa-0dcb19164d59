package com.bulkloads.web.phonenumber.repository;

import java.time.Instant;
import com.bulkloads.web.phonenumber.domain.CachedPhoneNumber;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PhoneNumberRepository extends ListCrudRepository<CachedPhoneNumber, Long> {

  @Query("""
      select p from CachedPhoneNumber p
      where p.phoneNumber = :phoneNumber AND p.dateAdded >= :date
      """)
  CachedPhoneNumber getCachedPhoneNumberAndDateAddedIsAfter(
      @Param("phoneNumber") final Long phoneNumber,
      @Param("date") final Instant date);
}
