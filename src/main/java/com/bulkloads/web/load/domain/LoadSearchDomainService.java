package com.bulkloads.web.load.domain;

import java.time.Instant;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.load.domain.entity.LoadSearch;
import com.bulkloads.web.load.mapper.LoadSearchMapper;
import com.bulkloads.web.load.service.LoadSearchData;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoadSearchDomainService extends BaseDomainService<LoadSearch> {

  private final LoadSearchMapper loadSearchMapper;

  public Result<LoadSearch> create(LoadSearchData data) {
    final LoadSearch loadSearch = new LoadSearch();
    return super.validate(loadSearch, null, data, ValidationMethod.CREATE);
  }

  public void mapToEntityAuto(Object data, LoadSearch entity) {
    loadSearchMapper.dataToEntity((LoadSearchData) data, entity);
  }

  @Override
  public void validateDataAndMapToEntity(Result<LoadSearch> result, LoadSearch entity,
                                         LoadSearch existing, Object data, ValidationMethod method) {

    final int userId = UserUtil.getUserIdOrThrow();
    entity.setUserId(userId);
    entity.setSearchType(10);
    entity.setDate(Instant.now());
  }

  @Override
  public void validateEntity(Result<LoadSearch> result, LoadSearch entity) {

  }

}
