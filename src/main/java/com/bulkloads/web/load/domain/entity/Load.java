package com.bulkloads.web.load.domain.entity;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.jpa.CsvListSize;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.common.event.DomainEvent;
import com.bulkloads.web.common.jpa.converter.CsvIntegerListConverter;
import com.bulkloads.web.common.jpa.converter.CsvStringListConverter;
import com.bulkloads.web.contracts.domain.entity.Contract;
import com.bulkloads.web.equipment.domain.entity.Equipment;
import com.bulkloads.web.rate.domain.entity.ExternalRateProductCategory;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.domain.AbstractAggregateRoot;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Entity
@Table(name = "loads")
@Getter
@Setter
public class Load extends AbstractAggregateRoot<Load> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "load_id")
  private Integer loadId;

  @Size(max = 50, message = "The external_load_id field must be up to 50 chars")
  @NotNull
  @Column(name = "external_load_id")
  private String externalLoadId = "";

  @Size(max = 100, message = "Enter up to 100 chars")
  @NotNull
  @Column(name = "contact_name")
  private String contactName = "";

  @Size(max = 50, message = "Enter up to 50 chars")
  @NotNull
  @Column(name = "contact_number")
  private String contactNumber = "";

  @Size(max = 10)
  @NotNull
  @Column(name = "contact_number_type")
  private String contactNumberType = "";

  @Size(max = 50)
  @NotNull
  @Column(name = "nickname")
  private String nickname = "";

  @Column(name = "ship_from")
  private LocalDate shipFrom;

  @Column(name = "ship_to")
  private LocalDate shipTo;

  @NotNull
  @Column(name = "load_security")
  private Integer loadSecurity = 1;

  @NotNull
  @Column(name = "number_of_loads")
  private Integer numberOfLoads = 1;

  @Size(max = 20)
  @NotNull
  @Column(name = "estimated_weight")
  private String estimatedWeight = "";

  @NotNull
  @Column(name = "default_bill_weight_use")
  private String defaultBillWeightUse = "unload_weight";

  @Size(max = 25)
  @NotNull
  @Column(name = "origin_country")
  private String originCountry = "";

  @Size(max = 15)
  @NotNull
  @Column(name = "origin_state")
  private String originState = "0";

  @Size(max = 60)
  @NotNull
  @Column(name = "origin_city")
  private String originCity = "";

  @Size(max = 25)
  @NotNull
  @Column(name = "destination_country")
  private String destinationCountry = "";

  @Size(max = 25)
  @NotNull
  @Column(name = "destination_state")
  private String destinationState = "0";

  @Size(max = 60)
  @NotNull
  @Column(name = "destination_city")
  private String destinationCity = "";

  @Column(name = "estimated_miles")
  private BigDecimal estimatedMiles;

  @Size(max = 50, message = "Enter up to 50 chars for the rate")
  @NotNull
  @Column(name = "rate")
  private String rate = "";

  @Size(max = 2000)
  @NotNull
  @Column(name = "load_comments")
  private String loadComments = "";

  @NotNull
  @Column(name = "post_date")
  private Instant postDate;

  @Column(name = "update_date")
  private Instant updateDate;

  @Size(max = 3)
  @Column(name = "text")
  private String text;

  @Size(max = 7)
  @NotNull
  @Column(name = "origin_zipcode")
  private String originZipcode = "";

  @Size(max = 7)
  @NotNull
  @Column(name = "destination_zipcode")
  private String destinationZipcode = "";

  @Column(name = "origin_lat")
  private Double originLat;

  @Column(name = "origin_long")
  private Double originLong;

  @Column(name = "dest_lat")
  private Double destLat;

  @Column(name = "dest_long")
  private Double destLong;

  @NotNull
  @Column(name = "active")
  private Boolean active = true;

  @NotNull
  @Column(name = "is_managed")
  private Boolean isManaged = false;

  @Size(max = 4)
  @NotNull
  @Column(name = "radius")
  private String radius = "";

  @Size(max = 2)
  @NotNull
  @Column(name = "send_to_amount")
  private String sendToAmount = "";

  @Size(max = 15)
  @NotNull
  @Column(name = "text_type")
  private String textType = "";

  @Size(max = 100)
  @NotNull
  @Column(name = "outside_tracking_id")
  private String outsideTrackingId = "";

  @Column(name = "miles_checked")
  private Boolean milesChecked = false;

  @Column(name = "pcmiler")
  private Integer pcmiler;

  @Column(name = "scraped")
  private Integer scraped = 0;

  @Size(max = 1000, message = "Enter up to 1000 chars in the Origin Notes")
  @NotNull
  @Column(name = "lo_origin_notes")
  private String loOriginNotes = "";

  @Size(max = 1000, message = "Enter up to 1000 chars in the Destination Notes")
  @NotNull
  @Column(name = "lo_destination_notes")
  private String loDestinationNotes = "";

  @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH})
  @JoinColumn(name = "commodity_id")
  private Commodity commodity;

  @Size(max = 100)
  @NotNull
  @Column(name = "lo_commodity")
  private String loCommodity = "";

  @Column(name = "lo_rate")
  private BigDecimal loRate;

  @NotNull(message = "Enter a valid rate type")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "lo_rate_type")
  private RateType loRateType;

  @Size(max = 500)
  @NotNull
  @Column(name = "lo_comments")
  private String loComments = "";

  @NotNull
  @Column(name = "site_id")
  private Integer siteId = 1;

  @Size(max = 75, message = "Enter up to 75 chars")
  @NotNull
  @Column(name = "lo_contract_number")
  private String loContractNumber = "";

  @NotNull
  @Column(name = "starting_load_assignment_number")
  private Integer startingLoadAssignmentNumber = 1;

  @Column(name = "edit_date")
  private Instant editDate;

  @Column(name = "edit_by_user_id")
  private Integer editByUserId;

  @Column(name = "inactive_date")
  private Instant inactiveDate;

  @Column(name = "inactive_by_user_id")
  private Integer inactiveByUserId;

  @Column(name = "delete_date")
  private Instant deleteDate;

  @Column(name = "delete_by_user_id")
  private Integer deleteByUserId;

  @NotNull
  @Column(name = "deleted")
  private Boolean deleted = false;

  @Column(name = "lo_public_post_date")
  private Instant loPublicPostDate;

  @Column(name = "lo_est_rate_per_mile")
  private BigDecimal loEstRatePerMile;

  @Column(name = "lo_est_pay")
  private BigDecimal loEstPay;

  @Column(name = "lo_estimated_weight")
  private BigDecimal loEstimatedWeight;

  @Column(name = "lo_estimated_volume")
  private BigDecimal loEstimatedVolume;

  @Column(name = "lo_est_quantity")
  private BigDecimal loEstQuantity;

  @Column(name = "lo_est_miles")
  private BigDecimal loEstMiles;

  @Column(name = "lo_est_hours")
  private BigDecimal loEstHours;

  @Column(name = "routing_result")
  private String routingResult = "";

  @Column(name = "load_bearing")
  private Double loadBearing;

  @Size(max = 3)
  @Column(name = "load_bearing_direction")
  private String loadBearingDirection;

  @Column(name = "routeid")
  private Integer routeid;

  @Column(name = "fl_num_of_stops")
  private Integer flNumOfStops;

  @Size(max = 10)
  @NotNull
  @Column(name = "fl_truck_size")
  private String flTruckSize = "Full";

  @Size(max = 50)
  @NotNull
  @Column(name = "source")
  private String source = "";

  @Size(max = 45)
  @NotNull
  @Column(name = "source_id")
  private String sourceId = "";

  @NotNull
  @Column(name = "lead_count")
  private Integer leadCount = 0;

  @Column(name = "manual_rate_per_mile")
  private BigDecimal manualRatePerMile;

  @Column(name = "manual_rate_per_mile_date")
  private Instant manualRatePerMileDate;

  @NotNull
  @Column(name = "load_access")
  private String loadAccess = "public";

  @Column(name = "pickup_drop_miles")
  private BigDecimal pickupDropMiles;

  @NotNull
  @Column(name = "originals_required")
  private Boolean originalsRequired = false;

  @NotNull
  @Column(name = "washout_required")
  private Boolean washoutRequired = false;

  @NotNull
  @Column(name = "pickup_appt")
  private Boolean pickupAppt = false;

  @NotNull
  @Column(name = "drop_appt")
  private Boolean dropAppt = false;

  @Size(max = 100)
  @NotNull
  @Column(name = "pickup_po")
  private String pickupPo = "";

  @Size(max = 100)
  @NotNull
  @Column(name = "drop_po")
  private String dropPo = "";

  @NotNull
  @Column(name = "number_of_assigned_loads")
  private Integer numberOfAssignedLoads = 0;

  @NotNull
  @Column(name = "number_of_assigned_drivers")
  private Integer numberOfAssignedDrivers = 0;

  @NotNull
  @Column(name = "number_of_available_loads")
  private Integer numberOfAvailableLoads = 0;

  @NotNull
  @Column(name = "number_of_delivered_loads")
  private Integer numberOfDeliveredLoads = 0;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "contract_id")
  private Contract contract;

  @Column(name = "buy_contract_id")
  private Integer buyContractId;

  @Column(name = "sell_contract_id")
  private Integer sellContractId;

  @Column(name = "create_date")
  private Instant createDate;

  @CsvListSize(max = 45)
  @Convert(converter = CsvIntegerListConverter.class)
  @Column(name = "required_file_type_ids")
  private List<Integer> requiredFileTypeIds = new ArrayList<>();

  @CsvListSize(max = 300)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "required_file_types")
  private List<String> requiredFileTypes = new ArrayList<>();

  @Size(max = 1024, message = "Enter up to 1024 chars")
  @NotNull
  @Column(name = "required_files_note")
  private String requiredFilesNote = "";

  @Column(name = "modified_date")
  private Instant modifiedDate;

  @NotNull
  @Column(name = "added_by_user_id")
  private Integer addedByUserId = 0;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "rate_product_category_id")
  private RateProductCategory rateProductCategory;

  @Size(max = 50)
  @NotNull
  @Column(name = "rate_product_category")
  private String rateProductCategory1 = "";

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumns({
      @JoinColumn(name = "external_rate_product_category_id", referencedColumnName = "external_rate_product_category_id",
          insertable = false, updatable = false),
      @JoinColumn(name = "user_company_id", referencedColumnName = "user_company_id", insertable = false, updatable = false)
  })
  private ExternalRateProductCategory externalRateProductCategoryId;

  @NotNull
  @Column(name = "is_hazmat")
  private Boolean isHazmat = false;

  @Column(name = "avg_rate_per_mile")
  private BigDecimal avgRatePerMile;

  @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH})
  @JoinTable(name = "load_equipment",
      joinColumns = @JoinColumn(name = "load_id"),
      inverseJoinColumns = @JoinColumn(name = "equipment_id"))
  private List<Equipment> equipments = new ArrayList<>();

  @CsvListSize(max = 100)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "equipment_ids")
  private List<String> equipmentIds = new ArrayList<>();

  @CsvListSize(max = 300)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "equipment_names")
  private List<String> equipmentNames = new ArrayList<>();

  @Size(max = 500)
  @NotNull
  @Column(name = "load_details_url")
  private String loadDetailsUrl = "";

  @NotNull
  @Column(name = "is_broker")
  private Boolean isBroker = false;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "hiring_ab_company_id")
  private AbCompany hiringAbCompany;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "hiring_ab_user_id")
  private AbUser hiringAbUser;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "bill_to_ab_company_id")
  private AbCompany billToAbCompany;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "bill_to_ab_user_id")
  private AbUser billToAbUser;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "pickup_ab_company_id")
  private AbCompany pickupAbCompany;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "drop_ab_company_id")
  private AbCompany dropAbCompany;

  @Column(name = "sales_rep_user_id")
  private Integer salesRepUserId;

  @NotNull
  @Column(name = "shared_with_hiring_company")
  private Boolean sharedWithHiringCompany = false;

  @NotNull
  @Column(name = "is_root_load")
  private Boolean isRootLoad = true;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @SQLRestriction("deleted = 0")
  @OneToMany(mappedBy = "load", cascade = CascadeType.ALL, orphanRemoval = true)
  private List<Assignment> assignments = new ArrayList<>();

  @SQLRestriction("to_deleted = 0")
  @OneToMany(mappedBy = "toLoad", cascade = CascadeType.ALL, orphanRemoval = true)
  private List<Assignment> bookings = new ArrayList<>();

  @Setter(AccessLevel.NONE)
  @Transient
  private List<Integer> reroutedAssignments = new ArrayList<>();

  public void addReroutedAssignment(final Integer assignmentId) {
    reroutedAssignments.add(assignmentId);
  }

  public void registerDomainEvent(final DomainEvent event) {
    registerEvent(event);
  }
}
