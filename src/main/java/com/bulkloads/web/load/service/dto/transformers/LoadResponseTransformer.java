package com.bulkloads.web.load.service.dto.transformers;

import static com.bulkloads.web.load.service.dto.transformers.LoadListResponseTransformer.toResponse;
import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.load.service.dto.LoadResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadResponseTransformer implements TupleTransformer<LoadResponse> {

  @Override
  public LoadResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    LoadResponse response = (LoadResponse)toResponse(columns, aliases);

    response.setFirstName(parts.asString("first_name"));
    response.setLastName(parts.asString("last_name"));
    response.setEmail(parts.asString("email"));
    response.setPhone1(parts.asString("phone_1"));
    response.setPhone2(parts.asString("phone_2"));
    response.setUserPhone1(parts.asString("user_phone_1"));
    response.setUserPhone2(parts.asString("user_phone_2"));
    response.setCellPhone(parts.asString("cell_phone"));
    response.setFax(parts.asString("fax"));
    response.setCity(parts.asString("city"));
    response.setState(parts.asString("state"));
    response.setWebsite(parts.asString("website"));
    response.setAvatarLargeUrl(parts.asString("avatar_large_url"));
    response.setAvatarThumbUrl(parts.asString("avatar_thumb_url"));
    response.setLane(parts.asString("lane"));
    response.setEstimatedWeight(parts.asString("estimated_weight"));
    response.setLoadComments(parts.asString("load_comments"));
    response.setMinViewedDate(parts.asInstant("min_viewed_date"));
    response.setMaxEmailedDate(parts.asInstant("max_emailed_date"));

    return response;
  }

}

