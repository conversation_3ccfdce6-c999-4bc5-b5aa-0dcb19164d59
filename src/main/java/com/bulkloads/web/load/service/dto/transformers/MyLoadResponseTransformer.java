package com.bulkloads.web.load.service.dto.transformers;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.load.api.dto.MyLoadV2ListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class MyLoadResponseTransformer implements TupleTransformer<MyLoadV2ListResponse> {

  @Override
  public MyLoadV2ListResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    MyLoadV2ListResponse response = new MyLoadV2ListResponse();
    response.setLoadId(parts.asInteger("load_id"));
    response.setNumberOfLoads(parts.asInteger("number_of_loads"));
    response.setNumberOfAssignedLoads(parts.asInteger("number_of_assigned_loads"));
    response.setNumberOfAvailableLoads(parts.asInteger("number_of_available_loads"));
    response.setNumberOfDeliveredLoads(parts.asInteger("number_of_delivered_loads"));
    response.setPickupDropMiles(parts.asBigDecimal("pickup_drop_miles"));
    response.setLoCommodity(parts.asString("lo_commodity"));
    response.setLoContractNumber(parts.asString("lo_contract_number"));
    response.setLoRate(parts.asBigDecimal("lo_rate"));
    response.setLoRateTypeLabel(parts.asString("lo_rate_type_label"));
    response.setRateProductCategory(parts.asString("rate_product_category"));
    response.setParentHiringCompanyName(parts.asString("parent_hiring_company_name"));
    response.setPickupCity(parts.asString("pickup_city"));
    response.setPickupCompanyName(parts.asString("pickup_company_name"));
    response.setPickupState(parts.asString("pickup_state"));
    response.setPickupLatitude(parts.asDouble("pickup_latitude"));
    response.setPickupLongitude(parts.asDouble("pickup_longitude"));
    response.setDropCity(parts.asString("drop_city"));
    response.setDropCompanyName(parts.asString("drop_company_name"));
    response.setDropState(parts.asString("drop_state"));
    response.setDropLatitude(parts.asDouble("drop_latitude"));
    response.setDropLongitude(parts.asDouble("drop_longitude"));
    response.setPostedToBulkLoads(parts.asBoolean("posted_to_bulk_loads"));
    response.setShipFrom(parts.asLocalDate("ship_from"));
    response.setShipTo(parts.asLocalDate("ship_to"));
    response.setPostDate(parts.asInstant("post_date"));
    response.setAddedBy(parts.asString("added_by"));
    return response;
  }

}
