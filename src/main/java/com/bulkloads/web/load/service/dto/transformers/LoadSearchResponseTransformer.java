package com.bulkloads.web.load.service.dto.transformers;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.load.service.dto.LoadSearchResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadSearchResponseTransformer implements TupleTransformer<LoadSearchResponse> {

  @Override
  public LoadSearchResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    LoadSearchResponse response = LoadSearchResponse.builder()
        .userCompanyIds(parts.asString("user_company_ids"))
        .originCountry(parts.asString("origin_country"))
        .originState(parts.asString("origin_state"))
        .originCity(parts.asString("origin_city"))
        .originZip(parts.asString("origin_zip"))
        .originLat(parts.asDouble("origin_lat"))
        .originLong(parts.asDouble("origin_long"))
        .originRadius(parts.asDouble("origin_radius"))
        .destinationCountry(parts.asString("destination_country"))
        .destinationState(parts.asString("destination_state"))
        .destinationCity(parts.asString("destination_city"))
        .destinationZip(parts.asString("destination_zip"))
        .destinationLat(parts.asDouble("destination_lat"))
        .destinationLong(parts.asDouble("destination_long"))
        .destinationRadius(parts.asDouble("destination_radius"))
        .equipmentIds(parts.asString("equipment_ids"))
        .onlyFavoriteLoads(parts.asBoolean("only_favorite_loads"))
        .onlyOfferedLoads(parts.asBoolean("only_offered_loads"))
        .onlyFavoriteLanes(parts.asBoolean("only_favorite_lanes"))
        .onlyFavoriteCompanies(parts.asBoolean("only_favorite_companies"))
        .build();

    return response;
  }

}
