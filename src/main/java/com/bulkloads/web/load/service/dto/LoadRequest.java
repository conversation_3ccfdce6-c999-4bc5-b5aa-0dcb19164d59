package com.bulkloads.web.load.service.dto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.assignment.service.dto.AssignmentFileRequest;
import com.bulkloads.web.assignment.service.dto.AssignmentSurchargeRequest;
import com.bulkloads.web.assignment.service.dto.CreateLoadAssignmentSubRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class LoadRequest {

  @Schema(name = "is_managed", example = "0", description = "0 if you are only posting a load to the BulkLoads.com load board.",
      requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> isManaged;
  @Schema(name = "external_load_id", example = "1234", description = "Unique ID from YOUR system.  We will return this with Completed Loads.",
      requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> externalLoadId;
  @Schema(name = "number_of_loads", example = "5", description = "(LOAD BOARD / DISPATCH) Number of Loads in this Shipment.",
      requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Integer> numberOfLoads;
  @Schema(name = "ship_from", example = "01-30-2021", description = "Starting date (MM-DD-YYYY) of load(s) schedule.",
      requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> shipFrom;
  @Schema(name = "ship_to", example = "02-28-2021", description = "Ending date (MM-DD-YYYY) of load(s) schedule.",
      requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> shipTo;
  @Schema(name = "origin", example = "Omaha, NE", description = "Origin City/St of load.  Please use 'cities' endpoints to validate city names.",
      requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> origin;
  @Schema(name = "origin_city", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> originCity;
  @Schema(name = "origin_state", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> originState;
  @Schema(name = "origin_zipcode", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> originZipcode;
  @Schema(name = "origin_country", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> originCountry;
  @Schema(name = "origin_latitude", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Double> originLatitude;
  @Schema(name = "origin_longitude", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Double> originLongitude;
  @Schema(name = "destination", example = "Kansas City, MO", description = "Destination City/St of load.  Please use 'cities' endpoints to validate city "
      + "names.", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> destination;
  @Schema(name = "destination_city", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> destinationCity;
  @Schema(name = "destination_state", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> destinationState;
  @Schema(name = "destination_zipcode", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> destinationZipcode;
  @Schema(name = "destination_country", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> destinationCountry;
  @Schema(name = "destination_latitude", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Double> destinationLatitude;
  @Schema(name = "destination_longitude", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Double> destinationLongitude;
  @Schema(name = "is_hazmat", example = "0", description = "0 | 1 - If load is Hazmat, value should be set to 1. If not, 0.",
      requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> isHazmat;
  @Schema(name = "equipment_ids", example = "H,HHS,HLS,B,ED", description = "List of trailer type ID's that would qualify to haul this load "
      + "(from equipments endpoint)", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> equipmentIds;
  @Schema(name = "load_access", example = "public", description = "public | private | carrier", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> loadAccess;
  @Schema(name = "contact_name", example = "Jonny Appleseed", description = "The contact name displayed with the BulkLoads.com load board posting.",
      requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> contactName;
  @Schema(name = "contact_number", example = "402-813-4566 <NAME_EMAIL>", description = "The contact number displayed with the "
      + "BulkLoads.com load board posting.", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<String> contactNumber;
  @Schema(name = "rate", example = "$25/ton negotiable", description = "The rate displayed with the BulkLoads.com load board posting.",
      requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> rate;
  @Schema(name = "load_comments", example = "Loading next week, call for rate.", description = "Comments displayed with the BulkLoads.com load board posting.",
      requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loadComments;
  @Schema(name = "rate_product_category_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> rateProductCategoryId;
  @Schema(name = "external_rate_product_category_id", description = "From GET /products/categories/external", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> externalRateProductCategoryId;
  @Schema(name = "originals_required", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> originalsRequired;
  @Schema(name = "washout_required", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> washoutRequired;
  @Schema(name = "pickup_po", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> pickupPo;
  @Schema(name = "drop_po", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> dropPo;
  @Schema(name = "is_broker", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Boolean> isBroker;
  @Schema(name = "hiring_ab_company_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> hiringAbCompanyId;
  @Schema(name = "hiring_ab_user_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> hiringAbUserId;
  @Schema(name = "bill_to_ab_company_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> billToAbCompanyId;
  @Schema(name = "bill_to_ab_user_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> billToAbUserId;
  @Schema(name = "lo_contract_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loContractNumber;
  @Schema(name = "lo_origin_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loOriginNotes;
  @Schema(name = "lo_destination_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loDestinationNotes;
  @Schema(name = "commodity_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> commodityId;
  @Schema(name = "lo_commodity", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loCommodity;
  @Schema(name = "lo_rate", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> loRate;
  @Schema(name = "lo_rate_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loRateType;
  @Schema(name = "lo_estimated_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> loEstimatedWeight;
  @Schema(name = "lo_estimated_volume", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> loEstimatedVolume;
  @Schema(name = "lo_est_miles", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> loEstMiles;
  @Schema(name = "lo_est_hours", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> loEstHours;
  @Schema(name = "lo_comments", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loComments;
  @Schema(name = "pickup_ab_company_id", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Integer> pickupAbCompanyId;
  @Schema(name = "drop_ab_company_id", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Integer> dropAbCompanyId;
  @Schema(name = "user_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> userId;
  @Schema(name = "sales_rep_user_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> salesRepUserId;
  @Schema(name = "shared_with_hiring_company", requiredMode = Schema.RequiredMode.REQUIRED)
  Optional<Boolean> sharedWithHiringCompany;
  @Schema(name = "default_bill_weight_use", description = "either loaded_weight or unload_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> defaultBillWeightUse;
  @Schema(name = "bill_weight_use", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> billWeightUse;
  @Valid
  @Schema(name = "bill_hours", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> billHours;
  @Valid
  @Schema(name = "bill_miles", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> billMiles;
  @Schema(name = "bol_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> bolNumber;
  @Schema(name = "hauled_date", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> hauledDate;
  @Schema(name = "hauled_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> hauledNotes;
  @Valid
  @Schema(name = "loaded_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> loadedWeight;
  @Schema(name = "loading_ticket_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loadingTicketNumber;
  @Schema(name = "unload_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> unloadWeight;
  @Schema(name = "unloading_ticket_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> unloadingTicketNumber;
  @Schema(name = "pickup_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> pickupNotes;
  @Schema(name = "drop_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> dropNotes;
  @Schema(name = "inside_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> insideNotes;
  @Schema(name = "personal_message", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> personalMessage;
  @Schema(name = "share_driver_location", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> shareDriverLocation;
  @Schema(name = "contract_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> contractId;
  @Schema(name = "auto_invoice", description = "1 to send an invoice immediately . Must be 'delivered'.",
      requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> autoInvoice;
  @Schema(name = "ready_to_invoice", description = "1 to move the booking to Ready To Invoice to be invoiced manually If both auto_invoice and "
      + "ready_to_invoice are 0, no invoice will be sent.Must be 'delivered'.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> readyToInvoice;
  @Schema(name = "required_file_type_ids", description = "comma-delimited list of file_type_id from GET /files/types",
      requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> requiredFileTypeIds;
  @Schema(name = "required_files_note", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> requiredFilesNote;
  @Schema(name = "assignment_status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> assignmentStatus;
  @Valid
  @Schema(name = "assignments", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<List<@Valid CreateLoadAssignmentSubRequest>> assignments;
  @Valid
  @Schema(name = "files", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<List<@Valid AssignmentFileRequest>> files;
  @Valid
  @Schema(name = "surcharges", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<List<@Valid AssignmentSurchargeRequest>> surcharges;
  @Schema(name = "certified_owner_required", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> certifiedOwnerRequired;
  @Schema(name = "certified_owner", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> certifiedOwner;

  Optional<Boolean> isRootLoad;
  Optional<Boolean> deferLoadInvoice;
  Optional<Boolean> sendBookingConfirmation;
  Optional<Boolean> sendConfirmation;
  boolean validateCities;
}

