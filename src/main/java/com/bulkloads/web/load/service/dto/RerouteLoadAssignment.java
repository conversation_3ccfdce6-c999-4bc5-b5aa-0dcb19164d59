package com.bulkloads.web.load.service.dto;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder(toBuilder = true)
@Jacksonized
public class RerouteLoadAssignment {

  @Positive
  @NotNull(message = "Assignment must have a load_assignment_id.")
  @Schema(description = "Reroute contract id.", requiredMode = Schema.RequiredMode.REQUIRED)
  Integer loadAssignmentId;

  @Positive(message = "Reroute rate must be a positive number.")
  @Schema(description = "Reroute rate.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  BigDecimal rerouteRate;

  @Schema(description = "Reroute rate type.", requiredMode = Schema.RequiredMode.NOT_REQUIRED, defaultValue = "2000")
  String rerouteRateType;

  @Schema(description = "Reroute load assignment number.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String rerouteLoadAssignmentNumber;

  @Schema(description = "Reroute pickup number.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String reroutePickupNumber;

  @Size(max = 10, message = "Reroute drop number should be less than 10 characters.")
  @Schema(description = "Reroute drop number.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String rerouteDropNumber;

  @Schema(description = "Reroute work order number.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String rerouteWorkOrderNumber;
}