package com.bulkloads.web.city.api;

import java.util.List;
import com.bulkloads.web.city.service.CityService;
import com.bulkloads.web.city.service.dto.CityResponse;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/cities")
@Tag(name = "Cities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class CityQueryController {

  private final CityService cityService;

  @Operation(summary = "Get the cities")
  @GetMapping
  public List<CityResponse> getCities(
      @Parameter(description = "The search term.")
      @RequestParam(value = "term", required = false) String term,
      @RequestParam(value = "include_states", required = false) Boolean includeStates) {
    return cityService.getCities(term, includeStates);
  }

  @Operation(summary = "Get the city names")
  @GetMapping("/name")
  public List<String> getCityNames(
      @Parameter(description = "The search term.")
      @RequestParam(value = "term") String term) {
    return cityService.getCityNames(term);
  }

  @Operation(summary = "Get city near me")
  @GetMapping("/nearme")
  public List<CityResponse> getCityNearMe(
      @RequestParam(value = "lat") double latitude,
      @RequestParam(value = "long") double longitude) {
    return cityService.getCityNearMe(latitude, longitude);
  }

}
