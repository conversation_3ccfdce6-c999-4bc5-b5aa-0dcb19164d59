package com.bulkloads.web.city.repository;

import static com.bulkloads.web.city.repository.template.GetCitiesQueryTemplate.GET_CITIES_QUERY_TEMPLATE;
import static com.bulkloads.web.city.repository.template.GetCitiesQueryTemplate.GET_CITY_NAMES_QUERY_TEMPLATE;
import static com.bulkloads.web.city.repository.template.GetCitiesQueryTemplate.GET_CITY_NEAR_ME_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.city.service.dto.CityResponse;
import com.bulkloads.web.city.service.dto.transformer.CityResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class CityQueryRepositoryImpl implements CityQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final CityResponseTransformer cityResponseTransformer;

  @Override
  public List<CityResponse> getCities(final String term, final Boolean includeStates) {

    Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("term", term == null ? "" : term);
    queryParams.put("includeStates", includeStates);

    return jpaNativeQueryService.query(
        GET_CITIES_QUERY_TEMPLATE,
        queryParams,
        cityResponseTransformer);
  }

  @Override
  public List<CityResponse> getCityNames(final String term) {

    Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("term", term == null ? "" : term);

    return jpaNativeQueryService.query(
        GET_CITY_NAMES_QUERY_TEMPLATE,
        queryParams,
        cityResponseTransformer);
  }

  @Override
  public List<CityResponse> getCityNearMe(final double lng, final double lat) {

    Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("latitude", lat);
    queryParams.put("longitude", lng);

    return jpaNativeQueryService.query(
        GET_CITY_NEAR_ME_QUERY_TEMPLATE,
        queryParams,
        cityResponseTransformer);
  }
}
