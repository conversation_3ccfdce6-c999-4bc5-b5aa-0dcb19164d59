package com.bulkloads.web.city.repository.template;

import org.intellij.lang.annotations.Language;

public class GetCitiesQueryTemplate {

  @Language("SQL")
  public static final String GET_CITIES_QUERY_TEMPLATE = """
          SELECT
            name,
            city,
            state,
            zip,
            country,
            latitude,
            longitude,
            freq
          from cities
          where 1=1
            <% var term_words = term.split("\\s+") %>
            <% for (int i = 0; i < term_words.length; i++) { %>
                <% params.put("term_words_"+i, "%"+term_words[i]+"%") %>
                AND name LIKE :<% print("term_words_"+i) %>
            <% } %>
            <% if (paramIsTrue("includeStates")) { %>
              union all
              SELECT
                state as name,
                '' as city,
                abbreviation as state,
                '' as zip,
                country,
                0.0 as latitude,
                0.0 as longitude,
                10000 as freq
              from states
              where state_id NOT IN (71,72,73,74,75)
              <% params.put("term_wild", "%"+term+"%") %>
              and state like :term_wild
            <% } %>
          order by freq desc
          limit 15
      """;

  public static final String GET_CITY_NAMES_QUERY_TEMPLATE = """         
          SELECT
            name,
            city,
            state,
            zip,
            country,
            latitude,
            longitude,
            freq
          from cities
          where 1=1
            <% var term_words = term.split("\\s+") %>
            <% for (int i = 0; i < term_words.length; i++) { %>
                <% params.put("term_words_"+i, "%"+term_words[i]+"%") %>
                AND name LIKE :<% print("term_words_" + i) %>
            <% } %>
          order by freq desc
          limit 15
      """;

  public static final String GET_CITY_NEAR_ME_QUERY_TEMPLATE = """    
          <% params.put("latitude", latitude) %>
          <% params.put("longitude", longitude) %>
          SELECT
            name,
            city,
            state,
            zip,
            country,
            latitude,
            longitude,
            round(
              st_distance_sphere(
                point(:longitude, :latitude),
                point(longitude, latitude)
               )/1609.34
            , 1) as distance,
            'coords' as match_accuracy
          from cities
          where latitude between :latitude - 1 and :latitude + 1
            and longitude between :longitude - 1 and :longitude + 1
          order by st_distance_sphere(point(:longitude, :latitude), point(longitude, latitude))/1609.34
          limit 1
      """;

}
