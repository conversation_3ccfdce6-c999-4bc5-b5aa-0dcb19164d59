package com.bulkloads.web.report.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.time.LocalDate;
import java.util.List;
import com.bulkloads.web.report.service.ReportService;
import com.bulkloads.web.report.service.dto.AssignmentStatusReportResponse;
import com.bulkloads.web.report.service.dto.CarriersUsedReportResponse;
import com.bulkloads.web.report.service.dto.DispatchedLoadReportResponse;
import com.bulkloads.web.report.service.dto.DispatcherPaymentsReportResponse;
import com.bulkloads.web.report.service.dto.DispatcherReportResponse;
import com.bulkloads.web.report.service.dto.PayablesReportResponse;
import com.bulkloads.web.report.service.dto.ProductReportResponse;
import com.bulkloads.web.report.service.dto.ShippersUsedReportResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/reports")
@Tag(name = "Reports")
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class ReportQueryController {

  private final ReportService reportService;

  @Operation(summary = "Get Assignment Status Report")
  @GetMapping("/assignment_status")
  public List<AssignmentStatusReportResponse> getAssignmentStatusReport() {
    return reportService.getAssignmentStatusReport();
  }

  @Operation(summary = "Get Dispatcher Report")
  @GetMapping("/dispatchers")
  public List<DispatcherReportResponse> getDispatcherReport(
      @RequestParam(value = "start_date", required = true) LocalDate startDate,
      @RequestParam(value = "end_date", required = true) LocalDate endDate
  ) {
    return reportService.getDispatcherReport(startDate, endDate);
  }

  @Operation(summary = "Get Dispatched Load Report")
  @GetMapping("/dispatched_loads")
  public List<DispatchedLoadReportResponse> getDispatchedLoadReport() {
    return reportService.getDispatchedLoadReport();
  }

  @Operation(summary = "Get Product Report")
  @GetMapping("/products")
  public List<ProductReportResponse> getProductReport() {
    return reportService.getProductReport();
  }

  @Operation(summary = "Get Shippers Used Report")
  @GetMapping("/shippers_used")
  public List<ShippersUsedReportResponse> getShippersUsedReport(
      @RequestParam(value = "start_date", required = true) LocalDate startDate,
      @RequestParam(value = "end_date", required = true) LocalDate endDate
  ) {
    return reportService.getShippersUsedReport(startDate, endDate);
  }

  @Operation(summary = "Get Carriers Used Report")
  @GetMapping("/carriers_used")
  public List<CarriersUsedReportResponse> getCarriersUsedReport(
      @RequestParam(value = "start_date", required = true) LocalDate startDate,
      @RequestParam(value = "end_date", required = true) LocalDate endDate
  ) {
    return reportService.getCarriersUsedReport(startDate, endDate);
  }

  @Operation(summary = "Get Payables Report")
  @GetMapping("/payables")
  public List<PayablesReportResponse> getPayablesReport(
      @RequestParam(value = "start_date", required = true) LocalDate startDate,
      @RequestParam(value = "end_date", required = true) LocalDate endDate
  ) {
    return reportService.getPayablesReport(startDate, endDate);
  }

  @Operation(summary = "Get Dispatcher Payments Report")
  @GetMapping("/dispatcher_payments")
  public List<DispatcherPaymentsReportResponse> getDispatcherPaymentsReport() {
    return reportService.getDispatcherPaymentsReport();
  }
}
