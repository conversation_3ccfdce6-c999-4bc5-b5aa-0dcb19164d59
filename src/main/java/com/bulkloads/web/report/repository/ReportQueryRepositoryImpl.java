package com.bulkloads.web.report.repository;

import static com.bulkloads.common.Converters.dateToSql;
import static com.bulkloads.web.report.repository.template.GetAssignmentStatusQueryTemplate.GET_ASSIGNMENT_STATUS_QUERY_TEMPLATE;
import static com.bulkloads.web.report.repository.template.GetCarriersUsedQueryTemplate.GET_CARRIERS_USED_QUERY_TEMPLATE;
import static com.bulkloads.web.report.repository.template.GetDispatchedLoadQueryTemplate.GET_DISPATCHED_LOAD_QUERY_TEMPLATE;
import static com.bulkloads.web.report.repository.template.GetDispatcherPaymentsQueryTemplate.GET_DISPATCHER_PAYMENTS_QUERY_TEMPLATE;
import static com.bulkloads.web.report.repository.template.GetDispatcherQueryTemplate.GET_DISPATCHER_QUERY_TEMPLATE;
import static com.bulkloads.web.report.repository.template.GetPayablesQueryTemplate.GET_PAYABLES_QUERY_TEMPLATE;
import static com.bulkloads.web.report.repository.template.GetProductQueryTemplate.GET_PRODUCT_QUERY_TEMPLATE;
import static com.bulkloads.web.report.repository.template.GetShippersUsedQueryTemplate.GET_SHIPPERS_USED_QUERY_TEMPLATE;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.report.service.dto.AssignmentStatusReportResponse;
import com.bulkloads.web.report.service.dto.CarriersUsedReportResponse;
import com.bulkloads.web.report.service.dto.DispatchedLoadReportResponse;
import com.bulkloads.web.report.service.dto.DispatcherPaymentsReportResponse;
import com.bulkloads.web.report.service.dto.DispatcherReportResponse;
import com.bulkloads.web.report.service.dto.PayablesReportResponse;
import com.bulkloads.web.report.service.dto.ProductReportResponse;
import com.bulkloads.web.report.service.dto.ShippersUsedReportResponse;
import com.bulkloads.web.report.service.dto.transformer.AssignmentStatusReportResponseTransformer;
import com.bulkloads.web.report.service.dto.transformer.CarriersUsedReportResponseTransformer;
import com.bulkloads.web.report.service.dto.transformer.DispatchedLoadReportResponseTransformer;
import com.bulkloads.web.report.service.dto.transformer.DispatcherPaymentsReportResponseTransformer;
import com.bulkloads.web.report.service.dto.transformer.DispatcherReportResponseTransformer;
import com.bulkloads.web.report.service.dto.transformer.PayablesReportResponseTransformer;
import com.bulkloads.web.report.service.dto.transformer.ProductReportResponseTransformer;
import com.bulkloads.web.report.service.dto.transformer.ShippersUsedReportResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class ReportQueryRepositoryImpl implements ReportQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final AssignmentStatusReportResponseTransformer assignmentStatusReportResponseTransformer;
  private final DispatcherReportResponseTransformer dispatcherReportResponseTransformer;
  private final DispatchedLoadReportResponseTransformer dispatchedLoadReportResponseTransformer;
  private final ProductReportResponseTransformer productReportResponseTransformer;
  private final ShippersUsedReportResponseTransformer shippersUsedReportResponseTransformer;
  private final CarriersUsedReportResponseTransformer carriersUsedReportResponseTransformer;
  private final PayablesReportResponseTransformer payablesReportResponseTransformer;
  private final DispatcherPaymentsReportResponseTransformer dispatcherPaymentsReportResponseTransformer;

  @Override
  public List<AssignmentStatusReportResponse> getAssignmentStatusReport(final int userCompanyId) {
    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);

    return jpaNativeQueryService.query(GET_ASSIGNMENT_STATUS_QUERY_TEMPLATE, params, assignmentStatusReportResponseTransformer);
  }

  public List<DispatcherReportResponse> getDispatcherReport(final int userCompanyId, final LocalDate startDate, final LocalDate endDate) {

    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);
    params.put("startDate", dateToSql(startDate));
    params.put("endDate", dateToSql(endDate));

    return jpaNativeQueryService.query(GET_DISPATCHER_QUERY_TEMPLATE, params, dispatcherReportResponseTransformer);
  }

  public List<DispatchedLoadReportResponse> getDispatchedLoadReport(final int userCompanyId) {
    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);

    return jpaNativeQueryService.query(GET_DISPATCHED_LOAD_QUERY_TEMPLATE, params, dispatchedLoadReportResponseTransformer);
  }

  public List<ProductReportResponse> getProductReport(final int userCompanyId) {
    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);

    return jpaNativeQueryService.query(GET_PRODUCT_QUERY_TEMPLATE, params, productReportResponseTransformer);
  }

  public List<ShippersUsedReportResponse> getShippersUsedReport(final int userCompanyId, final LocalDate startDate, final LocalDate endDate) {
    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);
    params.put("startDate", dateToSql(startDate));
    params.put("endDate", dateToSql(endDate));

    return jpaNativeQueryService.query(GET_SHIPPERS_USED_QUERY_TEMPLATE, params, shippersUsedReportResponseTransformer);
  }

  public List<CarriersUsedReportResponse> getCarriersUsedReport(final int userCompanyId, final LocalDate startDate, final LocalDate endDate) {
    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);
    params.put("startDate", dateToSql(startDate));
    params.put("endDate", dateToSql(endDate));

    return jpaNativeQueryService.query(GET_CARRIERS_USED_QUERY_TEMPLATE, params, carriersUsedReportResponseTransformer);
  }

  public List<PayablesReportResponse> getPayablesReport(final int userCompanyId, final LocalDate startDate, final LocalDate endDate) {
    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);
    params.put("startDate", dateToSql(startDate));
    params.put("endDate", dateToSql(endDate));

    return jpaNativeQueryService.query(GET_PAYABLES_QUERY_TEMPLATE, params, payablesReportResponseTransformer);
  }

  public List<DispatcherPaymentsReportResponse> getDispatcherPaymentsReport(final int userCompanyId) {
    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);

    return jpaNativeQueryService.query(GET_DISPATCHER_PAYMENTS_QUERY_TEMPLATE, params, dispatcherPaymentsReportResponseTransformer);
  }

}
