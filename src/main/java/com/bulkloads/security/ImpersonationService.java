package com.bulkloads.security;

import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.usercompany.repository.UserCompanyRepository;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ImpersonationService {

  private final BulkLoadsUserDetailsService userDetailsService;
  private final UserCompanyRepository userCompanyRepository;

  public void impersonate(final int userId) {
    Actor actor = userDetailsService.loadUserByIdWithPermissions(userId, null);
    var newContext = SecurityContextHolder.createEmptyContext();
    newContext.setAuthentication(new UsernamePasswordAuthenticationToken(actor, null, actor.getAuthorities()));
    SecurityContextHolder.setContext(newContext);
  }

  public void impersonateByUserCompanyId(final int userCompanyId) {
    final UserCompany userCompany = userCompanyRepository.getReferenceById(userCompanyId);
    final int userId = userCompany.getOwner().getUserId();
    impersonate(userId);
  }

}
