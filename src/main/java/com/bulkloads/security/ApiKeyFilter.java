package com.bulkloads.security;

import static com.bulkloads.config.AppConstants.API_KEY_ATTRIBUTE;
import static com.bulkloads.config.AppConstants.Header.X_API_KEY;
import java.io.IOException;
import java.util.Optional;
import com.bulkloads.web.apikey.projection.ApiKeyResultWithEndpointId;
import com.bulkloads.web.apikey.repository.ApiKeyRepository;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class ApiKeyFilter extends OncePerRequestFilter {

  private final ApiKeyRepository apiKeyRepository;

  @Override
  protected void doFilterInternal(@NotNull final HttpServletRequest request,
                                  @NotNull final HttpServletResponse response,
                                  @NotNull final FilterChain filterChain) throws IOException, ServletException {

    //if (appProperties.isProdMode()) {
    //TODO enable
    validateApiKey(request);

    filterChain.doFilter(request, response);
  }

  @SuppressWarnings("java:S2201")
  private void validateApiKey(final HttpServletRequest request) {

    final String method = request.getMethod();
    final String path = request.getRequestURI()
        .replace("/rest", "")
        .replaceAll("/files/[a-zA-Z0-9]+/track_view", "/files/{int}/track_view")
        .replaceAll("/\\d+", "/{int}");

    // login endpoint allowed w/o api key
    //TODO sort this out
    if (path.toLowerCase().startsWith("/login")
        || path.toLowerCase().contains("/eld/authorization")
        || path.toLowerCase().contains("/quickbooks/authorization")
        || path.toLowerCase().startsWith("/links")) {
      return;
    }

    final String apiKeyToken = request.getHeader(X_API_KEY);

    if (!StringUtils.hasText(apiKeyToken)) {
      throw new BadCredentialsException("The X-API-KEY is missing");
    }

    final Optional<ApiKeyResultWithEndpointId> apiKeyWithEndpointOpt = apiKeyRepository.findApiKeyWithEndpoint(apiKeyToken, method, path);
    if (apiKeyWithEndpointOpt.isEmpty()) {
      throw new BadCredentialsException("The X-API-KEY is invalid");
    }

    apiKeyWithEndpointOpt.get().getApiEndpointId()
        .orElseThrow(() -> new AccessDeniedException("The X-API-KEY doesn't have access to this endpoint"));

    request.setAttribute(API_KEY_ATTRIBUTE, apiKeyWithEndpointOpt.get());

  }
}
