package com.bulkloads.security;

import com.bulkloads.config.AppProperties;
import com.bulkloads.security.oauth.Oauth2AuthenticationSuccessHandler;
import com.bulkloads.security.oauth.QuickBooksRealmIdCaptureFilter;
import com.bulkloads.web.apikey.repository.ApiKeyRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.hierarchicalroles.RoleHierarchy;
import org.springframework.security.access.hierarchicalroles.RoleHierarchyImpl;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.authentication.configuration.EnableGlobalAuthentication;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.CsrfConfigurer;
import org.springframework.security.config.annotation.web.configurers.RequestCacheConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.DefaultOAuth2AuthorizationRequestResolver;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestResolver;
import org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.expression.DefaultWebSecurityExpressionHandler;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@EnableGlobalAuthentication
public class SecurityConfiguration {

  private final BulkLoadsJwtTokenService bulkloadsJwtTokenService;
  private final BulkLoadsUserDetailsService userDetailsService;
  private final ApiKeyRepository apiKeyRepository;
  private final HandlerExceptionResolver resolver;
  private final Oauth2AuthenticationSuccessHandler oauth2AuthenticationSuccessHandler;
  private final AppProperties appProperties;

  public SecurityConfiguration(final BulkLoadsJwtTokenService bulkloadsJwtTokenService,
                               final BulkLoadsUserDetailsService userDetailsService,
                               final ApiKeyRepository apiKeyRepository,
                               @Qualifier("handlerExceptionResolver") final HandlerExceptionResolver resolver,
                               final Oauth2AuthenticationSuccessHandler oauth2AuthenticationSuccessHandler,
                               final AppProperties appProperties) {
    this.bulkloadsJwtTokenService = bulkloadsJwtTokenService;
    this.userDetailsService = userDetailsService;
    this.apiKeyRepository = apiKeyRepository;
    this.resolver = resolver;
    this.oauth2AuthenticationSuccessHandler = oauth2AuthenticationSuccessHandler;
    this.appProperties = appProperties;
  }

  @Bean
  public AuthenticationManager authenticationManager(AuthenticationConfiguration conf) throws Exception {
    return conf.getAuthenticationManager();
  }

  @Bean
  public PasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
  }

  // TODO: Create test to verify functionallity
  @Bean
  public RoleHierarchy roleHierarchy() {
    RoleHierarchyImpl hierarchy = new RoleHierarchyImpl();
    hierarchy.setHierarchy("ROLE_SITE_ADMIN > ROLE_USER");
    return hierarchy;
  }

  // TODO: Create test to verify functionallity
  @Bean
  public DefaultMethodSecurityExpressionHandler defaultMethodSecurityExpressionHandler() {
    var methodSecurityHandler = new DefaultMethodSecurityExpressionHandler();
    methodSecurityHandler.setRoleHierarchy(roleHierarchy());
    return methodSecurityHandler;
  }

  // TODO: Create test to verify functionallity
  @Bean
  public DefaultWebSecurityExpressionHandler expressionHandler() {
    var expressionHandler = new DefaultWebSecurityExpressionHandler();
    expressionHandler.setRoleHierarchy(roleHierarchy());
    return expressionHandler;
  }

  @Bean
  public OAuth2AuthorizationRequestResolver authorizationRequestResolver(
      ClientRegistrationRepository clientRegistrationRepository) {
    return new DefaultOAuth2AuthorizationRequestResolver(
        clientRegistrationRepository,
        "/rest/oauth2/authorization"
    );
  }

  @Bean
  @Order(1)
  public SecurityFilterChain apiConfiguration(
      final HttpSecurity http,
      final OAuth2AuthorizationRequestResolver authorizationRequestResolver) throws Exception {
    return http
        .securityMatcher("/rest/**", "/login/**")
        .cors(Customizer.withDefaults())
        .csrf(CsrfConfigurer::disable)
        .sessionManagement(conf -> conf.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .requestCache(RequestCacheConfigurer::disable)
        .anonymous(AbstractHttpConfigurer::disable)
        .exceptionHandling(handling -> handling
            .authenticationEntryPoint((request, response, exception) -> {
              throw exception;
            }))
        .authorizeHttpRequests(authorize -> authorize
            .requestMatchers(HttpMethod.GET, "/rest/cities/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/commodity_listings/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/companies/auto").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/equipments").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/forum/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/loads/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/loads/searches").permitAll()
            .requestMatchers(HttpMethod.POST, "/rest/login").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/signin/google").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/phones/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/states/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/trucks/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/users/auto").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/utils/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/washouts").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/washouts/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/eld/authorization/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/rest/quickbooks/authorization/**").permitAll()
            .requestMatchers("/rest/integration/**").permitAll()
            .requestMatchers("/rest/links").permitAll()
            .requestMatchers("/rest/links/**").permitAll()
            .anyRequest().authenticated()
        )
        .addFilterBefore(quickBooksRealmIdCaptureFilter(), OAuth2LoginAuthenticationFilter.class)
        .addFilterBefore(jwtFilter(), OAuth2AuthorizationRequestRedirectFilter.class)
        .addFilterBefore(apiKeyFilter(), JwtFilter.class)
        .addFilterBefore(loggingFilter(), ApiKeyFilter.class)
        .addFilterBefore(filterChainExceptionHandler(), LoggingFilter.class)
        .oauth2Login(oauth2Login -> oauth2Login
            .authorizationEndpoint(authorizationEndpoint ->
                authorizationEndpoint.authorizationRequestResolver(authorizationRequestResolver))
            .successHandler(oauth2AuthenticationSuccessHandler))
        .build();
  }

  @Bean
  @Order(2)
  public SecurityFilterChain docsConfiguration(HttpSecurity http) throws Exception {
    return http.securityMatcher("/api-docs/**")
        .cors(Customizer.withDefaults())
        .csrf(CsrfConfigurer::disable)
        .sessionManagement(conf -> conf.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .requestCache(RequestCacheConfigurer::disable)
        .anonymous(AbstractHttpConfigurer::disable)
        .authorizeHttpRequests(authorize -> authorize
            .requestMatchers("/api-docs/**").permitAll()
            .anyRequest().authenticated()
        )
        .build();
  }

  private OncePerRequestFilter quickBooksRealmIdCaptureFilter() {
    return new QuickBooksRealmIdCaptureFilter();
  }

  private OncePerRequestFilter jwtFilter() {
    return new JwtFilter(bulkloadsJwtTokenService, userDetailsService, appProperties);
  }

  private OncePerRequestFilter apiKeyFilter() {
    return new ApiKeyFilter(apiKeyRepository);
  }

  private OncePerRequestFilter loggingFilter() {
    return new LoggingFilter(bulkloadsJwtTokenService);
  }

  private OncePerRequestFilter filterChainExceptionHandler() {
    return new FilterChainExceptionHandler(resolver);
  }
}
