package com.bulkloads.common.mui.model;

import static com.bulkloads.common.Converters.dateToSql;
import static com.bulkloads.common.StringUtil.toSnakeCase;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import com.bulkloads.common.mui.ExcludeFromMuiQuery;
import com.bulkloads.common.mui.NaturalSort;
import com.bulkloads.exception.BulkloadsException;
import com.qs.core.model.QSObject;
import com.qs.core.parser.ParseException;
import com.qs.core.parser.QSParser;
import jakarta.validation.ConstraintViolationException;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class QueryParams {

  private FilterModel filterModel;
  private SortModel sortModel;
  private Map<String, FieldInfo> fields;

  public QueryParams(String queryString) {
    this(queryString, null);
  }

  public QueryParams(String queryString, Map<String, FieldInfo> fields) {

    if (queryString == null) {
      queryString = "";
    }
    QSObject res;
    try {
      res = new QSParser().parse(queryString);
    } catch (ParseException e) {
      throw new BulkloadsException(e);
    }
    this.fields = fields;

    filterModel = FilterModel.parse(res, fields);
    sortModel = SortModel.parse(res, fields);

    if (fields != null) {
      validate();
    }
  }

  public void validate() {
    validateFilterAndSortFieldNamesExist();
  }

  private void validateFilterAndSortFieldNamesExist() {
    Set<String> requestedFieldNames = getFilterAndSortFieldNames();
    Set<String> validFieldNames = fields.keySet();

    for (String column : requestedFieldNames) {
      if (!validFieldNames.contains(column)) {
        throw new ConstraintViolationException("Invalid column requested: " + column, Set.of());
      }
    }
  }

  private Set<String> getFilterAndSortFieldNames() {
    Set<String> fields = getFilterFieldNames();
    fields.addAll(getSortFieldNames());
    return fields;
  }

  private Set<String> getFilterFieldNames() {
    return getFilterModel().getItems()
        .stream()
        .map(FilterItem::getField)
        .collect(Collectors.toSet());
  }

  private Set<String> getSortFieldNames() {
    return getSortModel().getSortItems()
        .stream()
        .map(SortItem::getField)
        .collect(Collectors.toSet());
  }

  /*
  Sample STRING operations:
    name = 'alice';                  -- equals and is
    name <> 'alice';                 -- doesNotEqual and isNot
    name like '%alice%';             -- contains
    name not like '%alice%';         -- doesNotcontain
    name like 'ali%';                -- startsWith
    name like '%ice';                -- endsWith
    name is null or name = '';       -- isEmpty
    name is not null and name != ''; -- isNotEmpty
    name in ('Alice', 'Bob') ;       -- isAnyOf
   */

  private Map<String, Object> getFilterQueryValues() {
    Map<String, Object> params = new HashMap<>();

    for (FilterItem item : filterModel.getItems()) {

      String operator = item.getOperator();
      String field = item.getField();
      List<String> values = item.getValues();

      if (item.isString()) {

        if (operator.equals("equals") || operator.equals("is")) {
          params.put(field, values.get(0));
        } else if (operator.equals("doesNotEqual") || operator.equals("isNot")) {
          params.put(field, values.get(0));
        } else if (operator.equals("contains")) {
          params.put(field, "%" + values.get(0) + "%");
        } else if (operator.equals("doesNotContain")) {
          params.put(field, "%" + values.get(0) + "%");
        } else if (operator.equals("startsWith")) {
          params.put(field, values.get(0) + "%");
        } else if (operator.equals("endsWith")) {
          params.put(field, "%" + values.get(0));
        } else if (operator.equals("isEmpty")) {
          // params.put(field, null);
        } else if (operator.equals("isNotEmpty")) {
          // params.put(field, null);
        } else if (operator.equals("isAnyOf")) {
          for (int i = 0; i < values.size(); i++) {
            String paramName = field + "_in_" + i;
            params.put(paramName, values.get(i));
          }
        }
      } else if (item.isNumeric()) {

        if (operator.equals("=")) {
          params.put(field, values.get(0));
        } else if (operator.equals("!=")) {
          params.put(field, values.get(0));
        } else if (operator.equals(">")) {
          params.put(field, values.get(0));
        } else if (operator.equals(">=")) {
          params.put(field, values.get(0));
        } else if (operator.equals("<")) {
          params.put(field, values.get(0));
        } else if (operator.equals("<=")) {
          params.put(field, values.get(0));
        } else if (operator.equals("isEmpty")) {
          // do not add param
        } else if (operator.equals("isNotEmpty")) {
          // do not add param
        } else if (operator.equals("isAnyOf")) {
          for (int i = 0; i < values.size(); i++) {
            String paramName = field + "_in_" + i;
            params.put(paramName, values.get(i));
          }
        }

      } else if (item.isBoolean()) {

        if (operator.equals("is")) {
          if (values.get(0) != null) {
            if (values.get(0).equals("true") || values.get(0).equals("false")) {
              params.put(field, Boolean.valueOf(values.get(0)));
            }
          }
        }
      } else if (item.isDate()) {

        if (operator.equals("isEmpty")) {
          // do not add param
        } else if (operator.equals("isNotEmpty")) {
          // do not add param
        } else {
          if (!isEmpty(values.get(0))) {
            String sqlFormatedDate = dateToSql(values.get(0));
            if (operator.equals("is")) {
              params.put(field, sqlFormatedDate);
            } else if (operator.equals("not")) {
              params.put(field, sqlFormatedDate);
            } else if (operator.equals("after")) {
              params.put(field, sqlFormatedDate);
            } else if (operator.equals("onOrAfter")) {
              params.put(field, sqlFormatedDate);
            } else if (operator.equals("before")) {
              params.put(field, sqlFormatedDate);
            } else if (operator.equals("onOrBefore")) {
              params.put(field, sqlFormatedDate);
            }
          }
        }
      }
    }

    // add the values for quick filter too:
    final List<String> terms = filterModel.getQuickFilterValues();
    for (int i = 0; i < terms.size(); i++) {
      String term = terms.get(i);
      if (!term.isEmpty()) {
        params.put("quick_filter_search_word_" + i, "%" + term + "%");
      }
    }

    return params;
  }

  private String getFilterQuery() {
    StringBuilder query = new StringBuilder();
    List<String> filterParts = new ArrayList<>();

    for (FilterItem item : filterModel.getItems()) {

      String operator = item.getOperator();
      String field = item.getField();
      List<String> values = item.getValues();
      // String value = item.getValues().get(0);

      if (item.isString()) {

        if (operator.equals("equals") || operator.equals("is")) {
          filterParts.add("(`" + field + "` = :" + field + ")");

        } else if (operator.equals("doesNotEqual") || operator.equals("isNot")) {
          filterParts.add("(`" + field + "` <> :" + field + ")");

        } else if (operator.equals("contains")) {
          filterParts.add("(`" + field + "` like :" + field + ")");

        } else if (operator.equals("doesNotContain")) {
          filterParts.add("(`" + field + "` not like :" + field + ")");

        } else if (operator.equals("startsWith")) {
          filterParts.add("(`" + field + "` like :" + field + ")");

        } else if (operator.equals("endsWith")) {
          filterParts.add("(`" + field + "` like :" + field + ")");

        } else if (operator.equals("isEmpty")) {
          filterParts.add("(`" + field + "` is null or `" + field + "` = '')");

        } else if (operator.equals("isNotEmpty")) {
          filterParts.add("(`" + field + "` is not null and `" + field + "` != '')");

        } else if (operator.equals("isAnyOf")) {

          List<String> inParamsList = new ArrayList<>();
          for (int i = 0; i < values.size(); i++) {
            inParamsList.add(":" + field + "_in_" + i);
          }
          String inParams = String.join(", ", inParamsList);
          filterParts.add("(`" + field + "` in (" + inParams + "))");
        }

      } else if (item.isNumeric()) {

        if (operator.equals("=")) {
          filterParts.add("(`" + field + "` = :" + field + ")");
        } else if (operator.equals("!=")) {
          filterParts.add("(`" + field + "` <> :" + field + ")");
        } else if (operator.equals(">")) {
          filterParts.add("(`" + field + "` > :" + field + ")");
        } else if (operator.equals(">=")) {
          filterParts.add("(`" + field + "` >= :" + field + ")");
        } else if (operator.equals("<")) {
          filterParts.add("(`" + field + "` < :" + field + ")");
        } else if (operator.equals("<=")) {
          filterParts.add("(`" + field + "` <= :" + field + ")");
        } else if (operator.equals("isEmpty")) {
          filterParts.add("(`" + field + "` is null)");
        } else if (operator.equals("isNotEmpty")) {
          filterParts.add("(`" + field + "` is not null)");
        } else if (operator.equals("isAnyOf")) {

          List<String> inParamsList = new ArrayList<>();
          for (int i = 0; i < values.size(); i++) {
            inParamsList.add(":" + field + "_in_" + i);
          }
          String inParams = String.join(", ", inParamsList);
          filterParts.add("(`" + field + "` in (" + inParams + "))");
        }
      } else if (item.isBoolean()) {
        if (item.getValues().get(0) != null) {
          if (item.getValues().get(0).equals("true") || item.getValues().get(0).equals("false")) {
            filterParts.add("(`" + field + "` = :" + field + ")");
          }
        }
      } else if (item.isDate()) {

        if (operator.equals("isEmpty")) {
          filterParts.add("(`" + field + "` is null)");
        } else if (operator.equals("isNotEmpty")) {
          filterParts.add("(`" + field + "` is not null)");
        } else {
          if (!isEmpty(item.getValues().get(0))) {
            if (operator.equals("is")) {
              filterParts.add("(`" + field + "` = :" + field + ")");
            } else if (operator.equals("not")) {
              filterParts.add("(`" + field + "` <> :" + field + ")");
            } else if (operator.equals("after")) {
              filterParts.add("(`" + field + "` > :" + field + ")");
            } else if (operator.equals("onOrAfter")) {
              filterParts.add("(`" + field + "` >= :" + field + ")");
            } else if (operator.equals("before")) {
              filterParts.add("(`" + field + "` < :" + field + ")");
            } else if (operator.equals("onOrBefore")) {
              filterParts.add("(`" + field + "` <= :" + field + ")");
            }
          }
        }
      }
    }

    final List<String> terms = filterModel.getQuickFilterValues();

    for (int i = 0; i < terms.size(); i++) {
      String term = terms.get(i);
      if (!term.isEmpty()) {
        List<String> fieldComparisons = new ArrayList<>();
        for (String field : fields.keySet()) {
          fieldComparisons.add("`" + field + "` like :quick_filter_search_word_" + i);
        }
        String group = "(" + String.join(" OR ", fieldComparisons) + ")";
        filterParts.add(group);
      }
    }

    String delimeter = " " + filterModel.getLogicOperator() + " ";
    query.append(String.join(delimeter, filterParts));
    return query.toString();
  }

  private String getSortQuery() {
    List<String> sortParts = new ArrayList<>();
    for (SortItem item : sortModel.getSortItems()) {
      var field = item.getField();
      var sort = item.getSort();
      String orderByPart;
      if (item.isNaturalSort()) {
        orderByPart = "natural_sort(`" + field + "`) " + sort;
      } else {
        orderByPart = "`" + field + "` " + sort;
      }
      sortParts.add(orderByPart);
    }
    return String.join(",", sortParts);
  }

  public Map<String, Object> buildParamsMap() {
    Map<String, Object> params = new HashMap<>(getFilterQueryValues());
    // TODO: why do we also include FilterQueryValues in params but both in the first level?
    params.put("params", getFilterQueryValues());
    params.put("having_clause", getFilterQuery());
    params.put("order_by_clause", getSortQuery());
    return params;
  }

  public static Map<String, FieldInfo> getFieldInfoFromClass(Class<?> clazz) {
    Field[] fields = clazz.getDeclaredFields();
    return Arrays.stream(fields)
        .filter(field -> !field.isAnnotationPresent(ExcludeFromMuiQuery.class))
        .collect(Collectors.toMap(
            field -> toSnakeCase(field.getName()),
            field -> FieldInfo.builder()
                .type(field.getType())
                .naturalSort(field.isAnnotationPresent(NaturalSort.class))
                .build()
        ));
  }

}
