package com.bulkloads.common.validation;

import static java.util.Objects.isNull;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The methods starting with <b>exists</b> or <b>isMissing</b> are only applicable to <b>Optional<?></b>
 */

public class ValidationUtils {

  private static final Pattern URL_PATTERN = Pattern.compile("^(https?|ftp)://[^\s/$.?#].[^\s]*$", Pattern.CASE_INSENSITIVE);
  private static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$";
  private static final Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_REGEX);
  private static final String PHONE_REGEX = "^(\\+\\d{1,3}[- ]?)?\\d{10}$";
  private static final Pattern PHONE_PATTERN = Pattern.compile(PHONE_REGEX);

  private ValidationUtils() {
  }

  public static boolean exists(final Object object) {
    return object != null;
  }

  public static boolean isEmpty(final Object object) {
    if (object == null) {
      return true;
    }
    if (object instanceof Optional<?> o) {
      if (o.isEmpty()) {
        return true;
      }
      if (o.get() instanceof CharSequence cs) {
        return cs.isEmpty();
      }
    }
    if (object instanceof CharSequence cs) {
      return cs.isEmpty();
    }
    if (object.getClass().isArray()) {
      return Array.getLength(object) == 0;
    }
    if (object instanceof Collection<?>) {
      return ((Collection<?>) object).isEmpty();
    }
    if (object instanceof Map<?, ?>) {
      return ((Map<?, ?>) object).isEmpty();
    }
    return false;
  }

  public static boolean existsAndIsEmpty(final Optional<?> value) {
    return exists(value) && isEmpty(value);
  }

  public static boolean existsAndIsNotEmpty(final Optional<?> value) {
    return exists(value) && !isEmpty(value);
  }

  public static boolean existsAndIsTrue(final Optional<Boolean> value) {
    return exists(value) && value.isPresent() && value.get();
  }

  public static boolean existsAndIsFalse(final Optional<Boolean> value) {
    return exists(value) && value.isPresent() && !value.get();
  }

  public static boolean isMissingOrIsEmpty(final Optional<?> val) {
    return !exists(val) || existsAndIsEmpty(val);
  }

  public static boolean hasChange(final String oldValue, final Optional<String> newValue) {
    return exists(newValue) && newValue.map(o -> !o.equals(oldValue)).orElseGet(() -> oldValue != null);
  }

  /*
  public static boolean hasChange(final BigDecimal oldValue, final Optional<BigDecimal> newValue) {
    return exists(newValue) && newValue.map(o -> o.compareTo(oldValue) != 0).orElseGet(() -> oldValue != null);
  }
  */

  public static <T> boolean hasChange(final T oldValue, final Optional<?> newValueOpt) {
    if (!exists(newValueOpt)) {
      return false;  // if Optional is null, no change
    }
    if (newValueOpt.isEmpty()) {
      return oldValue != null;  // change detected if old value was non-null
    }

    if (oldValue == null) {
      return true;  // change detected if old value was null
    }

    final Object newValue = newValueOpt.get();
    // if newValue is BigDecimal, use compareTo
    if (oldValue instanceof BigDecimal && newValue instanceof BigDecimal) {
      return ((BigDecimal) newValue).compareTo((BigDecimal) oldValue) != 0;
    }

    return !Objects.equals(oldValue, newValue);  // compare actual values
  }

  public static boolean isValidEmail(String email) {
    Matcher matcher = EMAIL_PATTERN.matcher(email);
    return matcher.matches();
  }

  public static boolean isValidUrl(String url) {
    Matcher matcher = URL_PATTERN.matcher(url);
    return matcher.matches();
  }

  public static boolean isValidPhone(String phone) {
    Matcher matcher = PHONE_PATTERN.matcher(phone);
    return matcher.matches();
  }

  public static boolean isBetween(Double val, double lo, double hi) {
    if (val == null) {
      return false;
    }
    return val > lo && val < hi;
  }

  public static boolean isBetween(BigDecimal val, double lo, double hi) {
    if (Objects.isNull(val)) {
      return false;
    }
    return val.compareTo(BigDecimal.valueOf(lo)) > 0 && val.compareTo(BigDecimal.valueOf(hi)) < 0;
  }

  public static boolean isBetweenOrEqual(Double val, double lo, double hi) {
    if (val == null) {
      return false;
    }
    return val >= lo && val <= hi;
  }

  public static boolean isBetweenOrEqual(BigDecimal val, double lo, double hi) {
    if (Objects.isNull(val)) {
      return false;
    }
    return val.compareTo(BigDecimal.valueOf(lo)) >= 0 && val.compareTo(BigDecimal.valueOf(hi)) <= 0;
  }

  public static boolean isGreater(Double val, double lo) {
    if (val == null) {
      return false;
    }
    return val > lo;
  }

  public static boolean isGreater(BigDecimal val, double lo) {
    if (Objects.isNull(val)) {
      return false;
    }
    return val.compareTo(BigDecimal.valueOf(lo)) > 0;
  }

  public static boolean isGreaterOrEqual(Double val, double lo) {
    if (val == null) {
      return false;
    }
    return val >= lo;
  }

  public static boolean isGreaterOrEqual(BigDecimal val, double lo) {
    if (Objects.isNull(val)) {
      return false;
    }
    return val.compareTo(BigDecimal.valueOf(lo)) >= 0;
  }

  public static boolean isLess(Double val, double hi) {
    if (val == null) {
      return false;
    }
    return val < hi;
  }

  public static boolean isLess(BigDecimal val, double hi) {
    if (Objects.isNull(val)) {
      return false;
    }
    return val.compareTo(BigDecimal.valueOf(hi)) < 0;
  }

  public static boolean isLessOrEqual(Double val, double hi) {
    if (val == null) {
      return false;
    }
    return val <= hi;
  }

  public static boolean isLessOrEqual(BigDecimal val, double hi) {
    if (Objects.isNull(val)) {
      return false;
    }
    return val.compareTo(BigDecimal.valueOf(hi)) <= 0;
  }

  public static boolean isNegative(Double val) {
    return isLess(val, 0);
  }

  public static boolean isNegative(BigDecimal val) {
    return isLess(val, 0);
  }

  public static boolean isPositive(Double val) {
    return isGreater(val, 0);
  }

  public static boolean isPositive(BigDecimal val) {
    return isGreater(val, 0);
  }

  public static boolean equal(String val1, String val2) {
    if (isNull(val1)) {
      return false;
    }
    return val1.equalsIgnoreCase(val2);
  }

}