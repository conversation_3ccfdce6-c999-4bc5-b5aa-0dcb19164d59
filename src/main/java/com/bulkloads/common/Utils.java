package com.bulkloads.common;

import java.awt.Desktop;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Utils {

  public static void openFileWithDefaultApp(Path file) {

    if (!Desktop.isDesktopSupported()) {
      log.warn("Desktop is not supported, cannot open the file.");
      return;
    }

    Desktop desktop = Desktop.getDesktop();
    if (file.toFile().exists()) {
      try {
        desktop.open(file.toFile());
      } catch (IOException e) {
        log.error("Failed to open the file: {}", e.getMessage());
      }
    } else {
      log.warn("The file does not exist: {}", file);
    }
  }

  // TODO: move this to FileUtils most def

  public static Path getFileFromResources(String resourcePath) throws URISyntaxException {
    URL resourceUrl = Utils.class.getClassLoader().getResource(resourcePath);

    if (resourceUrl == null) {
      throw new IllegalArgumentException("Resource not found: " + resourcePath);
    }

    return Paths.get(resourceUrl.toURI());
  }
}
