package com.bulkloads.common;

import static java.time.temporal.ChronoUnit.SECONDS;
import static java.util.Objects.isNull;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import com.bulkloads.exception.BulkloadsException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

public class Converters {

  public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
  static final DateTimeFormatter DATE_FORMATTER_ISO = DateTimeFormatter.ISO_DATE;
  static final DateTimeFormatter DATE_FORMATTER_ISO_INSTANT = DateTimeFormatter.ISO_INSTANT;

  public static <T> Optional<T> wrap(T value) {
    return Optional.ofNullable(value);
  }

  public static <T> T unwrap(Optional<T> optional) {
    if (optional == null) {
      return null;
    }
    return optional.orElse(null);
  }

  public static <T> Collector<T, ?, ArrayList<T>> asList() {
    return Collectors.toCollection(ArrayList::new);
  }

  public static String asCsList(String[] values) {
    return asCsList(Arrays.asList(values));
  }

  public static String asCsList(Collection<?> values) {
    return values.stream().map(String::valueOf).collect(Collectors.joining(","));
  }

  public static String instantToSql(Instant dateTime) {
    if (dateTime == null) {
      return null;
    }
    return dateTime.atOffset(OffsetDateTime.now().getOffset()).toLocalDateTime().truncatedTo(SECONDS).toString();
  }

  public static String dateToSql(String value) {
    OffsetDateTime offsetDateTime = OffsetDateTime.parse(value, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    LocalDate date = offsetDateTime.toLocalDate();
    return date.toString();
  }

  public static String dateToSql(LocalDate date) {
    if (date == null) {
      return null;
    }
    return date.format(DATE_FORMATTER_ISO);
  }

  public static Map<String, Object> jsonStringToMap(String data) {
    try {
      return OBJECT_MAPPER.readValue(data, new TypeReference<>() {
      });
    } catch (JsonProcessingException e) {
      throw new BulkloadsException(e);
    }
  }

  public static String mapToJsonString(Map<String, Object> data) {
    try {
      return OBJECT_MAPPER.writeValueAsString(data);
    } catch (JsonProcessingException e) {
      throw new BulkloadsException(e);
    }
  }

  public static List<Integer> jsonStringToIntegerList(String data) {
    try {
//      if (isEmpty(data)) {
//        return new ArrayList<>();
//      }
      return OBJECT_MAPPER.readValue(data, new TypeReference<>() {
      });
    } catch (JsonProcessingException e) {
      throw new BulkloadsException(e);
    }
  }

  public static String integerListToJsonString(List<Integer> data) {
    try {
      if (isNull(data)) {
        return null;
      }
      return OBJECT_MAPPER.writeValueAsString(data);
    } catch (JsonProcessingException e) {
      throw new BulkloadsException(e);
    }
  }

}
