package com.bulkloads.fmcsa.service;

import java.util.List;
import com.bulkloads.fmcsa.model.FmcsaCity;
import com.bulkloads.fmcsa.repository.FmcsaCityRepository;
import org.springframework.data.util.Streamable;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class FmcsaCityService {

  private final FmcsaCityRepository fmcsaCityRepository;

  public List<FmcsaCity> findAll() {
    return Streamable.of(fmcsaCityRepository.findAll()).toList();
  }
}
