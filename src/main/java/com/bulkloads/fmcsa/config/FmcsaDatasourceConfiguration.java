package com.bulkloads.fmcsa.config;

import java.util.Objects;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    basePackages = "com.bulkloads.fmcsa",
    entityManagerFactoryRef = "fmcsaEntityManagerFactory",
    transactionManagerRef = "fmcsaTransactionManager"
)
public class FmcsaDatasourceConfiguration {

  @Bean
  @ConfigurationProperties("spring.datasource.fmcsa")
  public DataSourceProperties fmcsaDataSourceProperties() {
    return new DataSourceProperties();
  }

  @Bean
  @ConfigurationProperties("spring.datasource.fmcsa.hikari")
  public DataSource fmcsaDataSource() {
    return fmcsaDataSourceProperties()
        .initializeDataSourceBuilder()
        .build();
  }

  @Bean
  public LocalContainerEntityManagerFactoryBean fmcsaEntityManagerFactory(
      @Qualifier("fmcsaDataSource") DataSource dataSource,
      EntityManagerFactoryBuilder builder) {

    return builder.dataSource(dataSource)
        .packages("com.bulkloads.fmcsa")
        .build();
  }

  @Bean
  public PlatformTransactionManager fmcsaTransactionManager(
      @Qualifier("fmcsaEntityManagerFactory")
      LocalContainerEntityManagerFactoryBean fmcsaEntityManagerFactory) {

    return new JpaTransactionManager(Objects.requireNonNull(fmcsaEntityManagerFactory.getObject()));
  }

}

