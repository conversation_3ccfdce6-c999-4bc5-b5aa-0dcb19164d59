package com.bulkloads;

import static com.bulkloads.config.AppConstants.Paths.FILETYPE_ICONS;
import static com.bulkloads.config.AppConstants.Paths.IMAGES;
import static com.bulkloads.config.AppConstants.Paths.TEMP;
import static com.bulkloads.config.AppConstants.Paths.TEMP_THUMBS;
import static com.bulkloads.web.file.util.FileUtils.ensureDirectoryExists;
import static com.bulkloads.web.file.util.FileUtils.testDirectoryExists;

import javax.annotation.PostConstruct;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

@SpringBootApplication
@ConfigurationPropertiesScan("com.bulkloads")
public class WebApplication {

  @PostConstruct
  public void init() {
    ensureDirectoryExists(TEMP);
    ensureDirectoryExists(TEMP_THUMBS);
    testDirectoryExists(IMAGES);
    testDirectoryExists(FILETYPE_ICONS);
  }

  public static void main(String[] args) {
    SpringApplication springApplication = new SpringApplication(WebApplication.class);
    springApplication.addListeners(new ApplicationPidFileWriter());
    springApplication.run(args);
  }
}
