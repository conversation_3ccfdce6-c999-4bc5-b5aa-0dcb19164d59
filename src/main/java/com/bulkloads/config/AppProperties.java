package com.bulkloads.config;

import java.util.List;
import org.springframework.boot.context.properties.ConfigurationProperties;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@ConfigurationProperties(prefix = "bulkloads")
public class AppProperties {

  private boolean prodMode;
  private boolean webSocketsEnabled;
  private PubNub pubNub;
  private Twilio twilio;
  private Google google;
  private Firebase firebase;
  private Mapbox mapBox;
  private Mailing mailing;
  private Queue notification;
  private Queue confirmation;
  private Queue offer;
  private Queue sms;
  private Queue splitPdf;
  private Queue invoice;
  private Queue quickBooks;
  private FileOcr fileOcr;
  private String domainUrl;
  private String shortDomainUrl;
  private String integrationRedirectUri;
  private String ftpRoot;
  private String fusionReactorApplicationName;

  private Agtrax agtrax;
  private Aws aws;

  @Getter
  @Builder
  public static class Agtrax {
    private String provider;
    private String providerPassword;
    private String username;
    private String password;
  }

  @Getter
  @Builder
  public static class Aws {
    private String amazonS3;
    private String accessKey;
    private String secretKey;
    private String bucketName;
  }

  @Getter
  @Builder
  public static class Mailing {

    private String emailQueueName;
    private List<String> errorEmailAddresses;
    private String fromEmail;
    private String failToEmail;
    private String replyToEmail;
    private String siteEmail;
  }

  @Getter
  @Builder
  public static class SplitPdf {
    private String queueName;
  }

  @Getter
  @Builder
  public static class PubNub {

    private String queueName;
    private String pubKey;
    private String subKey;
    private String uuid;
  }

  @Getter
  @Builder
  public static class Queue {

    private String queueName;
  }

  @Getter
  @Builder
  public static class FileOcr {
    private String queueName;
    private String endpoint;
    private String apiVersion;
    private String apiKey;
  }

  @Getter
  @Builder
  public static class Twilio {

    private String sid;
    private String token;
    private String fromNumber;
  }

  @Getter
  @Builder
  public static class Google {

    private Maps maps;
  }

  @Getter
  @Builder
  public static class Firebase {

    private DynamicLinks dynamicLinks;
  }

  @Getter
  @Builder
  public static class DynamicLinks {

    private String baseUrl;
    private String domainUriPrefix;
    private String apiKey;
    private String androidPackageName;
    private String iosBundleId;
    private String iosAppStoreId;
  }

  @Getter
  @Builder
  public static class Maps {

    private String apiKey;
  }

  @Getter
  @Builder
  public static class Mapbox {

    private String key;
  }

}
