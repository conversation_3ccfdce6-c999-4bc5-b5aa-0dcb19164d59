package com.bulkloads.config;

import static com.bulkloads.config.AppConstants.QuickBooksEnvironments.PRODUCTION;

import javax.annotation.PostConstruct;
import com.intuit.ipp.util.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * Manages QuickBooks SDK configuration by combining intuit-config.xml with 
 * environment-specific overrides from application properties.
 */
@Slf4j
@Component
public class QuickBooksConfigurationManager {

  private final String environment;
  private final QuickBooksLoggingProperties loggingProperties;

  public QuickBooksConfigurationManager(
      @Value("${quickbooks.environment:sandbox}") String environment,
      QuickBooksLoggingProperties loggingProperties) {
    this.environment = environment;
    this.loggingProperties = loggingProperties;
  }

  @PostConstruct
  public void configureQuickBooksSDK() {
    log.info("Configuring QuickBooks SDK for environment: {}", environment);
    
    // Override base URL based on environment
    configureEnvironment();
    
    // Override logging settings based on application properties
    configureLogging();
    
    // Apply additional configuration
    configurePerformanceSettings();
    
    log.info("QuickBooks SDK configuration completed");
  }

  private void configureEnvironment() {
    if (PRODUCTION.equalsIgnoreCase(environment)) {
      log.info("Configuring QuickBooks SDK for PRODUCTION environment");
      Config.setProperty("BASE_URL_QBO", "https://quickbooks.api.intuit.com/v3/company");
      Config.setProperty("SANDBOX_MODE", "false");
    } else {
      log.info("Configuring QuickBooks SDK for SANDBOX environment");
      Config.setProperty("BASE_URL_QBO", "https://sandbox-quickbooks.api.intuit.com/v3/company");
      Config.setProperty("SANDBOX_MODE", "true");
    }
  }

  private void configureLogging() {
    // Override XML configuration with application properties
    if (loggingProperties.isEnabled()) {
      log.info("Enabling QuickBooks SDK logging via application properties");
      
      Config.setProperty("REQUEST_RESPONSE_LOGGING_ENABLED", "true");
      Config.setProperty("LOGGING_ENABLED", "true");
      Config.setProperty("ENABLE_REQUEST_RESPONSE_LOGGING", "true");
      
      if (loggingProperties.isDetailed()) {
        Config.setProperty("VERBOSE_LOGGING", "true");
        Config.setProperty("LOG_REQUEST_BODY", "true");
        Config.setProperty("LOG_RESPONSE_BODY", "true");
        Config.setProperty("LOG_REQUEST_HEADERS", "true");
        Config.setProperty("LOG_RESPONSE_HEADERS", "true");
      } else {
        Config.setProperty("VERBOSE_LOGGING", "false");
        Config.setProperty("LOG_REQUEST_BODY", "false");
        Config.setProperty("LOG_RESPONSE_BODY", "false");
      }
      
      if (loggingProperties.isLogExecutionTime()) {
        Config.setProperty("LOG_EXECUTION_TIME", "true");
        Config.setProperty("PERFORMANCE_MONITORING_ENABLED", "true");
      }
      
    } else {
      log.info("Disabling QuickBooks SDK logging via application properties");
      Config.setProperty("REQUEST_RESPONSE_LOGGING_ENABLED", "false");
      Config.setProperty("LOGGING_ENABLED", "false");
      Config.setProperty("ENABLE_REQUEST_RESPONSE_LOGGING", "false");
      Config.setProperty("VERBOSE_LOGGING", "false");
    }
  }

  private void configurePerformanceSettings() {
    // Set reasonable defaults for production
    if (PRODUCTION.equalsIgnoreCase(environment)) {
      Config.setProperty("TIMEOUT", "60000"); // 60 seconds for production
      Config.setProperty("READ_TIMEOUT", "60000");
      Config.setProperty("MAX_CONNECTIONS", "20");
      Config.setProperty("REQUESTS_PER_MINUTE", "450"); // Conservative rate limit
    } else {
      Config.setProperty("TIMEOUT", "30000"); // 30 seconds for development
      Config.setProperty("READ_TIMEOUT", "30000");
      Config.setProperty("MAX_CONNECTIONS", "10");
      Config.setProperty("REQUESTS_PER_MINUTE", "500");
    }
    
    // Enable compression for better performance
    Config.setProperty("COMPRESSION_ENABLED", "true");
    
    // Set custom user agent
    Config.setProperty("USER_AGENT", "BulkLoads-QuickBooks-Integration/1.0");
  }

  /**
   * Get current configuration summary for debugging
   */
  public String getConfigurationSummary() {
    return String.format("""
        QuickBooks SDK Configuration:
        - Environment: %s
        - Base URL: %s
        - Logging Enabled: %s
        - Detailed Logging: %s
        - Timeout: %s ms
        - Max Connections: %s
        """,
        environment,
        Config.getProperty("BASE_URL_QBO"),
        Config.getProperty("LOGGING_ENABLED"),
        Config.getProperty("VERBOSE_LOGGING"),
        Config.getProperty("TIMEOUT"),
        Config.getProperty("MAX_CONNECTIONS")
    );
  }
}
