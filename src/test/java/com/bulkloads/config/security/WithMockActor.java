package com.bulkloads.config.security;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import org.junit.jupiter.api.extension.ExtendWith;

@Target({TYPE, METHOD, ANNOTATION_TYPE})
@Retention(RUNTIME)
@ExtendWith(MockActorExtension.class)
public @interface WithMockActor {

  int userId() default 1;
  String username() default "beepbeep";
  String firstName() default "Road";
  String lastName() default "Runner";
  String email() default "<EMAIL>";
  int userCompanyId() default 1;
  String userCompanyName() default "acme corporation";
  String[] userTypes() default {};
  int[] userTypeIds() default {};
  int abUserId() default 1;
  boolean isPro() default false;
  String[] permissions() default {};
  String[] roles() default {};
}
