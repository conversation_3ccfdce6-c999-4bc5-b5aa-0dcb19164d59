package com.bulkloads.web.mcp.service.dto;

import static com.bulkloads.web.commons.Util.authenticate;
import static com.bulkloads.web.confg.TestConstants.DEFAULT_APP_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.util.List;
import java.util.Optional;
import com.bulkloads.security.Actor;
import com.bulkloads.web.addressbook.abcompany.service.AbCompanyService;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyRequest;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.addressbook.abuser.service.AbUserService;
import com.bulkloads.web.common.TestUsersProvider;
import com.bulkloads.web.infra.websocket.WebSocketService;
import com.bulkloads.web.infra.websocket.dto.WebSocketPublishDto;
import com.bulkloads.web.mcp.domain.entity.UserCompanyMcpIntegration;
import com.bulkloads.web.mcp.mapper.McpMapper;
import com.bulkloads.web.mcp.repository.UserCompanyMcpIntegrationRepository;
import com.bulkloads.web.mcp.service.McpService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

class McpServiceTest extends TestUsersProvider {

  @Autowired
  AbCompanyService abCompanyService;

  @Autowired
  UserCompanyMcpIntegrationRepository mcpIntegrationRepository;

  @MockBean
  McpMapper mcpMapper;

  @MockBean
  McpService mcpService;

  @MockBean
  WebSocketService webSocketService;

  @MockBean
  AbUserService abUserService;

  @MockBean
  AbUserRepository abUserRepository;


  @SuppressWarnings("checkstyle:VariableDeclarationUsageDistance")
  @Test
  void willRequestMonitoring_whenCreatingACompany_withMcpMonitoringTrue() {
    final Actor user = Actor.fromUser(createUser("user"), DEFAULT_APP_NAME);
    authenticate(user);

    int cid = user.getUserCompanyId().get();

    UserCompanyMcpIntegration mcpInt = new UserCompanyMcpIntegration();
    mcpInt.setUserCompanyId(user.getUserCompanyId().orElse(null));
    mcpInt.setMcpUsername("xxx");
    mcpInt.setMcpPassword("xxx");
    mcpIntegrationRepository.save(mcpInt);

    AbCompanyRequest createReq = AbCompanyRequest.builder()
        .companyName(Optional.of("AbCompany1"))
        .userTypeIds(Optional.of("20,30"))
        .mcpMonitored(Optional.of(true))
        .censusNum(Optional.of(1))
        .mcNum(Optional.of(""))
        .build();

    doNothing().when(mcpService).requestMonitoring(cid, "1", "");
    doNothing().when(mcpService).cancelMonitoring(cid, "1", "");

    doNothing().when(mcpMapper).mcpAbCompanyResponseToAbCompanyData(any(), any());

    McpAbCompanyResponse carrierLookupResponse = new McpAbCompanyResponse();
    carrierLookupResponse.setMcpMonitored(false);
    when(mcpService.getCarrierInfo(cid, "1", "")).thenReturn(carrierLookupResponse);

    doNothing().when(webSocketService).sendToWebSocket((WebSocketPublishDto) any());
    when(abUserRepository.getAbUsersIncludingDeletedAbCompany(anyList(), anyInt())).thenReturn(List.of());
    doNothing().when(abUserService).sendToWebSocket(any(), any(), any());

    AbCompanyResponse response = abCompanyService.create(createReq);

    verify(mcpService, times(1)).requestMonitoring(cid, "1", "");
    verify(mcpService, never()).cancelMonitoring(cid, "1", "");

    AbCompanyRequest updateReq = AbCompanyRequest.builder()
        .mcpMonitored(Optional.of(false))
        .censusNum(Optional.of(1))
        .mcNum(Optional.of(""))
        .build();

    McpAbCompanyResponse afterCreateCarrierLookupResponse = new McpAbCompanyResponse();
    afterCreateCarrierLookupResponse.setMcpMonitored(true);
    when(mcpService.getCarrierInfo(cid, "1", "")).thenReturn(afterCreateCarrierLookupResponse);

    abCompanyService.update(response.getAbCompanyId(), updateReq);

    verify(mcpService, times(1)).cancelMonitoring(cid, "1", "");
  }

}
