package com.bulkloads.web.phonenumber.service;

import static org.junit.jupiter.api.Assertions.assertThrows;
import com.bulkloads.web.common.TestUsersProvider;
import com.bulkloads.web.phonenumber.repository.PhoneNumberRepository;
import com.bulkloads.web.phonenumber.service.exception.InvalidNumberException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

class CachedPhoneNumberServiceTest extends TestUsersProvider {

  @Autowired
  PhoneNumberRepository phoneNumberRepository;

  @Autowired
  PhoneNumberService phoneNumberService;

  @MockBean
  TwilioService twilioService;

  @Test
  void invalidNumberResultsExceptionAndNotToCall() {
    String invalid = "1234";
    assertThrows(InvalidNumberException.class, () -> phoneNumberService.validate(invalid));
  }

  //  @Test
  //  void emptyNumberResultsToNoCall() {
  //    String empty = "";
  //    assertEquals(0, phoneNumberRepository.count());
  //    when(twilioService.validate(empty)).thenReturn(null);
  //
  //    var result = phoneNumberService.validate(empty, true);
  //    assertEquals(PhoneNumberService.ERROR_UNABLE_TO_PARSE_NUMBER, result.getErrorCode());
  //    verify(twilioService, times(0)).validate(empty);
  //    assertEquals(0, phoneNumberRepository.count());
  //  }
  //
  //  @Test
  //  void phoneNumberResultsToCall() {
  //    assertEquals(0, phoneNumberRepository.count());
  //
  //    String phone = "**********"; // phone number to validate
  //
  //    @Language("JSON")
  //    var twilioResponse = """
  //        {
  //          "carrier": {
  //            "mobile_country_code": "311",
  //            "mobile_network_code": "489",
  //            "name": "Verizon Wireless",
  //            "type": "mobile",
  //            "error_code": null
  //          },
  //          "url": "https://lookups.twilio.com/v1/PhoneNumbers/+1**********?Type=carrier",
  //          "callerName": null,
  //          "countryCode": "US",
  //          "phoneNumber": "+1**********",
  //          "nationalFormat": "(*************",
  //          "addOns": null
  //        }
  //        """;
  //
  //    var twilioPhoneNumber = PhoneNumber.fromJson(twilioResponse, new ObjectMapper());
  //
  //    when(twilioService.validate(phone)).thenReturn(twilioPhoneNumber);
  //
  //    phoneNumberService.validate(phone, true);
  //    phoneNumberService.validate(phone, true);
  //    phoneNumberService.validate(phone, true);
  //
  //    verify(twilioService, times(1)).validate(phone);
  //
  //    assertEquals(1, phoneNumberRepository.count());
  //  }

}
