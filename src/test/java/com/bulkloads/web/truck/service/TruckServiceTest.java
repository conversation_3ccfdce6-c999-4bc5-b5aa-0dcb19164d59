package com.bulkloads.web.truck.service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.city.repository.CityRepository;
import com.bulkloads.web.truck.api.TruckQueryController;
import com.bulkloads.web.truck.mapper.TruckMapper;
import com.bulkloads.web.truck.repository.TruckQueryRepository;
import com.bulkloads.web.truck.repository.TruckRepository;
import com.bulkloads.web.truck.service.dto.TruckResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ExtendWith(MockitoExtension.class)
class TruckServiceTest {

  @Mock
  private TruckRepository truckRepository;

  @Mock
  private CityRepository cityRepository;

  @Mock
  private TruckQueryController truckQueryController;

  @Mock
  private TruckQueryRepository truckQueryRepository;

  @Mock
  private TruckMapper truckMapper;

  @Mock
  private UserUtil userUtil;

  @InjectMocks
  private TruckService truckService;

//  @Test
//  void testShouldReturnMyTrucksWhenAuthorized() {
//    // Given
//    final int userId = 3;
//
//    when(UserUtil.getUserIdOrThrow()).thenReturn(userId);
//
//    final String order = "Newest";
//    final int skip = 0;
//    final int limit = 100;
//
//    // Mock trucks with userId matching the authorized user
//    List<MyTrucksResponse> truckResponses = new ArrayList<>();
//    truckResponses.add(new MyTrucksResponse(
//        3,
//        "CINDY ()",
//        "309-537-3289",
//        "FLAT BED",
//        "",
//        5,
//        Instant.now(),
//        LocalDate.now(),
//        "MORNING",
//        41.3394,
//        41.3394,
//        "DENVER",
//        "NEW YORK",
//        LocalDate.now()
//    ));
//    // Add more mock trucks as needed
//
//    // Mock the behavior of truckQueryController.getMyTrucks() to return truckResponses
//    when(truckQueryController.getMyTrucks(order, skip, limit)).thenReturn(truckResponses);
//
//    // Call the method under test
//    List<MyTrucksResponse> result = truckService.getMyTrucks(order, skip, limit);
//
//    // Then
//    assertEquals(1, result.size()); // Adjust the expected size based on the number of mock trucks
//  }






  @Test
  void testGetTrucks() {
    // Given
    String originState = "FL";
    String originCity = "LIVE OAK";
    Integer distance = 5;
    String equipment = "equipment type";
    LocalDate shipFrom = LocalDate.now();
    LocalDate shipTo = LocalDate.now();
    Integer days = 5;
    String order = "Newest";
    Integer skip = 0;
    Integer limit = 100;

    List<TruckResponse> expectedTrucks = Collections.singletonList(TruckResponse.builder().build());

    // Mock UserUtil using MockedStatic
    try (MockedStatic<UserUtil> userUtilMockedStatic = Mockito.mockStatic(UserUtil.class)) {
      userUtilMockedStatic.when(UserUtil::getUserCompanyId).thenReturn(Optional.of(1));

      // When
      List<TruckResponse> actualTrucks = truckService.getTrucks(
          originState,
          originCity,
          distance,
          equipment,
          shipFrom,
          shipTo,
          days,
          order,
          skip,
          limit);
    }
  }
}
