package com.bulkloads.web.contracts.mapper;

import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_CONTRACTS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Optional;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abcompany.repository.AbCompanyRepository;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.addressbook.abusergroup.repository.AbUserGroupRepository;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.contracts.domain.data.ContractData;
import com.bulkloads.web.contracts.domain.entity.Contract;
import com.bulkloads.web.contracts.service.dto.ContractRequest;
import com.bulkloads.web.contracts.service.dto.ContractResponse;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.load.repository.LoadRepository;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.rate.repository.RateProductCategoryRepository;
import com.bulkloads.web.rate.repository.RateTypeRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.repository.UserRepository;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

@SpringBootTest(classes = {ContractMapperImpl.class, CommonMapperImpl.class})
class ContractMapperTest {

  final int userCompanyId = 1;
  @MockBean
  AbCompanyRepository abCompanyRepository;
  @MockBean
  RateTypeRepository rateTypeRepository;
  @MockBean
  RateProductCategoryRepository rateProductCategoryRepository;
  @MockBean
  private LoadRepository loadRepository;
  @MockBean
  private AbUserRepository abUserRepository;
  @MockBean
  private AbUserGroupRepository abUserGroupRepository;
  @MockBean
  FileRepository fileRepository;
  @MockBean
  UserRepository userRepository;
  @Autowired
  ContractMapper contractMapper;
  RateType rateType;
  RateType freightRateType;
  Commodity commodity;
  AbCompany pickupAbCompany;
  AbCompany dropAbCompany;

  private static ContractResponse buildFullResponse(final LocalDate shipFrom,
                                                    final LocalDate shipTo) {
    return ContractResponse.builder()
        .contractId(1)
        .userId(1)
        .userCompanyId(1)
        .contractStatus("Open")
        .externalContractId("externalContractId")
        .buySell("buy")
        .contractNumber("contractNumber")
        .rate(BigDecimal.ONE)
        .rateType("rateType")
        .freightRate(BigDecimal.ONE)
        .freightRateType("freightRateType")
        .numberOfLoads(1)
        .quantity(BigDecimal.ONE)
        .commodityId(1)
        .commodityName("commodity")
        .shipFrom(shipFrom)
        .shipTo(shipTo)
        .contactInfo("contactInfo")
        .notes("notes")
        .pickupAbCompanyId(1)
        .pickupCompanyName("pickupCompanyName")
        .pickupAddress("pickupAddress")
        .pickupLocation("pickupLocation")
        .pickupCity("pickupCity")
        .pickupState("pickupState")
        .pickupZip("pickupZip")
        .pickupCountry("pickupCountry")
        .pickupLat(1.0)
        .pickupLong(1.0)
        .dropCompanyName("dropCompanyName")
        .dropAddress("dropAddress")
        .dropLocation("dropLocation")
        .dropCity("dropCity")
        .dropState("dropState")
        .dropZip("dropZip")
        .dropCountry("dropCountry")
        .dropLat(1.0)
        .dropLong(1.0)
        .dropAbCompanyId(2)
        .build();
  }

  @BeforeEach
  void setUp() {
    rateType = new RateType();
    rateType.setRateType("rateType");

    freightRateType = new RateType();
    freightRateType.setRateType("freightRateType");

    commodity = new Commodity();
    commodity.setCommodityId(1);
    commodity.setCommodity("commodity");

    pickupAbCompany = new AbCompany();
    pickupAbCompany = buildCreateAbCompany(1, "pickup");

    dropAbCompany = new AbCompany();
    dropAbCompany = buildCreateAbCompany(2, "drop");
    dropAbCompany.setAbCompanyId(2);
  }

  @AfterEach
  void tearDown() {
    verifyNoMoreInteractions(abCompanyRepository, rateTypeRepository);
  }

  @Test
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void shouldMapRequestToData() {
    final LocalDate shipFrom = LocalDate.now();
    final LocalDate shipTo = shipFrom.minusDays(1);
    final ContractRequest request = buildContractRequest(shipFrom, shipTo);

    when(abCompanyRepository.findByAbCompanyIdAndUserCompanyUserCompanyId(1, userCompanyId)).thenReturn(Optional.of(pickupAbCompany));
    when(abCompanyRepository.findByAbCompanyIdAndUserCompanyUserCompanyId(2, userCompanyId)).thenReturn(Optional.of(dropAbCompany));
    when(rateTypeRepository.findByRateTypeAndIsWeightTrue("rateType")).thenReturn(Optional.of(rateType));
    when(rateTypeRepository.findById("freightRateType")).thenReturn(Optional.of(freightRateType));

    final ContractData actual = contractMapper.requestToData(request, new HashMap<>());
    final ContractData expected = buildContractData(shipFrom, shipTo);

    assertEquals(expected, actual);

    verify(abCompanyRepository).findByAbCompanyIdAndUserCompanyUserCompanyId(1, userCompanyId);
    verify(abCompanyRepository).findByAbCompanyIdAndUserCompanyUserCompanyId(2, userCompanyId);
    verify(rateTypeRepository).findByRateTypeAndIsWeightTrue("rateType");
    verify(rateTypeRepository).findById("freightRateType");
  }

  @Test
  void shouldMapDataToEntity() {
    final LocalDate shipFrom = LocalDate.now();
    final LocalDate shipTo = shipFrom.minusDays(1);
    final ContractData contractData = buildContractData(shipFrom, shipTo);

    final Contract actual = buildContractEntity(shipFrom, shipTo);

    contractMapper.dataToEntity(contractData, actual);

    final Contract expected = buildContractEntity(shipFrom, shipTo);

    assertThat(actual)
        .usingRecursiveComparison()
        .ignoringFields("addedDate", "user.signUpDate")
        .isEqualTo(expected);
  }

  @Test
  void shouldMapEntityToResponse() {
    final LocalDate shipFrom = LocalDate.now();
    final LocalDate shipTo = shipFrom.minusDays(1);

    final Contract entity = buildContractEntity(shipFrom, shipTo);

    final ContractResponse actual = contractMapper.entityToFullResponse(entity);

    final ContractResponse expected = buildFullResponse(shipFrom, shipTo);

    assertThat(actual)
        .usingRecursiveComparison()
        .ignoringFields("addedDate")
        .isEqualTo(expected);
  }

  @Test
  void shouldMapEntityToFullResponse() {
    final LocalDate shipFrom = LocalDate.now();
    final LocalDate shipTo = shipFrom.minusDays(1);
    final Contract entity = buildContractEntity(shipFrom, shipTo);

    final ContractResponse actual = contractMapper.entityToFullResponse(entity);
    final ContractResponse expected = buildFullResponse(shipFrom, shipTo);

    assertThat(actual)
        .usingRecursiveComparison()
        .ignoringFields("addedDate")
        .isEqualTo(expected);
  }

  @Test
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void shouldMapDropAbCompanyOptById() {
    when(abCompanyRepository.findByAbCompanyIdAndUserCompanyUserCompanyId(1, userCompanyId)).thenReturn(Optional.of(new AbCompany()));

    final Optional<AbCompany> result = contractMapper.mapDropAbCompanyOptById(Optional.of(1), new HashMap<>());

    assertTrue(result.isPresent());

    verify(abCompanyRepository).findByAbCompanyIdAndUserCompanyUserCompanyId(1, userCompanyId);
  }

  @Test
  void shouldNotMapDropAbCompanyOptByIdWhenIdNotPresent() {
    final Optional<AbCompany> result = contractMapper.mapDropAbCompanyOptById(Optional.empty(), new HashMap<>());

    assertFalse(result.isPresent());
  }

  @Test
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void shouldMapPickupAbCompanyOptById() {
    when(abCompanyRepository.findByAbCompanyIdAndUserCompanyUserCompanyId(1, userCompanyId)).thenReturn(Optional.of(new AbCompany()));

    final Optional<AbCompany> result = contractMapper.mapPickupAbCompanyOptById(Optional.of(1), new HashMap<>());

    assertTrue(result.isPresent());

    verify(abCompanyRepository).findByAbCompanyIdAndUserCompanyUserCompanyId(1, userCompanyId);
  }

  @Test
  void shouldNotMapPickupAbCompanyOptByIdWhenIdNotPresent() {
    final Optional<AbCompany> result = contractMapper.mapPickupAbCompanyOptById(Optional.empty(), new HashMap<>());

    assertFalse(result.isPresent());
  }

  private Contract buildContractEntity(final LocalDate shipFrom,
                                       final LocalDate shipTo) {
    final UserCompany userCompany = new UserCompany();
    userCompany.setUserCompanyId(1);
    final User user = new User();
    user.setUserId(1);
    user.setUserCompany(userCompany);

    final Contract entity = new Contract();
    entity.setContractId(1);
    entity.setUser(user);
    entity.setUserCompany(user.getUserCompany());
    entity.setExternalContractId("externalContractId");
    entity.setBuySell("buy");
    entity.setContractNumber("contractNumber");
    entity.setRate(BigDecimal.ONE);
    entity.setRateType(rateType);
    entity.setFreightRate(BigDecimal.ONE);
    entity.setFreightRateType(freightRateType);
    entity.setNumberOfLoads(1);
    entity.setQuantity(BigDecimal.ONE);
    entity.setCommodityName("commodity");
    entity.setCommodity(commodity);
    entity.setShipFrom(shipFrom);
    entity.setShipTo(shipTo);
    entity.setContactInfo("contactInfo");
    entity.setNotes("notes");
    entity.setPickupAbCompany(pickupAbCompany);
    entity.setDropAbCompany(dropAbCompany);
    return entity;
  }

  private ContractData buildContractData(final LocalDate shipFrom,
                                         final LocalDate shipTo) {
    return new ContractData()
        .setExternalContractId(Optional.of("externalContractId"))
        .setBuySell(Optional.of("buy"))
        .setContractNumber(Optional.of("contractNumber"))
        .setRate(Optional.of(BigDecimal.ONE))
        .setRateType(Optional.of(rateType))
        .setFreightRate(Optional.of(BigDecimal.ONE))
        .setFreightRateType(Optional.of(freightRateType))
        .setNumberOfLoads(Optional.of(1))
        .setQuantity(Optional.of(BigDecimal.ONE))
        .setShipFrom(Optional.of(shipFrom))
        .setShipTo(Optional.of(shipTo))
        .setContactInfo(Optional.of("contactInfo"))
        .setNotes(Optional.of("notes"))
        .setPickupAbCompany(Optional.of(pickupAbCompany))
        .setDropAbCompany(Optional.of(dropAbCompany));
  }

  private ContractRequest buildContractRequest(final LocalDate shipFrom,
                                               final LocalDate shipTo) {
    return ContractRequest.builder()
        .externalContractId(Optional.of("externalContractId"))
        .buySell(Optional.of("buy"))
        .contractNumber(Optional.of("contractNumber"))
        .rate(Optional.of(BigDecimal.ONE))
        .rateType(Optional.of("rateType"))
        .freightRate(Optional.of(BigDecimal.ONE))
        .freightRateType(Optional.of("freightRateType"))
        .numberOfLoads(Optional.of(1))
        .quantity(Optional.of(BigDecimal.ONE))
        .commodity(Optional.of("commodity"))
        .commodityId(Optional.of(1))
        .shipFrom(Optional.of(shipFrom))
        .shipTo(Optional.of(shipTo))
        .contactInfo(Optional.of("contactInfo"))
        .notes(Optional.of("notes"))
        .pickupAbCompanyId(Optional.of(1))
        .dropAbCompanyId(Optional.of(2))
        .build();
  }

  private AbCompany buildCreateAbCompany(final int companyId,
                                         final String prefix) {
    final AbCompany abCompany = new AbCompany();
    abCompany.setAbCompanyId(companyId);
    abCompany.setCompanyName(prefix + "CompanyName");
    abCompany.setAddress(prefix + "Address");
    abCompany.setLocation(prefix + "Location");
    abCompany.setCity(prefix + "City");
    abCompany.setState(prefix + "State");
    abCompany.setZip(prefix + "Zip");
    abCompany.setCountry(prefix + "Country");
    abCompany.setLatitude(1.0);
    abCompany.setLongitude(1.0);
    return abCompany;
  }
}
