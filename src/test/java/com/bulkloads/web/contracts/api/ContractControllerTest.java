package com.bulkloads.web.contracts.api;

import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_CONTRACTS;
import static com.bulkloads.web.confg.TestConstants.DUMMY_PERMISSION;
import static com.bulkloads.web.contracts.ContractUtils.CONTRACT_RESPONSE_JSON;
import static com.bulkloads.web.contracts.ContractUtils.CREATE_CONTRACT_REQUEST_JSON;
import static com.bulkloads.web.contracts.ContractUtils.UPDATE_CONTRACT_REQUEST_JSON;
import static com.bulkloads.web.contracts.ContractUtils.buildContractResponse;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.confg.TestConstants;
import com.bulkloads.web.contracts.api.dto.ContractApiRequest;
import com.bulkloads.web.contracts.mapper.ContractMapper;
import com.bulkloads.web.contracts.service.ContractService;
import com.bulkloads.web.contracts.service.dto.ContractRequest;
import com.bulkloads.web.contracts.service.dto.ContractResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(ContractController.class)
@AutoConfigureMockMvc(addFilters = false)
class ContractControllerTest extends ControllerTest {

  private static final String URL = TestConstants.CONTRACTS_URL;

  @MockBean
  ContractService service;
  @MockBean
  ContractMapper mapper;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(service);
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenAuthenticatedUserWithRightPermissions_whenCreateContract_thenShouldBeOk() {

    final ContractApiRequest contractApiRequest =
        objectMapper.readValue(CREATE_CONTRACT_REQUEST_JSON, ContractApiRequest.class);

    final ContractRequest request =
        objectMapper.readValue(CREATE_CONTRACT_REQUEST_JSON, ContractRequest.class);
    final ContractResponse response = buildContractResponse();

    final String responseJson = """
        {
          "message" : "Contract created",
          "key" : 1,
          "data" : %s
        }""".formatted(CONTRACT_RESPONSE_JSON);

    when(mapper.apiRequestToServiceRequest(contractApiRequest)).thenReturn(request);
    when(service.create(request)).thenReturn(response);

    mockMvc.perform(post(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(CREATE_CONTRACT_REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).create(request);
    verify(mapper).apiRequestToServiceRequest(contractApiRequest);
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = DUMMY_PERMISSION)
  void givenAuthenticatedUserWithWrongPermissions_whenCreateContract_thenShouldBeForbidden() {
    mockMvc.perform(post(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(CREATE_CONTRACT_REQUEST_JSON))
        .andExpect(status().isForbidden())
        .andReturn();
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenCreateContract_thenShouldBeUnauthorized() {
    mockMvc.perform(post(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(CREATE_CONTRACT_REQUEST_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenAuthenticatedUserWithRightPermissions_whenUpdateContract_thenShouldBeOk() {

    final int contractId = 1;
    final ContractApiRequest contractApiRequest =
        objectMapper.readValue(UPDATE_CONTRACT_REQUEST_JSON, ContractApiRequest.class);

    final ContractRequest request =
        objectMapper.readValue(UPDATE_CONTRACT_REQUEST_JSON, ContractRequest.class);

    final ContractResponse response = buildContractResponse();

    final String responseJson = """
        {
          "message" : "Contract updated",
          "key" : 1,
          "data" : %s
        }""".formatted(CONTRACT_RESPONSE_JSON);

    when(service.update(contractId, request)).thenReturn(response);
    when(mapper.apiRequestToServiceRequest(contractApiRequest)).thenReturn(request);

    mockMvc.perform(put(URL + "/{contract_id}", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(UPDATE_CONTRACT_REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).update(contractId, request);
    verify(mapper).apiRequestToServiceRequest(contractApiRequest);
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenANegativeContractId_whenUpdateContract_thenShouldBeBadRequest() {
    final int contractId = -1;
    mockMvc.perform(put(URL + "/{contract_id}", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(UPDATE_CONTRACT_REQUEST_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = DUMMY_PERMISSION)
  void givenAuthenticatedUserWithWrongPermissions_whenUpdateContract_thenShouldBeForbidden() {
    final int contractId = 1;
    mockMvc.perform(put(URL + "/{contract_id}", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(UPDATE_CONTRACT_REQUEST_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenUpdateContract_thenShouldBeUnauthorized() {
    final int contractId = 1;
    mockMvc.perform(put(URL + "/{contract_id}", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(UPDATE_CONTRACT_REQUEST_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenAuthenticatedUserWithRightPermissions_whenDeleteContract_thenShouldBeOk() {
    final int contractId = 1;

    final String responseJson = """
        {
          "message" : "Contract deleted",
          "key" : 1
        }""";

    doNothing().when(service).deleteById(contractId);

    mockMvc.perform(delete(URL + "/{contract_id}", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).deleteById(contractId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenANegativeContractId_whenDeleteContract_thenShouldBeBadRequest() {
    final int contractId = -1;
    mockMvc.perform(delete(URL + "/{contract_id}", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = DUMMY_PERMISSION)
  void givenAuthenticatedUserWithWrongPermissions_whenDeleteContract_thenShouldBeForbidden() {
    final int contractId = 1;
    mockMvc.perform(delete(URL + "/{contract_id}", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenDeleteContract_thenShouldBeUnauthorized() {
    final int contractId = 1;
    mockMvc.perform(delete(URL + "/{contract_id}", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenAuthenticatedUserWithRightPermissions_whenCloseContract_thenShouldBeOk() {

    final int contractId = 1;

    final String responseJson = """
        {
          "message" : "Contract closed",
          "key" : 1
        }""";

    doNothing().when(service).setClosed(contractId);

    mockMvc.perform(post(URL + "/{contract_id}/close", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).setClosed(contractId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenANegativeContractId_whenCloseContract_thenShouldBeBadRequest() {
    final int contractId = -1;
    mockMvc.perform(post(URL + "/{contract_id}/close", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = DUMMY_PERMISSION)
  void givenAuthenticatedUserWithWrongPermissions_whenCloseContract_thenShouldBeUnauthorised() {
    final int contractId = 1;
    mockMvc.perform(post(URL + "/{contract_id}/close", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenCloseContract_thenShouldBeUnauthorized() {
    final int contractId = 1;
    mockMvc.perform(post(URL + "/{contract_id}/close", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenAuthenticatedUserWithRightPermissions_whenOpenContract_thenShouldBeOk() {

    final int contractId = 1;

    final String responseJson = """
        {
          "message" : "Contract re-opened",
          "key" : 1
        }""";

    doNothing().when(service).setOpened(contractId);

    mockMvc.perform(post(URL + "/{contract_id}/reopen", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).setOpened(contractId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_CONTRACTS)
  void givenANegativeContractId_whenOpenContract_thenShouldBeBadRequest() {
    final int contractId = -1;
    mockMvc.perform(post(URL + "/{contract_id}/reopen", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = DUMMY_PERMISSION)
  void givenAuthenticatedUserWithWrongPermissions_whenOpenContract_thenShouldBeForbidden() {
    final int contractId = 1;
    mockMvc.perform(post(URL + "/{contract_id}/reopen", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenOpenContract_thenShouldBeUnauthorized() {
    final int contractId = 1;
    mockMvc.perform(post(URL + "/{contract_id}/reopen", contractId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }
}
