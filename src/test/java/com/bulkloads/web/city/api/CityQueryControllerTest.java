package com.bulkloads.web.city.api;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.List;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.city.service.CityService;
import com.bulkloads.web.city.service.dto.CityResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(CityQueryController.class)
@AutoConfigureMockMvc(addFilters = false)
class CityQueryControllerTest extends ControllerTest {

  private static final String URL = "/rest/cities";

  @MockBean
  CityService cityService;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(cityService);
  }

  @Test
  @SneakyThrows
  void whenGetCities_thenShouldBeOk() {
    final List<CityResponse> response = List.of();
    final String term = "New York";
    final Boolean includeStates = true;

    when(cityService.getCities(term, includeStates)).thenReturn(response);

    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("term", term)
            .param("include_states", "true"))
        .andExpect(status().isOk())
        .andExpect(content().json("[]"));

    verify(cityService).getCities(term, includeStates);
  }

  @Test
  @SneakyThrows
  void whenGetCitiesWithoutParams_thenShouldBeOk() {
    final List<CityResponse> response = List.of();

    when(cityService.getCities(null, null)).thenReturn(response);

    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json("[]"));

    verify(cityService).getCities(null, null);
  }

  @Test
  @SneakyThrows
  void whenGetCityNames_thenShouldBeOk() {
    final List<String> response = List.of("New York", "Los Angeles");
    final String term = "test";

    when(cityService.getCityNames(term)).thenReturn(response);

    mockMvc.perform(get(URL + "/name")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("term", term))
        .andExpect(status().isOk())
        .andExpect(content().json("""
           ["New York", "Los Angeles"]
         """));

    verify(cityService).getCityNames(term);
  }

  @Test
  @SneakyThrows
  void whenGetCityNearMe_thenShouldBeOk() {
    final List<CityResponse> response = List.of();
    final double latitude = 40.7128;
    final double longitude = -74.0060;

    when(cityService.getCityNearMe(latitude, longitude)).thenReturn(response);

    mockMvc.perform(get(URL + "/nearme")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("lat", "40.7128")
            .param("long", "-74.0060"))
        .andExpect(status().isOk())
        .andExpect(content().json("[]"));

    verify(cityService).getCityNearMe(latitude, longitude);
  }
}
