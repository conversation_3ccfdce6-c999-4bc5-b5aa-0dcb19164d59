package com.bulkloads.web.addressbook.abcompany.domain;

import static org.assertj.core.api.Assertions.assertThat;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.addressbook.abcompany.domain.data.AbCompanyData;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.usercompany.domain.entity.UserType;
import com.bulkloads.web.usercompany.repository.UserTypeRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AbCompanyDomainServiceTest {

  @Mock
  UserTypeRepository userTypeRepository;

  @InjectMocks
  private AbCompanyDomainService abCompanyDomainService;

  private List<UserType> userTypes(int... userTypeIds) {
    List<UserType> userTypes = new LinkedList<>();
    for (int typeId : userTypeIds) {
      userTypes.add(UserType.of(typeId));
    }
    return userTypes;
  }

  @Test
  void test_validateUserTypes_1() {
    AbCompany abCompany = new AbCompany();
    AbCompanyData abCompanyRequest = new AbCompanyData();

    abCompanyRequest.setUserTypeIds(null);
    abCompanyDomainService.validateUserTypes(abCompany, abCompanyRequest);

    assertThat(abCompany.getUserTypes()).isEqualTo(List.of());
    assertThat(abCompany.getUserTypeIds()).isEqualTo(List.of());
  }

  @Test
  void test_validateUserTypes_2() {
    AbCompany abCompany = new AbCompany();
    AbCompanyData abCompanyRequest = new AbCompanyData();

    abCompanyRequest.setUserTypeIds(Optional.of(List.of()));
    abCompanyDomainService.validateUserTypes(abCompany, abCompanyRequest);

    // TODO
    assertThat(abCompany.getUserTypes()).isEqualTo(List.of());
    assertThat(abCompany.getUserTypeIds()).isEqualTo(List.of());
  }

  @Test
  void test_validateUserTypes_3() {
    AbCompany abCompany = new AbCompany();
    AbCompanyData abCompanyRequest = new AbCompanyData();

    Mockito.when(userTypeRepository.searchByUserTypeIdsOrderByUserTypeIdAsc(List.of(20, 30)))
        .thenReturn(userTypes(20, 30));

    abCompanyRequest.setUserTypeIds(Optional.of(List.of(20, 30)));
    abCompanyDomainService.validateUserTypes(abCompany, abCompanyRequest);

    // TODO: will this compare references or values?
    assertThat(abCompany.getUserTypes()).isEqualTo(List.of("Carrier", "Broker"));
    assertThat(abCompany.getUserTypeIds()).isEqualTo(List.of(20, 30));
  }

}
