package com.bulkloads.web.addressbook.abusergroup.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import static com.bulkloads.web.confg.TestConstants.DUMMY_ROLE;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.List;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.addressbook.abusergroup.service.AbUserGroupService;
import com.bulkloads.web.addressbook.abusergroup.service.dto.AbUserGroupResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(AbUserGroupQueryController.class)
@AutoConfigureMockMvc(addFilters = false)
class AbUserGroupQueryControllerTest extends ControllerTest {

  private static final String URL = TestConstants.AB_BOOK_USER_GROUPS_URL;

  private static final String AB_USER_GROUP_RESPONSE_JSON = """
      {
        "ab_user_group_id": 1,
        "group_name": "Test Group",
        "ab_user_ids": "1,2,3"
      }""";

  @MockBean
  AbUserGroupService service;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(service);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetAbUserGroupById_thenShouldBeOk() {
    final int abUserGroupId = 1;
    final AbUserGroupResponse response = new AbUserGroupResponse(1, "Test Group", "1,2,3");

    when(service.getAbUserGroup(abUserGroupId)).thenReturn(response);

    mockMvc.perform(get(URL + "/{ab_user_group_id}", abUserGroupId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(AB_USER_GROUP_RESPONSE_JSON)));

    verify(service).getAbUserGroup(abUserGroupId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenANegativeAbUserGroupId_whenGetAbUserGroupById_thenShouldBeBadRequest() {
    final int abUserGroupId = -1;
    mockMvc.perform(get(URL + "/{ab_user_group_id}", abUserGroupId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenGetAbUserGroupById_thenShouldBeForbidden() {
    final int abUserGroupId = 1;
    mockMvc.perform(get(URL + "/{ab_user_group_id}", abUserGroupId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenGetAbUserGroupById_thenShouldBeUnauthorized() {
    final int abUserGroupId = 1;
    mockMvc.perform(get(URL + "/{ab_user_group_id}", abUserGroupId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetAbUserGroups_thenShouldBeOk() {
    final List<AbUserGroupResponse> response = List.of(new AbUserGroupResponse(1, "Test Group", "1,2,3"));

    when(service.getAbUserGroups()).thenReturn(response);

    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson("[ " + AB_USER_GROUP_RESPONSE_JSON + " ]")));

    verify(service).getAbUserGroups();
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenGetAbUserGroups_thenShouldBeForbidden() {
    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenGetAbUserGroups_thenShouldBeUnauthorized() {
    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }
}
