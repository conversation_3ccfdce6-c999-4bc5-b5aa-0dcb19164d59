package com.bulkloads.web.activity.service;

import static com.bulkloads.web.commons.Util.authenticate;
import static com.bulkloads.web.confg.TestConstants.DEFAULT_APP_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import java.util.List;
import com.bulkloads.security.Actor;
import com.bulkloads.web.activity.domain.entity.Activity;
import com.bulkloads.web.activity.domain.entity.ActivityType.ActivityTypes;
import com.bulkloads.web.activity.repository.ActivityRepository;
import com.bulkloads.web.activity.service.dto.ActivityRequest;
import com.bulkloads.web.common.TestUsersProvider;
import com.bulkloads.web.user.domain.entity.User;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ActivityServiceTest extends TestUsersProvider {

  @Autowired
  ActivityRepository activityRepository;

  @Autowired
  ActivityService activityService;

  @Test
  void createActivity() {
    final User user = createUser("user");
    final Actor actor = Actor.fromUser(user, DEFAULT_APP_NAME);

    final ActivityRequest request = new ActivityRequest();
    request.setUserId(user.getUserId());
    request.setActivityTypeId(ActivityTypes.CONTRACTS_ACTIVITY_ID.get());
    request.setAction("CREATE_CONTRACT");
    request.setActivity("Contract created.");
    request.setData("{\"contract_ids\": \"123\"}");

    authenticate(actor);


    final List<Activity> activities = activityService.createActivities(List.of(request));
    final Activity foundActivity = activityRepository.findById(activities.get(0).getActivityId()).orElseThrow();

    assertEquals(request.getActivity(), foundActivity.getActivity());
    assertEquals(request.getAction(), foundActivity.getAction());
    assertEquals(request.getData(), foundActivity.getData());
  }
}