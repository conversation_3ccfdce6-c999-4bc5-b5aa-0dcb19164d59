package com.bulkloads.web.infra.messaging.consumer;

import static com.bulkloads.config.AppConstants.WebSocket.Channel.CONTRACTS;
import static com.bulkloads.web.confg.TestConstants.QUEUE_NAME;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

import com.bulkloads.web.infra.websocket.dto.WebSocketHereNowDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import lombok.SneakyThrows;

@ExtendWith(MockitoExtension.class)
class MessageQueueSenderTest {

  @Mock
  RabbitTemplate template;
  @Mock
  ObjectMapper objectMapper;
  @InjectMocks
  MessageQueueSender queueSender;

  @AfterEach
  void tearDown() {
    verifyNoMoreInteractions(template, objectMapper);
  }

  @Test
  @SneakyThrows
  void shouldSend() {
    final WebSocketHereNowDto dto = WebSocketHereNowDto.builder()
        .channel(CONTRACTS)
        .includeState(true)
        .includeUuids(true).build();

    queueSender.send(QUEUE_NAME, dto);

    verify(template).convertAndSend(QUEUE_NAME, dto);
  }
}
