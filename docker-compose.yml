networks:
  bulkloads:

services:

  bulkloads-server:
    image: bulkloads-server:latest
    hostname: "${SERVER_CONTAINER_NAME:-bulkloads-server}"
    container_name: "${SERVER_CONTAINER_NAME:-bulkloads-server}"
    restart: unless-stopped
#    env_file:
#      - env.vars
    ports:
      - "${EXTERNAL_PORT:-9000}:9000"
      - "${FUSION_REACTOR_PORT:-8088}:8088"
      - "5005:5005" # Debug port
    volumes:
      - "${FUSION_REACTOR_CONTEXT:-/}:/fusionreactor"
      - "/mnt/ftp:/mnt/ftp"
    networks:
      - bulkloads

  bulkloads-db:
    image: mysql:8.2.0
    hostname: bulkloads-db
    container_name: bulkloads-db
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=test_master
      - MYSQL_PASSWORD=root
    volumes:
      - ./docker-data/bulkloads/mysql:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - bulkloads

  # guest/guest
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    hostname: rabbitmq
    restart: unless-stopped
    # volumes:
    # - ./docker-data/bulkloads/rabbitmq/data/:/var/lib/rabbitmq/
    # - ./docker-data/bulkloads/rabbitmq/log/:/var/log/rabbitmq
    volumes:
      - "${RABBITMQ_DATA_DIR:-/opt/docker-rabbitmq/data}:/var/lib/rabbitmq/"
      - "${RABBITMQ_LOG_DIR:-/opt/docker-rabbitmq/log}:/var/log/rabbitmq/"
    command: bash -c "rabbitmq-plugins enable rabbitmq_management rabbitmq_shovel rabbitmq_shovel_management rabbitmq_prometheus && rabbitmq-server"
    environment:
      RABBITMQ_SERVER_ADDITIONAL_ERL_ARGS: "-rabbitmq_management prometheus_return_per_object_metrics true"
    ports:
      - "5672:5672"
      - "15672:15672"
      - "15692:15692"
    networks:
      - bulkloads

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    hostname: prometheus
    restart: unless-stopped
    volumes:
#      - "${PROMETHEUS_DATA_DIR:-/opt/docker-prometheus/data}:/prometheus"
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./config/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml
    ports:
      - "9090:9090"
    networks:
      - bulkloads

  alertmanager:
    image: prom/alertmanager
    container_name: alertmanager
    hostname: alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./config/alertmanager/config.yml:/etc/alertmanager/config.yml
    command:
      - '--config.file=/etc/alertmanager/config.yml'
      - '--storage.path=/alertmanager'
    networks:
      - bulkloads

  mail:
    image: maildev/maildev
    hostname: mail
    container_name: mail
    restart: unless-stopped
    command: bin/maildev --web 80 --smtp 25 --verbose --base-pathname /maildev
    ports:
      - "1080:80"
      - "1030:25"
    networks:
      - bulkloads

  logs:
    image: amir20/dozzle:latest
    hostname: logs
    container_name: logs
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    ports:
      - "9999:8080"
    networks:
      - bulkloads

  # The Nginx container is needed for local deployment of the
  # bulkloads application (on test and prod servers Nginx is installed directly on the host).
  #
  # When the container is started it will play the ansible playbook
  # responsible for configuring the nginx server and can be started
  # using various configurations depending on where the DEVOPS projects
  # should be cloned/mounted from.
  #
  # Devops: https://github.com/bulkloads/bulkloads-devops
  #
  # You can either check out a specific remote branch of the
  # bulkloads-devops project in `../bulkloads-devops` directory
  # or use the combination of `USE_MOUNTED_DEVOPS` and a mountpoint
  # to use with local copy of the devops repository.
  #
  # The following config used the branch `test` as exists on the remote repo:
  #
  #  volumes:
  #    - ./nginx_config.yaml:/opt/dev_server.yaml:ro
  #
  # And to use the local copy of the devops repository:
  #
  #  environment:
  #    - USE_MOUNTED_DEVOPS=1
  #  volumes:
  #    - ../bulkloads-devops:/opt/bulkloads-devops:ro
  #
  # where `../bulkloads-devops` is the path to the local copy of the devops repository.
  #
  #
  # TMS:
  #   if the variable TMS_BRANCH is ommited the container will skip building
  #   tms altogether (faster startup but TMS files will be missing).
  #
  # NOTE: Should we do the same for TMS as we do for devops?
  #
  # IMPORTANT:
  #
  # Changing/Removing/Adding configuration here in this file to the
  # nginx container configuration requires to delete the docker image
  # as well not only the container.
  #
  # Contrary, if you use a local clone of the devops repository
  # you can simply restart the container without deleting the image
  # to pickup changes of the java routes configuration.

  nginx:
    build:
      context: ./docker
      dockerfile: nginx.Dockerfile
    container_name: nginx
    restart: unless-stopped
#    env_file:
#      - env.vars
    environment:
      # - TMS_BRANCH=test
      # - DEVOPS_BRANCH=test
      - USE_MOUNTED_DEVOPS=1
    volumes:
      - ./nginx_config.yaml:/opt/dev_server.yaml:ro
      - ../bulkloads-devops:/opt/bulkloads-devops:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - bulkloads
    extra_hosts:
      - "host.docker.internal:host-gateway"

  nextjs:
    build:
      context: ./docker
      dockerfile: nextjs.Dockerfile
    container_name: nextjs
    hostname: nextjs
    restart: unless-stopped
#    env_file:
#      - env.vars
    environment:
      - NEXTJS_BRANCH=test
    ports:
      - "3000:3000"
    networks:
      - bulkloads
    extra_hosts:
      - "host.docker.internal:host-gateway"

  coldfusion:
    image: adobecoldfusion/coldfusion2018:latest
    hostname: local.bulkloads.com
    container_name: coldfusion
    ports:
      - "8500:8500"
    environment:
      - acceptEULA=YES
      - password=admin
    volumes:
      - ./../bulkloads-web:/app
      - ./../bulkloads-web/docs/tutorials/setup-coldfusion-guide/mysql-connector-java-8.0.16.jar:/opt/coldfusion/cfusion/lib/mysql-connector-java-8.0.16.jar
      - ./../bulkloads-web/docs/tutorials/setup-coldfusion-guide:/opt/additional
    networks:
      - bulkloads

  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:latest
    container_name: nginx-exporter
    hostname: nginx-exporter
    command:
      - --nginx.scrape-uri=http://host.docker.internal:80/nginx_status
    ports:
      - "9113:9113"
    networks:
      - bulkloads
    extra_hosts:
      - "host.docker.internal:host-gateway"

  agent:
    image: intergral/observability-agent:latest
    restart: unless-stopped
    environment:
      - api_key=${FUSION_REACTOR_API_KEY:-/}
      - rabbitmq_scrape_target=rabbitmq:15692
      - service_name="logs-collector-test"
      - scrape_targets="nginx-exporter:9113"
    ports:
      - "12345:12345"
