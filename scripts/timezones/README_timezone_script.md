# Timezone Backfill Script

This script backfills timezone information for database tables that have latitude and longitude coordinates.

## Setup

### Prerequisites

- Python 3.6+
- pip (Python package manager)

### Installation & Execution

The setup process is the same for all operating systems (Windows, macOS, Linux).

1. Create a `.env` file in the same directory as the script with your database credentials:
   ```
   DB_HOST=your_database_host
   DB_USER=your_database_user
   DB_PASSWORD=your_database_password
   DB_NAME=your_database_name
   ```

2. Run the setup and execution script:
   ```
   python setup_and_run_timezone.py
   ```

   This script will:
    - Install all required Python packages
    - Run the timezone backfill process

## Configuration

The script uses environment variables for database configuration. These are loaded from a `.env` file in the same directory as the script.

Required environment variables:

- `DB_HOST`: Database hostname (default: localhost)
- `DB_USER`: Database username (default: root)
- `DB_PASSWORD`: Database password
- `DB_NAME`: Database name (default: test_master)

## Manual Execution

If you prefer to install dependencies and run the script separately:

1. Install required packages:
   ```
   pip install mysql-connector-python timezonefinder python-dotenv
   ```

2. Run the script:
   ```
   python set_timezones.py
   ```

The script will process records in batches to avoid memory issues with large datasets.