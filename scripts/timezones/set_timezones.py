import mysql.connector
from timezonefinder import TimezoneFinder
import time
import sys
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', ''),
    'database': os.environ.get('DB_NAME', 'test_master')
}

BATCH_SIZE = 1000

def get_db_connection():
    return mysql.connector.connect(**DB_CONFIG)

def backfill_timezones_for_table(table, id_field, lat_field, lon_field):
    print(f"Starting timezone backfill for {table}...")
    tf = TimezoneFinder()
    db_conn = None

    try:
        db_conn = get_db_connection()
        cursor = db_conn.cursor()

        # Count records to process
        cursor.execute(
            f"SELECT COUNT(*) FROM {table} WHERE {lat_field} IS NOT NULL AND {lon_field} IS NOT NULL AND timezone = ''"
        )
        total_to_process = cursor.fetchone()[0]
        print(f"Found {total_to_process} {table} records needing a timezone.")

        if total_to_process == 0:
            print(f"No {table} records to update. Exiting.")
            return

        processed_count = 0

        while True:
            query = (
                f"SELECT {id_field}, {lat_field}, {lon_field} "
                f"FROM {table} "
                f"WHERE {lat_field} IS NOT NULL AND {lon_field} IS NOT NULL AND timezone = '' "
                f"LIMIT {BATCH_SIZE}"
            )
            cursor.execute(query)
            batch = cursor.fetchall()

            if not batch:
                print(f"No more {table} records to process. Loop finished.")
                break

            updates_to_execute = []
            for rec_id, latitude, longitude in batch:
                if latitude is not None and longitude is not None:
                    timezone_name = tf.timezone_at(lng=float(longitude), lat=float(latitude))
                    if timezone_name:
                        updates_to_execute.append((timezone_name, rec_id))

            if updates_to_execute:
                update_query = f"UPDATE {table} SET timezone = %s WHERE {id_field} = %s"
                cursor.executemany(update_query, updates_to_execute)
                db_conn.commit()

            processed_count += len(batch)
            print(f"Processed {processed_count} of {total_to_process} records in {table}...")

    except mysql.connector.Error as err:
        print(f"Database error: {err}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if db_conn and db_conn.is_connected():
            cursor.close()
            db_conn.close()
            print(f"Database connection for {table} closed.")

if __name__ == "__main__":
    backfill_timezones_for_table('cities', 'id', 'latitude', 'longitude')
    backfill_timezones_for_table('ab_companies', 'ab_company_id', 'latitude', 'longitude')