# QuickBooks SDK Logging Configuration

This document explains how to enable and configure logging for QuickBooks SDK remote calls using the official `intuit-config.xml` configuration file.

## Overview

The QuickBooks SDK logging implementation provides comprehensive logging of all API calls including:
- Request parameters and payloads
- Response data
- Execution times
- Error details
- HTTP-level communication via the official SDK configuration
- Environment-specific settings (sandbox vs production)

## Configuration Approach

### 1. XML-Based Configuration (Recommended)

The primary configuration is done via `src/main/resources/intuit-config.xml`. This is the **official way** recommended by Intuit for configuring the QuickBooks SDK.

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Logging Configuration -->
    <property name="REQUEST_RESPONSE_LOGGING_ENABLED" value="true"/>
    <property name="LOGGING_ENABLED" value="true"/>
    <property name="ENABLE_REQUEST_RESPONSE_LOGGING" value="true"/>
    <property name="VERBOSE_LOGGING" value="true"/>
    
    <!-- Environment Configuration -->
    <property name="BASE_URL_QBO" value="https://sandbox-quickbooks.api.intuit.com/v3/company"/>
    
    <!-- Performance Settings -->
    <property name="TIMEOUT" value="30000"/>
    <property name="COMPRESSION_ENABLED" value="true"/>
</configuration>
```

### 2. Application Properties Override

You can override XML settings via `application.yml`:

```yaml
quickbooks:
  environment: sandbox  # or production
  logging:
    enabled: true              # Enable/disable QuickBooks API logging
    detailed: false            # Enable detailed logging (includes full request/response bodies)
    log-requests: true         # Log request parameters
    log-responses: true        # Log response data
    log-execution-time: true   # Log execution time for each API call
    log-errors: true           # Log errors and exceptions
```

### 3. Spring Boot Logging Levels

Configure logging levels in your `application.yml`:

```yaml
logging:
  level:
    # QuickBooks SDK logging
    com.intuit.ipp: DEBUG
    com.intuit.ipp.services: DEBUG
    com.intuit.ipp.core: DEBUG
    com.intuit.ipp.util: DEBUG
    # Your QuickBooks wrapper classes
    com.bulkloads.web.infra.quickbooks: DEBUG
```

## Features

### 1. XML-Based SDK Configuration
- Official Intuit-supported configuration method
- Comprehensive settings for all SDK aspects
- Environment-specific configurations
- Performance and timeout settings

### 2. Dynamic Configuration Management
- `QuickBooksConfigurationManager` handles environment-specific overrides
- Application properties can override XML settings
- Automatic configuration based on active profile

### 3. Application-Level Logging
- Custom wrapper (`QbLoggingWrapper`) around all DataService calls
- Provides structured logging with consistent format
- Configurable via application properties

### 4. Logging Output Examples

#### Basic API Call Logging:
```
INFO  - QuickBooks API Call - findById: entityType=Customer, entityId=123
INFO  - QuickBooks API Response - findById: entityType=Customer, entityId=123, duration=245ms, found=true
```

#### SDK-Level HTTP Logging (when enabled):
```
DEBUG - QuickBooks SDK HTTP Request: POST https://sandbox-quickbooks.api.intuit.com/v3/company/123/customers
DEBUG - QuickBooks SDK HTTP Response: 200 OK, Content-Length: 1024
```

#### Configuration Summary:
```
QuickBooks SDK Configuration:
- Environment: sandbox
- Base URL: https://sandbox-quickbooks.api.intuit.com/v3/company
- Logging Enabled: true
- Detailed Logging: false
- Timeout: 30000 ms
- Max Connections: 10
```

## Usage

### Testing the Configuration

1. Enable logging in your configuration
2. Make a QuickBooks API call through your application
3. Check the application logs for detailed output

Test endpoints:
```
GET /rest/quickbooks/test/logging    # Test API calls with logging
GET /rest/quickbooks/test/config     # View current configuration
```

### Production Considerations

1. **Performance**: Detailed logging can impact performance. Use `detailed=false` in production.
2. **Log Volume**: QuickBooks API calls can generate significant log volume. Consider log rotation.
3. **Sensitive Data**: Be careful with detailed logging as it may include sensitive business data.
4. **Environment**: The configuration automatically switches between sandbox and production URLs.

## Configuration Files

### Key Files:
1. **`src/main/resources/intuit-config.xml`** - Primary SDK configuration
2. **`QuickBooksConfigurationManager.java`** - Dynamic configuration management
3. **`QuickBooksLoggingProperties.java`** - Application properties binding
4. **`QbLoggingWrapper.java`** - Application-level logging wrapper
5. **`application.yml`** - Environment-specific overrides

### Configuration Properties

| XML Property | Application Property | Description |
|-------------|---------------------|-------------|
| `LOGGING_ENABLED` | `quickbooks.logging.enabled` | Master switch for SDK logging |
| `VERBOSE_LOGGING` | `quickbooks.logging.detailed` | Include full request/response bodies |
| `LOG_REQUEST_BODY` | `quickbooks.logging.log-requests` | Log request parameters |
| `LOG_RESPONSE_BODY` | `quickbooks.logging.log-responses` | Log response data |
| `LOG_EXECUTION_TIME` | `quickbooks.logging.log-execution-time` | Log API call duration |
| `BASE_URL_QBO` | `quickbooks.environment` | Environment (sandbox/production) |

## Architecture

### Configuration Hierarchy:
1. **XML Configuration** (`intuit-config.xml`) - Base configuration
2. **Configuration Manager** - Environment-specific overrides
3. **Application Properties** - Runtime configuration
4. **Spring Logging** - Framework-level logging

### Benefits of XML Approach:
- **Official Support**: Recommended by Intuit
- **Comprehensive**: Covers all SDK configuration aspects
- **Performance**: Optimized by the SDK
- **Maintainable**: Clear separation of concerns
- **Flexible**: Can be overridden programmatically when needed

## Troubleshooting

1. **No logs appearing**: 
   - Check `intuit-config.xml` has `LOGGING_ENABLED=true`
   - Verify Spring logging levels are set to DEBUG
   - Ensure `quickbooks.logging.enabled=true` in application.yml

2. **Too much detail**: 
   - Set `VERBOSE_LOGGING=false` in XML or `detailed=false` in properties
   - Reduce Spring logging levels to INFO

3. **Performance issues**: 
   - Disable detailed logging in production
   - Adjust timeout settings in XML configuration

4. **Environment issues**:
   - Check `BASE_URL_QBO` property in logs
   - Verify `quickbooks.environment` setting
   - Use `/rest/quickbooks/test/config` endpoint to view current config

## Security Notes

- XML configuration may include sensitive settings
- Detailed logging may include sensitive data (customer info, financial data)
- Consider log retention policies for compliance
- Ensure logs are properly secured and access-controlled
- Use environment-specific configurations for production
