# Bulkloads-v2 Development Guide

## Build/Test/Lint Commands
- Build: `./gradlew clean build`
- Run all tests: `./gradlew test`
- Run single test class: `./gradlew test --tests com.bulkloads.package.TestClass`
- Run single test method: `./gradlew test --tests com.bulkloads.package.TestClass.methodName`
- Lint (checkstyle): `./gradlew checkstyleMain checkstyleTest`
- Docker build: `./gradlew bootBuildImage`

## Code Style Guidelines
- Line length: Max 160 characters
- Indentation: 2 spaces (no tabs)
- Naming: camelCase for variables/methods, PascalCase for classes
- Imports: No wildcard imports, sorted alphabetically within groups
- Error handling: Use custom exceptions (BulkloadsException, ServiceException)
- Use Lombok annotations (@Getter, @Setter, @Builder) to reduce boilerplate
- Optional<T>: Use as return type, not parameters
- Follow Java bean naming conventions
- Use interfaces for implementation hiding

## Testing
- JUnit 5 with TestContainers for integration tests
- Base classes: TestContainerTest, IntegrationTest, RepositoryTest