digraph prod_env {
    label = "Production Environment";
    
    graph [fontname = "Helvetica"; rankdir = LR; compound = true;];
    node [shape = box; fontname = "Helvetica";];
    edge [fontname = "Helvetica";];
    
    subgraph cluster_prod_1_server {
        label = "bulkloads.com (prod1)";
        style = filled;
        color = lightgrey;
        node [shape = component;];
        
        subgraph cluster_github_runner_1 {
            label = "Github Runner 1";
            style = "filled";
            color = lightblue;
            pencolor = black;
            github_runner_1;
        }
        
        subgraph cluster_docker_1 {
            label = "Docker 1";
            style = filled;
            color = lightblue;
            spring_1 [label = "Spring :9000";];
            rabbitmq_1 [label = "RabbitMQ";];
        }
        
        subgraph cluster_pm2_1 {
            label = "PM2_1";
            style = "filled";
            color = lightsteelblue;
            nextjs_1 [label = "NextJS :3000";];
        }
        
        nginx_1 [label = "NGINX";URL = "";];
        static_files_1 [label = "/var/www/tms";shape = "folder";];
    }
    
    subgraph cluster_prod_2_server {
        label = "bulkloads.com (prod2)";
        style = filled;
        color = lightgrey;
        node [shape = component;];
        
        subgraph cluster_github_runner_2 {
            label = "Github Runner 2";
            style = "filled";
            color = lightblue;
            pencolor = black;
            github_runner_2;
        }
        
        subgraph cluster_docker_2 {
            label = "Docker 2";
            style = filled;
            color = lightblue;
            
            spring_2 [label = "Spring :9000";];
            rabbitmq_2 [label = "RabbitMQ";];
        }
        
        subgraph cluster_pm2_2 {
            label = "PM2_1";
            style = "filled";
            color = lightsteelblue;
            nextjs_2 [label = "NextJS :3000";];
        }
        
        nginx_2 [label = "NGINX";URL = "";];
        static_files_2 [label = "/var/www/tms";shape = "folder";];
    }
    
    subgraph cluster_cf_1_server {
        label = "ColdFusion Node1";
        style = filled;
        color = lightgrey;
        node [shape = component;];
        coldfusion_1 [label = "CF1";];
    }
    
    subgraph cluster_cf_2_server {
        label = "ColdFusion Node2";
        style = filled;
        color = lightgrey;
        node [shape = component;];
        coldfusion_2 [label = "CF2";];
    }
    
    subgraph cluster_mysql_server {
        label = "10.8.84.10 / s08194";
        style = filled;
        color = lightgrey;
        node [shape = component;];
        mysql [label = "DB";shape = cylinder;];
    }
    
    // Out of node 1
    spring_1 -> rabbitmq_1 [label = "";];
    spring_1 -> mysql [label = ""; lhead = cluster_mysql_server;];
    coldfusion_1 -> mysql [label = ""; lhead = cluster_mysql_server;];
    
    nginx_1 -> spring_1 [label = "";];
    nginx_1 -> nextjs_1 [label = "";];
    nginx_1 -> static_files_1 [label = "";];
    
    nginx_1 -> coldfusion_1 [style = dashed; color = "#ff6600"; arrowhead = vee; penwidth = 2; lhead = cluster_cf_1_server;];
    nginx_1 -> coldfusion_2 [style = dashed; color = "#ff6600"; arrowhead = vee; penwidth = 2; lhead = cluster_cf_2_server;];
    
    // Out of node 2
    
    spring_2 -> rabbitmq_2 [label = "";];
    spring_2 -> mysql [label = ""; lhead = cluster_mysql_server;];
    coldfusion_2 -> mysql [label = ""; lhead = cluster_mysql_server;];
    
    nginx_2 -> spring_2 [label = "";];
    nginx_2 -> nextjs_2 [label = "";];
    nginx_2 -> static_files_2 [label = "";];
    
    nginx_2 -> coldfusion_1 [style = dashed; color = "#ff6600"; arrowhead = vee; penwidth = 2; lhead = cluster_cf_1_server;];
    nginx_2 -> coldfusion_2 [style = dashed; color = "#ff6600"; arrowhead = vee; penwidth = 2; lhead = cluster_cf_2_server;];
}