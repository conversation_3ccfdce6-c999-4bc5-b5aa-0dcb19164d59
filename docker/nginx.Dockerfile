FROM ubuntu:24.04

RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    git openssh-client python3 python3-pip ansible-core nginx curl jq unzip && \
    rm -rf /var/lib/apt/lists/*

RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y nodejs && \
    npm install -g yarn && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /opt
ENV DEVOPS_DIR=/opt/bulkloads-devops

RUN mkdir -p /opt/bulkloads-tms /var/www/tms

COPY nginx-entrypoint.sh /usr/local/bin/nginx-entrypoint
RUN chmod +x /usr/local/bin/nginx-entrypoint

EXPOSE 80 443
ENTRYPOINT ["nginx-entrypoint"]
