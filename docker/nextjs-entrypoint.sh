#!/usr/bin/env sh

set -euo pipefail
# set -x
: "${GITHUB_TOKEN:?GITHUB_TOKEN is not set – aborting.}"

BRANCH="${NEXTJS_BRANCH:-test}"
REPO_DIR="/app"

git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "**************:"
git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "ssh://**************/"

repo_url="https://${GITHUB_TOKEN}@github.com/bulkloads/bulkloads-nextjs.git"

if [ -d "$REPO_DIR/.git" ]; then
  echo "▶ Repo exists – pulling latest"
  git -C "$REPO_DIR" remote set-url origin "$repo_url"
  git -C "$REPO_DIR" fetch origin "$BRANCH"
  git -C "$REPO_DIR" reset --hard "origin/$BRANCH"
else
  echo "▶ First‑time clone into [$REPO_DIR]"
  git clone --depth 1 --branch "$BRANCH" "$repo_url" "$REPO_DIR"
fi

cd "$REPO_DIR"

echo "▶ Installing dependencies"
yarn install --immutable

echo "▶ Starting local environment of NextJS"
exec yarn dev:local
