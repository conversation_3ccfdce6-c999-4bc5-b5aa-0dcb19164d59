plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.3'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'org.asciidoctor.jvm.convert' version '3.3.2'
    id 'checkstyle'
    id "com.gorylenko.gradle-git-properties" version "2.4.1"
}

group = 'com.bulkloads'
version = '0.0.1'

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}
configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
    google()
    maven { url 'https://jitpack.io' }
}

ext {
    set('snippetsDir', file("build/generated-snippets"))
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'org.springframework.boot:spring-boot-starter-freemarker'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework:spring-webflux'

    implementation "software.amazon.awssdk:s3:$awsSdkS3"

    implementation "org.hsqldb:hsqldb:$hsqlDbVersion"
    implementation "com.h2database:h2:$h2DbVersion"

    implementation 'org.liquibase:liquibase-core'
    implementation "org.reflections:reflections:$reflectionsVersion"
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:$springDocOpenApiVersion"
    implementation "org.jetbrains:annotations:$jetBrainsAnnotationsVersion"
    implementation "net.jodah:typetools:$typeToolsVersion"
    implementation "org.apache.commons:commons-lang3:$commonsLangVersion"
    implementation "org.apache.commons:commons-text:$commonsTextVersion"
    implementation "commons-io:commons-io:$commonsIoVersion"

    implementation "org.apache.poi:poi:$apachePoiVersion"
    implementation "org.apache.poi:poi-ooxml:$apachePoiVersion"
    implementation "commons-validator:commons-validator:$commonsValidatorVersion"
    implementation "com.google.guava:guava:$guavaVersion"
    implementation "org.passay:passay:$passayVersion"
    implementation "org.hibernate.validator:hibernate-validator:$hibernateValidatorVersion"
    implementation "com.fasterxml.jackson.module:jackson-modules-java8:$jacksonModulesJava8Version"
    implementation "jakarta.xml.bind:jakarta.xml.bind-api:$jakartaXmlBindApiVersion"
    implementation "org.codehaus.groovy:groovy-templates:$groovyTemplatesVersion"
    implementation "com.googlecode.libphonenumber:libphonenumber:$libphonenumberVersion"
    implementation "com.twilio.sdk:twilio:$twilioVersion"
    implementation "com.pubnub:pubnub-gson:$pubNubVersion"
    implementation "com.google.maps:google-maps-services:$googleMapsServicesVersion"
    implementation "com.mapbox.mapboxsdk:mapbox-sdk-services:$mapboxServicesVersion"
    implementation "androidx.annotation:annotation:$androidxAnnotationVersion"
    implementation "org.jsoup:jsoup:$jsoupVersion"
    implementation "com.sendgrid:sendgrid-java:$sendGridVersion"
    implementation "org.mapstruct:mapstruct:$mapstructVersion"
    implementation "com.openhtmltopdf:openhtmltopdf-pdfbox:$openHtmlToPdfVersion"
    implementation "org.apache.pdfbox:pdfbox:$pdfBoxVersion"
    implementation "org.apache.commons:commons-lang3:$apacheCommonsLang3Version"
    implementation "com.google.firebase:firebase-admin:$firebaseVersion"
    implementation "com.github.qianshui423:qs:$qsVersion"
    implementation "org.atteo:evo-inflector:$evoInflectorVersion"
    implementation "net.javacrumbs.shedlock:shedlock-spring:$shedlockVersion"
    implementation "net.javacrumbs.shedlock:shedlock-provider-jdbc-template:$shedlockVersion"
    implementation "org.sqids:sqids:$sqidsVersion"

    implementation "com.intuit.quickbooks-online:payments-api:$quickbooksSdkVersion"
    implementation "com.intuit.quickbooks-online:ipp-v3-java-devkit:$quickbooksSdkVersion"

    implementation "com.twelvemonkeys.imageio:imageio-jpeg:$imageioJpegVersion"
    implementation "com.drewnoakes:metadata-extractor:$metadataExtractorVersion"

    implementation "io.jsonwebtoken:jjwt-api:$jjwtVersion"
    runtimeOnly "io.jsonwebtoken:jjwt-impl:$jjwtVersion"
    runtimeOnly "io.jsonwebtoken:jjwt-gson:$jjwtVersion"
    runtimeOnly 'com.mysql:mysql-connector-j'
    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor",
            "org.mapstruct:mapstruct-processor:${mapstructVersion}",
            "org.projectlombok:lombok",
            "org.projectlombok:lombok-mapstruct-binding:${lombokMapstructBindingVersion}")

    testCompileOnly 'org.projectlombok:lombok'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'com.vaadin.external.google', module: 'android-json'
    }
    testImplementation 'org.springframework.restdocs:spring-restdocs-mockmvc'
    testImplementation 'org.springframework.security:spring-security-test'

    testImplementation "org.testcontainers:testcontainers:$testContainersVersion"
    testImplementation "org.testcontainers:mariadb:$testContainersVersion"
    testImplementation 'org.testcontainers:junit-jupiter'

    testRuntimeOnly "org.mariadb.jdbc:mariadb-java-client:$mariaDbClientVersion"
    testRuntimeOnly "com.h2database:h2:$h2DbVersion"

    testAnnotationProcessor 'org.projectlombok:lombok'
    // implementation 'com.itextpdf:itextpdf:5.0.6'

    implementation "net.iakovlev:timeshape:$timeshapeVersion"
    implementation "com.github.luben:zstd-jni:$zstdJniVersion"
}

checkstyle {
    toolVersion = "10.12.5"
    ignoreFailures = false
    maxWarnings = 0
    tasks.withType(Checkstyle).tap {
        configureEach {
            exclude '**/com/bulkloads/web/mcp/ext/**'
        }
    }
}

gitProperties {
    gitPropertiesResourceDir = file("${project.projectDir}/src/main/resources")
}

tasks.processResources {
    dependsOn tasks.generateGitProperties
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

tasks.named('test') {
    outputs.dir snippetsDir
    useJUnitPlatform()
}

tasks.named('asciidoctor') {
    inputs.dir snippetsDir
    dependsOn test
}

bootJar {
    archiveFileName = 'bulkloads-server.jar'
}

static def isWindows() {
    def os = System.getProperty("os.name").toLowerCase()
    return os.contains("win")
}


static def getCleanupCommandLine() {
    if (isWindows()) {
        return ['cmd', '/c', 'docker image prune -f']
    } else {
        return ['sh', '-c', 'docker image prune -f']
    }
}

task cleanOldImages(type: Exec) {
    doFirst {
        commandLine getCleanupCommandLine()
    }
    doLast {
        logger.lifecycle 'Old Docker images and volumes have been cleaned up.'
    }
}

bootBuildImage {
    imageName = "bulkloads-server"
    tags = ["bulkloads-server:$version", "bulkloads-server:latest"]
    runImage = "paketobuildpacks/run-jammy-full:latest"

    def fusionReactorInstanceName = project.findProperty('fusionReactorInstanceName') ?: ""

    if (project.hasProperty("fusionReactorInstanceName")) {
        if (fusionReactorInstanceName.isEmpty()) {
            throw new GradleException("The fusionReactorInstanceName is empty.")
        }
        logger.lifecycle("Using fusionReactorAgentJar=[/fusionreactor/fusionreactor.jar] and fusionReactorInstanceName=[${fusionReactorInstanceName}]")

        def javaAgent = "-javaagent:/fusionreactor/fusionreactor.jar=${fusionReactorInstanceName},address=8088"

        environment = ["BPE_DELIM_JAVA_TOOL_OPTIONS" : " ",
                       "BPE_APPEND_JAVA_TOOL_OPTIONS": "${javaAgent}"]
    } else {
        logger.lifecycle('Building without Fusion Reactor')
    }
    finalizedBy cleanOldImages
}


